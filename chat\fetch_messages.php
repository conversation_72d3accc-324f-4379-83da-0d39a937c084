<?php
require_once '../includes/db.php';

$sender_id = $_POST['sender_id'] ?? null;
$receiver_id = $_POST['receiver_id'] ?? null;
$sender_role = $_POST['sender_role'] ?? '';

if (!$sender_id || !$receiver_id || !in_array($sender_role, ['student', 'alumni'])) {
    echo "<div class='text-danger'>Invalid chat request.</div>";
    exit;
}

$opposite_role = $sender_role === 'student' ? 'alumni' : 'student';

// Prepare the SQL statement to fetch messages along with sender details
$stmt = $conn->prepare("
    SELECT m.*, 
           CASE 
               WHEN m.sender_role = 'student' THEN s.full_name 
               ELSE a.full_name 
           END as sender_name,
           CASE 
               WHEN m.sender_role = 'student' THEN s.profile_photo 
               ELSE a.profile_photo 
           END as sender_photo
    FROM messages m
    LEFT JOIN students s ON m.sender_id = s.id AND m.sender_role = 'student'
    LEFT JOIN alumni a ON m.sender_id = a.id AND m.sender_role = 'alumni'
    WHERE (m.sender_id = ? AND m.receiver_id = ? AND m.sender_role = ?) 
       OR (m.sender_id = ? AND m.receiver_id = ? AND m.sender_role = ?)
    ORDER BY m.created_at ASC
");
$stmt->execute([$sender_id, $receiver_id, $sender_role, $receiver_id, $sender_id, $opposite_role]);

$messages = $stmt->fetchAll(PDO::FETCH_ASSOC);

foreach ($messages as $msg) {
    $align = $msg['sender_id'] == $sender_id && $msg['sender_role'] == $sender_role ? 'text-end text-primary' : 'text-start text-secondary';
    echo "<div class='$align mb-1'>";
    echo "<img src='../uploads/" . htmlspecialchars($msg['sender_photo']) . "' alt='Profile Photo' class='rounded-circle' style='width: 30px; height: 30px; cursor: pointer;' onclick='openProfile(\"" . htmlspecialchars($msg['sender_id']) . "\", \"" . htmlspecialchars($msg['sender_role']) . "\")'>";
    echo "<strong> :</strong> " . htmlspecialchars($msg['message']);
    echo "</div>";
}
?>