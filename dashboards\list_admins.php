<?php
session_start();
require_once '../includes/db.php';
require_once '../includes/header.php';

if (!isset($_SESSION['superadmin_id'])) {
    header("Location: ../auth/superadmin_login.php");
    exit;
}

// Pagination setup
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 10; // Items per page
$offset = ($page - 1) * $limit;

// Search functionality
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$searchCondition = '';
$params = [];

if (!empty($search)) {
    $searchCondition = " AND (ca.name LIKE ? OR ca.email LIKE ? OR c.name LIKE ?)";
    $searchParam = "%$search%";
    $params = [$searchParam, $searchParam, $searchParam];
}

// Count total admins for pagination
$countSql = "SELECT COUNT(*) FROM college_admins ca JOIN colleges c ON ca.college_id = c.id WHERE 1=1" . $searchCondition;
$countStmt = $conn->prepare($countSql);
if (!empty($search)) {
    $countStmt->execute($params);
} else {
    $countStmt->execute();
}
$totalAdmins = $countStmt->fetchColumn();
$totalPages = ceil($totalAdmins / $limit);

// Fetch all college admins with their college names
$sql = "SELECT ca.*, c.name AS college_name 
        FROM college_admins ca 
        JOIN colleges c ON ca.college_id = c.id 
        WHERE 1=1" . $searchCondition . "
        ORDER BY ca.created_at DESC
        LIMIT $limit OFFSET $offset";

$stmt = $conn->prepare($sql);
if (!empty($search)) {
    $stmt->execute($params);
} else {
    $stmt->execute();
}
$admins = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!-- Main Content -->
<div class="container mx-auto">
    <div class="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">College Administrators</h1>
            <p class="text-gray-600 dark:text-gray-400 mt-1">Manage all college admin accounts</p>
        </div>
        <div class="mt-4 md:mt-0">
            <a href="assign_admin.php" class="btn btn-primary flex items-center">
                <i class="fas fa-user-plus mr-2"></i> Add New Admin
            </a>
        </div>
    </div>
    
    <!-- Search and Filters -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 mb-6">
        <div class="p-4">
            <form method="GET" class="flex flex-col sm:flex-row gap-4">
                <div class="flex-grow relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                    <input 
                        type="text" 
                        name="search" 
                        value="<?= htmlspecialchars($search) ?>" 
                        class="form-input pl-10 w-full" 
                        placeholder="Search by name, email or college..."
                    >
                </div>
                <div class="flex gap-2">
                    <button type="submit" class="btn btn-primary">
                        Search
                    </button>
                    <?php if (!empty($search)): ?>
                        <a href="list_admins.php" class="btn btn-outline">
                            Clear
                        </a>
                    <?php endif; ?>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Admins List -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700">
        <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4 flex justify-between items-center">
            <h2 class="font-semibold text-gray-800 dark:text-white">
                All College Admins
                <?php if ($totalAdmins > 0): ?>
                    <span class="ml-2 text-sm text-gray-500 dark:text-gray-400">
                        (<?= $totalAdmins ?> total)
                    </span>
                <?php endif; ?>
            </h2>
            <?php if (!empty($search)): ?>
                <span class="bg-primary-light dark:bg-primary-dark/30 text-primary dark:text-primary-light text-sm py-1 px-3 rounded-full">
                    Search results for: "<?= htmlspecialchars($search) ?>"
                </span>
            <?php endif; ?>
        </div>
        
        <?php if (count($admins) > 0): ?>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead>
                        <tr>
                            <th class="px-6 py-3 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">ID</th>
                            <th class="px-6 py-3 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Admin Name</th>
                            <th class="px-6 py-3 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Email</th>
                            <th class="px-6 py-3 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">College</th>
                            <th class="px-6 py-3 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Assigned On</th>
                            <th class="px-6 py-3 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                        <?php foreach ($admins as $admin): ?>
                            <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"><?= $admin['id'] ?></td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center text-blue-600 dark:text-blue-400 mr-3">
                                            <i class="fas fa-user-shield"></i>
                                        </div>
                                        <span class="font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($admin['name']) ?></span>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400"><?= htmlspecialchars($admin['email']) ?></td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-block bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 text-xs px-2 py-1 rounded">
                                        <?= htmlspecialchars($admin['college_name']) ?>
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                    <?= date('M d, Y', strtotime($admin['created_at'])) ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm">
                                    <div class="flex space-x-2">
                                        <a href="view_admin.php?id=<?= $admin['id'] ?>" class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300">
                                            <i class="fas fa-eye"></i> View
                                        </a>
                                        <a href="edit_admin.php?id=<?= $admin['id'] ?>" class="text-yellow-600 dark:text-yellow-400 hover:text-yellow-800 dark:hover:text-yellow-300">
                                            <i class="fas fa-edit"></i> Edit
                                        </a>
                                        <a href="#" onclick="confirmDelete(<?= $admin['id'] ?>)" class="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300">
                                            <i class="fas fa-trash-alt"></i> Delete
                                        </a>
                                        <a href="reset_admin_password.php?id=<?= $admin['id'] ?>" class="text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300">
                                            <i class="fas fa-key"></i> Reset Password
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <?php if ($totalPages > 1): ?>
                <div class="px-6 py-4 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-700">
                    <div class="flex justify-between items-center">
                        <div class="text-sm text-gray-500 dark:text-gray-400">
                            Showing <?= ($offset + 1) ?> to <?= min($offset + $limit, $totalAdmins) ?> of <?= $totalAdmins ?> admins
                        </div>
                        <div class="flex space-x-1">
                            <?php if ($page > 1): ?>
                                <a href="?page=<?= ($page - 1) ?><?= !empty($search) ? '&search=' . urlencode($search) : '' ?>" class="btn btn-outline btn-sm">
                                    <i class="fas fa-chevron-left mr-1"></i> Previous
                                </a>
                            <?php endif; ?>
                            
                            <?php if ($page < $totalPages): ?>
                                <a href="?page=<?= ($page + 1) ?><?= !empty($search) ? '&search=' . urlencode($search) : '' ?>" class="btn btn-outline btn-sm">
                                    Next <i class="fas fa-chevron-right ml-1"></i>
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        <?php else: ?>
            <div class="p-6 text-center">
                <?php if (!empty($search)): ?>
                    <div class="py-12">
                        <div class="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-search text-gray-400 text-2xl"></i>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No matching results</h3>
                        <p class="text-gray-500 dark:text-gray-400 mb-4">We couldn't find any admin matching your search.</p>
                        <a href="list_admins.php" class="btn btn-outline">
                            Clear Search
                        </a>
                    </div>
                <?php else: ?>
                    <div class="py-12">
                        <div class="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-user-shield text-gray-400 text-2xl"></i>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No admins assigned yet</h3>
                        <p class="text-gray-500 dark:text-gray-400 mb-4">Get started by assigning your first college admin.</p>
                        <a href="assign_admin.php" class="btn btn-primary">
                            <i class="fas fa-user-plus mr-2"></i> Assign Admin
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>
</div>

<script>
function confirmDelete(adminId) {
    if (confirm('Are you sure you want to delete this admin? This action cannot be undone.')) {
        window.location.href = 'delete_admin.php?id=' + adminId;
    }
}
</script>

<?php require_once '../includes/footer.php'; ?>
