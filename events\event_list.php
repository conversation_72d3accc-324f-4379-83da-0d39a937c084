<?php
ini_set('display_errors', 1);
error_reporting(E_ALL);

require_once '../includes/db.php';

// Check session
if (!isset($_SESSION['student_id']) && !isset($_SESSION['faculty_id']) && !isset($_SESSION['alumni_id']) && !isset($_SESSION['collegeadmin_id'])) {
    // Redirect to appropriate login page instead of logout
    if (isset($_SERVER['HTTP_REFERER']) && strpos($_SERVER['HTTP_REFERER'], 'collegeadmin') !== false) {
        header("Location: ../auth/collegeadmin_login.php");
    } else if (isset($_SERVER['HTTP_REFERER']) && strpos($_SERVER['HTTP_REFERER'], 'faculty') !== false) {
        header("Location: ../auth/faculty_login.php");
    } else if (isset($_SERVER['HTTP_REFERER']) && strpos($_SERVER['HTTP_REFERER'], 'alumni') !== false) {
        header("Location: ../auth/alumni_login.php");
    } else if (isset($_SERVER['HTTP_REFERER']) && strpos($_SERVER['HTTP_REFERER'], 'student') !== false) {
        header("Location: ../auth/student_login.php");
    } else {
        header("Location: ../auth/collegeadmin_login.php");
    }
    exit;
}

$college_id = $_SESSION['college_id'];

// Set role explicitly for the sidebar
if (isset($_SESSION['collegeadmin_id'])) {
    $role = 'collegeadmin';
    $user_id = $_SESSION['collegeadmin_id'];
    $user_name = $_SESSION['collegeadmin_name'] ?? 'Admin';
} elseif (isset($_SESSION['faculty_id'])) {
    $role = 'faculty';
    $user_id = $_SESSION['faculty_id'];
    $user_name = $_SESSION['faculty_name'] ?? 'Faculty';
} elseif (isset($_SESSION['alumni_id'])) {
    $role = 'alumni';
    $user_id = $_SESSION['alumni_id'];
    $user_name = $_SESSION['alumni_name'] ?? 'Alumni';
} elseif (isset($_SESSION['student_id'])) {
    $role = 'student';
    $user_id = $_SESSION['student_id'];
    $user_name = $_SESSION['student_name'] ?? 'Student';
}
$current_page = basename($_SERVER['PHP_SELF']);

// Get upcoming events
$stmt = $conn->prepare("SELECT * FROM events WHERE college_id = ? AND event_date >= CURDATE() ORDER BY event_date ASC");
$stmt->execute([$college_id]);
$upcoming_events = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get past events
$pastStmt = $conn->prepare("SELECT * FROM events WHERE college_id = ? AND event_date < CURDATE() ORDER BY event_date DESC LIMIT 5");
$pastStmt->execute([$college_id]);
$past_events = $pastStmt->fetchAll(PDO::FETCH_ASSOC);

// Get already registered event IDs
$registered = [];
if (isset($_SESSION['student_id'])) {
    $regStmt = $conn->prepare("SELECT event_id FROM event_attendees WHERE student_id = ?");
    $regStmt->execute([$_SESSION['student_id']]);
    $registered = array_column($regStmt->fetchAll(PDO::FETCH_ASSOC), 'event_id');
}

// Include the header
require_once '../includes/header.php';
?>

<!-- Main Content -->
<main class="flex-grow py-8 px-4">
    <div class="container mx-auto">
        <!-- Breadcrumb -->
        <nav class="mb-6 text-sm" aria-label="Breadcrumb">
            <ol class="flex items-center space-x-2">
                <li>
                    <a href="<?= $role === 'collegeadmin' ? '../dashboards/collegeadmin_dashboard.php' : '../index.php' ?>" class="text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary-light">
                        <i class="fas fa-home"></i>
                        <span class="sr-only">Dashboard</span>
                    </a>
                </li>
                <li class="flex items-center">
                    <i class="fas fa-chevron-right text-gray-400 text-xs mx-1"></i>
                    <span class="text-gray-700 dark:text-gray-300">Events</span>
                </li>
            </ol>
        </nav>

        <div class="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                    <i class="fas fa-calendar-alt text-primary mr-3"></i>
                    Events
                </h1>
                <p class="text-gray-600 dark:text-gray-400 mt-1">
                    Browse and register for upcoming events
                </p>
            </div>
            <?php if ($role === 'collegeadmin' || $role === 'faculty'): ?>
            <div class="mt-4 md:mt-0">
                <a href="create_event.php" class="btn btn-primary flex items-center">
                    <i class="fas fa-plus mr-2"></i> Create Event
                </a>
            </div>
            <?php endif; ?>
        </div>

        <?php if (isset($_SESSION['flash_message'])): ?>
            <div class="bg-green-50 dark:bg-green-900/30 border-l-4 border-green-500 text-green-700 dark:text-green-400 p-4 mb-6 flex justify-between items-center" role="alert">
                <div class="flex items-center">
                    <i class="fas fa-check-circle mr-2"></i>
                    <span><?= $_SESSION['flash_message']; unset($_SESSION['flash_message']); ?></span>
                </div>
                <button type="button" class="text-green-700 dark:text-green-400 hover:opacity-75" onclick="this.parentElement.style.display='none';">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        <?php endif; ?>

        <!-- Upcoming Events -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 mb-8">
            <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                <h2 class="text-lg font-semibold text-gray-800 dark:text-white flex items-center">
                    <i class="fas fa-calendar-day text-primary mr-2"></i> Upcoming Events
                </h2>
                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    Events scheduled for the future
                </p>
            </div>
            
            <div class="p-6">
                <?php if ($upcoming_events): ?>
                    <div class="space-y-6">
                        <?php foreach ($upcoming_events as $event): ?>
                            <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm overflow-hidden">
                                <div class="p-5">
                                    <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                                        <div>
                                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                                                <?= htmlspecialchars($event['title']) ?>
                                            </h3>
                                            <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                                                <?= htmlspecialchars($event['description']) ?>
                                            </p>
                                        </div>
                                        <?php if ($role === 'collegeadmin' || $role === 'faculty'): ?>
                                        <div class="mt-4 md:mt-0">
                                            <a href="edit_event.php?id=<?= $event['id'] ?>" class="btn btn-sm btn-outline">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <div class="mt-4 flex flex-wrap gap-4 items-center text-sm">
                                        <div class="flex items-center text-gray-700 dark:text-gray-300">
                                            <i class="fas fa-user-tie mr-2 text-primary"></i>
                                            <span><?= htmlspecialchars($event['speaker_name'] ?: 'No speaker specified') ?></span>
                                        </div>
                                        <div class="flex items-center text-gray-700 dark:text-gray-300">
                                            <i class="fas fa-calendar mr-2 text-primary"></i>
                                            <span><?= date("F j, Y", strtotime($event['event_date'])) ?></span>
                                        </div>
                                        <div class="flex items-center text-gray-700 dark:text-gray-300">
                                            <i class="fas fa-clock mr-2 text-primary"></i>
                                            <span><?= date("g:i A", strtotime($event['event_time'])) ?></span>
                                        </div>
                                    </div>
                                    
                                    <div class="mt-4 flex flex-wrap gap-2">
                                        <?php if (isset($_SESSION['student_id'])): ?>
                                            <?php if (in_array($event['id'], $registered)): ?>
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400">
                                                    <i class="fas fa-check-circle mr-1"></i> Registered
                                                </span>
                                                <?php if (!empty($event['meet_link'])): ?>
                                                    <a href="<?= htmlspecialchars($event['meet_link']) ?>" target="_blank" class="btn btn-sm btn-primary">
                                                        <i class="fas fa-video mr-1"></i> Join Meeting
                                                    </a>
                                                <?php endif; ?>
                                            <?php else: ?>
                                                <a href="event_register.php?event_id=<?= $event['id'] ?>" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-user-plus mr-1"></i> Register
                                                </a>
                                            <?php endif; ?>
                                        <?php elseif ($role === 'collegeadmin' || $role === 'faculty' || $role === 'alumni'): ?>
                                            <?php if (!empty($event['meet_link'])): ?>
                                                <a href="<?= htmlspecialchars($event['meet_link']) ?>" target="_blank" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-video mr-1"></i> Join Meeting
                                                </a>
                                            <?php endif; ?>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="bg-gray-50 dark:bg-gray-700/30 border border-gray-200 dark:border-gray-700 rounded-lg p-6 text-center">
                        <div class="flex flex-col items-center">
                            <div class="h-16 w-16 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center text-gray-400 dark:text-gray-500 mb-4">
                                <i class="fas fa-calendar-times text-2xl"></i>
                            </div>
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-1">No Upcoming Events</h3>
                            <p class="text-gray-500 dark:text-gray-400">
                                There are no events scheduled at this time. Check back later for updates.
                            </p>
                            <?php if ($role === 'collegeadmin' || $role === 'faculty'): ?>
                                <a href="create_event.php" class="btn btn-primary mt-4">
                                    <i class="fas fa-plus mr-2"></i> Create Event
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Past Events -->
        <?php if ($past_events): ?>
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700">
            <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                <h2 class="text-lg font-semibold text-gray-800 dark:text-white flex items-center">
                    <i class="fas fa-history text-primary mr-2"></i> Past Events
                </h2>
                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    Recently concluded events
                </p>
            </div>
            
            <div class="p-6">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead class="bg-gray-50 dark:bg-gray-700">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Event</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Speaker</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Date</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Time</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                            <?php foreach ($past_events as $event): ?>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($event['title']) ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500 dark:text-gray-400"><?= htmlspecialchars($event['speaker_name'] ?: 'N/A') ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500 dark:text-gray-400"><?= date("F j, Y", strtotime($event['event_date'])) ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500 dark:text-gray-400"><?= date("g:i A", strtotime($event['event_time'])) ?></div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</main>

<?php require_once '../includes/footer.php'; ?>
