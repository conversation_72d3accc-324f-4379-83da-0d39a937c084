/**
 * Mentoshri - LinkedIn-inspired JavaScript functionality
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize dropdowns
    initializeDropdowns();
    
    // Initialize mobile menu
    initializeMobileMenu();
    
    // Initialize search modal
    initializeSearchModal();
    
    // Initialize theme toggle
    initializeThemeToggle();
    
    // Initialize notifications
    initializeNotifications();
    
    // Initialize sticky sidebar
    initializeStickyElements();
    
    // Initialize tooltips
    initializeTooltips();
});

/**
 * Initialize dropdown menus with improved functionality
 */
function initializeDropdowns() {
    const dropdowns = document.querySelectorAll('[data-dropdown]');
    
    dropdowns.forEach(dropdown => {
        const trigger = dropdown.querySelector('[data-dropdown-trigger]');
        const menu = dropdown.querySelector('[data-dropdown-menu]');
        
        if (trigger && menu) {
            // Toggle dropdown on click
            trigger.addEventListener('click', (e) => {
                e.stopPropagation();
                
                // Close all other dropdowns first
                document.querySelectorAll('[data-dropdown-menu]:not(.hidden)').forEach(openMenu => {
                    if (openMenu !== menu) {
                        openMenu.classList.add('hidden');
                    }
                });
                
                menu.classList.toggle('hidden');
                
                // Add active state to trigger
                if (!menu.classList.contains('hidden')) {
                    trigger.classList.add('text-[#0a66c2]');
                } else {
                    trigger.classList.remove('text-[#0a66c2]');
                }
            });
            
            // Close dropdown when clicking outside
            document.addEventListener('click', () => {
                menu.classList.add('hidden');
                trigger.classList.remove('text-[#0a66c2]');
            });
            
            // Prevent closing when clicking inside dropdown
            menu.addEventListener('click', (e) => {
                e.stopPropagation();
            });
        }
    });
}

/**
 * Initialize mobile menu with improved transitions
 */
function initializeMobileMenu() {
    const menuButton = document.querySelector('[data-mobile-menu]');
    const mobileMenu = document.querySelector('[data-mobile-menu-panel]');
    
    if (menuButton && mobileMenu) {
        menuButton.addEventListener('click', () => {
            mobileMenu.classList.toggle('hidden');
            
            // Toggle icon
            const icon = menuButton.querySelector('i');
            if (icon) {
                if (mobileMenu.classList.contains('hidden')) {
                    icon.classList.remove('fa-times');
                    icon.classList.add('fa-bars');
                } else {
                    icon.classList.remove('fa-bars');
                    icon.classList.add('fa-times');
                }
            }
        });
    }
}

/**
 * Initialize search modal with improved UX
 */
function initializeSearchModal() {
    const searchButtons = document.querySelectorAll('[data-search-modal]');
    const searchModal = document.getElementById('searchModal');
    
    if (searchModal) {
        const closeButtons = searchModal.querySelectorAll('[data-close-modal], #closeSearchModal, #cancelSearchModal');
        const overlay = searchModal.querySelector('[data-modal-overlay], #searchModalOverlay');
        
        // Open search modal
        searchButtons.forEach(button => {
            button.addEventListener('click', () => {
                searchModal.classList.remove('hidden');
                document.body.classList.add('overflow-hidden');
                
                // Focus search input
                const searchInput = searchModal.querySelector('input[type="text"]');
                if (searchInput) {
                    setTimeout(() => {
                        searchInput.focus();
                    }, 100);
                }
            });
        });
        
        // Close search modal
        const closeModal = () => {
            searchModal.classList.add('hidden');
            document.body.classList.remove('overflow-hidden');
        };
        
        closeButtons.forEach(button => {
            if (button) button.addEventListener('click', closeModal);
        });
        
        if (overlay) {
            overlay.addEventListener('click', closeModal);
        }
        
        // Close on escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && !searchModal.classList.contains('hidden')) {
                closeModal();
            }
        });
    }
}

/**
 * Initialize theme toggle with improved visual feedback
 */
function initializeThemeToggle() {
    const themeToggles = document.querySelectorAll('#themeToggle');
    
    themeToggles.forEach(toggle => {
        if (toggle) {
            toggle.addEventListener('click', () => {
                if (document.documentElement.classList.contains('dark')) {
                    document.documentElement.classList.remove('dark');
                    localStorage.theme = 'light';
                } else {
                    document.documentElement.classList.add('dark');
                    localStorage.theme = 'dark';
                }
            });
        }
    });
    
    // Check for saved theme preference or respect OS preference
    if (localStorage.theme === 'dark' || 
        (!('theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
        document.documentElement.classList.add('dark');
    } else {
        document.documentElement.classList.remove('dark');
    }
}

/**
 * Initialize notifications dropdown
 */
function initializeNotifications() {
    const notificationButtons = document.querySelectorAll('.notification-button');
    
    notificationButtons.forEach(button => {
        if (button) {
            const dropdown = button.nextElementSibling;
            
            if (dropdown) {
                button.addEventListener('click', (e) => {
                    e.stopPropagation();
                    dropdown.classList.toggle('hidden');
                    
                    // Close all other dropdowns
                    document.querySelectorAll('.notification-dropdown:not(.hidden)').forEach(openDropdown => {
                        if (openDropdown !== dropdown) {
                            openDropdown.classList.add('hidden');
                        }
                    });
                });
                
                // Close when clicking outside
                document.addEventListener('click', () => {
                    dropdown.classList.add('hidden');
                });
                
                // Prevent closing when clicking inside dropdown
                dropdown.addEventListener('click', (e) => {
                    e.stopPropagation();
                });
            }
        }
    });
}

/**
 * Initialize sticky elements (sidebar, header, etc.)
 */
function initializeStickyElements() {
    const stickySidebars = document.querySelectorAll('.sticky-sidebar');
    
    if (stickySidebars.length > 0 && window.innerWidth >= 768) {
        window.addEventListener('scroll', () => {
            const scrollY = window.scrollY;
            
            stickySidebars.forEach(sidebar => {
                const sidebarTop = sidebar.offsetTop;
                const sidebarHeight = sidebar.offsetHeight;
                const windowHeight = window.innerHeight;
                
                if (scrollY > sidebarTop - 20) {
                    sidebar.style.position = 'sticky';
                    sidebar.style.top = '20px';
                }
            });
        });
    }
}

/**
 * Initialize tooltips
 */
function initializeTooltips() {
    const tooltips = document.querySelectorAll('[data-tooltip]');
    
    tooltips.forEach(tooltip => {
        const tooltipText = tooltip.getAttribute('data-tooltip');
        
        if (tooltipText) {
            tooltip.addEventListener('mouseenter', () => {
                const tooltipElement = document.createElement('div');
                tooltipElement.className = 'absolute z-50 bg-gray-900 text-white text-xs rounded py-1 px-2 -mt-8 -ml-2';
                tooltipElement.textContent = tooltipText;
                tooltip.appendChild(tooltipElement);
            });
            
            tooltip.addEventListener('mouseleave', () => {
                const tooltipElement = tooltip.querySelector('.absolute');
                if (tooltipElement) {
                    tooltipElement.remove();
                }
            });
        }
    });
}

/**
 * Show a toast notification
 * @param {string} message - The message to display
 * @param {string} type - The type of notification (success, error, info)
 * @param {number} duration - Duration in milliseconds
 */
function showToast(message, type = 'info', duration = 3000) {
    const toast = document.createElement('div');
    toast.className = `fixed bottom-4 right-4 px-4 py-2 rounded-lg shadow-lg z-50 transform transition-all duration-300 translate-y-0 opacity-0`;
    
    // Set background color based on type
    if (type === 'success') {
        toast.classList.add('bg-green-500', 'text-white');
    } else if (type === 'error') {
        toast.classList.add('bg-red-500', 'text-white');
    } else {
        toast.classList.add('bg-[#0a66c2]', 'text-white');
    }
    
    toast.textContent = message;
    document.body.appendChild(toast);
    
    // Show the toast
    setTimeout(() => {
        toast.classList.remove('translate-y-0', 'opacity-0');
        toast.classList.add('translate-y-0', 'opacity-100');
    }, 10);
    
    // Hide and remove the toast
    setTimeout(() => {
        toast.classList.remove('opacity-100');
        toast.classList.add('opacity-0', 'translate-y-4');
        
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 300);
    }, duration);
}
