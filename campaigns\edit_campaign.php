<?php
require_once '../includes/db.php';

// Check session
if (!isset($_SESSION['faculty_id']) && !isset($_SESSION['collegeadmin_id'])) {
    // Redirect to appropriate login page instead of logout
    if (isset($_SERVER['HTTP_REFERER']) && strpos($_SERVER['HTTP_REFERER'], 'collegeadmin') !== false) {
        header("Location: ../auth/collegeadmin_login.php");
    } else if (isset($_SERVER['HTTP_REFERER']) && strpos($_SERVER['HTTP_REFERER'], 'faculty') !== false) {
        header("Location: ../auth/faculty_login.php");
    } else {
        header("Location: ../auth/collegeadmin_login.php");
    }
    exit;
}

$college_id = $_SESSION['college_id'] ?? null;

// Set role explicitly for the sidebar
if (isset($_SESSION['collegeadmin_id'])) {
    $role = 'collegeadmin';
    $user_id = $_SESSION['collegeadmin_id'];
    $user_name = $_SESSION['collegeadmin_name'] ?? 'Admin';
    $creator_role = 'admin';
    $is_admin = true;
    $is_faculty = false;
} elseif (isset($_SESSION['faculty_id'])) {
    $role = 'faculty';
    $user_id = $_SESSION['faculty_id'];
    $user_name = $_SESSION['faculty_name'] ?? 'Faculty';
    $creator_role = 'faculty';
    $is_admin = false;
    $is_faculty = true;
}
$current_page = basename($_SERVER['PHP_SELF']);

$campaign_id = $_GET['campaign_id'] ?? null;
if (!$campaign_id || !$user_id || !$college_id) {
    header("Location: manage_campaigns.php");
    exit;
}

// Fetch campaign
$stmt = $conn->prepare("SELECT * FROM deals WHERE id = ? AND college_id = ?");
$stmt->execute([$campaign_id, $college_id]);
$campaign = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$campaign || $campaign['status'] !== 'pending') {
    $_SESSION['flash_error'] = "Campaign not found or cannot be edited.";
    header("Location: manage_campaigns.php");
    exit;
}

// Access control
if ($role === 'faculty' && ($campaign['creator_role'] !== 'faculty' || $campaign['created_by_user_id'] != $user_id)) {
    $_SESSION['flash_error'] = "You are not authorized to edit this campaign.";
    header("Location: manage_campaigns.php");
    exit;
}

require_once '../includes/header.php';

$errors = [];
$success = false;

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $title = trim($_POST['title']);
    $category = $_POST['category'];
    $goal_amount = !empty($_POST['goal_amount']) ? floatval($_POST['goal_amount']) : null;
    $contribution_types = isset($_POST['contribution_types']) ? implode(',', $_POST['contribution_types']) : '';
    $description = trim($_POST['description']);
    $start_date = $_POST['start_date'];
    $end_date = !empty($_POST['end_date']) ? $_POST['end_date'] : null;
    $contact_name = trim($_POST['contact_name']);
    $contact_email = trim($_POST['contact_email']);

    if (!$title || !$description || !$category || !$contribution_types || !$contact_email) {
        $errors[] = "Please fill in all required fields.";
    }

    // Handle optional thumbnail replacement
    if (!empty($_FILES['thumbnail']['name'])) {
        $upload_dir = '../uploads/campaigns/';
        if (!file_exists($upload_dir)) mkdir($upload_dir, 0777, true);
        $ext = pathinfo($_FILES['thumbnail']['name'], PATHINFO_EXTENSION);
        $filename = uniqid('campaign_') . '.' . $ext;
        $target_file = $upload_dir . $filename;

        if (move_uploaded_file($_FILES['thumbnail']['tmp_name'], $target_file)) {
            $thumbnail_path = 'uploads/campaigns/' . $filename;
        } else {
            $errors[] = "Failed to upload image.";
        }
    } else {
        $thumbnail_path = $campaign['thumbnail_path'];
    }

    if (empty($errors)) {
        $stmt = $conn->prepare("UPDATE deals SET 
            title = ?, category = ?, goal_amount = ?, contribution_types = ?, 
            description = ?, start_date = ?, end_date = ?, contact_name = ?, contact_email = ?, thumbnail_path = ?
            WHERE id = ?");

        $stmt->execute([
            $title, $category, $goal_amount, $contribution_types,
            $description, $start_date, $end_date, $contact_name, $contact_email, $thumbnail_path,
            $campaign_id
        ]);

        $success = true;
    }
}
?>

<!-- Main Content -->
<main class="flex-grow py-8 px-4">
    <div class="container mx-auto">
        <!-- Breadcrumb -->
        <nav class="mb-6 text-sm" aria-label="Breadcrumb">
            <ol class="flex items-center space-x-2">
                <li>
                    <a href="<?= $role === 'collegeadmin' ? '../dashboards/collegeadmin_dashboard.php' : '../dashboards/faculty_dashboard.php' ?>" class="text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary-light">
                        <i class="fas fa-home"></i>
                        <span class="sr-only">Dashboard</span>
                    </a>
                </li>
                <li class="flex items-center">
                    <i class="fas fa-chevron-right text-gray-400 text-xs mx-1"></i>
                    <a href="manage_campaigns.php" class="text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary-light">
                        Campaigns
                    </a>
                </li>
                <li class="flex items-center">
                    <i class="fas fa-chevron-right text-gray-400 text-xs mx-1"></i>
                    <span class="text-gray-700 dark:text-gray-300">Edit Campaign</span>
                </li>
            </ol>
        </nav>

        <div class="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                    <i class="fas fa-edit text-primary mr-3"></i>
                    Edit Campaign
                </h1>
                <p class="text-gray-600 dark:text-gray-400 mt-1">
                    Update details for: <?= htmlspecialchars($campaign['title']) ?>
                </p>
            </div>
            <div class="mt-4 md:mt-0">
                <a href="manage_campaigns.php" class="btn btn-outline flex items-center">
                    <i class="fas fa-arrow-left mr-2"></i> Back to Campaigns
                </a>
            </div>
        </div>

        <?php if ($success): ?>
            <div class="bg-green-50 dark:bg-green-900/30 border-l-4 border-green-500 text-green-700 dark:text-green-400 p-4 mb-6 flex justify-between items-center" role="alert">
                <div class="flex items-center">
                    <i class="fas fa-check-circle mr-2"></i>
                    <span>Campaign updated successfully!</span>
                </div>
                <button type="button" class="text-green-700 dark:text-green-400 hover:opacity-75" onclick="this.parentElement.style.display='none';">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        <?php elseif (!empty($errors)): ?>
            <div class="bg-red-50 dark:bg-red-900/30 border-l-4 border-red-500 text-red-700 dark:text-red-400 p-4 mb-6 flex justify-between items-center" role="alert">
                <div class="flex items-center">
                    <i class="fas fa-exclamation-circle mr-2"></i>
                    <span><?= implode('<br>', $errors) ?></span>
                </div>
                <button type="button" class="text-red-700 dark:text-red-400 hover:opacity-75" onclick="this.parentElement.style.display='none';">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        <?php endif; ?>

        <!-- Campaign Form -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700">
            <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                <h2 class="text-lg font-semibold text-gray-800 dark:text-white flex items-center">
                    <i class="fas fa-bullhorn text-primary mr-2"></i> Campaign Details
                </h2>
                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    Update the details below to modify this campaign
                </p>
            </div>
            
            <div class="p-6">
                <form method="POST" enctype="multipart/form-data" id="campaignForm">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div>
                            <label for="title" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Campaign Title <span class="text-red-500">*</span>
                            </label>
                            <input type="text" id="title" name="title" 
                                class="form-input w-full" 
                                value="<?= htmlspecialchars($campaign['title']) ?>"
                                placeholder="Enter a clear, descriptive title" required>
                        </div>
                        
                        <div>
                            <label for="category" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Category <span class="text-red-500">*</span>
                            </label>
                            <select id="category" name="category" class="form-select w-full" required>
                                <option value="">-- Select --</option>
                                <?php foreach (['infrastructure', 'scholarship', 'event', 'equipment', 'training'] as $cat): ?>
                                    <option value="<?= $cat ?>" <?= $campaign['category'] === $cat ? 'selected' : '' ?>><?= ucfirst($cat) ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div>
                            <label for="goal_amount" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Target Amount (Optional)
                            </label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <span class="text-gray-500 dark:text-gray-400">₹</span>
                                </div>
                                <input type="number" id="goal_amount" name="goal_amount" 
                                    class="form-input w-full pl-7" 
                                    value="<?= htmlspecialchars($campaign['goal_amount']) ?>"
                                    placeholder="Enter target amount" step="0.01">
                            </div>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Contribution Types Needed <span class="text-red-500">*</span>
                            </label>
                            <div class="flex flex-wrap gap-4">
                                <?php
                                $types = explode(',', $campaign['contribution_types']);
                                foreach (['monetary', 'in-kind', 'service'] as $type):
                                ?>
                                    <label class="inline-flex items-center">
                                        <input type="checkbox" name="contribution_types[]" value="<?= $type ?>" 
                                            class="form-checkbox"
                                            <?= in_array($type, $types) ? 'checked' : '' ?>>
                                        <span class="ml-2 text-gray-700 dark:text-gray-300"><?= ucfirst($type) ?></span>
                                    </label>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-6">
                        <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Description <span class="text-red-500">*</span>
                        </label>
                        <textarea id="description" name="description" rows="4" 
                            class="form-input w-full" 
                            placeholder="Provide details about the campaign..." required><?= htmlspecialchars($campaign['description']) ?></textarea>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div>
                            <label for="start_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Start Date <span class="text-red-500">*</span>
                            </label>
                            <input type="date" id="start_date" name="start_date" 
                                class="form-input w-full" 
                                value="<?= $campaign['start_date'] ?>" required>
                        </div>
                        
                        <div>
                            <label for="end_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                End Date (Optional)
                            </label>
                            <input type="date" id="end_date" name="end_date" 
                                class="form-input w-full"
                                value="<?= $campaign['end_date'] ?>">
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div>
                            <label for="contact_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Contact Person Name <span class="text-red-500">*</span>
                            </label>
                            <input type="text" id="contact_name" name="contact_name" 
                                class="form-input w-full"
                                value="<?= htmlspecialchars($campaign['contact_name']) ?>" required>
                        </div>
                        
                        <div>
                            <label for="contact_email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Contact Email <span class="text-red-500">*</span>
                            </label>
                            <input type="email" id="contact_email" name="contact_email" 
                                class="form-input w-full"
                                value="<?= htmlspecialchars($campaign['contact_email']) ?>" required>
                        </div>
                    </div>
                    
                    <div class="mb-6">
                        <label for="thumbnail" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Replace Thumbnail (Optional)
                        </label>
                        <input type="file" id="thumbnail" name="thumbnail" 
                            class="form-input w-full" 
                            accept="image/jpeg,image/png">
                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                            Recommended image size: 1280x720 pixels. Maximum file size: 2MB.
                        </p>
                        
                        <?php if (!empty($campaign['thumbnail_path'])): ?>
                            <div class="mt-2 p-2 border border-gray-200 dark:border-gray-700 rounded">
                                <p class="text-xs text-gray-500 dark:text-gray-400 mb-1">Current thumbnail:</p>
                                <img src="../<?= htmlspecialchars($campaign['thumbnail_path']) ?>" alt="Campaign thumbnail" class="h-32 object-cover rounded">
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="flex items-center justify-end space-x-3">
                        <a href="manage_campaigns.php" class="btn btn-outline">
                            <i class="fas fa-times mr-2"></i> Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save mr-2"></i> Update Campaign
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</main>

<?php require_once '../includes/footer.php'; ?>
