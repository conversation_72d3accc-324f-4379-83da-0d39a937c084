<?php
require_once '../includes/db.php';
require_once '../includes/header.php';

if (!isset($_SESSION['alumni_id'])) {
    header("Location: ../auth/alumni_login.php");
    exit;
}

ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

$alumni_id = $_SESSION['alumni_id'];
$college_id = $_SESSION['college_id'];
$alumni_name = $_SESSION['alumni_name'];

// Fetch total mentorships accepted
$stmt = $conn->prepare("SELECT COUNT(*) FROM student_requests WHERE alumni_id = ? AND status = 'accepted'");
$stmt->execute([$alumni_id]);
$mentorships = (int) $stmt->fetchColumn();

// Fetch total resources uploaded
$stmt = $conn->prepare("SELECT COUNT(*) FROM resources WHERE uploaded_by = ?");
$stmt->execute([$alumni_id]);
$resources = (int) $stmt->fetchColumn();

// Fetch total events participated
$stmt = $conn->prepare("SELECT COUNT(*) FROM event_participants WHERE alumni_id = ?");
$stmt->execute([$alumni_id]);
$events = (int) $stmt->fetchColumn();

// Fetch average student rating
$stmt = $conn->prepare("SELECT AVG(score) FROM ratings WHERE alumni_id = ?");
$stmt->execute([$alumni_id]);
$avg_rating_raw = $stmt->fetchColumn();
$avg_rating = $avg_rating_raw !== false ? round((float) $avg_rating_raw, 1) : 0.0;

// Fetch profile
$stmt = $conn->prepare("SELECT profile_photo, full_name, current_position, company, graduation_year, bio, skills FROM alumni WHERE id = ?");
$stmt->execute([$alumni_id]);
$alumni = $stmt->fetch(PDO::FETCH_ASSOC);

// Photo fallback
$photo = (!empty($alumni['profile_photo']) && file_exists("../uploads/{$alumni['profile_photo']}"))
    ? $alumni['profile_photo']
    : 'profile.gif';

// Mentorship Requests Count
$reqStmt = $conn->prepare("SELECT status, COUNT(*) as total FROM student_requests WHERE alumni_id = ? GROUP BY status");
$reqStmt->execute([$alumni_id]);
$stats = ['pending' => 0, 'accepted' => 0, 'rejected' => 0];
foreach ($reqStmt->fetchAll(PDO::FETCH_ASSOC) as $row) {
    $status = $row['status'];
    $total = (int) $row['total'];
    if (isset($stats[$status])) {
        $stats[$status] = $total;
    }
}

// Fetch total campaigns contributed
$stmt = $conn->prepare("SELECT COUNT(DISTINCT deal_id) FROM alumni_pledges WHERE alumni_id = ?");
$stmt->execute([$alumni_id]);
$stmt = $conn->prepare("SELECT COUNT(*) FROM alumni_pledges WHERE alumni_id = ?");
$stmt->execute([$alumni_id]);
$total_contributions = (int) $stmt->fetchColumn();

// Fetch active chats
$chatStmt = $conn->prepare("SELECT sr.student_id, s.full_name, s.profile_photo FROM student_requests sr
    JOIN students s ON sr.student_id = s.id
    WHERE sr.alumni_id = ? AND sr.status = 'accepted'");
$chatStmt->execute([$alumni_id]);
$chats = $chatStmt->fetchAll(PDO::FETCH_ASSOC);

// Fetch recent activities
$activityStmt = $conn->prepare("
    SELECT 'event_joined' as type, e.title as title, e.event_date as date, NULL as student_name
    FROM event_participants ep
    JOIN events e ON ep.event_id = e.id
    WHERE ep.alumni_id = ?
    UNION
    SELECT 'resource_shared' as type, r.title as title, r.uploaded_at as date, NULL as student_name
    FROM resources r
    WHERE r.uploaded_by = ?
    UNION
    SELECT 'mentorship_accepted' as type, NULL as title, sr.updated_at as date, s.full_name as student_name
    FROM student_requests sr
    JOIN students s ON sr.student_id = s.id
    WHERE sr.alumni_id = ? AND sr.status = 'accepted'
    ORDER BY date DESC
    LIMIT 5
");
$activityStmt->execute([$alumni_id, $alumni_id, $alumni_id]);
$activities = $activityStmt->fetchAll(PDO::FETCH_ASSOC);

// Fetch skills
$skills = !empty($alumni['skills']) ? explode(',', $alumni['skills']) : [];
?>

<!-- LinkedIn-style Dashboard Layout -->
<div class="max-w-7xl mx-auto">
    <div class="flex flex-col lg:flex-row gap-5">
        <!-- Left Column -->
        <div class="w-full lg:w-1/4">
            <!-- Profile Card -->
            <div class="profile-card mb-5 sticky-sidebar">
                <!-- Cover Photo -->
                <div class="h-24 bg-gradient-to-r from-[#0a66c2] to-[#0073b1] w-full"></div>
                
                <!-- Profile Photo & Basic Info -->
                <div class="profile-photo-container">
                    <img src="../uploads/<?= htmlspecialchars($photo) ?>" class="profile-photo" alt="Profile Photo">
                    
                    <div class="mt-3">
                        <h2 class="text-xl font-bold text-gray-800 dark:text-white"><?= htmlspecialchars($alumni['full_name'] ?? '') ?></h2>
                        <p class="text-gray-600 dark:text-gray-400"><?= htmlspecialchars($alumni['current_position'] ?? '') ?> at <?= htmlspecialchars($alumni['company'] ?? '') ?></p>
                        <p class="text-sm text-gray-500 dark:text-gray-500 mt-1">Class of <?= htmlspecialchars($alumni['graduation_year'] ?? '') ?></p>
                    </div>
                    
                    <div class="mt-3 flex space-x-2">
                        <a href="alumni_profile.php" class="btn-outline text-sm py-1.5 flex-1 flex justify-center items-center">
                            <i class="fas fa-pen text-xs mr-1.5"></i> Edit Profile
                        </a>
                        <a href="availability.php" class="btn-outline text-sm py-1.5 flex-1 flex justify-center items-center">
                            <i class="far fa-calendar text-xs mr-1.5"></i> Availability
                        </a>
                    </div>
                </div>
                
                <!-- Bio -->
                <?php if (!empty($alumni['bio'])): ?>
                <div class="px-4 py-3 border-t border-gray-200 dark:border-gray-700">
                    <p class="text-sm text-gray-700 dark:text-gray-300"><?= nl2br(htmlspecialchars($alumni['bio'] ?? '')) ?></p>
                </div>
                <?php endif; ?>
                
                <!-- Skills -->
                <?php if (!empty($skills)): ?>
                <div class="px-4 py-3 border-t border-gray-200 dark:border-gray-700">
                    <h3 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Skills</h3>
                    <div class="flex flex-wrap gap-2">
                        <?php foreach ($skills as $skill): ?>
                            <span class="bg-[#e7f3ff] text-[#0a66c2] dark:bg-blue-900 dark:text-blue-200 text-xs px-2 py-1 rounded-full"><?= htmlspecialchars(trim($skill)) ?></span>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>
                
                <!-- Dashboard Stats -->
                <div class="px-4 py-3 border-t border-gray-200 dark:border-gray-700">
                    <h3 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Your Dashboard</h3>
                    
                    <div class="space-y-2">
                        <div class="flex items-center">
                            <div class="w-7 h-7 rounded-full bg-[#e7f3ff] dark:bg-blue-900 flex items-center justify-center mr-3">
                                <i class="fas fa-eye text-[#0a66c2] dark:text-blue-400 text-xs"></i>
                            </div>
                            <div>
                                <div class="text-xs text-gray-500 dark:text-gray-400">Profile viewers</div>
                                <div class="text-sm font-medium">24</div>
                            </div>
                        </div>
                        
                        <div class="flex items-center">
                            <div class="w-7 h-7 rounded-full bg-[#e7f3ff] dark:bg-blue-900 flex items-center justify-center mr-3">
                                <i class="fas fa-user-graduate text-[#0a66c2] dark:text-blue-400 text-xs"></i>
                            </div>
                            <div>
                                <div class="text-xs text-gray-500 dark:text-gray-400">Mentorships</div>
                                <div class="text-sm font-medium"><?= $mentorships ?></div>
                            </div>
                        </div>
                        
                        <div class="flex items-center">
                            <div class="w-7 h-7 rounded-full bg-[#e7f3ff] dark:bg-blue-900 flex items-center justify-center mr-3">
                                <i class="fas fa-star text-[#0a66c2] dark:text-blue-400 text-xs"></i>
                            </div>
                            <div>
                                <div class="text-xs text-gray-500 dark:text-gray-400">Rating</div>
                                <div class="text-sm font-medium"><?= $avg_rating ?>/5</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Middle Column -->
        <div class="w-full lg:w-2/4">
            <!-- Create Post/Share Resource Card -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 mb-5">
                <div class="p-4">
                    <div class="flex items-center">
                        <img src="../uploads/<?= htmlspecialchars($photo) ?>" class="w-10 h-10 rounded-full mr-3" alt="Profile">
                        <button class="bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-600 dark:text-gray-300 rounded-full py-2.5 px-4 w-full text-left">
                            Share an update or resource...
                        </button>
                    </div>
                    
                    <div class="flex justify-between mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
                        <a href="../resources/upload.php" class="flex items-center justify-center px-3 py-1.5 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors">
                            <i class="fas fa-file-upload text-green-500 dark:text-green-400 mr-2"></i>
                            <span>Resource</span>
                        </a>
                        
                        <a href="../events/create_event.php" class="flex items-center justify-center px-3 py-1.5 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors">
                            <i class="fas fa-calendar-plus text-yellow-500 dark:text-yellow-400 mr-2"></i>
                            <span>Event</span>
                        </a>
                        
                        <a href="../alumni/write_article.php" class="flex items-center justify-center px-3 py-1.5 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors">
                            <i class="fas fa-pen text-blue-500 dark:text-blue-400 mr-2"></i>
                            <span>Article</span>
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Mentorship Requests Card -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 mb-5">
                <div class="p-4">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold text-gray-800 dark:text-white">Mentorship Requests</h3>
                        <a href="incoming_requests.php" class="text-[#0a66c2] hover:underline text-sm">See all</a>
                    </div>
                    
                    <div class="grid grid-cols-3 gap-4 text-center">
                        <div class="p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
                            <div class="text-[#0a66c2] dark:text-[#0a66c2] font-medium text-xl"><?= $stats['pending'] ?></div>
                            <div class="text-gray-600 dark:text-gray-400 text-sm mt-1">Pending</div>
                        </div>
                        <div class="p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
                            <div class="text-green-600 dark:text-green-400 font-medium text-xl"><?= $stats['accepted'] ?></div>
                            <div class="text-gray-600 dark:text-gray-400 text-sm mt-1">Accepted</div>
                        </div>
                        <div class="p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
                            <div class="text-red-600 dark:text-red-400 font-medium text-xl"><?= $stats['rejected'] ?></div>
                            <div class="text-gray-600 dark:text-gray-400 text-sm mt-1">Rejected</div>
                        </div>
                    </div>
                    
                    <?php if ($stats['pending'] > 0): ?>
                    <div class="mt-4 text-center">
                        <a href="incoming_requests.php" class="btn-primary inline-block w-full">
                            Respond to Requests
                        </a>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Recent Activities -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 mb-5">
                <div class="p-4">
                    <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4">Recent Activities</h3>
                    
                    <?php if ($activities): ?>
                        <div class="space-y-4">
                            <?php foreach ($activities as $activity): ?>
                                <div class="flex">
                                    <div class="flex-shrink-0 mr-3">
                                        <?php if ($activity['type'] == 'event_joined'): ?>
                                            <div class="w-10 h-10 rounded-full bg-[#e7f3ff] dark:bg-blue-900 flex items-center justify-center">
                                                <i class="fas fa-calendar-check text-[#0a66c2] dark:text-blue-400"></i>
                                            </div>
                                        <?php elseif ($activity['type'] == 'resource_shared'): ?>
                                            <div class="w-10 h-10 rounded-full bg-[#e7f3ff] dark:bg-blue-900 flex items-center justify-center">
                                                <i class="fas fa-file-alt text-[#0a66c2] dark:text-blue-400"></i>
                                            </div>
                                        <?php elseif ($activity['type'] == 'mentorship_accepted'): ?>
                                            <div class="w-10 h-10 rounded-full bg-[#e7f3ff] dark:bg-blue-900 flex items-center justify-center">
                                                <i class="fas fa-user-check text-[#0a66c2] dark:text-blue-400"></i>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div>
                                        <p class="text-gray-800 dark:text-gray-200">
                                            <?php 
                                            if ($activity['type'] == 'event_joined') {
                                                echo "You joined an event: <strong>" . htmlspecialchars($activity['title']) . "</strong>";
                                            } elseif ($activity['type'] == 'resource_shared') {
                                                echo "You shared a resource: <strong>" . htmlspecialchars($activity['title']) . "</strong>";
                                            } elseif ($activity['type'] == 'mentorship_accepted') {
                                                echo "You accepted a mentorship request from <strong>" . htmlspecialchars($activity['student_name']) . "</strong>";
                                            }
                                            ?>
                                        </p>
                                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                            <?= date('M j, Y', strtotime($activity['date'])) ?>
                                        </p>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-6">
                            <div class="text-gray-500 dark:text-gray-400">No recent activities to show.</div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- Right Column -->
        <div class="w-full lg:w-1/4">
            <!-- Active Mentorship Chats -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 mb-5">
                <div class="p-4">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="font-semibold text-gray-800 dark:text-white">Active Mentorships</h3>
                        <a href="../chat/chat.php" class="text-[#0a66c2] hover:underline text-sm">See all</a>
                    </div>
                    
                    <?php if ($chats): ?>
                        <div class="space-y-3">
                            <?php foreach ($chats as $chat): ?>
                                <?php 
                                $studentPhoto = (!empty($chat['profile_photo']) && file_exists("../uploads/{$chat['profile_photo']}"))
                                    ? $chat['profile_photo'] : 'profile.gif';
                                ?>
                                <a href="../chat/chat.php?student_id=<?= $chat['student_id'] ?>" class="flex items-center p-2 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-md transition-colors">
                                    <img src="../uploads/<?= htmlspecialchars($studentPhoto) ?>" class="w-10 h-10 rounded-full object-cover mr-3" alt="Student">
                                    <div>
                                        <div class="font-medium text-gray-800 dark:text-white"><?= htmlspecialchars($chat['full_name'] ?? '') ?></div>
                                        <div class="text-xs text-gray-500 dark:text-gray-400">Student</div>
                                    </div>
                                    <div class="ml-auto">
                                        <span class="w-2 h-2 bg-green-500 rounded-full inline-block"></span>
                                    </div>
                                </a>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <div class="text-gray-500 dark:text-gray-400">No active mentorships yet.</div>
                            <a href="../alumni/browse_students.php" class="text-[#0a66c2] hover:underline text-sm mt-2 inline-block">
                                Browse Students
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 mb-5">
                <div class="p-4">
                    <h3 class="font-semibold text-gray-800 dark:text-white mb-3">Quick Actions</h3>
                    
                    <div class="space-y-2">
                        <a href="../events/create_event.php" class="quick-action">
                            <i class="fas fa-calendar-plus quick-action-icon"></i>
                            <div>
                                <div class="quick-action-text">Create Event</div>
                                <div class="quick-action-subtext">Host webinars or talks</div>
                            </div>
                        </a>
                        
                        <a href="../resources/upload.php" class="quick-action">
                            <i class="fas fa-file-upload quick-action-icon"></i>
                            <div>
                                <div class="quick-action-text">Share Resource</div>
                                <div class="quick-action-subtext">Upload helpful materials</div>
                            </div>
                        </a>
                        
                        <a href="../alumni/browse_campaigns.php" class="quick-action">
                            <i class="fas fa-hand-holding-heart quick-action-icon"></i>
                            <div>
                                <div class="quick-action-text">Support Campaign</div>
                                <div class="quick-action-subtext">Contribute to initiatives</div>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Upcoming Events -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 mb-5">
                <div class="p-4">
                    <div class="flex justify-between items-center mb-3">
                        <h3 class="font-semibold text-gray-800 dark:text-white">Upcoming Events</h3>
                        <a href="../events/event_list.php" class="text-[#0a66c2] hover:underline text-sm">View all</a>
                    </div>
                    
                    <?php
                    // Fetch upcoming events
                    $eventStmt = $conn->prepare("
                        SELECT e.id, e.title, e.event_date, e.event_type 
                        FROM events e
                        WHERE e.event_date >= CURDATE()
                        ORDER BY e.event_date ASC
                        LIMIT 3
                    ");
                    $eventStmt->execute();
                    $events = $eventStmt->fetchAll(PDO::FETCH_ASSOC);
                    ?>
                    
                    <?php if ($events): ?>
                        <div class="space-y-3">
                            <?php foreach ($events as $event): ?>
                                <a href="../events/event_details.php?id=<?= $event['id'] ?>" class="block p-3 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700">
                                    <div class="flex items-start">
                                        <div class="flex-shrink-0 w-12 h-12 bg-[#e7f3ff] dark:bg-blue-900 rounded-lg flex flex-col items-center justify-center mr-3 text-[#0a66c2] dark:text-blue-400">
                                            <span class="text-xs font-medium"><?= date('M', strtotime($event['event_date'])) ?></span>
                                            <span class="text-lg font-bold leading-tight"><?= date('d', strtotime($event['event_date'])) ?></span>
                                        </div>
                                        <div>
                                            <h4 class="font-medium text-gray-800 dark:text-white"><?= htmlspecialchars($event['title']) ?></h4>
                                            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                                <?= date('g:i A', strtotime($event['event_date'])) ?> • 
                                                <?= ucfirst(htmlspecialchars($event['event_type'])) ?>
                                            </p>
                                        </div>
                                    </div>
                                </a>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <div class="text-gray-500 dark:text-gray-400">No upcoming events.</div>
                            <a href="../events/create_event.php" class="text-[#0a66c2] hover:underline text-sm mt-2 inline-block">
                                Create an Event
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once '../includes/footer.php'; ?>
