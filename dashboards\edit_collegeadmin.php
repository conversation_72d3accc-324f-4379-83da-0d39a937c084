<?php
session_start();
require_once '../includes/db.php';

if (!isset($_SESSION['superadmin_id'])) {
    header("Location: ../auth/superadmin_login.php");
    exit;
}

$admin_id = $_GET['id'] ?? null;
if (!$admin_id) {
    $_SESSION['flash_message'] = "Invalid admin ID.";
    header("Location: superadmin_dashboard.php");
    exit;
}

// Fetch the admin's data
$stmt = $conn->prepare("SELECT ca.*, c.name AS college_name 
                       FROM college_admins ca 
                       JOIN colleges c ON ca.college_id = c.id 
                       WHERE ca.id = ?");
$stmt->execute([$admin_id]);
$admin = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$admin) {
    $_SESSION['flash_message'] = "College admin not found.";
    header("Location: superadmin_dashboard.php");
    exit;
}

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = trim($_POST['name']);
    $email = trim($_POST['email']);
    $password = $_POST['password'];

    if (!$name || !$email) {
        $error = "Name and email are required.";
    } else {
        // Check if email already exists for another admin
        $check = $conn->prepare("SELECT COUNT(*) FROM college_admins WHERE email = ? AND id != ?");
        $check->execute([$email, $admin_id]);
        $exists = $check->fetchColumn();
        
        if ($exists) {
            $error = "An admin with this email already exists.";
        } else {
            if (!empty($password)) {
                $hashed = password_hash($password, PASSWORD_DEFAULT);
                $update = $conn->prepare("UPDATE college_admins SET name = ?, email = ?, password = ? WHERE id = ?");
                $update->execute([$name, $email, $hashed, $admin_id]);
            } else {
                $update = $conn->prepare("UPDATE college_admins SET name = ?, email = ? WHERE id = ?");
                $update->execute([$name, $email, $admin_id]);
            }
            $success = "College admin updated successfully.";
            
            // Refresh data
            $stmt->execute([$admin_id]);
            $admin = $stmt->fetch(PDO::FETCH_ASSOC);
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit College Admin - Mentoshri</title>
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts - Inter -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        'primary': '#0a66c2',
                        'primary-dark': '#004182',
                        'primary-light': '#e7f3ff',
                    },
                    fontFamily: {
                        'sans': ['Inter', 'sans-serif'],
                    },
                }
            }
        }
    </script>
    <!-- Design System -->
    <link rel="stylesheet" href="../assets/css/design-system.css">
</head>
<body class="font-sans bg-gray-50 dark:bg-gray-900 min-h-screen flex flex-col">
    <!-- Header -->
    <header class="bg-white dark:bg-gray-800 shadow-sm">
        <div class="container mx-auto px-4 py-3 flex items-center justify-between">
            <div class="flex items-center">
                <a href="superadmin_dashboard.php" class="text-primary font-bold text-2xl">Mentoshri</a>
            </div>
            
            <div class="flex items-center space-x-4">
                <button id="themeToggle" class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700">
                    <i class="fas fa-moon dark:hidden"></i>
                    <i class="fas fa-sun hidden dark:block"></i>
                </button>
                <a href="superadmin_dashboard.php" class="text-gray-600 dark:text-gray-300 hover:text-primary dark:hover:text-primary">
                    <i class="fas fa-arrow-left mr-2"></i> Back to Dashboard
                </a>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="flex-grow py-8 px-4">
        <div class="max-w-2xl mx-auto">
            <div class="mb-6">
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Edit College Admin</h1>
                <p class="text-gray-600 dark:text-gray-400 mt-1">Update admin details for <?= htmlspecialchars($admin['college_name']) ?></p>
            </div>
            
            <?php if ($error): ?>
                <div class="bg-red-50 dark:bg-red-900/30 border-l-4 border-red-500 text-red-700 dark:text-red-400 p-4 mb-6" role="alert">
                    <p><?= htmlspecialchars($error) ?></p>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="bg-green-50 dark:bg-green-900/30 border-l-4 border-green-500 text-green-700 dark:text-green-400 p-4 mb-6" role="alert">
                    <p><?= htmlspecialchars($success) ?></p>
                </div>
            <?php endif; ?>
            
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700">
                <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4 flex justify-between items-center">
                    <h2 class="font-semibold text-gray-800 dark:text-white">Admin Details</h2>
                    <span class="badge badge-primary">ID: <?= $admin['id'] ?></span>
                </div>
                
                <div class="p-6">
                    <div class="bg-gray-50 dark:bg-gray-700/30 rounded-lg p-4 mb-6">
                        <div class="flex items-center">
                            <div class="w-12 h-12 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center text-blue-600 dark:text-blue-400 mr-4">
                                <i class="fas fa-user-shield text-xl"></i>
                            </div>
                            <div>
                                <h3 class="font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($admin['name']) ?></h3>
                                <p class="text-sm text-gray-500 dark:text-gray-400">
                                    <span class="inline-block bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 text-xs px-2 py-0.5 rounded">
                                        <?= htmlspecialchars($admin['college_name']) ?>
                                    </span>
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <form method="post" id="adminForm">
                        <div class="space-y-6">
                            <div class="form-group">
                                <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Admin Name <span class="text-red-500">*</span></label>
                                <input 
                                    type="text" 
                                    id="name" 
                                    name="name" 
                                    class="form-input" 
                                    value="<?= htmlspecialchars($admin['name']) ?>"
                                    placeholder="Enter admin's full name"
                                    required
                                >
                            </div>
                            
                            <div class="form-group">
                                <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Email <span class="text-red-500">*</span></label>
                                <input 
                                    type="email" 
                                    id="email" 
                                    name="email" 
                                    class="form-input" 
                                    value="<?= htmlspecialchars($admin['email']) ?>"
                                    placeholder="<EMAIL>"
                                    required
                                >
                                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Used as username for login</p>
                            </div>
                            
                            <div class="form-group">
                                <label for="password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">New Password</label>
                                <div class="relative">
                                    <input 
                                        type="password" 
                                        id="password" 
                                        name="password" 
                                        class="form-input pr-24" 
                                        placeholder="Leave blank to keep unchanged"
                                        minlength="8"
                                    >
                                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                        <button type="button" id="generatePassword" class="text-primary text-sm hover:text-primary-dark">
                                            Generate Strong
                                        </button>
                                    </div>
                                </div>
                                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Minimum 8 characters recommended</p>
                            </div>
                            
                            <div class="form-group">
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">College</label>
                                <div class="form-input bg-gray-100 dark:bg-gray-700 cursor-not-allowed">
                                    <?= htmlspecialchars($admin['college_name']) ?>
                                </div>
                                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">To change college assignment, create a new admin account</p>
                            </div>
                            
                            <div class="flex justify-between items-center pt-4">
                                <a href="superadmin_dashboard.php" class="btn btn-outline">
                                    Cancel
                                </a>
                                <div class="flex space-x-3">
                                    <a href="reset_admin_password.php?id=<?= $admin['id'] ?>" class="btn btn-outline">
                                        <i class="fas fa-key mr-2"></i> Reset Password
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save mr-2"></i> Update Admin
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 py-4 mt-auto">
        <div class="container mx-auto px-4">
            <div class="text-center text-gray-600 dark:text-gray-400 text-sm">
                &copy; <?= date('Y') ?> Mentoshri. All rights reserved.
            </div>
        </div>
    </footer>

    <script>
        // Theme Toggle
        const themeToggle = document.getElementById('themeToggle');
        const htmlElement = document.documentElement;
        
        // Check for saved theme preference or use system preference
        if (localStorage.theme === 'dark' || 
            (!('theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
            htmlElement.classList.add('dark');
        } else {
            htmlElement.classList.remove('dark');
        }
        
        // Toggle theme
        themeToggle.addEventListener('click', () => {
            if (htmlElement.classList.contains('dark')) {
                htmlElement.classList.remove('dark');
                localStorage.theme = 'light';
            } else {
                htmlElement.classList.add('dark');
                localStorage.theme = 'dark';
            }
        });
        
        // Generate strong password
        document.getElementById('generatePassword').addEventListener('click', function() {
            const length = 12;
            const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+";
            let password = "";
            
            for (let i = 0; i < length; i++) {
                const randomIndex = Math.floor(Math.random() * charset.length);
                password += charset[randomIndex];
            }
            
            document.getElementById('password').value = password;
            
            // Optional: Copy to clipboard
            navigator.clipboard.writeText(password).then(() => {
                alert('Strong password generated and copied to clipboard!');
            });
        });
        
        // Form validation
        document.getElementById('adminForm').addEventListener('submit', function(e) {
            const name = document.getElementById('name').value.trim();
            const email = document.getElementById('email').value.trim();
            const password = document.getElementById('password').value;
            
            if (!name || !email) {
                e.preventDefault();
                alert('Please fill in all required fields.');
            } else if (password && password.length < 8) {
                e.preventDefault();
                alert('Password should be at least 8 characters long.');
            } else if (!email.includes('@')) {
                e.preventDefault();
                alert('Please enter a valid email address.');
            }
        });
    </script>
</body>
</html>
