<?php
require_once '../includes/db.php';
require_once '../includes/header.php';

if (!isset($_SESSION['alumni_id'])) {
    header("Location: ../auth/alumni_login.php");
    exit;
}

$alumni_id = $_SESSION['alumni_id'];
$success = $error = '';

// Handle accept/reject actions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['request_id'], $_POST['action'])) {
    $request_id = intval($_POST['request_id']);
    $action = $_POST['action'];

    if (in_array($action, ['accepted', 'rejected'])) {
        $update = $conn->prepare("UPDATE student_requests SET status = ? WHERE id = ? AND alumni_id = ?");
        $update->execute([$action, $request_id, $alumni_id]);
        $success = "Request has been $action.";
    } else {
        $error = "Invalid action.";
    }
}

// Fetch pending requests
$stmt = $conn->prepare("SELECT sr.id, sr.message, sr.created_at, s.full_name, s.department, s.year, s.profile_photo
    FROM student_requests sr
    JOIN students s ON sr.student_id = s.id
    WHERE sr.alumni_id = ? AND sr.status = 'pending'
    ORDER BY sr.created_at DESC");
$stmt->execute([$alumni_id]);
$requests = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<h2 class="mb-4">📨 Incoming Mentorship Requests</h2>

<?php if ($success): ?>
    <div class="alert alert-success"><?= htmlspecialchars($success) ?></div>
<?php elseif ($error): ?>
    <div class="alert alert-danger"><?= htmlspecialchars($error) ?></div>
<?php endif; ?>

<?php if ($requests): ?>
    <?php foreach ($requests as $r): ?>
        <div class="card mb-3 shadow-sm">
            <div class="card-body d-flex align-items-center">
                <img src="../uploads/<?= file_exists("../uploads/{$r['profile_photo']}") ? $r['profile_photo'] : 'profile.gif' ?>" width="60" height="60" class="rounded-circle me-3">
                <div class="flex-grow-1">
                    <h5 class="mb-0"><?= htmlspecialchars($r['full_name']) ?></h5>
                    <small class="text-muted"><?= htmlspecialchars($r['department']) ?>, Year <?= htmlspecialchars($r['year']) ?></small>
                    <p class="mb-1 mt-2"><?= nl2br(htmlspecialchars($r['message'])) ?></p>
                </div>
                <form method="post" class="ms-3">
                    <input type="hidden" name="request_id" value="<?= $r['id'] ?>">
                    <button name="action" value="accepted" class="btn btn-success btn-sm me-2">Accept</button>
                    <button name="action" value="rejected" class="btn btn-danger btn-sm">Reject</button>
                </form>
            </div>
        </div>
    <?php endforeach; ?>
<?php else: ?>
    <div class="alert alert-info">No pending requests at the moment.</div>
<?php endif; ?>

<?php require_once '../includes/footer.php'; ?>
