<?php
// College Admin Sidebar - Modern LinkedIn-inspired Design
?>

<!-- Admin Profile Section -->
<div class="mb-8 px-4">
    <div class="bg-gradient-to-br from-primary/5 to-primary/10 dark:from-primary/10 dark:to-primary/20 rounded-xl p-4 border border-primary/10">
        <div class="flex items-center space-x-3">
            <div class="w-12 h-12 bg-gradient-to-br from-primary to-primary-dark rounded-xl flex items-center justify-center text-white shadow-lg">
                <i class="fas fa-user-shield text-lg"></i>
            </div>
            <div class="flex-1 min-w-0">
                <h3 class="font-semibold text-gray-900 dark:text-white text-sm truncate">
                    <?= htmlspecialchars($user_name ?? 'Admin') ?>
                </h3>
                <p class="text-xs text-primary font-medium">College Administrator</p>
            </div>
        </div>
        <div class="mt-3 pt-3 border-t border-primary/10">
            <div class="flex items-center justify-between text-xs">
                <span class="text-gray-600 dark:text-gray-400">Status</span>
                <span class="flex items-center text-green-600 dark:text-green-400 font-medium">
                    <i class="fas fa-circle text-green-500 mr-1" style="font-size: 6px;"></i>
                    Online
                </span>
            </div>
        </div>
    </div>
</div>

<!-- Main Navigation -->
<div class="mb-8 px-4">
    <div class="flex items-center justify-between mb-4">
        <h3 class="text-xs font-bold text-gray-500 dark:text-gray-400 uppercase tracking-wider">Main</h3>
        <div class="w-8 h-0.5 bg-gradient-to-r from-primary to-transparent rounded-full"></div>
    </div>
    <div class="space-y-2">
        <a href="/dashboards/collegeadmin_dashboard.php" class="group flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 <?= $current_page === 'collegeadmin_dashboard.php' ? 'bg-gradient-to-r from-primary to-primary-dark text-white shadow-lg shadow-primary/25' : 'text-gray-700 dark:text-gray-300 hover:bg-gradient-to-r hover:from-primary/10 hover:to-primary/5 hover:text-primary' ?>" title="Dashboard">
            <div class="w-10 h-10 rounded-lg <?= $current_page === 'collegeadmin_dashboard.php' ? 'bg-white/20' : 'bg-gray-100 dark:bg-gray-700 group-hover:bg-primary/10' ?> flex items-center justify-center mr-3 transition-all duration-200">
                <i class="fas fa-tachometer-alt <?= $current_page === 'collegeadmin_dashboard.php' ? 'text-white' : 'text-gray-600 dark:text-gray-400 group-hover:text-primary' ?>"></i>
            </div>
            <span class="flex-1">Dashboard</span>
            <?php if ($current_page === 'collegeadmin_dashboard.php'): ?>
                <i class="fas fa-chevron-right text-white/70 text-xs"></i>
            <?php endif; ?>
        </a>
        <a href="/dashboards/post_announcement.php" class="group flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 <?= $current_page === 'post_announcement.php' ? 'bg-gradient-to-r from-primary to-primary-dark text-white shadow-lg shadow-primary/25' : 'text-gray-700 dark:text-gray-300 hover:bg-gradient-to-r hover:from-primary/10 hover:to-primary/5 hover:text-primary' ?>" title="Announcements">
            <div class="w-10 h-10 rounded-lg <?= $current_page === 'post_announcement.php' ? 'bg-white/20' : 'bg-gray-100 dark:bg-gray-700 group-hover:bg-primary/10' ?> flex items-center justify-center mr-3 transition-all duration-200">
                <i class="fas fa-bullhorn <?= $current_page === 'post_announcement.php' ? 'text-white' : 'text-gray-600 dark:text-gray-400 group-hover:text-primary' ?>"></i>
            </div>
            <span class="flex-1">Announcements</span>
            <?php if ($current_page === 'post_announcement.php'): ?>
                <i class="fas fa-chevron-right text-white/70 text-xs"></i>
            <?php endif; ?>
        </a>
    </div>
</div>

<!-- User Management -->
<div class="mb-8 px-4">
    <div class="flex items-center justify-between mb-4">
        <h3 class="text-xs font-bold text-gray-500 dark:text-gray-400 uppercase tracking-wider">User Management</h3>
        <div class="w-8 h-0.5 bg-gradient-to-r from-blue-500 to-transparent rounded-full"></div>
    </div>
    <div class="space-y-2">
        <a href="/dashboards/list_faculty.php" class="group flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 <?= $current_page === 'list_faculty.php' || $current_page === 'add_faculty.php' || $current_page === 'edit_faculty.php' ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-lg shadow-blue-500/25' : 'text-gray-700 dark:text-gray-300 hover:bg-gradient-to-r hover:from-blue-50 hover:to-blue-100 dark:hover:from-blue-900/20 dark:hover:to-blue-800/20 hover:text-blue-600' ?>" title="Faculty">
            <div class="w-10 h-10 rounded-lg <?= $current_page === 'list_faculty.php' || $current_page === 'add_faculty.php' || $current_page === 'edit_faculty.php' ? 'bg-white/20' : 'bg-blue-100 dark:bg-blue-900/30 group-hover:bg-blue-200 dark:group-hover:bg-blue-800/40' ?> flex items-center justify-center mr-3 transition-all duration-200">
                <i class="fas fa-chalkboard-teacher <?= $current_page === 'list_faculty.php' || $current_page === 'add_faculty.php' || $current_page === 'edit_faculty.php' ? 'text-white' : 'text-blue-600 dark:text-blue-400' ?>"></i>
            </div>
            <span class="flex-1">Faculty</span>
            <?php if ($current_page === 'list_faculty.php' || $current_page === 'add_faculty.php' || $current_page === 'edit_faculty.php'): ?>
                <i class="fas fa-chevron-right text-white/70 text-xs"></i>
            <?php endif; ?>
        </a>
        <a href="/dashboards/list_students.php" class="group flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 <?= $current_page === 'list_students.php' || $current_page === 'add_student.php' || $current_page === 'edit_student.php' ? 'bg-gradient-to-r from-green-500 to-green-600 text-white shadow-lg shadow-green-500/25' : 'text-gray-700 dark:text-gray-300 hover:bg-gradient-to-r hover:from-green-50 hover:to-green-100 dark:hover:from-green-900/20 dark:hover:to-green-800/20 hover:text-green-600' ?>" title="Students">
            <div class="w-10 h-10 rounded-lg <?= $current_page === 'list_students.php' || $current_page === 'add_student.php' || $current_page === 'edit_student.php' ? 'bg-white/20' : 'bg-green-100 dark:bg-green-900/30 group-hover:bg-green-200 dark:group-hover:bg-green-800/40' ?> flex items-center justify-center mr-3 transition-all duration-200">
                <i class="fas fa-user-graduate <?= $current_page === 'list_students.php' || $current_page === 'add_student.php' || $current_page === 'edit_student.php' ? 'text-white' : 'text-green-600 dark:text-green-400' ?>"></i>
            </div>
            <span class="flex-1">Students</span>
            <?php if ($current_page === 'list_students.php' || $current_page === 'add_student.php' || $current_page === 'edit_student.php'): ?>
                <i class="fas fa-chevron-right text-white/70 text-xs"></i>
            <?php endif; ?>
        </a>
        <a href="/dashboards/list_alumni.php" class="group flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 <?= $current_page === 'list_alumni.php' || $current_page === 'invite_alumni.php' || $current_page === 'verify_alumni.php' ? 'bg-gradient-to-r from-purple-500 to-purple-600 text-white shadow-lg shadow-purple-500/25' : 'text-gray-700 dark:text-gray-300 hover:bg-gradient-to-r hover:from-purple-50 hover:to-purple-100 dark:hover:from-purple-900/20 dark:hover:to-purple-800/20 hover:text-purple-600' ?>" title="Alumni">
            <div class="w-10 h-10 rounded-lg <?= $current_page === 'list_alumni.php' || $current_page === 'invite_alumni.php' || $current_page === 'verify_alumni.php' ? 'bg-white/20' : 'bg-purple-100 dark:bg-purple-900/30 group-hover:bg-purple-200 dark:group-hover:bg-purple-800/40' ?> flex items-center justify-center mr-3 transition-all duration-200">
                <i class="fas fa-user-tie <?= $current_page === 'list_alumni.php' || $current_page === 'invite_alumni.php' || $current_page === 'verify_alumni.php' ? 'text-white' : 'text-purple-600 dark:text-purple-400' ?>"></i>
            </div>
            <span class="flex-1">Alumni</span>
            <?php if ($current_page === 'list_alumni.php' || $current_page === 'invite_alumni.php' || $current_page === 'verify_alumni.php'): ?>
                <i class="fas fa-chevron-right text-white/70 text-xs"></i>
            <?php endif; ?>
        </a>
        <a href="/dashboards/approve_students.php" class="group flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 <?= $current_page === 'approve_students.php' ? 'bg-gradient-to-r from-orange-500 to-orange-600 text-white shadow-lg shadow-orange-500/25' : 'text-gray-700 dark:text-gray-300 hover:bg-gradient-to-r hover:from-orange-50 hover:to-orange-100 dark:hover:from-orange-900/20 dark:hover:to-orange-800/20 hover:text-orange-600' ?>" title="Approvals">
            <div class="w-10 h-10 rounded-lg <?= $current_page === 'approve_students.php' ? 'bg-white/20' : 'bg-orange-100 dark:bg-orange-900/30 group-hover:bg-orange-200 dark:group-hover:bg-orange-800/40' ?> flex items-center justify-center mr-3 transition-all duration-200">
                <i class="fas fa-user-check <?= $current_page === 'approve_students.php' ? 'text-white' : 'text-orange-600 dark:text-orange-400' ?>"></i>
            </div>
            <span class="flex-1">Approvals</span>
            <?php if ($current_page === 'approve_students.php'): ?>
                <i class="fas fa-chevron-right text-white/70 text-xs"></i>
            <?php endif; ?>
        </a>
    </div>
</div>

<!-- Content Management -->
<div class="mb-8 px-4">
    <div class="flex items-center justify-between mb-4">
        <h3 class="text-xs font-bold text-gray-500 dark:text-gray-400 uppercase tracking-wider">Content</h3>
        <div class="w-8 h-0.5 bg-gradient-to-r from-indigo-500 to-transparent rounded-full"></div>
    </div>
    <div class="space-y-2">
        <a href="/campaigns/manage_campaigns.php" class="group flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 <?= $current_page === 'manage_campaigns.php' || $current_page === 'create_campaign.php' || $current_page === 'edit_campaign.php' ? 'bg-gradient-to-r from-indigo-500 to-indigo-600 text-white shadow-lg shadow-indigo-500/25' : 'text-gray-700 dark:text-gray-300 hover:bg-gradient-to-r hover:from-indigo-50 hover:to-indigo-100 dark:hover:from-indigo-900/20 dark:hover:to-indigo-800/20 hover:text-indigo-600' ?>" title="Campaigns">
            <div class="w-10 h-10 rounded-lg <?= $current_page === 'manage_campaigns.php' || $current_page === 'create_campaign.php' || $current_page === 'edit_campaign.php' ? 'bg-white/20' : 'bg-indigo-100 dark:bg-indigo-900/30 group-hover:bg-indigo-200 dark:group-hover:bg-indigo-800/40' ?> flex items-center justify-center mr-3 transition-all duration-200">
                <i class="fas fa-gift <?= $current_page === 'manage_campaigns.php' || $current_page === 'create_campaign.php' || $current_page === 'edit_campaign.php' ? 'text-white' : 'text-indigo-600 dark:text-indigo-400' ?>"></i>
            </div>
            <span class="flex-1">Campaigns</span>
            <?php if ($current_page === 'manage_campaigns.php' || $current_page === 'create_campaign.php' || $current_page === 'edit_campaign.php'): ?>
                <i class="fas fa-chevron-right text-white/70 text-xs"></i>
            <?php endif; ?>
        </a>
        <a href="/events/event_list.php" class="group flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 <?= $current_page === 'event_list.php' || $current_page === 'create_event.php' || $current_page === 'edit_event.php' || $current_page === 'view_participants.php' ? 'bg-gradient-to-r from-amber-500 to-amber-600 text-white shadow-lg shadow-amber-500/25' : 'text-gray-700 dark:text-gray-300 hover:bg-gradient-to-r hover:from-amber-50 hover:to-amber-100 dark:hover:from-amber-900/20 dark:hover:to-amber-800/20 hover:text-amber-600' ?>" title="Events">
            <div class="w-10 h-10 rounded-lg <?= $current_page === 'event_list.php' || $current_page === 'create_event.php' || $current_page === 'edit_event.php' || $current_page === 'view_participants.php' ? 'bg-white/20' : 'bg-amber-100 dark:bg-amber-900/30 group-hover:bg-amber-200 dark:group-hover:bg-amber-800/40' ?> flex items-center justify-center mr-3 transition-all duration-200">
                <i class="fas fa-calendar-alt <?= $current_page === 'event_list.php' || $current_page === 'create_event.php' || $current_page === 'edit_event.php' || $current_page === 'view_participants.php' ? 'text-white' : 'text-amber-600 dark:text-amber-400' ?>"></i>
            </div>
            <span class="flex-1">Events</span>
            <?php if ($current_page === 'event_list.php' || $current_page === 'create_event.php' || $current_page === 'edit_event.php' || $current_page === 'view_participants.php'): ?>
                <i class="fas fa-chevron-right text-white/70 text-xs"></i>
            <?php endif; ?>
        </a>
        <a href="/resources/resource_list.php" class="group flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 <?= $current_page === 'resource_list.php' || $current_page === 'upload.php' ? 'bg-gradient-to-r from-teal-500 to-teal-600 text-white shadow-lg shadow-teal-500/25' : 'text-gray-700 dark:text-gray-300 hover:bg-gradient-to-r hover:from-teal-50 hover:to-teal-100 dark:hover:from-teal-900/20 dark:hover:to-teal-800/20 hover:text-teal-600' ?>" title="Resources">
            <div class="w-10 h-10 rounded-lg <?= $current_page === 'resource_list.php' || $current_page === 'upload.php' ? 'bg-white/20' : 'bg-teal-100 dark:bg-teal-900/30 group-hover:bg-teal-200 dark:group-hover:bg-teal-800/40' ?> flex items-center justify-center mr-3 transition-all duration-200">
                <i class="fas fa-file-alt <?= $current_page === 'resource_list.php' || $current_page === 'upload.php' ? 'text-white' : 'text-teal-600 dark:text-teal-400' ?>"></i>
            </div>
            <span class="flex-1">Resources</span>
            <?php if ($current_page === 'resource_list.php' || $current_page === 'upload.php'): ?>
                <i class="fas fa-chevron-right text-white/70 text-xs"></i>
            <?php endif; ?>
        </a>
    </div>
</div>

<!-- Settings -->
<div class="mb-8 px-4">
    <div class="flex items-center justify-between mb-4">
        <h3 class="text-xs font-bold text-gray-500 dark:text-gray-400 uppercase tracking-wider">Settings</h3>
        <div class="w-8 h-0.5 bg-gradient-to-r from-gray-500 to-transparent rounded-full"></div>
    </div>
    <div class="space-y-2">
        <a href="/dashboards/collegeadmin_profile.php" class="group flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 <?= $current_page === 'collegeadmin_profile.php' ? 'bg-gradient-to-r from-gray-600 to-gray-700 text-white shadow-lg shadow-gray-600/25' : 'text-gray-700 dark:text-gray-300 hover:bg-gradient-to-r hover:from-gray-50 hover:to-gray-100 dark:hover:from-gray-800/50 dark:hover:to-gray-700/50 hover:text-gray-900 dark:hover:text-gray-100' ?>" title="Profile">
            <div class="w-10 h-10 rounded-lg <?= $current_page === 'collegeadmin_profile.php' ? 'bg-white/20' : 'bg-gray-100 dark:bg-gray-700 group-hover:bg-gray-200 dark:group-hover:bg-gray-600' ?> flex items-center justify-center mr-3 transition-all duration-200">
                <i class="fas fa-user <?= $current_page === 'collegeadmin_profile.php' ? 'text-white' : 'text-gray-600 dark:text-gray-400' ?>"></i>
            </div>
            <span class="flex-1">Profile</span>
            <?php if ($current_page === 'collegeadmin_profile.php'): ?>
                <i class="fas fa-chevron-right text-white/70 text-xs"></i>
            <?php endif; ?>
        </a>
        <a href="/dashboards/collegeadmin_settings.php" class="group flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 <?= $current_page === 'collegeadmin_settings.php' ? 'bg-gradient-to-r from-gray-600 to-gray-700 text-white shadow-lg shadow-gray-600/25' : 'text-gray-700 dark:text-gray-300 hover:bg-gradient-to-r hover:from-gray-50 hover:to-gray-100 dark:hover:from-gray-800/50 dark:hover:to-gray-700/50 hover:text-gray-900 dark:hover:text-gray-100' ?>" title="Settings">
            <div class="w-10 h-10 rounded-lg <?= $current_page === 'collegeadmin_settings.php' ? 'bg-white/20' : 'bg-gray-100 dark:bg-gray-700 group-hover:bg-gray-200 dark:group-hover:bg-gray-600' ?> flex items-center justify-center mr-3 transition-all duration-200">
                <i class="fas fa-cog <?= $current_page === 'collegeadmin_settings.php' ? 'text-white' : 'text-gray-600 dark:text-gray-400' ?>"></i>
            </div>
            <span class="flex-1">Settings</span>
            <?php if ($current_page === 'collegeadmin_settings.php'): ?>
                <i class="fas fa-chevron-right text-white/70 text-xs"></i>
            <?php endif; ?>
        </a>
    </div>
</div>

<!-- Sidebar Footer -->
<div class="mt-auto px-4 pb-6">
    <div class="bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900 rounded-xl p-4 border border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between mb-3">
            <h4 class="text-sm font-semibold text-gray-900 dark:text-white">Quick Stats</h4>
            <i class="fas fa-chart-line text-primary text-sm"></i>
        </div>
        <div class="space-y-2">
            <div class="flex justify-between items-center">
                <span class="text-xs text-gray-600 dark:text-gray-400">Active Users</span>
                <span class="text-xs font-semibold text-gray-900 dark:text-white"><?= ($total_students ?? 0) + ($total_faculty ?? 0) + ($total_alumni ?? 0) ?></span>
            </div>
            <div class="flex justify-between items-center">
                <span class="text-xs text-gray-600 dark:text-gray-400">This Month</span>
                <span class="text-xs font-semibold text-green-600 dark:text-green-400">+12%</span>
            </div>
        </div>
        <div class="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
            <a href="/auth/logout.php" class="flex items-center justify-center w-full px-3 py-2 text-xs font-medium text-gray-700 dark:text-gray-300 hover:text-red-600 dark:hover:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-all duration-200">
                <i class="fas fa-sign-out-alt mr-2"></i>
                Sign Out
            </a>
        </div>
    </div>
</div>