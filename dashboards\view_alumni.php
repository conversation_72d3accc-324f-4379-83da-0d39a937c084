<?php
// Include session configuration
require_once '../includes/session_config.php';
require_once '../includes/db.php';
require_once '../includes/db_schema_check.php';

if (!isset($_SESSION['collegeadmin_id'])) {
    header("Location: ../auth/collegeadmin_login.php");
    exit;
}

$college_id = $_SESSION['college_id'];
$admin_name = $_SESSION['collegeadmin_name'] ?? 'Admin';

// Set role explicitly for the sidebar
$role = 'collegeadmin';
$user_id = $_SESSION['collegeadmin_id'];
$user_name = $_SESSION['collegeadmin_name'];
$current_page = basename($_SERVER['PHP_SELF']);

// Get alumni ID from URL
$id = $_GET['id'] ?? null;

if (!$id) {
    $_SESSION['flash_message'] = "Invalid alumni ID.";
    header("Location: list_alumni.php");
    exit;
}

// Get college details
$collegeStmt = $conn->prepare("SELECT name FROM colleges WHERE id = ?");
$collegeStmt->execute([$college_id]);
$college = $collegeStmt->fetch(PDO::FETCH_ASSOC);

// Get alumni details
$stmt = $conn->prepare("
    SELECT a.*, c.name as college_name
    FROM alumni a
    LEFT JOIN colleges c ON a.college_id = c.id
    WHERE a.id = ? AND a.college_id = ?
");
$stmt->execute([$id, $college_id]);
$alumni = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$alumni) {
    $_SESSION['flash_message'] = "Alumni not found.";
    header("Location: list_alumni.php");
    exit;
}

// Get mentorship statistics
$mentorship_stats = ['total_mentorships' => 0, 'active_mentorships' => 0, 'completed_mentorships' => 0];
$recent_mentorships = [];

try {
    $mentorshipStmt = $conn->prepare("
        SELECT
            COUNT(*) as total_mentorships,
            COUNT(CASE WHEN status = 'active' THEN 1 END) as active_mentorships,
            COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_mentorships
        FROM mentorships
        WHERE alumni_id = ?
    ");
    $mentorshipStmt->execute([$id]);
    $mentorship_stats = $mentorshipStmt->fetch(PDO::FETCH_ASSOC);

    // Get recent mentorships
    $recentMentorshipsStmt = $conn->prepare("
        SELECT m.*, s.full_name as student_name, s.email as student_email
        FROM mentorships m
        LEFT JOIN students s ON m.student_id = s.id
        WHERE m.alumni_id = ?
        ORDER BY m.created_at DESC
        LIMIT 5
    ");
    $recentMentorshipsStmt->execute([$id]);
    $recent_mentorships = $recentMentorshipsStmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    // If mentorships table doesn't exist or has issues, use default values
    $mentorship_stats = ['total_mentorships' => 0, 'active_mentorships' => 0, 'completed_mentorships' => 0];
    $recent_mentorships = [];
}

// Get alumni events participation
$events_stats = ['events_participated' => 0];
try {
    $eventsStmt = $conn->prepare("
        SELECT COUNT(*) as events_participated
        FROM event_participants ep
        LEFT JOIN events e ON ep.event_id = e.id
        WHERE ep.alumni_id = ? AND e.college_id = ?
    ");
    $eventsStmt->execute([$id, $college_id]);
    $events_stats = $eventsStmt->fetch(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    // If events tables don't exist, use default value
    $events_stats = ['events_participated' => 0];
}

// Set photo path
$photo = $alumni['profile_image'] ?? 'default.png';

// Include the header
require_once '../includes/header.php';
?>

    <!-- Main Content -->
    <main class="flex-grow py-6 px-4">
        <div class="max-w-7xl mx-auto">
            <!-- Page Header -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8">
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="bg-gradient-to-br from-purple-500 to-purple-600 p-3 rounded-xl shadow-lg">
                            <i class="fas fa-user-tie text-white text-xl"></i>
                        </div>
                        <div>
                            <h1 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                                Alumni Profile
                            </h1>
                            <nav class="text-sm text-gray-600 dark:text-gray-400 mt-1" aria-label="Breadcrumb">
                                <ol class="flex items-center space-x-2">
                                    <li>
                                        <a href="collegeadmin_dashboard.php" class="hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
                                            Dashboard
                                        </a>
                                    </li>
                                    <li class="flex items-center">
                                        <i class="fas fa-chevron-right text-gray-400 text-xs mx-2"></i>
                                        <a href="list_alumni.php" class="hover:text-blue-600 dark:hover:text-blue-400 transition-colors">Alumni Management</a>
                                    </li>
                                    <li class="flex items-center">
                                        <i class="fas fa-chevron-right text-gray-400 text-xs mx-2"></i>
                                        <span class="text-gray-900 dark:text-white font-medium"><?= htmlspecialchars($alumni['full_name']) ?></span>
                                    </li>
                                </ol>
                            </nav>
                        </div>
                    </div>
                    <div class="mt-4 lg:mt-0 flex flex-wrap gap-3">
                        <a href="list_alumni.php" class="inline-flex items-center px-4 py-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-medium rounded-lg transition-colors">
                            <i class="fas fa-arrow-left mr-2"></i> Back to Alumni
                        </a>
                        <?php if (!$alumni['verified']): ?>
                        <a href="verify_alumni.php?id=<?= $id ?>" class="inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white font-medium rounded-lg transition-colors">
                            <i class="fas fa-check-circle mr-2"></i> Verify Alumni
                        </a>
                        <?php endif; ?>
                        <a href="mailto:<?= htmlspecialchars($alumni['email']) ?>" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors">
                            <i class="fas fa-envelope mr-2"></i> Send Email
                        </a>
                    </div>
                </div>
            </div>

            <!-- Main Content Grid -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Left Column: Alumni Profile -->
                <div class="lg:col-span-2 space-y-8">
                    <!-- Alumni Profile Card -->
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
                        <!-- Cover with Status -->
                        <div class="h-32 bg-gradient-to-br from-purple-600 via-purple-700 to-indigo-600 relative">
                            <div class="absolute top-4 right-4">
                                <?php if ($alumni['verified']): ?>
                                    <div class="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-medium flex items-center shadow-lg">
                                        <i class="fas fa-check mr-1"></i> Verified Alumni
                                    </div>
                                <?php else: ?>
                                    <div class="bg-yellow-500 text-white px-3 py-1 rounded-full text-sm font-medium flex items-center shadow-lg">
                                        <i class="fas fa-clock mr-1"></i> Pending Verification
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Profile Content -->
                        <div class="px-6 pb-6 relative">
                            <div class="flex flex-col sm:flex-row sm:items-center -mt-12 relative z-10">
                                <!-- Profile Picture -->
                                <div class="flex-shrink-0 mb-4 sm:mb-0 relative z-20">
                                    <div class="w-24 h-24 rounded-xl border-4 border-white dark:border-gray-800 bg-gray-100 dark:bg-gray-700 flex items-center justify-center overflow-hidden shadow-lg">
                                        <?php if ($photo === 'default.png' || empty($photo)): ?>
                                            <i class="fas fa-user-tie text-3xl text-gray-400 dark:text-gray-300"></i>
                                        <?php else: ?>
                                            <img src="../uploads/<?= htmlspecialchars($photo) ?>" alt="<?= htmlspecialchars($alumni['full_name']) ?>" class="w-full h-full object-cover">
                                        <?php endif; ?>
                                    </div>
                                </div>

                                <!-- Alumni Info -->
                                <div class="sm:ml-6 flex-grow relative z-20 mt-4 sm:mt-0">
                                    <div class="bg-white dark:bg-gray-800 rounded-xl p-4 border-2 border-gray-200 dark:border-gray-600 shadow-sm backdrop-blur-sm bg-opacity-95 dark:bg-opacity-95">
                                        <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-2"><?= htmlspecialchars($alumni['full_name']) ?></h2>
                                        <p class="text-gray-600 dark:text-gray-400 mb-3"><?= htmlspecialchars($alumni['email']) ?></p>
                                        <div class="flex flex-wrap gap-2">
                                            <?php if ($alumni['company']): ?>
                                            <span class="inline-flex items-center px-3 py-1 rounded-lg text-sm font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200">
                                                <i class="fas fa-building mr-1"></i>
                                                <?= htmlspecialchars($alumni['company']) ?>
                                            </span>
                                            <?php endif; ?>
                                            <?php if ($alumni['position']): ?>
                                            <span class="inline-flex items-center px-3 py-1 rounded-lg text-sm font-medium bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200">
                                                <i class="fas fa-briefcase mr-1"></i>
                                                <?= htmlspecialchars($alumni['position']) ?>
                                            </span>
                                            <?php endif; ?>
                                            <?php if ($alumni['industry']): ?>
                                            <span class="inline-flex items-center px-3 py-1 rounded-lg text-sm font-medium bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200">
                                                <i class="fas fa-industry mr-1"></i>
                                                <?= htmlspecialchars($alumni['industry']) ?>
                                            </span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Additional Details -->
                            <div class="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
                                <?php if ($alumni['graduation_year']): ?>
                                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                                    <div class="text-sm text-gray-500 dark:text-gray-400">Graduation Year</div>
                                    <div class="font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($alumni['graduation_year']) ?></div>
                                </div>
                                <?php endif; ?>
                                <?php if ($alumni['phone']): ?>
                                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                                    <div class="text-sm text-gray-500 dark:text-gray-400">Phone</div>
                                    <div class="font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($alumni['phone']) ?></div>
                                </div>
                                <?php endif; ?>
                                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                                    <div class="text-sm text-gray-500 dark:text-gray-400">Joined</div>
                                    <div class="font-medium text-gray-900 dark:text-white"><?= date('M d, Y', strtotime($alumni['created_at'])) ?></div>
                                </div>
                                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                                    <div class="text-sm text-gray-500 dark:text-gray-400">Events Participated</div>
                                    <div class="font-medium text-gray-900 dark:text-white"><?= $events_stats['events_participated'] ?? 0 ?></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Bio Section -->
                    <?php if ($alumni['bio']): ?>
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                            <i class="fas fa-user text-purple-600 mr-2"></i>
                            About
                        </h3>
                        <p class="text-gray-700 dark:text-gray-300 leading-relaxed"><?= nl2br(htmlspecialchars($alumni['bio'])) ?></p>
                    </div>
                    <?php endif; ?>

                    <!-- Mentorship Activity -->
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                            <i class="fas fa-hands-helping text-purple-600 mr-2"></i>
                            Mentorship Activity
                        </h3>

                        <!-- Mentorship Stats -->
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                            <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 text-center">
                                <div class="text-2xl font-bold text-blue-600 dark:text-blue-400"><?= $mentorship_stats['total_mentorships'] ?? 0 ?></div>
                                <div class="text-sm text-blue-700 dark:text-blue-300">Total Mentorships</div>
                            </div>
                            <div class="bg-green-50 dark:bg-green-900/20 rounded-lg p-4 text-center">
                                <div class="text-2xl font-bold text-green-600 dark:text-green-400"><?= $mentorship_stats['active_mentorships'] ?? 0 ?></div>
                                <div class="text-sm text-green-700 dark:text-green-300">Active Mentorships</div>
                            </div>
                            <div class="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4 text-center">
                                <div class="text-2xl font-bold text-purple-600 dark:text-purple-400"><?= $mentorship_stats['completed_mentorships'] ?? 0 ?></div>
                                <div class="text-sm text-purple-700 dark:text-purple-300">Completed</div>
                            </div>
                        </div>

                        <!-- Recent Mentorships -->
                        <?php if (count($recent_mentorships) > 0): ?>
                        <h4 class="font-medium text-gray-900 dark:text-white mb-3">Recent Mentorships</h4>
                        <div class="space-y-3">
                            <?php foreach ($recent_mentorships as $mentorship): ?>
                            <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center mr-3">
                                        <i class="fas fa-user text-purple-600 dark:text-purple-400 text-sm"></i>
                                    </div>
                                    <div>
                                        <div class="font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($mentorship['student_name'] ?? 'Unknown Student') ?></div>
                                        <div class="text-sm text-gray-600 dark:text-gray-400"><?= htmlspecialchars($mentorship['student_email'] ?? '') ?></div>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                        <?= $mentorship['status'] === 'active' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                                           ($mentorship['status'] === 'completed' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' :
                                            'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200') ?>">
                                        <?= ucfirst($mentorship['status']) ?>
                                    </span>
                                    <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                        <?= date('M d, Y', strtotime($mentorship['created_at'])) ?>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        <?php else: ?>
                        <div class="text-center py-8">
                            <div class="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-hands-helping text-gray-400 text-2xl"></i>
                            </div>
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Mentorships Yet</h3>
                            <p class="text-gray-600 dark:text-gray-400">This alumni hasn't started any mentorships yet.</p>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Right Sidebar -->
                <div class="space-y-6">
                    <!-- Contact Information -->
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                        <div class="p-4 border-b border-gray-200 dark:border-gray-700">
                            <h3 class="font-semibold text-gray-900 dark:text-white flex items-center">
                                <i class="fas fa-address-card text-purple-600 mr-2"></i>
                                Contact Information
                            </h3>
                        </div>
                        <div class="p-4 space-y-3">
                            <div class="flex items-center">
                                <i class="fas fa-envelope text-gray-400 mr-3 w-4"></i>
                                <a href="mailto:<?= htmlspecialchars($alumni['email']) ?>" class="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300">
                                    <?= htmlspecialchars($alumni['email']) ?>
                                </a>
                            </div>
                            <?php if ($alumni['phone']): ?>
                            <div class="flex items-center">
                                <i class="fas fa-phone text-gray-400 mr-3 w-4"></i>
                                <span class="text-gray-700 dark:text-gray-300"><?= htmlspecialchars($alumni['phone']) ?></span>
                            </div>
                            <?php endif; ?>
                            <?php if ($alumni['linkedin']): ?>
                            <div class="flex items-center">
                                <i class="fab fa-linkedin text-gray-400 mr-3 w-4"></i>
                                <a href="<?= htmlspecialchars($alumni['linkedin']) ?>" target="_blank" class="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300">
                                    LinkedIn Profile
                                </a>
                            </div>
                            <?php endif; ?>
                            <?php if ($alumni['website']): ?>
                            <div class="flex items-center">
                                <i class="fas fa-globe text-gray-400 mr-3 w-4"></i>
                                <a href="<?= htmlspecialchars($alumni['website']) ?>" target="_blank" class="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300">
                                    Website
                                </a>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Professional Details -->
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                        <div class="p-4 border-b border-gray-200 dark:border-gray-700">
                            <h3 class="font-semibold text-gray-900 dark:text-white flex items-center">
                                <i class="fas fa-briefcase text-purple-600 mr-2"></i>
                                Professional Details
                            </h3>
                        </div>
                        <div class="p-4 space-y-3">
                            <?php if ($alumni['company']): ?>
                            <div>
                                <div class="text-sm text-gray-500 dark:text-gray-400">Company</div>
                                <div class="font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($alumni['company']) ?></div>
                            </div>
                            <?php endif; ?>
                            <?php if ($alumni['position']): ?>
                            <div>
                                <div class="text-sm text-gray-500 dark:text-gray-400">Position</div>
                                <div class="font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($alumni['position']) ?></div>
                            </div>
                            <?php endif; ?>
                            <?php if ($alumni['industry']): ?>
                            <div>
                                <div class="text-sm text-gray-500 dark:text-gray-400">Industry</div>
                                <div class="font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($alumni['industry']) ?></div>
                            </div>
                            <?php endif; ?>
                            <?php if ($alumni['experience_years']): ?>
                            <div>
                                <div class="text-sm text-gray-500 dark:text-gray-400">Experience</div>
                                <div class="font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($alumni['experience_years']) ?> years</div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                        <div class="p-4 border-b border-gray-200 dark:border-gray-700">
                            <h3 class="font-semibold text-gray-900 dark:text-white flex items-center">
                                <i class="fas fa-bolt text-purple-600 mr-2"></i>
                                Quick Actions
                            </h3>
                        </div>
                        <div class="p-4 space-y-2">
                            <?php if (!$alumni['verified']): ?>
                            <a href="verify_alumni.php?id=<?= $id ?>" class="w-full inline-flex items-center justify-center px-3 py-2 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-lg transition-colors">
                                <i class="fas fa-check-circle mr-2"></i> Verify Alumni
                            </a>
                            <?php endif; ?>
                            <a href="mailto:<?= htmlspecialchars($alumni['email']) ?>" class="w-full inline-flex items-center justify-center px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors">
                                <i class="fas fa-envelope mr-2"></i> Send Email
                            </a>
                            <a href="list_alumni.php" class="w-full inline-flex items-center justify-center px-3 py-2 bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium rounded-lg transition-colors">
                                <i class="fas fa-list mr-2"></i> All Alumni
                            </a>
                            <a href="collegeadmin_dashboard.php" class="w-full inline-flex items-center justify-center px-3 py-2 bg-indigo-600 hover:bg-indigo-700 text-white text-sm font-medium rounded-lg transition-colors">
                                <i class="fas fa-home mr-2"></i> Dashboard
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

<script>
function toggleDropdown(id) {
    const dropdown = document.getElementById(id);
    dropdown.classList.toggle('hidden');
}

// Close dropdown when clicking outside
document.addEventListener('click', function(event) {
    const dropdowns = document.querySelectorAll('[id$="Dropdown"]');
    dropdowns.forEach(dropdown => {
        if (!dropdown.contains(event.target) && !event.target.closest('button[onclick*="' + dropdown.id + '"]')) {
            dropdown.classList.add('hidden');
        }
    });
});
</script>

<?php require_once '../includes/footer.php'; ?>
