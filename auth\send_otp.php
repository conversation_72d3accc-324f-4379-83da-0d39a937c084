<?php
// Include session configuration
require_once '../includes/session_config.php';
require_once '../includes/db.php';
require_once '../notify/send_email.php';
require_once '../includes/testing_config.php';

function generateOTP(): string {
    return str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);
}

if (!isset($_SESSION['pending_2fa'])) {
    header("Location: ../auth/login_choice.php");
    exit;
}

// Testing mode: Skip OTP sending and go directly to verification
if (isTestingMode() && isOtpBypassEnabled()) {
    logTestingAction('OTP_SEND_SKIP', 'Skipping OTP email in testing mode');
    header("Location: verify_otp.php");
    exit;
}

$pending = $_SESSION['pending_2fa'];
$role = $pending['role'];
$email = $pending['email'];
$user_id = $pending['id'];

$otp = generateOTP();
$expires = date('Y-m-d H:i:s', strtotime('+5 minutes'));

$table = [
    'collegeadmin' => 'college_admins',
    'faculty' => 'faculties',
    'alumni' => 'alumni'
][$role];

// Update OTP and expiry
$stmt = $conn->prepare("UPDATE $table SET otp_code = ?, otp_expires_at = ? WHERE id = ?");
$stmt->execute([$otp, $expires, $user_id]);

// Email the OTP
$subject = "🔐 Your OTP for Connect My Students";
$body = "
    <p>Hello,</p>
    <p>Your One-Time Password (OTP) is:</p>
    <h2>$otp</h2>
    <p>This code is valid for 5 minutes.</p>
    <p>If you didn’t request this, please ignore.</p>
    <br><p>– Connect My Students Team</p>
";

// Add recipient name (using email as name for now since we don't have the actual name)
sendEmail($email, $email, $subject, $body);

header("Location: verify_otp.php");
exit;
