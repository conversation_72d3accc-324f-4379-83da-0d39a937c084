<?php
session_start();
require_once '../includes/db.php';
require_once '../includes/db_schema_check.php';
require_once '../notify/send_email.php';

if (!isset($_SESSION['collegeadmin_id'])) {
    header("Location: ../auth/collegeadmin_login.php");
    exit;
}

$college_id = $_SESSION['college_id'];
$admin_name = $_SESSION['collegeadmin_name'] ?? 'Admin';

// Set role explicitly for the sidebar
$role = 'collegeadmin';
$user_id = $_SESSION['collegeadmin_id'];
$user_name = $_SESSION['collegeadmin_name'];
$current_page = basename($_SERVER['PHP_SELF']);

$id = $_GET['id'] ?? null;
$email_sent = false;

if (!$id) {
    $_SESSION['flash_message'] = "Invalid alumni ID.";
    header("Location: list_alumni.php");
    exit;
}

// Get college details
$collegeStmt = $conn->prepare("SELECT name FROM colleges WHERE id = ?");
$collegeStmt->execute([$college_id]);
$college = $collegeStmt->fetch(PDO::FETCH_ASSOC);

// Get alumni details
$stmt = $conn->prepare("SELECT * FROM alumni WHERE id = ? AND college_id = ?");
$stmt->execute([$id, $college_id]);
$alumni = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$alumni) {
    $_SESSION['flash_message'] = "Alumni not found or access denied.";
    header("Location: list_alumni.php");
    exit;
}

// Check if alumni is already verified
if ($alumni['verified'] == 1) {
    $_SESSION['flash_message'] = "Alumni is already verified.";
    header("Location: view_alumni.php?id=" . $id);
    exit;
}

$success = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $notify_alumni = isset($_POST['notify_alumni']) ? true : false;
    
    try {
        // Update alumni verification status
        $updateStmt = $conn->prepare("UPDATE alumni SET verified = 1 WHERE id = ? AND college_id = ?");
        $updateStmt->execute([$id, $college_id]);
        
        // Send email notification if requested
        if ($notify_alumni) {
            $subject = "Your Alumni Account Has Been Verified - " . htmlspecialchars($college['name']);
            $message = "<p>Dear " . htmlspecialchars($alumni['full_name']) . ",</p>";
            $message .= "<p>Your alumni account at " . htmlspecialchars($college['name']) . " has been verified.</p>";
            $message .= "<p>You can now log in to your account and access all features of the alumni network, including mentorship opportunities, events, and more.</p>";
            $message .= "<p>Login at: <a href='https://" . $_SERVER['HTTP_HOST'] . "/auth/alumni_login.php'>Alumni Login</a></p>";
            $message .= "<p>Regards,<br>" . htmlspecialchars($admin_name) . "<br>Administration Team<br>" . htmlspecialchars($college['name']) . "</p>";
            
            $email_sent = sendEmail($alumni['email'], $alumni['full_name'], $subject, $message);
        }
        
        $success = "Alumni verified successfully.";
        if ($email_sent) {
            $success .= " Notification email has been sent to the alumni.";
        }
        
    } catch (PDOException $e) {
        $error = "Database error: " . $e->getMessage();
    }
}

// Profile photo handling
$photo = !empty($alumni['profile_image']) && file_exists("../uploads/{$alumni['profile_image']}")
    ? $alumni['profile_image']
    : 'default.png';

// Include the header
require_once '../includes/header.php';
?>

<!-- Main Content -->
<main class="flex-grow py-6 px-4">
    <div class="max-w-7xl mx-auto">
        <!-- Page Header -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                <div class="flex items-center space-x-4">
                    <div class="bg-gradient-to-br from-green-500 to-green-600 p-3 rounded-xl shadow-lg">
                        <i class="fas fa-user-check text-white text-xl"></i>
                    </div>
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                            Verify Alumni Account
                        </h1>
                        <nav class="text-sm text-gray-600 dark:text-gray-400 mt-1" aria-label="Breadcrumb">
                            <ol class="flex items-center space-x-2">
                                <li>
                                    <a href="collegeadmin_dashboard.php" class="hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
                                        Dashboard
                                    </a>
                                </li>
                                <li class="flex items-center">
                                    <i class="fas fa-chevron-right text-gray-400 text-xs mx-2"></i>
                                    <a href="list_alumni.php" class="hover:text-blue-600 dark:hover:text-blue-400 transition-colors">Alumni Management</a>
                                </li>
                                <li class="flex items-center">
                                    <i class="fas fa-chevron-right text-gray-400 text-xs mx-2"></i>
                                    <a href="view_alumni.php?id=<?= $id ?>" class="hover:text-blue-600 dark:hover:text-blue-400 transition-colors"><?= htmlspecialchars($alumni['full_name']) ?></a>
                                </li>
                                <li class="flex items-center">
                                    <i class="fas fa-chevron-right text-gray-400 text-xs mx-2"></i>
                                    <span class="text-gray-900 dark:text-white font-medium">Verify Alumni</span>
                                </li>
                            </ol>
                        </nav>
                        <p class="text-gray-600 dark:text-gray-400 mt-2">
                            Verify this alumni's account to grant them full access to the platform
                        </p>
                    </div>
                </div>
                <div class="mt-4 lg:mt-0 flex flex-wrap gap-3">
                    <a href="view_alumni.php?id=<?= $id ?>" class="inline-flex items-center px-4 py-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-medium rounded-lg transition-colors">
                        <i class="fas fa-arrow-left mr-2"></i> Back to Profile
                    </a>
                    <a href="list_alumni.php" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors">
                        <i class="fas fa-list mr-2"></i> All Alumni
                    </a>
                </div>
            </div>
        </div>

        <!-- Alert Messages -->
        <?php if ($error): ?>
            <div class="bg-red-50 dark:bg-red-900/50 border border-red-200 dark:border-red-800 rounded-xl p-4 mb-6">
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-circle text-red-600 dark:text-red-400"></i>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-red-800 dark:text-red-200">Error</h3>
                        <p class="text-sm text-red-700 dark:text-red-300 mt-1"><?= htmlspecialchars($error) ?></p>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <?php if ($success): ?>
            <div class="bg-green-50 dark:bg-green-900/50 border border-green-200 dark:border-green-800 rounded-xl p-4 mb-6">
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <i class="fas fa-check-circle text-green-600 dark:text-green-400"></i>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-green-800 dark:text-green-200">Success</h3>
                        <p class="text-sm text-green-700 dark:text-green-300 mt-1"><?= htmlspecialchars($success) ?></p>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- Main Content Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Left Column: Alumni Profile & Verification Form -->
            <div class="lg:col-span-2 space-y-8">
                <!-- Alumni Profile Card -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
                    <!-- Cover with Status -->
                    <div class="h-32 bg-gradient-to-br from-purple-600 via-purple-700 to-indigo-600 relative">
                        <div class="absolute top-4 right-4">
                            <div class="bg-yellow-500 text-white px-3 py-1 rounded-full text-sm font-medium flex items-center shadow-lg">
                                <i class="fas fa-clock mr-1"></i> Pending Verification
                            </div>
                        </div>
                    </div>

                    <!-- Profile Content -->
                    <div class="px-6 pb-6 relative">
                        <div class="flex flex-col sm:flex-row sm:items-center -mt-12 relative z-10">
                            <!-- Profile Picture -->
                            <div class="flex-shrink-0 mb-4 sm:mb-0 relative z-20">
                                <div class="w-24 h-24 rounded-xl border-4 border-white dark:border-gray-800 bg-gray-100 dark:bg-gray-700 flex items-center justify-center overflow-hidden shadow-lg">
                                    <?php if (!empty($alumni['profile_image'])): ?>
                                        <img src="../uploads/<?= htmlspecialchars($alumni['profile_image']) ?>" alt="<?= htmlspecialchars($alumni['full_name']) ?>" class="w-full h-full object-cover">
                                    <?php else: ?>
                                        <i class="fas fa-user-tie text-3xl text-gray-400 dark:text-gray-300"></i>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <!-- Alumni Info -->
                            <div class="sm:ml-6 flex-grow relative z-20 mt-4 sm:mt-0">
                                <div class="bg-white dark:bg-gray-800 rounded-xl p-4 border-2 border-gray-200 dark:border-gray-600 shadow-sm backdrop-blur-sm bg-opacity-95 dark:bg-opacity-95">
                                    <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-2"><?= htmlspecialchars($alumni['full_name']) ?></h2>
                                    <p class="text-gray-600 dark:text-gray-400 mb-3"><?= htmlspecialchars($alumni['email']) ?></p>
                                    <div class="flex flex-wrap gap-2">
                                        <?php if ($alumni['company']): ?>
                                        <span class="inline-flex items-center px-3 py-1 rounded-lg text-sm font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200">
                                            <i class="fas fa-building mr-1"></i>
                                            <?= htmlspecialchars($alumni['company']) ?>
                                        </span>
                                        <?php endif; ?>
                                        <?php if ($alumni['position']): ?>
                                        <span class="inline-flex items-center px-3 py-1 rounded-lg text-sm font-medium bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200">
                                            <i class="fas fa-briefcase mr-1"></i>
                                            <?= htmlspecialchars($alumni['position']) ?>
                                        </span>
                                        <?php endif; ?>
                                        <?php if ($alumni['industry']): ?>
                                        <span class="inline-flex items-center px-3 py-1 rounded-lg text-sm font-medium bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200">
                                            <i class="fas fa-industry mr-1"></i>
                                            <?= htmlspecialchars($alumni['industry']) ?>
                                        </span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Additional Details -->
                        <div class="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
                            <?php if ($alumni['graduation_year']): ?>
                            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                                <div class="text-sm text-gray-500 dark:text-gray-400">Graduation Year</div>
                                <div class="font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($alumni['graduation_year']) ?></div>
                            </div>
                            <?php endif; ?>
                            <?php if ($alumni['phone']): ?>
                            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                                <div class="text-sm text-gray-500 dark:text-gray-400">Phone</div>
                                <div class="font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($alumni['phone']) ?></div>
                            </div>
                            <?php endif; ?>
                            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                                <div class="text-sm text-gray-500 dark:text-gray-400">Joined</div>
                                <div class="font-medium text-gray-900 dark:text-white"><?= date('M d, Y', strtotime($alumni['created_at'])) ?></div>
                            </div>
                        </div>
                    </div>
                </div>
                            </span>
                        </div>
                        
                        <div class="mt-6 border-t border-gray-200 dark:border-gray-700 pt-4">
                            <div class="flex items-center justify-center space-x-4">
                                <a href="mailto:<?= htmlspecialchars($alumni['email']) ?>" class="text-gray-600 dark:text-gray-400 hover:text-primary dark:hover:text-primary-light">
                                    <i class="fas fa-envelope"></i>
                                </a>
                                <?php if (!empty($alumni['phone'])): ?>
                                    <a href="tel:<?= htmlspecialchars($alumni['phone']) ?>" class="text-gray-600 dark:text-gray-400 hover:text-primary dark:hover:text-primary-light">
                                        <i class="fas fa-phone"></i>
                                    </a>
                                <?php endif; ?>
                                <?php if (!empty($alumni['linkedin_url'])): ?>
                                    <a href="<?= htmlspecialchars($alumni['linkedin_url']) ?>" target="_blank" rel="noopener noreferrer" class="text-gray-600 dark:text-gray-400 hover:text-primary dark:hover:text-primary-light">
                                        <i class="fab fa-linkedin"></i>
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Verification Form -->
            <div class="lg:col-span-2">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700">
                    <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                        <h2 class="text-lg font-semibold text-gray-800 dark:text-white flex items-center">
                            <i class="fas fa-user-check text-primary mr-2"></i> Alumni Verification
                        </h2>
                        <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                            Review the alumni information before verifying their account
                        </p>
                    </div>
                    
                    <div class="p-6">
                        <div class="mb-6">
                            <h3 class="text-md font-medium text-gray-800 dark:text-white mb-3">Alumni Information</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Full Name</p>
                                    <p class="text-sm text-gray-900 dark:text-white"><?= htmlspecialchars($alumni['full_name']) ?></p>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Email Address</p>
                                    <p class="text-sm text-gray-900 dark:text-white"><?= htmlspecialchars($alumni['email']) ?></p>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Graduation Year</p>
                                    <p class="text-sm text-gray-900 dark:text-white"><?= htmlspecialchars($alumni['graduation_year'] ?? 'Not specified') ?></p>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Industry</p>
                                    <p class="text-sm text-gray-900 dark:text-white"><?= htmlspecialchars($alumni['industry'] ?? 'Not specified') ?></p>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Company</p>
                                    <p class="text-sm text-gray-900 dark:text-white"><?= htmlspecialchars($alumni['company'] ?? 'Not specified') ?></p>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Position</p>
                                    <p class="text-sm text-gray-900 dark:text-white"><?= htmlspecialchars($alumni['position'] ?? 'Not specified') ?></p>
                                </div>
                            </div>
                        </div>
                        
                        <?php if (!empty($alumni['bio'])): ?>
                            <div class="mb-6">
                                <h3 class="text-md font-medium text-gray-800 dark:text-white mb-2">Bio</h3>
                                <p class="text-sm text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-700/50 p-3 rounded-md">
                                    <?= nl2br(htmlspecialchars($alumni['bio'])) ?>
                                </p>
                            </div>
                        <?php endif; ?>
                        
                        <div class="bg-yellow-50 dark:bg-yellow-900/20 border-l-4 border-yellow-500 text-yellow-700 dark:text-yellow-400 p-4 mb-6">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm">
                                        Verifying this alumni will grant them full access to the alumni network, including mentorship features, events, and resources. Please ensure their information is accurate before proceeding.
                                    </p>
                                </div>
                            </div>
                        </div>
                        
                        <form method="POST" action="">
                            <div class="mb-6">
                                <label class="flex items-center">
                                    <input type="checkbox" name="notify_alumni" value="1" checked class="form-checkbox">
                                    <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                                        Send email notification to alumni
                                    </span>
                                </label>
                            </div>
                            
                            <div class="flex items-center justify-end space-x-3">
                                <a href="list_alumni.php" class="btn btn-outline">Cancel</a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-check-circle mr-2"></i> Verify Alumni
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

<?php
// Include the footer
require_once '../includes/footer.php';
?> 