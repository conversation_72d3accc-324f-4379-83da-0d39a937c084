<?php
require_once '../includes/db.php';
require_once '../includes/header.php';

if (!isset($_SESSION['alumni_id'])) {
    header("Location: ../auth/alumni_login.php");
    exit;
}

$alumni_id = $_SESSION['alumni_id'];

$stmt = $conn->prepare("SELECT r.score, r.feedback, r.created_at, s.full_name, s.profile_photo 
    FROM ratings r 
    JOIN students s ON r.student_id = s.id 
    WHERE r.alumni_id = ? 
    ORDER BY r.created_at DESC");
$stmt->execute([$alumni_id]);
$ratings = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<div class="container mt-4">
    <h3>⭐ Student Feedback</h3>
    <p class="text-muted">See what students say about your mentorship</p>

    <?php if ($ratings): ?>
        <?php foreach ($ratings as $r): ?>
            <div class="card mb-3 shadow-sm">
                <div class="card-body d-flex">
                    <img src="../uploads/<?= file_exists("../uploads/{$r['profile_photo']}") ? $r['profile_photo'] : 'default.png' ?>" width="60" height="60" class="rounded-circle me-3 border">
                    <div>
                        <h6 class="mb-0"><?= htmlspecialchars($r['full_name']) ?></h6>
                        <small class="text-muted"><?= date("F j, Y", strtotime($r['created_at'])) ?></small>
                        <p class="mt-2 mb-1"><?= nl2br(htmlspecialchars($r['feedback'])) ?></p>
                        <span class="badge bg-warning text-dark">Rated: <?= $r['score'] ?>/5</span>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    <?php else: ?>
        <div class="alert alert-info">No ratings received yet.</div>
    <?php endif; ?>
</div>

<?php require_once '../includes/footer.php'; ?>
