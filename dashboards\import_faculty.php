<?php
// Include session configuration
require_once '../includes/session_config.php';
require_once '../includes/db.php';
require_once '../includes/db_schema_check.php';
require_once '../notify/send_email.php';

if (!isset($_SESSION['collegeadmin_id'])) {
    header("Location: ../auth/collegeadmin_login.php");
    exit;
}

$college_id = $_SESSION['college_id'];
$admin_name = $_SESSION['collegeadmin_name'];

// Get college details
$collegeStmt = $conn->prepare("SELECT name FROM colleges WHERE id = ?");
$collegeStmt->execute([$college_id]);
$college = $collegeStmt->fetch(PDO::FETCH_ASSOC);

// Process form submission
$success = '';
$error = '';
$preview_data = [];
$column_mapping = [];
$has_header = true;
$uploaded_file = '';
$import_summary = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Handle file upload
    if (isset($_POST['upload']) && isset($_FILES['import_file'])) {
        $file = $_FILES['import_file'];
        $has_header = isset($_POST['has_header']) ? true : false;
        $send_credentials = isset($_POST['send_credentials']) ? true : false;

        // Check for errors
        if ($file['error'] !== UPLOAD_ERR_OK) {
            $error = "File upload failed. Error code: " . $file['error'];
        } else {
            // Check file size (10MB limit)
            if ($file['size'] > 10 * 1024 * 1024) {
                $error = "File size exceeds the 10MB limit.";
            } else {
                $file_name = $file['name'];
                $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));
                
                // Check file extension
                if (!in_array($file_ext, ['csv', 'xlsx', 'xls'])) {
                    $error = "Only CSV and Excel files are allowed.";
                } else {
                    // Create uploads directory if it doesn't exist
                    $upload_dir = '../uploads/temp';
                    if (!file_exists($upload_dir)) {
                        mkdir($upload_dir, 0755, true);
                    }
                    
                    // Generate unique filename
                    $uploaded_file = $upload_dir . '/' . uniqid() . '_' . $file_name;
                    
                    // Move uploaded file
                    if (move_uploaded_file($file['tmp_name'], $uploaded_file)) {
                        // Process CSV file
                        if ($file_ext === 'csv') {
                            // Read CSV file
                            $handle = fopen($uploaded_file, 'r');
                            if ($handle !== false) {
                                $row_count = 0;
                                $headers = [];
                                
                                // Read first 10 rows for preview
                                while (($data = fgetcsv($handle)) !== false && $row_count < 11) {
                                    if ($row_count === 0 && $has_header) {
                                        $headers = $data;
                                    } else {
                                        $preview_data[] = $data;
                                    }
                                    $row_count++;
                                }
                                
                                fclose($handle);
                                
                                // If no header row, generate column names
                                if (!$has_header) {
                                    $headers = [];
                                    for ($i = 0; $i < count($preview_data[0]); $i++) {
                                        $headers[] = "Column " . ($i + 1);
                                    }
                                }
                                
                                // Create column mapping
                                $required_fields = ['full_name', 'email', 'department', 'designation'];
                                $optional_fields = ['faculty_id', 'specialization', 'phone', 'password'];
                                $all_fields = array_merge($required_fields, $optional_fields);
                                
                                foreach ($headers as $index => $header) {
                                    $header_clean = strtolower(trim(str_replace(' ', '_', $header)));

                                    // Try to match header with field using various patterns
                                    $matched = false;

                                    // Direct mapping for common variations
                                    $field_mappings = [
                                        'full_name' => ['full_name', 'name', 'faculty_name'],
                                        'email' => ['email', 'email_address', 'mail'],
                                        'department' => ['department', 'dept'],
                                        'designation' => ['designation', 'position', 'title', 'role'],
                                        'faculty_id' => ['faculty_id', 'id', 'emp_id', 'employee_id'],
                                        'specialization' => ['specialization', 'specialty', 'expertise', 'bio'],
                                        'phone' => ['phone', 'mobile', 'contact', 'phone_number'],
                                        'password' => ['password', 'pass']
                                    ];

                                    foreach ($field_mappings as $field => $variations) {
                                        foreach ($variations as $variation) {
                                            if (strpos($header_clean, $variation) !== false) {
                                                $column_mapping[$index] = $field;
                                                $matched = true;
                                                break 2;
                                            }
                                        }
                                    }

                                    if (!$matched) {
                                        $column_mapping[$index] = '';
                                    }
                                }
                                
                                $success = "File uploaded successfully. Please review the data and complete the import.";
                            } else {
                                $error = "Failed to open the uploaded file.";
                            }
                        } else {
                            // For Excel files - would need PhpSpreadsheet library
                            $error = "Excel file processing requires the PhpSpreadsheet library. Please use CSV format for now.";
                            // Delete the uploaded file
                            if (file_exists($uploaded_file)) {
                                unlink($uploaded_file);
                                $uploaded_file = '';
                            }
                        }
                    } else {
                        $error = "Failed to move uploaded file.";
                    }
                }
            }
        }
    }
    
    // Handle import confirmation
    if (isset($_POST['confirm_import']) && !empty($_POST['uploaded_file'])) {
        $uploaded_file = $_POST['uploaded_file'];
        $has_header = isset($_POST['has_header']) && $_POST['has_header'] === '1';
        $send_credentials = isset($_POST['send_credentials']) && $_POST['send_credentials'] === '1';
        $column_mapping = $_POST['column_mapping'] ?? [];

        if (file_exists($uploaded_file)) {
            try {
                $file_ext = strtolower(pathinfo($uploaded_file, PATHINFO_EXTENSION));
                $import_data = [];

                if ($file_ext === 'csv') {
                    if (($handle = fopen($uploaded_file, "r")) !== FALSE) {
                        $row_count = 0;
                        while (($data = fgetcsv($handle, 1000, ",")) !== FALSE) {
                            $row_count++;

                            // Skip header row if specified
                            if ($has_header && $row_count === 1) {
                                continue;
                            }

                            $import_data[] = $data;
                        }
                        fclose($handle);
                    }
                }

                // Process the import data
                $imported_count = 0;
                $skipped_count = 0;
                $errors = [];

                $total_rows = count($import_data);
                if ($total_rows === 0) {
                    $error = "No data rows found in the uploaded file.";
                } else {
                    $conn->beginTransaction();
                    try {
                        foreach ($import_data as $row_index => $row_data) {
                            $faculty_data = [];

                            // Map columns to database fields
                            foreach ($column_mapping as $col_index => $field_name) {
                                if (!empty($field_name) && isset($row_data[$col_index])) {
                                    $value = trim($row_data[$col_index]);

                                    switch ($field_name) {
                                        case 'full_name':
                                            $faculty_data['name'] = $value;
                                            break;
                                        case 'email':
                                            $faculty_data['email'] = $value;
                                            break;
                                        case 'department':
                                            $faculty_data['department'] = $value;
                                            break;
                                        case 'designation':
                                            $faculty_data['position'] = $value;
                                            break;
                                        case 'faculty_id':
                                            $faculty_data['faculty_id'] = $value;
                                            break;
                                        case 'specialization':
                                            $faculty_data['bio'] = $value;
                                            break;
                                        case 'phone':
                                            $faculty_data['phone'] = $value;
                                            break;
                                        case 'password':
                                            $faculty_data['password'] = $value;
                                            break;
                                    }
                                }
                            }

                            // Validate required fields
                            if (empty($faculty_data['name']) || empty($faculty_data['email']) || empty($faculty_data['department'])) {
                                $skipped_count++;
                                $errors[] = "Row " . ($row_index + 2) . ": Missing required fields (Name, Email, or Department)";
                                continue;
                            }

                            // Check if email already exists
                            $check_email = $conn->prepare("SELECT COUNT(*) FROM faculties WHERE email = ? AND college_id = ?");
                            $check_email->execute([$faculty_data['email'], $college_id]);
                            if ($check_email->fetchColumn() > 0) {
                                $skipped_count++;
                                $errors[] = "Row " . ($row_index + 2) . ": Email already exists - " . $faculty_data['email'];
                                continue;
                            }

                            // Generate password if not provided
                            if (empty($faculty_data['password'])) {
                                $faculty_data['password'] = bin2hex(random_bytes(4)); // 8 character random password
                            }

                            // Hash the password
                            $hashed_password = password_hash($faculty_data['password'], PASSWORD_DEFAULT);

                            // Insert faculty member - try with all fields first
                            try {
                                $insert_sql = "INSERT INTO faculties (name, email, department, position, bio, phone, password, college_id, status, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'active', NOW())";
                                $insert_stmt = $conn->prepare($insert_sql);

                                $result = $insert_stmt->execute([
                                    $faculty_data['name'],
                                    $faculty_data['email'],
                                    $faculty_data['department'],
                                    $faculty_data['position'] ?? '',
                                    $faculty_data['bio'] ?? '',
                                    $faculty_data['phone'] ?? '',
                                    $hashed_password,
                                    $college_id
                                ]);
                            } catch (PDOException $e) {
                                // If that fails, try with minimal required fields
                                try {
                                    $insert_sql = "INSERT INTO faculties (name, email, department, password, college_id, created_at) VALUES (?, ?, ?, ?, ?, NOW())";
                                    $insert_stmt = $conn->prepare($insert_sql);

                                    $result = $insert_stmt->execute([
                                        $faculty_data['name'],
                                        $faculty_data['email'],
                                        $faculty_data['department'],
                                        $hashed_password,
                                        $college_id
                                    ]);
                                } catch (PDOException $e2) {
                                    $result = false;
                                    $errors[] = "Row " . ($row_index + 2) . ": Database error - " . $e2->getMessage();
                                }
                            }

                            if ($result) {
                                $imported_count++;

                                // Send credentials email if requested
                                if ($send_credentials) {
                                    try {
                                        $email_subject = "Faculty Account Created - " . ($college['name'] ?? 'College');
                                        $email_body = "Dear " . $faculty_data['name'] . ",\n\n";
                                        $email_body .= "Your faculty account has been created.\n\n";
                                        $email_body .= "Login Details:\n";
                                        $email_body .= "Email: " . $faculty_data['email'] . "\n";
                                        $email_body .= "Password: " . $faculty_data['password'] . "\n\n";
                                        $email_body .= "Please login and change your password.\n\n";
                                        $email_body .= "Best regards,\n" . ($college['name'] ?? 'College Administration');

                                        sendEmail($faculty_data['email'], $email_subject, $email_body);
                                    } catch (Exception $e) {
                                        // Email sending failed, but don't stop the import
                                        $errors[] = "Row " . ($row_index + 2) . ": Faculty imported but email failed to send";
                                    }
                                }
                            } else {
                                $skipped_count++;
                                $errors[] = "Row " . ($row_index + 2) . ": Database error occurred - " . $insert_stmt->errorInfo()[2];
                            }
                        }
                        $conn->commit();
                    } catch (Exception $e) {
                        $conn->rollBack();
                        $error = "A critical error occurred during import, and all changes have been rolled back. Please check your file and try again. Error: " . $e->getMessage();
                        $imported_count = 0;
                        $skipped_count = $total_rows;
                    }
                }

                // Clean up uploaded file
                if (file_exists($uploaded_file)) {
                    unlink($uploaded_file);
                }

                // Set success message
                if (empty($error)) {
                    $success = "Import completed! Imported: $imported_count faculty members out of $total_rows total rows.";
                    if ($skipped_count > 0) {
                        $success .= " Skipped: $skipped_count rows.";
                    }
                }

                // Clear preview data
                $preview_data = [];
                $uploaded_file = '';

                // Set import summary for display
                $import_summary = [
                    'imported' => $imported_count,
                    'skipped' => $skipped_count,
                    'errors' => $errors
                ];
            } catch (Exception $e) {
                $error = "Import failed: " . $e->getMessage();
            }
        } else {
            $error = "Uploaded file not found. Please try uploading again.";
        }
    }
}

// Set role explicitly for the sidebar
$role = 'collegeadmin';
$user_id = $_SESSION['collegeadmin_id'];
$user_name = $_SESSION['collegeadmin_name'];
$current_page = basename($_SERVER['PHP_SELF']);

// Set dashboard URL for header
$dashboard_url = 'collegeadmin_dashboard.php';

// Include the header
require_once '../includes/header.php';
?>
    <!-- Main Content -->
    <main class="flex-grow py-6 px-4">
        <div class="max-w-7xl mx-auto">
            <!-- Page Header -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8">
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="bg-gradient-to-br from-blue-500 to-blue-600 p-3 rounded-xl shadow-lg">
                            <i class="fas fa-file-import text-white text-xl"></i>
                        </div>
                        <div>
                            <h1 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                                Import Faculty Members
                            </h1>
                            <p class="text-gray-600 dark:text-gray-400 mt-1">
                                Bulk import faculty members to <span class="font-medium text-blue-600 dark:text-blue-400"><?= htmlspecialchars($college['name'] ?? 'your college') ?></span>
                                <span class="mx-2">•</span>
                                <span class="text-sm"><?= date('l, F j, Y') ?></span>
                            </p>
                        </div>
                    </div>
                    <div class="mt-6 lg:mt-0 flex flex-wrap gap-3">
                        <a href="list_faculty.php" class="btn btn-outline flex items-center text-sm hover:shadow-md transition-all duration-200">
                            <i class="fas fa-arrow-left mr-2"></i> Back to Faculty List
                        </a>
                        <a href="add_faculty.php" class="btn btn-outline flex items-center text-sm hover:shadow-md transition-all duration-200">
                            <i class="fas fa-user-plus mr-2"></i> Add Individual
                        </a>
                    </div>
                </div>
            </div>

            <!-- Flash Messages -->
            <?php if (!empty($error)): ?>
                <div class="bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 rounded-xl p-4 mb-6">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-circle text-red-500 mr-3"></i>
                        <span class="text-red-700 dark:text-red-300"><?= htmlspecialchars($error) ?></span>
                    </div>
                </div>
            <?php endif; ?>

            <?php if (!empty($success)): ?>
                <div class="bg-green-50 dark:bg-green-900/30 border border-green-200 dark:border-green-800 rounded-xl p-4 mb-6">
                    <div class="flex items-center">
                        <i class="fas fa-check-circle text-green-500 mr-3"></i>
                        <span class="text-green-700 dark:text-green-300"><?= htmlspecialchars($success) ?></span>
                    </div>
                </div>
            <?php endif; ?>

            <?php if (!empty($import_summary)): ?>
                <div class="bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-800 rounded-xl p-6 mb-6">
                    <h3 class="text-lg font-semibold text-blue-800 dark:text-blue-300 mb-4 flex items-center">
                        <i class="fas fa-chart-bar mr-2"></i> Import Summary
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div class="bg-white dark:bg-blue-800/20 rounded-lg p-4">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-check text-green-600 dark:text-green-400"></i>
                                </div>
                                <div>
                                    <p class="text-2xl font-bold text-green-600 dark:text-green-400"><?= $import_summary['imported'] ?></p>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">Successfully Imported</p>
                                </div>
                            </div>
                        </div>
                        <div class="bg-white dark:bg-blue-800/20 rounded-lg p-4">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-exclamation-triangle text-orange-600 dark:text-orange-400"></i>
                                </div>
                                <div>
                                    <p class="text-2xl font-bold text-orange-600 dark:text-orange-400"><?= $import_summary['skipped'] ?></p>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">Skipped/Errors</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <?php if (!empty($import_summary['errors'])): ?>
                        <div class="bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg p-4">
                            <h4 class="font-medium text-orange-800 dark:text-orange-300 mb-2">Issues Found:</h4>
                            <ul class="text-sm text-orange-700 dark:text-orange-400 space-y-1">
                                <?php foreach (array_slice($import_summary['errors'], 0, 10) as $error): ?>
                                    <li>• <?= htmlspecialchars($error) ?></li>
                                <?php endforeach; ?>
                                <?php if (count($import_summary['errors']) > 10): ?>
                                    <li class="font-medium">... and <?= count($import_summary['errors']) - 10 ?> more issues</li>
                                <?php endif; ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <div class="mt-4 flex space-x-3">
                        <a href="list_faculty.php" class="btn btn-primary">
                            <i class="fas fa-list mr-2"></i> View Faculty List
                        </a>
                        <a href="import_faculty.php" class="btn btn-outline">
                            <i class="fas fa-plus mr-2"></i> Import More
                        </a>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Main Content Grid -->
            <div class="grid grid-cols-1 xl:grid-cols-4 gap-8">
                <!-- Left Column - Main Content (3/4 width) -->
                <div class="xl:col-span-3">
                    <!-- Import Instructions -->
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 mb-8">
                        <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
                            <h2 class="text-lg font-semibold text-gray-800 dark:text-white flex items-center">
                                <i class="fas fa-info-circle mr-3 text-blue-600"></i> Import Instructions
                            </h2>
                            <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">Follow these guidelines for successful faculty import</p>
                        </div>

                        <div class="p-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                                <div>
                                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-3 flex items-center">
                                        <i class="fas fa-file-alt mr-2 text-green-600"></i> File Requirements
                                    </h3>
                                    <ul class="space-y-2 text-gray-700 dark:text-gray-300">
                                        <li class="flex items-center">
                                            <i class="fas fa-check text-green-500 mr-2 text-sm"></i>
                                            CSV (.csv) or Excel (.xlsx) format
                                        </li>
                                        <li class="flex items-center">
                                            <i class="fas fa-check text-green-500 mr-2 text-sm"></i>
                                            Maximum file size: 10MB
                                        </li>
                                        <li class="flex items-center">
                                            <i class="fas fa-check text-green-500 mr-2 text-sm"></i>
                                            First row should contain headers
                                        </li>
                                        <li class="flex items-center">
                                            <i class="fas fa-check text-green-500 mr-2 text-sm"></i>
                                            Maximum 500 faculty per import
                                        </li>
                                    </ul>
                                </div>
                                <div>
                                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-3 flex items-center">
                                        <i class="fas fa-columns mr-2 text-blue-600"></i> Required Columns
                                    </h3>
                                    <ul class="space-y-2 text-gray-700 dark:text-gray-300">
                                        <li class="flex items-center">
                                            <span class="w-2 h-2 bg-red-500 rounded-full mr-2"></span>
                                            Full Name
                                        </li>
                                        <li class="flex items-center">
                                            <span class="w-2 h-2 bg-red-500 rounded-full mr-2"></span>
                                            Email Address
                                        </li>
                                        <li class="flex items-center">
                                            <span class="w-2 h-2 bg-red-500 rounded-full mr-2"></span>
                                            Department
                                        </li>
                                        <li class="flex items-center">
                                            <span class="w-2 h-2 bg-gray-400 rounded-full mr-2"></span>
                                            Position (optional)
                                        </li>
                                    </ul>
                                </div>
                            </div>

                            <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 mb-6">
                                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-3 flex items-center">
                                    <i class="fas fa-download mr-2 text-purple-600"></i> Download Templates
                                </h3>
                                <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
                                    Use these templates to ensure your data is formatted correctly
                                </p>
                                <div class="flex flex-wrap gap-3">
                                    <a href="../templates/sample_faculty_import.csv" download class="btn btn-outline flex items-center hover:shadow-md transition-all duration-200">
                                        <i class="fas fa-file-csv mr-2 text-green-600"></i> CSV Template
                                    </a>
                                    <a href="../templates/sample_faculty_import.xlsx" download class="btn btn-outline flex items-center hover:shadow-md transition-all duration-200">
                                        <i class="fas fa-file-excel mr-2 text-green-600"></i> Excel Template
                                    </a>
                                </div>
                            </div>

                            <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                                <h4 class="font-medium text-blue-800 dark:text-blue-300 mb-2 flex items-center">
                                    <i class="fas fa-lightbulb mr-2"></i> Important Notes
                                </h4>
                                <ul class="text-sm text-blue-700 dark:text-blue-400 space-y-1">
                                    <li>• Email addresses must be unique across the system</li>
                                    <li>• If passwords are not provided, random passwords will be generated</li>
                                    <li>• All faculty members will be initially set as "Active"</li>
                                    <li>• Duplicate email addresses will be skipped during import</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Upload Form -->
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 mb-8">
                        <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
                            <h2 class="text-lg font-semibold text-gray-800 dark:text-white flex items-center">
                                <i class="fas fa-cloud-upload-alt mr-3 text-green-600"></i> Upload Faculty File
                            </h2>
                            <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">Select your CSV or Excel file to import faculty members</p>
                        </div>

                        <div class="p-6">
                            <form method="POST" enctype="multipart/form-data" class="space-y-6">
                                <!-- File Upload Area -->
                                <div class="space-y-4">
                                    <label for="import_file" class="block text-sm font-semibold text-gray-700 dark:text-gray-300">
                                        Select File <span class="text-red-500">*</span>
                                    </label>
                                    <div class="relative">
                                        <label for="import_file" class="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 dark:border-gray-600 border-dashed rounded-xl cursor-pointer bg-gray-50 dark:bg-gray-700/50 hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-200">
                                            <div class="flex flex-col items-center justify-center pt-5 pb-6">
                                                <i class="fas fa-cloud-upload-alt text-3xl text-gray-400 mb-3"></i>
                                                <p class="mb-2 text-sm text-gray-500 dark:text-gray-400">
                                                    <span class="font-semibold">Click to upload</span> or drag and drop
                                                </p>
                                                <p class="text-xs text-gray-500 dark:text-gray-400">CSV or Excel files (MAX. 10MB)</p>
                                            </div>
                                            <input id="import_file" name="import_file" type="file" class="hidden" accept=".csv,.xlsx,.xls" required />
                                        </label>
                                        <div id="file_info" class="hidden mt-3 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                                            <div class="flex items-center">
                                                <i class="fas fa-file text-blue-600 mr-2"></i>
                                                <span id="file_name" class="text-sm font-medium text-blue-800 dark:text-blue-300"></span>
                                                <span id="file_size" class="text-xs text-blue-600 dark:text-blue-400 ml-2"></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Options -->
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div class="space-y-4">
                                        <h3 class="text-sm font-semibold text-gray-700 dark:text-gray-300">Import Options</h3>
                                        <div class="space-y-3">
                                            <div class="flex items-center">
                                                <input type="checkbox" id="has_header" name="has_header" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" checked>
                                                <label for="has_header" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                                                    File contains header row
                                                </label>
                                            </div>
                                            <div class="flex items-center">
                                                <input type="checkbox" id="send_credentials" name="send_credentials" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                                <label for="send_credentials" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                                                    Send login credentials via email
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="space-y-4">
                                        <h3 class="text-sm font-semibold text-gray-700 dark:text-gray-300">Quick Stats</h3>
                                        <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                                            <div class="text-sm text-gray-600 dark:text-gray-400">
                                                <div class="flex justify-between mb-2">
                                                    <span>Max file size:</span>
                                                    <span class="font-medium">10MB</span>
                                                </div>
                                                <div class="flex justify-between mb-2">
                                                    <span>Max records:</span>
                                                    <span class="font-medium">500</span>
                                                </div>
                                                <div class="flex justify-between">
                                                    <span>Supported formats:</span>
                                                    <span class="font-medium">CSV, Excel</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Submit Button -->
                                <div class="flex justify-end pt-4 border-t border-gray-200 dark:border-gray-700">
                                    <button type="submit" name="upload" class="btn btn-primary shadow-lg hover:shadow-xl transition-all duration-200">
                                        <i class="fas fa-upload mr-2"></i> Upload and Preview
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Right Column - Sidebar (1/4 width) -->
                <div class="xl:col-span-1">
                    <div class="space-y-6">
                        <!-- Quick Actions -->
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                            <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
                                <h3 class="text-lg font-semibold text-gray-800 dark:text-white flex items-center">
                                    <i class="fas fa-bolt mr-3 text-blue-600"></i> Quick Actions
                                </h3>
                            </div>
                            <div class="p-6 space-y-3">
                                <a href="list_faculty.php" class="flex items-center p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                                    <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-list text-blue-600 dark:text-blue-400"></i>
                                    </div>
                                    <div>
                                        <p class="font-medium text-gray-900 dark:text-white">All Faculty</p>
                                        <p class="text-sm text-gray-500 dark:text-gray-400">View faculty list</p>
                                    </div>
                                </a>

                                <a href="add_faculty.php" class="flex items-center p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                                    <div class="w-10 h-10 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-user-plus text-green-600 dark:text-green-400"></i>
                                    </div>
                                    <div>
                                        <p class="font-medium text-gray-900 dark:text-white">Add Individual</p>
                                        <p class="text-sm text-gray-500 dark:text-gray-400">Add single faculty</p>
                                    </div>
                                </a>

                                <a href="export_faculty.php" class="flex items-center p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                                    <div class="w-10 h-10 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-file-export text-purple-600 dark:text-purple-400"></i>
                                    </div>
                                    <div>
                                        <p class="font-medium text-gray-900 dark:text-white">Export Faculty</p>
                                        <p class="text-sm text-gray-500 dark:text-gray-400">Download faculty data</p>
                                    </div>
                                </a>
                            </div>
                        </div>

                        <!-- Import Progress -->
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                            <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
                                <h3 class="text-lg font-semibold text-gray-800 dark:text-white flex items-center">
                                    <i class="fas fa-chart-line mr-3 text-green-600"></i> Import Steps
                                </h3>
                            </div>
                            <div class="p-6">
                                <div class="space-y-4">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mr-3">
                                            <span class="text-sm font-medium text-blue-600 dark:text-blue-400">1</span>
                                        </div>
                                        <div>
                                            <p class="font-medium text-gray-900 dark:text-white">Download Template</p>
                                            <p class="text-sm text-gray-500 dark:text-gray-400">Get the correct format</p>
                                        </div>
                                    </div>
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mr-3">
                                            <span class="text-sm font-medium text-blue-600 dark:text-blue-400">2</span>
                                        </div>
                                        <div>
                                            <p class="font-medium text-gray-900 dark:text-white">Prepare Data</p>
                                            <p class="text-sm text-gray-500 dark:text-gray-400">Fill in faculty information</p>
                                        </div>
                                    </div>
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mr-3">
                                            <span class="text-sm font-medium text-blue-600 dark:text-blue-400">3</span>
                                        </div>
                                        <div>
                                            <p class="font-medium text-gray-900 dark:text-white">Upload & Preview</p>
                                            <p class="text-sm text-gray-500 dark:text-gray-400">Review before import</p>
                                        </div>
                                    </div>
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mr-3">
                                            <span class="text-sm font-medium text-gray-500 dark:text-gray-400">4</span>
                                        </div>
                                        <div>
                                            <p class="font-medium text-gray-500 dark:text-gray-400">Confirm Import</p>
                                            <p class="text-sm text-gray-500 dark:text-gray-400">Complete the process</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <?php if (!empty($preview_data)): ?>
            <!-- Data Preview and Mapping -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 mb-8">
                <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
                    <h2 class="text-lg font-semibold text-gray-800 dark:text-white flex items-center">
                        <i class="fas fa-table mr-3 text-orange-600"></i> Data Preview & Column Mapping
                    </h2>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">Review your data and map columns to the correct fields</p>
                </div>

                <div class="p-6">
                    <form method="POST" class="space-y-6">
                        <input type="hidden" name="uploaded_file" value="<?= htmlspecialchars($uploaded_file) ?>">
                        <input type="hidden" name="has_header" value="<?= $has_header ? '1' : '0' ?>">
                        <input type="hidden" name="send_credentials" value="<?= isset($_POST['send_credentials']) ? '1' : '0' ?>">
                        
                        <div class="mb-6">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-3">Column Mapping</h3>
                            <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
                                Please verify that the columns are correctly mapped to the appropriate fields. Required fields are marked with an asterisk (*).
                            </p>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                                <?php foreach ($column_mapping as $index => $field): ?>
                                    <div class="form-group">
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                            <?= htmlspecialchars($has_header ? $headers[$index] : "Column " . ($index + 1)) ?>
                                        </label>
                                        <select name="column_mapping[<?= $index ?>]" class="form-select w-full">
                                            <option value="">-- Ignore this column --</option>
                                            <option value="full_name" <?= $field === 'full_name' ? 'selected' : '' ?>>Full Name *</option>
                                            <option value="email" <?= $field === 'email' ? 'selected' : '' ?>>Email *</option>
                                            <option value="department" <?= $field === 'department' ? 'selected' : '' ?>>Department *</option>
                                            <option value="designation" <?= $field === 'designation' ? 'selected' : '' ?>>Designation *</option>
                                            <option value="faculty_id" <?= $field === 'faculty_id' ? 'selected' : '' ?>>Faculty ID</option>
                                            <option value="specialization" <?= $field === 'specialization' ? 'selected' : '' ?>>Specialization</option>
                                            <option value="phone" <?= $field === 'phone' ? 'selected' : '' ?>>Phone</option>
                                            <option value="password" <?= $field === 'password' ? 'selected' : '' ?>>Password</option>
                                        </select>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        
                        <div class="mb-6">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-3">Data Preview</h3>
                            <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
                                Showing up to 10 rows from your file. Please review the data before importing.
                            </p>
                            
                            <div class="overflow-x-auto">
                                <table class="min-w-full bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 text-sm">
                                    <thead>
                                        <tr>
                                            <?php foreach ($headers as $header): ?>
                                                <th class="py-2 px-3 bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-600 text-left"><?= htmlspecialchars($header) ?></th>
                                            <?php endforeach; ?>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($preview_data as $row): ?>
                                            <tr>
                                                <?php foreach ($row as $cell): ?>
                                                    <td class="py-2 px-3 border-b border-gray-200 dark:border-gray-600"><?= htmlspecialchars($cell) ?></td>
                                                <?php endforeach; ?>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        
                        <div class="flex justify-between items-center">
                            <div class="text-sm text-gray-600 dark:text-gray-400">
                                <p><i class="fas fa-info-circle mr-1"></i> Total rows to import: <span class="font-medium"><?= count($preview_data) ?></span></p>
                            </div>
                            <div class="flex space-x-3">
                                <a href="import_faculty.php" class="btn btn-outline">
                                    <i class="fas fa-times mr-2"></i> Cancel
                                </a>
                                <button type="submit" name="confirm_import" class="btn btn-primary">
                                    <i class="fas fa-file-import mr-2"></i> Import Faculty
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <?php endif; ?>
            
            <!-- Create Sample Templates Directory and Files -->
            <?php
            $templates_dir = '../templates';
            if (!file_exists($templates_dir)) {
                mkdir($templates_dir, 0755, true);
            }
            
            // Create CSV Template
            $csv_template = $templates_dir . '/sample_faculty_import.csv';
            if (!file_exists($csv_template)) {
                $csv_content = "Full Name,Email,Department,Designation,Faculty ID,Specialization,Phone,Password\n";
                $csv_content .= "Dr. John Doe,<EMAIL>,Computer Science,Associate Professor,FAC2023001,Machine Learning,+1234567890,password123\n";
                $csv_content .= "Prof. Jane Smith,<EMAIL>,Electrical Engineering,Professor,FAC2022045,Power Systems,+9876543210,securepass456";
                file_put_contents($csv_template, $csv_content);
            }
            
            // Note: Excel template would require a library like PhpSpreadsheet to create
            // For now, we'll just provide the CSV template
            ?>
        </div>
    </main>

    <script>
        // File upload handling
        document.getElementById('import_file').addEventListener('change', function(e) {
            const file = e.target.files[0];
            const fileInfo = document.getElementById('file_info');
            const fileName = document.getElementById('file_name');
            const fileSize = document.getElementById('file_size');

            if (file) {
                fileName.textContent = file.name;
                fileSize.textContent = `(${(file.size / 1024 / 1024).toFixed(2)} MB)`;
                fileInfo.classList.remove('hidden');
            } else {
                fileInfo.classList.add('hidden');
            }
        });

        // Theme toggle functionality
        const themeToggleBtn = document.getElementById('themeToggle');

        function getThemePreference() {
            return localStorage.getItem('color-theme') || 'light';
        }

        function setThemePreference(theme) {
            localStorage.setItem('color-theme', theme);
            if (theme === 'dark') {
                document.documentElement.classList.add('dark');
            } else {
                document.documentElement.classList.remove('dark');
            }
        }

        // Set initial theme
        setThemePreference(getThemePreference());

        // Toggle theme when button is clicked
        if (themeToggleBtn) {
            themeToggleBtn.addEventListener('click', function() {
                const currentTheme = getThemePreference();
                const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
                setThemePreference(newTheme);
            });
        }

        // Notifications button
        document.getElementById('notificationsBtn')?.addEventListener('click', function() {
            alert('Notifications feature coming soon!');
        });
    </script>

<?php
// Include the footer
require_once '../includes/footer.php';
?>
