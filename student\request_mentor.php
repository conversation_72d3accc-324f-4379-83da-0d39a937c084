<?php
require_once '../includes/db.php';
require_once '../includes/header.php';
require_once '../includes/gamify.php'; // Include the gamification logic

if (!isset($_SESSION['student_id'])) {
    header("Location: ../auth/student_login.php");
    exit;
}

$student_id = $_SESSION['student_id'];
$college_id = $_SESSION['college_id'];
$alumni_id = isset($_GET['alumni_id']) ? intval($_GET['alumni_id']) : 0;
$error = $success = '';

if ($alumni_id > 0) {
    // Fetch alumni details to confirm same college
    $stmt = $conn->prepare("SELECT full_name FROM alumni WHERE id = ? AND college_id = ? AND verified = 1");
    $stmt->execute([$alumni_id, $college_id]);
    $alumni = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$alumni) {
        $error = "Invalid alumni selected.";
    } elseif ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $message = trim($_POST['message']);
        if (!$message) {
            $error = "Please write a message explaining your request.";
        } else {
            // Check if already requested
            $check = $conn->prepare("SELECT id FROM student_requests WHERE student_id = ? AND alumni_id = ?");
            $check->execute([$student_id, $alumni_id]);
            if ($check->rowCount() > 0) {
                $error = "You have already sent a request to this alumni.";
            } else {
                // Insert the mentorship request
                $insert = $conn->prepare("INSERT INTO student_requests (student_id, alumni_id, message, status) VALUES (?, ?, ?, 'pending')");
                $insert->execute([$student_id, $alumni_id, $message]);
                
                // Award XP for the mentorship request
                awardXPForMentorshipRequest($conn, $student_id);

                $success = "Mentorship request sent successfully!";
            }
        }
    }
} else {
    $error = "No alumni selected.";
}
?>

<h2 class="mb-4">Request Mentorship</h2>

<?php if ($error): ?>
    <div class="alert alert-danger"><?= htmlspecialchars($error) ?></div>
<?php elseif ($success): ?>
    <div class="alert alert-success"><?= htmlspecialchars($success) ?></div>
    <a href="view_alumni.php" class="btn btn-secondary mt-2">Back to Alumni List</a>
<?php endif; ?>

<?php if (!$success && isset($alumni)): ?>
    <form method="post" class="card p-4 shadow">
        <div class="mb-3">
            <label class="form-label">To: <?= htmlspecialchars($alumni['full_name']) ?></label>
        </div>
        <div class="mb-3">
            <label class="form-label">Your Message</label>
            <textarea name="message" rows="5" class="form-control" required placeholder="Explain your goal or ask a question..."></textarea>
        </div>
        <button type="submit" class="btn btn-primary w-100">Send Request</button>
    </form>
<?php endif; ?>

<?php require_once '../includes/footer.php'; ?>