<?php
session_start();
require_once '../includes/db.php';
require_once '../includes/db_schema_check.php';

if (!isset($_SESSION['collegeadmin_id'])) {
    header("Location: ../auth/collegeadmin_login.php");
    exit;
}

$college_id = $_SESSION['college_id'];
$admin_name = $_SESSION['collegeadmin_name'] ?? 'Admin';
$error = '';
$success = '';

// Get college details
$collegeStmt = $conn->prepare("SELECT name FROM colleges WHERE id = ?");
$collegeStmt->execute([$college_id]);
$college = $collegeStmt->fetch(PDO::FETCH_ASSOC);

// Date range filter
$start_date = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-d', strtotime('-30 days'));
$end_date = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-d');

// Report type
$report_type = isset($_GET['report_type']) ? $_GET['report_type'] : 'overview';

// Student statistics
$studentStats = [];
try {
    // Total students
    $stmt = $conn->prepare("SELECT COUNT(*) FROM students WHERE college_id = ?");
    $stmt->execute([$college_id]);
    $studentStats['total'] = $stmt->fetchColumn();
    
    // Verified students
    $stmt = $conn->prepare("SELECT COUNT(*) FROM students WHERE college_id = ? AND verified = 1");
    $stmt->execute([$college_id]);
    $studentStats['verified'] = $stmt->fetchColumn();
    
    // Students by department
    $stmt = $conn->prepare("SELECT department, COUNT(*) as count FROM students 
                           WHERE college_id = ? GROUP BY department ORDER BY count DESC");
    $stmt->execute([$college_id]);
    $studentStats['by_department'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Students by year
    $stmt = $conn->prepare("SELECT year, COUNT(*) as count FROM students 
                           WHERE college_id = ? GROUP BY year ORDER BY year DESC");
    $stmt->execute([$college_id]);
    $studentStats['by_year'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // New students in date range
    $stmt = $conn->prepare("SELECT COUNT(*) FROM students 
                           WHERE college_id = ? AND created_at BETWEEN ? AND ?");
    $stmt->execute([$college_id, $start_date . ' 00:00:00', $end_date . ' 23:59:59']);
    $studentStats['new_in_range'] = $stmt->fetchColumn();
} catch (PDOException $e) {
    $error = "Error fetching student statistics: " . $e->getMessage();
}

// Alumni statistics
$alumniStats = [];
try {
    // Total alumni
    $stmt = $conn->prepare("SELECT COUNT(*) FROM alumni WHERE college_id = ?");
    $stmt->execute([$college_id]);
    $alumniStats['total'] = $stmt->fetchColumn();
    
    // Verified alumni
    $stmt = $conn->prepare("SELECT COUNT(*) FROM alumni WHERE college_id = ? AND verified = 1");
    $stmt->execute([$college_id]);
    $alumniStats['verified'] = $stmt->fetchColumn();
    
    // Alumni by industry
    $stmt = $conn->prepare("SELECT industry, COUNT(*) as count FROM alumni 
                           WHERE college_id = ? AND industry IS NOT NULL AND industry != '' 
                           GROUP BY industry ORDER BY count DESC LIMIT 10");
    $stmt->execute([$college_id]);
    $alumniStats['by_industry'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Alumni by graduation year
    $stmt = $conn->prepare("SELECT graduation_year, COUNT(*) as count FROM alumni 
                           WHERE college_id = ? AND graduation_year IS NOT NULL 
                           GROUP BY graduation_year ORDER BY graduation_year DESC");
    $stmt->execute([$college_id]);
    $alumniStats['by_graduation_year'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // New alumni in date range
    $stmt = $conn->prepare("SELECT COUNT(*) FROM alumni 
                           WHERE college_id = ? AND created_at BETWEEN ? AND ?");
    $stmt->execute([$college_id, $start_date . ' 00:00:00', $end_date . ' 23:59:59']);
    $alumniStats['new_in_range'] = $stmt->fetchColumn();
} catch (PDOException $e) {
    $error = "Error fetching alumni statistics: " . $e->getMessage();
}

// Mentorship statistics
$mentorshipStats = [];
try {
    // Check if mentorships table exists
    $tableExists = false;
    try {
        $checkTableStmt = $conn->prepare("SHOW TABLES LIKE 'mentorships'");
        $checkTableStmt->execute();
        $tableExists = $checkTableStmt->rowCount() > 0;
    } catch (PDOException $e) {
        // Table doesn't exist
    }
    
    if ($tableExists) {
        // Total mentorships
        $stmt = $conn->prepare("SELECT COUNT(*) FROM mentorships m 
                               JOIN students s ON m.student_id = s.id 
                               WHERE s.college_id = ?");
        $stmt->execute([$college_id]);
        $mentorshipStats['total'] = $stmt->fetchColumn();
        
        // Active mentorships
        $stmt = $conn->prepare("SELECT COUNT(*) FROM mentorships m 
                               JOIN students s ON m.student_id = s.id 
                               WHERE s.college_id = ? AND m.status = 'active'");
        $stmt->execute([$college_id]);
        $mentorshipStats['active'] = $stmt->fetchColumn();
        
        // Completed mentorships
        $stmt = $conn->prepare("SELECT COUNT(*) FROM mentorships m 
                               JOIN students s ON m.student_id = s.id 
                               WHERE s.college_id = ? AND m.status = 'completed'");
        $stmt->execute([$college_id]);
        $mentorshipStats['completed'] = $stmt->fetchColumn();
        
        // New mentorships in date range
        $stmt = $conn->prepare("SELECT COUNT(*) FROM mentorships m 
                               JOIN students s ON m.student_id = s.id 
                               WHERE s.college_id = ? AND m.created_at BETWEEN ? AND ?");
        $stmt->execute([$college_id, $start_date . ' 00:00:00', $end_date . ' 23:59:59']);
        $mentorshipStats['new_in_range'] = $stmt->fetchColumn();
    } else {
        $mentorshipStats['total'] = 0;
        $mentorshipStats['active'] = 0;
        $mentorshipStats['completed'] = 0;
        $mentorshipStats['new_in_range'] = 0;
    }
} catch (PDOException $e) {
    $error = "Error fetching mentorship statistics: " . $e->getMessage();
}

// Events statistics
$eventStats = [];
try {
    // Check if events table exists
    $tableExists = false;
    try {
        $checkTableStmt = $conn->prepare("SHOW TABLES LIKE 'events'");
        $checkTableStmt->execute();
        $tableExists = $checkTableStmt->rowCount() > 0;
    } catch (PDOException $e) {
        // Table doesn't exist
    }
    
    if ($tableExists) {
        // Total events
        $stmt = $conn->prepare("SELECT COUNT(*) FROM events WHERE college_id = ?");
        $stmt->execute([$college_id]);
        $eventStats['total'] = $stmt->fetchColumn();
        
        // Upcoming events
        $stmt = $conn->prepare("SELECT COUNT(*) FROM events 
                               WHERE college_id = ? AND event_date >= CURDATE()");
        $stmt->execute([$college_id]);
        $eventStats['upcoming'] = $stmt->fetchColumn();
        
        // Past events
        $stmt = $conn->prepare("SELECT COUNT(*) FROM events 
                               WHERE college_id = ? AND event_date < CURDATE()");
        $stmt->execute([$college_id]);
        $eventStats['past'] = $stmt->fetchColumn();
        
        // Events in date range
        $stmt = $conn->prepare("SELECT COUNT(*) FROM events 
                               WHERE college_id = ? AND event_date BETWEEN ? AND ?");
        $stmt->execute([$college_id, $start_date, $end_date]);
        $eventStats['in_range'] = $stmt->fetchColumn();
    } else {
        $eventStats['total'] = 0;
        $eventStats['upcoming'] = 0;
        $eventStats['past'] = 0;
        $eventStats['in_range'] = 0;
    }
} catch (PDOException $e) {
    $error = "Error fetching event statistics: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reports & Analytics - Mentoshri</title>
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts - Inter -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        'primary': '#0a66c2',
                        'primary-dark': '#004182',
                        'primary-light': '#e7f3ff',
                    },
                    fontFamily: {
                        'sans': ['Inter', 'sans-serif'],
                    },
                }
            }
        }
    </script>
    <!-- Design System -->
    <link rel="stylesheet" href="../assets/css/design-system.css">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="font-sans bg-gray-50 dark:bg-gray-900 min-h-screen flex flex-col">
    <!-- Header -->
    <header class="bg-white dark:bg-gray-800 shadow-sm">
        <div class="container mx-auto px-4 py-3 flex items-center justify-between">
            <div class="flex items-center">
                <a href="collegeadmin_dashboard.php" class="text-primary font-bold text-2xl">Mentoshri</a>
            </div>
            
            <div class="flex items-center space-x-4">
                <button id="themeToggle" class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700">
                    <i class="fas fa-moon dark:hidden"></i>
                    <i class="fas fa-sun hidden dark:block"></i>
                </button>
                <div class="relative">
                    <button id="notificationsBtn" class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 relative">
                        <i class="fas fa-bell"></i>
                        <span class="absolute top-0 right-0 h-4 w-4 bg-red-500 rounded-full text-xs text-white flex items-center justify-center">2</span>
                    </button>
                </div>
                <div class="relative group">
                    <button class="flex items-center space-x-1">
                        <div class="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center text-blue-600 dark:text-blue-400">
                            <i class="fas fa-user-shield"></i>
                        </div>
                        <span class="text-gray-700 dark:text-gray-300"><?= htmlspecialchars($admin_name) ?></span>
                        <i class="fas fa-chevron-down text-gray-400 text-xs"></i>
                    </button>
                    <div class="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg border border-gray-200 dark:border-gray-700 hidden group-hover:block z-10">
                        <div class="py-1">
                            <a href="collegeadmin_profile.php" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                <i class="fas fa-user mr-2"></i> Profile
                            </a>
                            <a href="collegeadmin_settings.php" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                <i class="fas fa-cog mr-2"></i> Settings
                            </a>
                            <div class="border-t border-gray-200 dark:border-gray-700"></div>
                            <a href="../auth/logout.php" class="block px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-700">
                                <i class="fas fa-sign-out-alt mr-2"></i> Logout
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="flex-grow py-8 px-4">
        <div class="container mx-auto">
            <!-- Breadcrumb -->
            <nav class="mb-6 text-sm" aria-label="Breadcrumb">
                <ol class="flex items-center space-x-2">
                    <li>
                        <a href="collegeadmin_dashboard.php" class="text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary-light">
                            <i class="fas fa-home"></i>
                            <span class="sr-only">Dashboard</span>
                        </a>
                    </li>
                    <li class="flex items-center">
                        <i class="fas fa-chevron-right text-gray-400 text-xs mx-1"></i>
                        <span class="text-gray-700 dark:text-gray-300">Reports & Analytics</span>
                    </li>
                </ol>
            </nav>

            <div class="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                        <i class="fas fa-chart-line text-primary mr-3"></i>
                        Reports & Analytics
                    </h1>
                    <p class="text-gray-600 dark:text-gray-400 mt-1">
                        View insights and statistics for <?= htmlspecialchars($college['name'] ?? 'your college') ?>
                    </p>
                </div>
                <div class="mt-4 md:mt-0">
                    <button id="exportReportBtn" class="btn btn-outline flex items-center">
                        <i class="fas fa-file-export mr-2"></i> Export Report
                    </button>
                </div>
            </div>
            
            <?php if ($error): ?>
                <div class="bg-red-50 dark:bg-red-900/30 border-l-4 border-red-500 text-red-700 dark:text-red-400 p-4 mb-6 flex justify-between items-center" role="alert">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-circle mr-2"></i>
                        <span><?= htmlspecialchars($error) ?></span>
                    </div>
                    <button type="button" class="text-red-700 dark:text-red-400 hover:opacity-75" onclick="this.parentElement.style.display='none';">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="bg-green-50 dark:bg-green-900/30 border-l-4 border-green-500 text-green-700 dark:text-green-400 p-4 mb-6 flex justify-between items-center" role="alert">
                    <div class="flex items-center">
                        <i class="fas fa-check-circle mr-2"></i>
                        <span><?= htmlspecialchars($success) ?></span>
                    </div>
                    <button type="button" class="text-green-700 dark:text-green-400 hover:opacity-75" onclick="this.parentElement.style.display='none';">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            <?php endif; ?> 
            
            <!-- Date Range Filter -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 mb-6">
                <div class="p-6">
                    <h2 class="text-lg font-semibold text-gray-800 dark:text-white mb-4 flex items-center">
                        <i class="fas fa-filter text-primary mr-2"></i> Report Filters
                    </h2>
                    <form method="GET" action="" class="space-y-4">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div>
                                <label for="report_type" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Report Type</label>
                                <select id="report_type" name="report_type" class="form-select">
                                    <option value="overview" <?= $report_type === 'overview' ? 'selected' : '' ?>>Overview</option>
                                    <option value="students" <?= $report_type === 'students' ? 'selected' : '' ?>>Students</option>
                                    <option value="alumni" <?= $report_type === 'alumni' ? 'selected' : '' ?>>Alumni</option>
                                    <option value="mentorships" <?= $report_type === 'mentorships' ? 'selected' : '' ?>>Mentorships</option>
                                    <option value="events" <?= $report_type === 'events' ? 'selected' : '' ?>>Events</option>
                                </select>
                            </div>
                            
                            <div>
                                <label for="start_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Start Date</label>
                                <input type="date" id="start_date" name="start_date" value="<?= htmlspecialchars($start_date) ?>" class="form-input">
                            </div>
                            
                            <div>
                                <label for="end_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">End Date</label>
                                <input type="date" id="end_date" name="end_date" value="<?= htmlspecialchars($end_date) ?>" class="form-input">
                            </div>
                        </div>
                        
                        <div class="flex justify-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search mr-2"></i> Generate Report
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Report Tabs -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 mb-6">
                <div class="border-b border-gray-200 dark:border-gray-700">
                    <nav class="flex -mb-px overflow-x-auto">
                        <button type="button" class="tab-btn <?= $report_type === 'overview' ? 'active' : '' ?>" data-tab="overview">
                            <i class="fas fa-th-large mr-2"></i> Overview
                        </button>
                        <button type="button" class="tab-btn <?= $report_type === 'students' ? 'active' : '' ?>" data-tab="students">
                            <i class="fas fa-user-graduate mr-2"></i> Students
                        </button>
                        <button type="button" class="tab-btn <?= $report_type === 'alumni' ? 'active' : '' ?>" data-tab="alumni">
                            <i class="fas fa-user-tie mr-2"></i> Alumni
                        </button>
                        <button type="button" class="tab-btn <?= $report_type === 'mentorships' ? 'active' : '' ?>" data-tab="mentorships">
                            <i class="fas fa-hands-helping mr-2"></i> Mentorships
                        </button>
                        <button type="button" class="tab-btn <?= $report_type === 'events' ? 'active' : '' ?>" data-tab="events">
                            <i class="fas fa-calendar-alt mr-2"></i> Events
                        </button>
                    </nav>
                </div>
                
                <!-- Overview Tab Content -->
                <div id="overview-tab" class="tab-content <?= $report_type === 'overview' ? 'block' : 'hidden' ?>">
                    <div class="p-6">
                        <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4">College Overview</h3>
                        
                        <!-- Stats Cards -->
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
                                <div class="flex items-center">
                                    <div class="p-3 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 mr-4">
                                        <i class="fas fa-user-graduate"></i>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Students</p>
                                        <p class="text-xl font-semibold text-gray-800 dark:text-white"><?= $studentStats['total'] ?></p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
                                <div class="flex items-center">
                                    <div class="p-3 rounded-full bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 mr-4">
                                        <i class="fas fa-user-tie"></i>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Alumni</p>
                                        <p class="text-xl font-semibold text-gray-800 dark:text-white"><?= $alumniStats['total'] ?></p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
                                <div class="flex items-center">
                                    <div class="p-3 rounded-full bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400 mr-4">
                                        <i class="fas fa-handshake"></i>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Active Mentorships</p>
                                        <p class="text-xl font-semibold text-gray-800 dark:text-white"><?= $mentorshipStats['active'] ?></p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
                                <div class="flex items-center">
                                    <div class="p-3 rounded-full bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400 mr-4">
                                        <i class="fas fa-calendar-alt"></i>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Upcoming Events</p>
                                        <p class="text-xl font-semibold text-gray-800 dark:text-white"><?= $eventStats['upcoming'] ?></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Charts Row -->
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
                                <h4 class="text-md font-semibold text-gray-800 dark:text-white mb-4">Students by Department</h4>
                                <div class="h-80">
                                    <canvas id="studentsByDepartmentChart"></canvas>
                                </div>
                            </div>
                            
                            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
                                <h4 class="text-md font-semibold text-gray-800 dark:text-white mb-4">Alumni by Industry</h4>
                                <div class="h-80">
                                    <canvas id="alumniByIndustryChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Students Tab Content -->
                <div id="students-tab" class="tab-content <?= $report_type === 'students' ? 'block' : 'hidden' ?>">
                    <div class="p-6">
                        <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4">Student Statistics</h3>
                        
                        <!-- Student Stats Cards -->
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
                                <div class="flex items-center">
                                    <div class="p-3 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 mr-4">
                                        <i class="fas fa-users"></i>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Students</p>
                                        <p class="text-xl font-semibold text-gray-800 dark:text-white"><?= $studentStats['total'] ?></p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
                                <div class="flex items-center">
                                    <div class="p-3 rounded-full bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 mr-4">
                                        <i class="fas fa-check-circle"></i>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Verified Students</p>
                                        <p class="text-xl font-semibold text-gray-800 dark:text-white"><?= $studentStats['verified'] ?></p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
                                <div class="flex items-center">
                                    <div class="p-3 rounded-full bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400 mr-4">
                                        <i class="fas fa-clock"></i>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Unverified Students</p>
                                        <p class="text-xl font-semibold text-gray-800 dark:text-white"><?= $studentStats['total'] - $studentStats['verified'] ?></p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
                                <div class="flex items-center">
                                    <div class="p-3 rounded-full bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400 mr-4">
                                        <i class="fas fa-user-plus"></i>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">New Students</p>
                                        <p class="text-xl font-semibold text-gray-800 dark:text-white"><?= $studentStats['new_in_range'] ?></p>
                                        <p class="text-xs text-gray-500 dark:text-gray-400">In selected date range</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Student Charts -->
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
                                <h4 class="text-md font-semibold text-gray-800 dark:text-white mb-4">Students by Department</h4>
                                <div class="h-80">
                                    <canvas id="studentsByDepartmentChartDetailed"></canvas>
                                </div>
                            </div>
                            
                            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
                                <h4 class="text-md font-semibold text-gray-800 dark:text-white mb-4">Students by Year</h4>
                                <div class="h-80">
                                    <canvas id="studentsByYearChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Alumni Tab Content -->
                <div id="alumni-tab" class="tab-content <?= $report_type === 'alumni' ? 'block' : 'hidden' ?>">
                    <div class="p-6">
                        <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4">Alumni Statistics</h3>
                        
                        <!-- Alumni Stats Cards -->
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
                                <div class="flex items-center">
                                    <div class="p-3 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 mr-4">
                                        <i class="fas fa-users"></i>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Alumni</p>
                                        <p class="text-xl font-semibold text-gray-800 dark:text-white"><?= $alumniStats['total'] ?></p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
                                <div class="flex items-center">
                                    <div class="p-3 rounded-full bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 mr-4">
                                        <i class="fas fa-check-circle"></i>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Verified Alumni</p>
                                        <p class="text-xl font-semibold text-gray-800 dark:text-white"><?= $alumniStats['verified'] ?></p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
                                <div class="flex items-center">
                                    <div class="p-3 rounded-full bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400 mr-4">
                                        <i class="fas fa-clock"></i>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Unverified Alumni</p>
                                        <p class="text-xl font-semibold text-gray-800 dark:text-white"><?= $alumniStats['total'] - $alumniStats['verified'] ?></p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
                                <div class="flex items-center">
                                    <div class="p-3 rounded-full bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400 mr-4">
                                        <i class="fas fa-industry"></i>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Alumni by Industry</p>
                                        <div class="flex flex-wrap gap-2">
                                            <?php foreach ($alumniStats['by_industry'] as $industry): ?>
                                                <span class="bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 text-xs font-medium px-2.5 py-0.5 rounded-full">
                                                    <?= htmlspecialchars($industry['industry']) ?> (<?= $industry['count'] ?>)
                                                </span>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Alumni Charts -->
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
                                <h4 class="text-md font-semibold text-gray-800 dark:text-white mb-4">Alumni by Graduation Year</h4>
                                <div class="h-80">
                                    <canvas id="alumniByGraduationYearChart"></canvas>
                                </div>
                            </div>
                            
                            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
                                <h4 class="text-md font-semibold text-gray-800 dark:text-white mb-4">New Alumni</h4>
                                <div class="h-80">
                                    <canvas id="newAlumniChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Mentorships Tab Content -->
                <div id="mentorships-tab" class="tab-content <?= $report_type === 'mentorships' ? 'block' : 'hidden' ?>">
                    <div class="p-6">
                        <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4">Mentorship Statistics</h3>
                        
                        <!-- Mentorship Stats Cards -->
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
                                <div class="flex items-center">
                                    <div class="p-3 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 mr-4">
                                        <i class="fas fa-hands-helping"></i>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Mentorships</p>
                                        <p class="text-xl font-semibold text-gray-800 dark:text-white"><?= $mentorshipStats['total'] ?></p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
                                <div class="flex items-center">
                                    <div class="p-3 rounded-full bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 mr-4">
                                        <i class="fas fa-check-circle"></i>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Active Mentorships</p>
                                        <p class="text-xl font-semibold text-gray-800 dark:text-white"><?= $mentorshipStats['active'] ?></p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
                                <div class="flex items-center">
                                    <div class="p-3 rounded-full bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400 mr-4">
                                        <i class="fas fa-calendar-alt"></i>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Completed Mentorships</p>
                                        <p class="text-xl font-semibold text-gray-800 dark:text-white"><?= $mentorshipStats['completed'] ?></p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
                                <div class="flex items-center">
                                    <div class="p-3 rounded-full bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400 mr-4">
                                        <i class="fas fa-user-plus"></i>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">New Mentorships</p>
                                        <p class="text-xl font-semibold text-gray-800 dark:text-white"><?= $mentorshipStats['new_in_range'] ?></p>
                                        <p class="text-xs text-gray-500 dark:text-gray-400">In selected date range</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Mentorship Charts -->
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
                                <h4 class="text-md font-semibold text-gray-800 dark:text-white mb-4">Mentorships by Status</h4>
                                <div class="h-80">
                                    <canvas id="mentorshipsByStatusChart"></canvas>
                                </div>
                            </div>
                            
                            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
                                <h4 class="text-md font-semibold text-gray-800 dark:text-white mb-4">Mentorships by Date Range</h4>
                                <div class="h-80">
                                    <canvas id="mentorshipsByDateRangeChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Events Tab Content -->
                <div id="events-tab" class="tab-content <?= $report_type === 'events' ? 'block' : 'hidden' ?>">
                    <div class="p-6">
                        <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4">Event Statistics</h3>
                        
                        <!-- Event Stats Cards -->
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
                                <div class="flex items-center">
                                    <div class="p-3 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 mr-4">
                                        <i class="fas fa-calendar-alt"></i>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Events</p>
                                        <p class="text-xl font-semibold text-gray-800 dark:text-white"><?= $eventStats['total'] ?></p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
                                <div class="flex items-center">
                                    <div class="p-3 rounded-full bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 mr-4">
                                        <i class="fas fa-check-circle"></i>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Upcoming Events</p>
                                        <p class="text-xl font-semibold text-gray-800 dark:text-white"><?= $eventStats['upcoming'] ?></p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
                                <div class="flex items-center">
                                    <div class="p-3 rounded-full bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400 mr-4">
                                        <i class="fas fa-calendar-times"></i>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Past Events</p>
                                        <p class="text-xl font-semibold text-gray-800 dark:text-white"><?= $eventStats['past'] ?></p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
                                <div class="flex items-center">
                                    <div class="p-3 rounded-full bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400 mr-4">
                                        <i class="fas fa-user-plus"></i>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Events in Date Range</p>
                                        <p class="text-xl font-semibold text-gray-800 dark:text-white"><?= $eventStats['in_range'] ?></p>
                                        <p class="text-xs text-gray-500 dark:text-gray-400">In selected date range</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Event Charts -->
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
                                <h4 class="text-md font-semibold text-gray-800 dark:text-white mb-4">Events by Date</h4>
                                <div class="h-80">
                                    <canvas id="eventsByDateChart"></canvas>
                                </div>
                            </div>
                            
                            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
                                <h4 class="text-md font-semibold text-gray-800 dark:text-white mb-4">Events by Type</h4>
                                <div class="h-80">
                                    <canvas id="eventsByTypeChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const reportTypeSelect = document.getElementById('report_type');
            const startDateInput = document.getElementById('start_date');
            const endDateInput = document.getElementById('end_date');
            const tabButtons = document.querySelectorAll('.tab-btn');
            const tabContents = document.querySelectorAll('.tab-content');

            function showTab(tabId) {
                tabButtons.forEach(button => {
                    button.classList.remove('active');
                });
                tabContents.forEach(content => {
                    content.classList.add('hidden');
                });
                document.getElementById(tabId + '-tab').classList.remove('hidden');
                document.querySelector(`.tab-btn[data-tab="${tabId}"]`).classList.add('active');
            }

            reportTypeSelect.addEventListener('change', function() {
                showTab(this.value);
            });

            // Initial call to show the correct tab based on URL parameters
            const initialReportType = reportTypeSelect.value;
            showTab(initialReportType);

            // Date range filter
            const dateRangeForm = document.querySelector('.tab-content.block form');
            if (dateRangeForm) {
                dateRangeForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    const startDate = startDateInput.value;
                    const endDate = endDateInput.value;
                    const reportType = reportTypeSelect.value;
                    const currentUrl = new URL(window.location.href);
                    currentUrl.searchParams.set('report_type', reportType);
                    currentUrl.searchParams.set('start_date', startDate);
                    currentUrl.searchParams.set('end_date', endDate);
                    window.location.href = currentUrl.toString();
                });
            }

            // Chart.js initialization
            const studentsByDepartmentChart = new Chart(document.getElementById('studentsByDepartmentChart'), {
                type: 'bar',
                data: {
                    labels: <?= json_encode(array_column($studentStats['by_department'], 'department')) ?>,
                    datasets: [{
                        label: 'Number of Students',
                        data: <?= json_encode(array_column($studentStats['by_department'], 'count')) ?>,
                        backgroundColor: 'rgba(54, 162, 235, 0.5)',
                        borderColor: 'rgba(54, 162, 235, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Number of Students'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Department'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top',
                        },
                        title: {
                            display: true,
                            text: 'Students by Department'
                        }
                    }
                }
            });

            const alumniByIndustryChart = new Chart(document.getElementById('alumniByIndustryChart'), {
                type: 'bar',
                data: {
                    labels: <?= json_encode(array_column($alumniStats['by_industry'], 'industry')) ?>,
                    datasets: [{
                        label: 'Number of Alumni',
                        data: <?= json_encode(array_column($alumniStats['by_industry'], 'count')) ?>,
                        backgroundColor: 'rgba(75, 192, 192, 0.5)',
                        borderColor: 'rgba(75, 192, 192, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Number of Alumni'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Industry'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top',
                        },
                        title: {
                            display: true,
                            text: 'Alumni by Industry'
                        }
                    }
                }
            });

            const studentsByDepartmentChartDetailed = new Chart(document.getElementById('studentsByDepartmentChartDetailed'), {
                type: 'bar',
                data: {
                    labels: <?= json_encode(array_column($studentStats['by_department'], 'department')) ?>,
                    datasets: [{
                        label: 'Number of Students',
                        data: <?= json_encode(array_column($studentStats['by_department'], 'count')) ?>,
                        backgroundColor: 'rgba(54, 162, 235, 0.5)',
                        borderColor: 'rgba(54, 162, 235, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Number of Students'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Department'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top',
                        },
                        title: {
                            display: true,
                            text: 'Students by Department'
                        }
                    }
                }
            });

            const studentsByYearChart = new Chart(document.getElementById('studentsByYearChart'), {
                type: 'bar',
                data: {
                    labels: <?= json_encode(array_column($studentStats['by_year'], 'year')) ?>,
                    datasets: [{
                        label: 'Number of Students',
                        data: <?= json_encode(array_column($studentStats['by_year'], 'count')) ?>,
                        backgroundColor: 'rgba(153, 102, 255, 0.5)',
                        borderColor: 'rgba(153, 102, 255, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Number of Students'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Year'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top',
                        },
                        title: {
                            display: true,
                            text: 'Students by Year'
                        }
                    }
                }
            });

            const alumniByGraduationYearChart = new Chart(document.getElementById('alumniByGraduationYearChart'), {
                type: 'bar',
                data: {
                    labels: <?= json_encode(array_column($alumniStats['by_graduation_year'], 'graduation_year')) ?>,
                    datasets: [{
                        label: 'Number of Alumni',
                        data: <?= json_encode(array_column($alumniStats['by_graduation_year'], 'count')) ?>,
                        backgroundColor: 'rgba(255, 99, 132, 0.5)',
                        borderColor: 'rgba(255, 99, 132, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Number of Alumni'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Graduation Year'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top',
                        },
                        title: {
                            display: true,
                            text: 'Alumni by Graduation Year'
                        }
                    }
                }
            });

            const newAlumniChart = new Chart(document.getElementById('newAlumniChart'), {
                type: 'bar',
                data: {
                    labels: ['New Alumni'],
                    datasets: [{
                        label: 'Number of New Alumni',
                        data: [<?= $alumniStats['new_in_range'] ?>],
                        backgroundColor: 'rgba(153, 102, 255, 0.5)',
                        borderColor: 'rgba(153, 102, 255, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Number of Alumni'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Type'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top',
                        },
                        title: {
                            display: true,
                            text: 'New Alumni'
                        }
                    }
                }
            });

            const mentorshipsByStatusChart = new Chart(document.getElementById('mentorshipsByStatusChart'), {
                type: 'doughnut',
                data: {
                    labels: ['Active', 'Completed'],
                    datasets: [{
                        data: [<?= $mentorshipStats['active'] ?>, <?= $mentorshipStats['completed'] ?>],
                        backgroundColor: ['rgba(75, 192, 192, 0.5)', 'rgba(255, 99, 132, 0.5)'],
                        borderColor: ['rgba(75, 192, 192, 1)', 'rgba(255, 99, 132, 1)'],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top',
                        },
                        title: {
                            display: true,
                            text: 'Mentorships by Status'
                        }
                    }
                }
            });

            const mentorshipsByDateRangeChart = new Chart(document.getElementById('mentorshipsByDateRangeChart'), {
                type: 'bar',
                data: {
                    labels: ['New Mentorships'],
                    datasets: [{
                        label: 'Number of New Mentorships',
                        data: [<?= $mentorshipStats['new_in_range'] ?>],
                        backgroundColor: 'rgba(153, 102, 255, 0.5)',
                        borderColor: 'rgba(153, 102, 255, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Number of Mentorships'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Type'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top',
                        },
                        title: {
                            display: true,
                            text: 'Mentorships by Date Range'
                        }
                    }
                }
            });

            const eventsByDateChart = new Chart(document.getElementById('eventsByDateChart'), {
                type: 'line',
                data: {
                    labels: <?= json_encode(array_column($eventStats['by_date'], 'event_date')) ?>,
                    datasets: [{
                        label: 'Number of Events',
                        data: <?= json_encode(array_column($eventStats['by_date'], 'count')) ?>,
                        borderColor: 'rgba(153, 102, 255, 1)',
                        backgroundColor: 'rgba(153, 102, 255, 0.2)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Number of Events'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Date'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top',
                        },
                        title: {
                            display: true,
                            text: 'Events by Date'
                        }
                    }
                }
            });

            const eventsByTypeChart = new Chart(document.getElementById('eventsByTypeChart'), {
                type: 'doughnut',
                data: {
                    labels: <?= json_encode(array_column($eventStats['by_type'], 'type')) ?>,
                    datasets: [{
                        data: <?= json_encode(array_column($eventStats['by_type'], 'count')) ?>,
                        backgroundColor: ['rgba(75, 192, 192, 0.5)', 'rgba(255, 99, 132, 0.5)', 'rgba(54, 162, 235, 0.5)', 'rgba(153, 102, 255, 0.5)'],
                        borderColor: ['rgba(75, 192, 192, 1)', 'rgba(255, 99, 132, 1)', 'rgba(54, 162, 235, 1)', 'rgba(153, 102, 255, 1)'],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top',
                        },
                        title: {
                            display: true,
                            text: 'Events by Type'
                        }
                    }
                }
            });

            // Theme toggle
            const themeToggleBtn = document.getElementById('themeToggle');
            const htmlElement = document.documentElement;
            const isDarkMode = htmlElement.classList.contains('dark');
            const moonIcon = themeToggleBtn.querySelector('i.fas.fa-moon');
            const sunIcon = themeToggleBtn.querySelector('i.fas.fa-sun');

            if (isDarkMode) {
                moonIcon.classList.add('hidden');
                sunIcon.classList.remove('hidden');
            } else {
                moonIcon.classList.remove('hidden');
                sunIcon.classList.add('hidden');
            }

            themeToggleBtn.addEventListener('click', function() {
                htmlElement.classList.toggle('dark');
                if (isDarkMode) {
                    moonIcon.classList.remove('hidden');
                    sunIcon.classList.add('hidden');
                } else {
                    moonIcon.classList.add('hidden');
                    sunIcon.classList.remove('hidden');
                }
            });

            // Notifications (placeholder)
            const notificationsBtn = document.getElementById('notificationsBtn');
            notificationsBtn.addEventListener('click', function() {
                alert('Notifications functionality not yet implemented.');
            });

            // Export report (placeholder)
            const exportReportBtn = document.getElementById('exportReportBtn');
            exportReportBtn.addEventListener('click', function() {
                alert('Export report functionality not yet implemented.');
            });
        });
    </script>
</body>
</html> 