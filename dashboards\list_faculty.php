<?php
// Include session configuration
require_once '../includes/session_config.php';
require_once '../includes/db.php';
require_once '../includes/db_schema_check.php';

if (!isset($_SESSION['collegeadmin_id'])) {
    header("Location: ../auth/collegeadmin_login.php");
    exit;
}

$college_id = $_SESSION['college_id'];
$admin_name = $_SESSION['collegeadmin_name'];

// Get college details
$collegeStmt = $conn->prepare("SELECT name FROM colleges WHERE id = ?");
$collegeStmt->execute([$college_id]);
$college = $collegeStmt->fetch(PDO::FETCH_ASSOC);

// Set role explicitly for the sidebar
$role = 'collegeadmin';
$user_id = $_SESSION['collegeadmin_id'];
$user_name = $_SESSION['collegeadmin_name'];
$current_page = basename($_SERVER['PHP_SELF']);

// Check and update faculties table schema
$schemaUpdates = checkAndUpdateFacultiesSchema($conn);
$schemaUpdateMessages = [];
if (!empty($schemaUpdates) && !isset($schemaUpdates['error'])) {
    $schemaUpdateMessages = $schemaUpdates;
}

// Pagination setup
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 10; // Items per page
$offset = ($page - 1) * $limit;

// Search functionality
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$searchCondition = '';
$params = [$college_id];

if (!empty($search)) {
    $searchCondition = " AND (name LIKE ? OR email LIKE ? OR department LIKE ?)";
    $searchParam = "%$search%";
    $params = array_merge($params, [$searchParam, $searchParam, $searchParam]);
}

// Department filter
$department_filter = isset($_GET['department']) ? trim($_GET['department']) : '';
if (!empty($department_filter)) {
    $searchCondition .= " AND department = ?";
    $params[] = $department_filter;
}

// Status filter
$status_filter = isset($_GET['status']) ? trim($_GET['status']) : '';
if (!empty($status_filter)) {
    $searchCondition .= " AND status = ?";
    $params[] = $status_filter;
}

// Count total faculty for pagination
$countSql = "SELECT COUNT(*) FROM faculties WHERE college_id = ?" . $searchCondition;
$countStmt = $conn->prepare($countSql);
$countStmt->execute($params);
$totalFaculty = $countStmt->fetchColumn();
$totalPages = ceil($totalFaculty / $limit);

// Fetch faculty with pagination and search
$sql = "SELECT id, college_id, name, email, password, department, profile_photo, bio, created_at FROM faculties WHERE college_id = ?" . $searchCondition . " ORDER BY created_at DESC LIMIT $limit OFFSET $offset";
$stmt = $conn->prepare($sql);
$stmt->execute($params);
$faculty_list = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Try to get additional columns if they exist
foreach ($faculty_list as &$faculty) {
    try {
        $extraStmt = $conn->prepare("SELECT position, phone, status, profile_image, updated_at FROM faculties WHERE id = ? LIMIT 1");
        $extraStmt->execute([$faculty['id']]);
        $extraData = $extraStmt->fetch(PDO::FETCH_ASSOC);
        if ($extraData) {
            $faculty = array_merge($faculty, $extraData);
        }
    } catch (PDOException $e) {
        // Some columns don't exist, set defaults
        $faculty['position'] = $faculty['position'] ?? null;
        $faculty['phone'] = $faculty['phone'] ?? null;
        $faculty['status'] = $faculty['status'] ?? 'active';
        $faculty['profile_image'] = $faculty['profile_image'] ?? null;
        $faculty['updated_at'] = $faculty['updated_at'] ?? null;
    }
}

// Get departments for filter
$deptStmt = $conn->prepare("SELECT DISTINCT department FROM faculties WHERE college_id = ? ORDER BY department ASC");
$deptStmt->execute([$college_id]);
$departments = $deptStmt->fetchAll(PDO::FETCH_COLUMN);

// Include the header
require_once '../includes/header.php';
?>

    <!-- Main Content -->
    <main class="flex-grow py-6 px-4">
        <div class="max-w-7xl mx-auto">
            <!-- Page Header -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8">
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="bg-gradient-to-br from-blue-500 to-blue-600 p-3 rounded-xl shadow-lg">
                            <i class="fas fa-chalkboard-teacher text-white text-xl"></i>
                        </div>
                        <div>
                            <h1 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                                Faculty Management
                                <span class="ml-3 px-3 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 text-sm font-medium rounded-full">
                                    <?= $totalFaculty ?> Members
                                </span>
                            </h1>
                            <p class="text-gray-600 dark:text-gray-400 mt-1">
                                Manage faculty members at <span class="font-medium text-blue-600 dark:text-blue-400"><?= htmlspecialchars($college['name'] ?? 'your college') ?></span>
                                <span class="mx-2">•</span>
                                <span class="text-sm"><?= date('l, F j, Y') ?></span>
                            </p>
                        </div>
                    </div>
                    <div class="mt-6 lg:mt-0 flex flex-wrap gap-3">
                        <a href="add_faculty.php" class="btn btn-primary flex items-center text-sm shadow-lg hover:shadow-xl transition-all duration-200">
                            <i class="fas fa-user-plus mr-2"></i> Add New Faculty
                        </a>
                        <a href="import_faculty.php" class="btn btn-outline flex items-center text-sm hover:shadow-md transition-all duration-200">
                            <i class="fas fa-file-import mr-2"></i> Import Faculty
                        </a>
                        <div class="relative group">
                            <button class="btn btn-outline flex items-center text-sm hover:shadow-md transition-all duration-200">
                                <i class="fas fa-file-export mr-2"></i> Export Faculty
                                <i class="fas fa-chevron-down ml-2 text-xs"></i>
                            </button>
                            <div class="absolute right-0 mt-2 w-56 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 hidden group-hover:block z-10">
                                <div class="py-2">
                                    <a href="export_faculty.php" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                        <i class="fas fa-cog mr-2"></i> Configure Export
                                    </a>
                                    <button onclick="exportWithCurrentFilters()" class="w-full text-left block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                        <i class="fas fa-download mr-2"></i> Quick Export (Current Filters)
                                    </button>
                                    <div class="border-t border-gray-200 dark:border-gray-700 my-1"></div>
                                    <a href="export_faculty.php?export=csv&department=all&status=active&fields[]=name&fields[]=email&fields[]=department&fields[]=position&fields[]=status" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                        <i class="fas fa-users mr-2"></i> Export Active Faculty
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Stats -->
                <div class="mt-6 grid grid-cols-2 lg:grid-cols-4 gap-6">
                    <!-- Total Faculty -->
                    <div class="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/30 p-6 rounded-xl border border-blue-200 dark:border-blue-700/50 hover:shadow-lg transition-all duration-200">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-semibold text-blue-700 dark:text-blue-300 uppercase tracking-wide">Total Faculty</p>
                                <p class="text-4xl font-black text-blue-900 dark:text-blue-100 mt-2 leading-none"><?= $totalFaculty ?></p>
                                <p class="text-xs text-blue-600 dark:text-blue-400 mt-1 font-medium">Members</p>
                            </div>
                            <div class="bg-blue-600 dark:bg-blue-500 p-4 rounded-xl shadow-lg">
                                <i class="fas fa-users text-white text-2xl"></i>
                            </div>
                        </div>
                    </div>

                    <!-- Active Faculty -->
                    <div class="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/30 p-6 rounded-xl border border-green-200 dark:border-green-700/50 hover:shadow-lg transition-all duration-200">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-semibold text-green-700 dark:text-green-300 uppercase tracking-wide">Active</p>
                                <p class="text-4xl font-black text-green-900 dark:text-green-100 mt-2 leading-none">
                                    <?= count(array_filter($faculty_list, function($f) { return ($f['status'] ?? 'active') === 'active'; })) ?>
                                </p>
                                <p class="text-xs text-green-600 dark:text-green-400 mt-1 font-medium">Online</p>
                            </div>
                            <div class="bg-green-600 dark:bg-green-500 p-4 rounded-xl shadow-lg">
                                <i class="fas fa-user-check text-white text-2xl"></i>
                            </div>
                        </div>
                    </div>

                    <!-- Departments -->
                    <div class="bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/30 p-6 rounded-xl border border-orange-200 dark:border-orange-700/50 hover:shadow-lg transition-all duration-200">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-semibold text-orange-700 dark:text-orange-300 uppercase tracking-wide">Departments</p>
                                <p class="text-4xl font-black text-orange-900 dark:text-orange-100 mt-2 leading-none">
                                    <?= count(array_unique(array_filter(array_column($faculty_list, 'department')))) ?>
                                </p>
                                <p class="text-xs text-orange-600 dark:text-orange-400 mt-1 font-medium">Active</p>
                            </div>
                            <div class="bg-orange-600 dark:bg-orange-500 p-4 rounded-xl shadow-lg">
                                <i class="fas fa-building text-white text-2xl"></i>
                            </div>
                        </div>
                    </div>

                    <!-- This Month -->
                    <div class="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/30 p-6 rounded-xl border border-purple-200 dark:border-purple-700/50 hover:shadow-lg transition-all duration-200">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-semibold text-purple-700 dark:text-purple-300 uppercase tracking-wide">This Month</p>
                                <p class="text-4xl font-black text-purple-900 dark:text-purple-100 mt-2 leading-none">
                                    <?= count(array_filter($faculty_list, function($f) {
                                        return isset($f['created_at']) && date('Y-m', strtotime($f['created_at'])) === date('Y-m');
                                    })) ?>
                                </p>
                                <p class="text-xs text-purple-600 dark:text-purple-400 mt-1 font-medium">New Joins</p>
                            </div>
                            <div class="bg-purple-600 dark:bg-purple-500 p-4 rounded-xl shadow-lg">
                                <i class="fas fa-calendar-plus text-white text-2xl"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <?php if (isset($_SESSION['flash_message'])): ?>
                <div class="bg-blue-50 dark:bg-blue-900/30 border-l-4 border-blue-500 text-blue-700 dark:text-blue-400 p-4 mb-6 flex justify-between items-center" role="alert">
                    <p><?= $_SESSION['flash_message']; unset($_SESSION['flash_message']); ?></p>
                    <button type="button" class="text-blue-700 dark:text-blue-400 hover:text-blue-900" onclick="this.parentElement.style.display='none';">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($schemaUpdateMessages)): ?>
                <div class="bg-blue-50 dark:bg-blue-900/30 border-l-4 border-blue-500 text-blue-700 dark:text-blue-400 p-4 mb-6 flex justify-between items-center" role="alert">
                    <div>
                        <p class="font-medium">Database schema has been updated</p>
                        <ul class="text-sm mt-1 list-disc list-inside">
                            <?php foreach ($schemaUpdateMessages as $message): ?>
                                <li><?= htmlspecialchars($message) ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                    <button type="button" class="text-blue-700 dark:text-blue-400 hover:text-blue-900" onclick="this.parentElement.style.display='none';">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            <?php endif; ?>
            
            <!-- Search and Filters -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 mb-8">
                <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
                    <h2 class="text-lg font-semibold text-gray-800 dark:text-white flex items-center">
                        <i class="fas fa-filter mr-3 text-blue-600"></i> Search & Filter Faculty
                    </h2>
                </div>
                <div class="p-6">
                    <form method="GET" class="space-y-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                            <!-- Search Input -->
                            <div class="lg:col-span-2">
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Search Faculty</label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <i class="fas fa-search text-gray-400"></i>
                                    </div>
                                    <input
                                        type="text"
                                        name="search"
                                        value="<?= htmlspecialchars($search) ?>"
                                        class="form-input pl-10 w-full rounded-lg border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="Search by name, email or department..."
                                    >
                                </div>
                            </div>

                            <!-- Department Filter -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Department</label>
                                <select name="department" class="form-input w-full rounded-lg border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                    <option value="">All Departments</option>
                                    <?php
                                    // Get unique departments for filter
                                    $deptQuery = $conn->prepare("SELECT DISTINCT department FROM faculties WHERE college_id = ? AND department IS NOT NULL AND department != '' ORDER BY department ASC");
                                    $deptQuery->execute([$college_id]);
                                    $allDepartments = $deptQuery->fetchAll(PDO::FETCH_COLUMN);
                                    foreach ($allDepartments as $dept): ?>
                                        <option value="<?= htmlspecialchars($dept) ?>" <?= $department_filter === $dept ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($dept) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <!-- Status Filter -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Status</label>
                                <select name="status" class="form-input w-full rounded-lg border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                    <option value="">All Status</option>
                                    <option value="active" <?= $status_filter === 'active' ? 'selected' : '' ?>>Active</option>
                                    <option value="inactive" <?= $status_filter === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                                </select>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
                            <div class="flex flex-wrap gap-3">
                                <button type="submit" class="btn btn-primary shadow-lg hover:shadow-xl transition-all duration-200">
                                    <i class="fas fa-search mr-2"></i> Search Faculty
                                </button>
                                <?php if (!empty($search) || !empty($department_filter) || !empty($status_filter)): ?>
                                    <a href="list_faculty.php" class="btn btn-outline hover:shadow-md transition-all duration-200">
                                        <i class="fas fa-times mr-2"></i> Clear Filters
                                    </a>
                                <?php endif; ?>
                                <button type="button" onclick="toggleAdvancedFilters()" class="btn btn-outline hover:shadow-md transition-all duration-200">
                                    <i class="fas fa-sliders-h mr-2"></i> Advanced
                                </button>
                            </div>

                            <div class="flex items-center space-x-4">
                                <div class="text-sm text-gray-500 dark:text-gray-400 flex items-center">
                                    <i class="fas fa-users mr-2"></i>
                                    <span class="font-medium"><?= $totalFaculty ?></span> faculty members found
                                </div>
                                <?php if ($totalFaculty > 0): ?>
                                    <button onclick="exportWithCurrentFilters()" class="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 flex items-center" title="Export current results">
                                        <i class="fas fa-download mr-1"></i> Export Results
                                    </button>
                                <?php endif; ?>
                                <div class="text-sm text-gray-500 dark:text-gray-400">
                                    Page <?= $page ?> of <?= $totalPages ?>
                                </div>
                            </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Faculty List -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4 flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
                    <div>
                        <h2 class="text-lg font-semibold text-gray-800 dark:text-white flex items-center">
                            <i class="fas fa-users mr-3 text-blue-600"></i> Faculty Directory
                            <?php if ($totalFaculty > 0): ?>
                                <span class="ml-3 px-3 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 text-sm font-medium rounded-full">
                                    <?= $totalFaculty ?> Members
                                </span>
                            <?php endif; ?>
                        </h2>
                        <?php if (!empty($search) || !empty($department_filter) || !empty($status_filter)): ?>
                            <div class="mt-2 flex flex-wrap gap-2">
                                <?php if (!empty($search)): ?>
                                    <span class="inline-flex items-center px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-xs rounded-full">
                                        <i class="fas fa-search mr-1"></i> "<?= htmlspecialchars($search) ?>"
                                    </span>
                                <?php endif; ?>
                                <?php if (!empty($department_filter)): ?>
                                    <span class="inline-flex items-center px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 text-xs rounded-full">
                                        <i class="fas fa-building mr-1"></i> <?= htmlspecialchars($department_filter) ?>
                                    </span>
                                <?php endif; ?>
                                <?php if (!empty($status_filter)): ?>
                                    <span class="inline-flex items-center px-2 py-1 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 text-xs rounded-full">
                                        <i class="fas fa-circle mr-1"></i> <?= htmlspecialchars($status_filter) ?>
                                    </span>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="flex items-center space-x-3">
                        <button onclick="toggleView('grid')" id="gridViewBtn" class="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-all duration-200">
                            <i class="fas fa-th-large"></i>
                        </button>
                        <button onclick="toggleView('list')" id="listViewBtn" class="p-2 text-blue-600 bg-blue-50 dark:bg-blue-900/20 rounded-lg transition-all duration-200">
                            <i class="fas fa-list"></i>
                        </button>
                    </div>
                </div>
                
        <?php if (count($faculty_list) > 0): ?>
                <!-- Faculty Cards Grid -->
                <div id="facultyGrid" class="p-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <?php foreach ($faculty_list as $faculty): ?>
                        <div class="group bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl p-6 hover:shadow-lg hover:border-blue-300 dark:hover:border-blue-600 transition-all duration-200">
                            <!-- Faculty Header -->
                            <div class="flex items-center space-x-4 mb-4">
                                <?php if (!empty($faculty['profile_image'])): ?>
                                    <div class="w-16 h-16 rounded-xl overflow-hidden">
                                        <img src="../uploads/<?= htmlspecialchars($faculty['profile_image']) ?>" alt="Profile" class="w-full h-full object-cover">
                                    </div>
                                <?php else: ?>
                                    <div class="w-16 h-16 rounded-xl bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-900/30 dark:to-blue-800/30 flex items-center justify-center">
                                        <i class="fas fa-chalkboard-teacher text-blue-600 dark:text-blue-400 text-xl"></i>
                                    </div>
                                <?php endif; ?>
                                <div class="flex-1 min-w-0">
                                    <h3 class="font-semibold text-gray-900 dark:text-white text-lg truncate group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                                        <?= htmlspecialchars($faculty['name']) ?>
                                    </h3>
                                    <p class="text-sm text-gray-500 dark:text-gray-400 truncate">
                                        <?= htmlspecialchars($faculty['position'] ?? $faculty['designation'] ?? 'Faculty Member') ?>
                                    </p>
                                    <div class="flex items-center mt-1">
                                        <span class="text-xs text-gray-400 mr-2">ID: <?= $faculty['id'] ?></span>
                                        <?php if (isset($faculty['status']) && $faculty['status'] === 'active'): ?>
                                            <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300">
                                                <i class="fas fa-circle text-green-500 mr-1" style="font-size: 6px;"></i>
                                                Active
                                            </span>
                                        <?php else: ?>
                                            <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300">
                                                <i class="fas fa-circle text-red-500 mr-1" style="font-size: 6px;"></i>
                                                Inactive
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>

                            <!-- Faculty Details -->
                            <div class="space-y-3 mb-4">
                                <div class="flex items-center text-sm">
                                    <i class="fas fa-envelope text-gray-400 mr-3 w-4"></i>
                                    <span class="text-gray-600 dark:text-gray-300 truncate"><?= htmlspecialchars($faculty['email']) ?></span>
                                </div>
                                <?php if (!empty($faculty['phone'])): ?>
                                    <div class="flex items-center text-sm">
                                        <i class="fas fa-phone text-gray-400 mr-3 w-4"></i>
                                        <span class="text-gray-600 dark:text-gray-300"><?= htmlspecialchars($faculty['phone']) ?></span>
                                    </div>
                                <?php endif; ?>
                                <div class="flex items-center text-sm">
                                    <i class="fas fa-building text-gray-400 mr-3 w-4"></i>
                                    <span class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300">
                                        <?= htmlspecialchars($faculty['department']) ?>
                                    </span>
                                </div>
                                <div class="flex items-center text-sm">
                                    <i class="fas fa-calendar text-gray-400 mr-3 w-4"></i>
                                    <span class="text-gray-600 dark:text-gray-300">
                                        Joined <?= date('M j, Y', strtotime($faculty['created_at'])) ?>
                                    </span>
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700">
                                <div class="flex space-x-2">
                                    <a href="view_faculty.php?id=<?= $faculty['id'] ?>" class="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-all duration-200" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="edit_faculty.php?id=<?= $faculty['id'] ?>" class="p-2 text-gray-400 hover:text-green-600 hover:bg-green-50 dark:hover:bg-green-900/20 rounded-lg transition-all duration-200" title="Edit Faculty">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button
                                        type="button"
                                        class="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-all duration-200"
                                        onclick="confirmDelete(<?= $faculty['id'] ?>, '<?= htmlspecialchars(addslashes($faculty['name'])) ?>')"
                                        title="Delete Faculty"
                                    >
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </div>
                                <a href="mailto:<?= htmlspecialchars($faculty['email']) ?>" class="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium transition-colors">
                                    Contact
                                </a>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <!-- List View (Hidden by default) -->
                <div id="facultyList" class="hidden">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                            <thead class="bg-gray-50 dark:bg-gray-700">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Faculty</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Contact</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Department</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Joined</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                <?php foreach ($faculty_list as $faculty): ?>
                                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <?php if (!empty($faculty['profile_image'])): ?>
                                                    <div class="w-10 h-10 rounded-lg overflow-hidden mr-3">
                                                        <img src="../uploads/<?= htmlspecialchars($faculty['profile_image']) ?>" alt="Profile" class="w-full h-full object-cover">
                                                    </div>
                                                <?php else: ?>
                                                    <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-900/30 dark:to-blue-800/30 flex items-center justify-center text-blue-600 dark:text-blue-400 mr-3">
                                                        <i class="fas fa-chalkboard-teacher"></i>
                                                    </div>
                                                <?php endif; ?>
                                                <div>
                                                    <div class="font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($faculty['name']) ?></div>
                                                    <div class="text-sm text-gray-500 dark:text-gray-400">ID: <?= $faculty['id'] ?></div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900 dark:text-white"><?= htmlspecialchars($faculty['email']) ?></div>
                                            <?php if (!empty($faculty['phone'])): ?>
                                                <div class="text-sm text-gray-500 dark:text-gray-400"><?= htmlspecialchars($faculty['phone']) ?></div>
                                            <?php endif; ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300">
                                                <?= htmlspecialchars($faculty['department']) ?>
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <?php if (isset($faculty['status']) && $faculty['status'] === 'active'): ?>
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300">
                                                    <i class="fas fa-circle text-green-500 mr-1" style="font-size: 6px;"></i>
                                                    Active
                                                </span>
                                            <?php else: ?>
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300">
                                                    <i class="fas fa-circle text-red-500 mr-1" style="font-size: 6px;"></i>
                                                    Inactive
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                            <?= date('M d, Y', strtotime($faculty['created_at'])) ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                                            <div class="flex items-center space-x-2">
                                                <a href="view_faculty.php?id=<?= $faculty['id'] ?>" class="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-all duration-200" title="View Details">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="edit_faculty.php?id=<?= $faculty['id'] ?>" class="p-2 text-gray-400 hover:text-green-600 hover:bg-green-50 dark:hover:bg-green-900/20 rounded-lg transition-all duration-200" title="Edit Faculty">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button
                                                    type="button"
                                                    class="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-all duration-200"
                                                    onclick="confirmDelete(<?= $faculty['id'] ?>, '<?= htmlspecialchars(addslashes($faculty['name'])) ?>')"
                                                    title="Delete Faculty"
                                                >
                                                    <i class="fas fa-trash-alt"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Pagination -->
                <?php if ($totalPages > 1): ?>
                    <div class="px-6 py-4 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-700 rounded-b-xl">
                        <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
                            <div class="text-sm text-gray-500 dark:text-gray-400">
                                Showing <span class="font-medium"><?= ($offset + 1) ?></span> to <span class="font-medium"><?= min($offset + $limit, $totalFaculty) ?></span> of <span class="font-medium"><?= $totalFaculty ?></span> faculty members
                            </div>
                            <div class="flex items-center space-x-2">
                                <?php if ($page > 1): ?>
                                    <a href="?page=<?= ($page - 1) ?><?= !empty($search) ? '&search=' . urlencode($search) : '' ?><?= !empty($department_filter) ? '&department=' . urlencode($department_filter) : '' ?><?= !empty($status_filter) ? '&status=' . urlencode($status_filter) : '' ?>" class="btn btn-outline btn-sm hover:shadow-md transition-all duration-200">
                                        <i class="fas fa-chevron-left mr-1"></i> Previous
                                    </a>
                                <?php endif; ?>

                                <!-- Page Numbers -->
                                <div class="flex items-center space-x-1">
                                    <?php
                                    $start = max(1, $page - 2);
                                    $end = min($totalPages, $page + 2);
                                    for ($i = $start; $i <= $end; $i++): ?>
                                        <a href="?page=<?= $i ?><?= !empty($search) ? '&search=' . urlencode($search) : '' ?><?= !empty($department_filter) ? '&department=' . urlencode($department_filter) : '' ?><?= !empty($status_filter) ? '&status=' . urlencode($status_filter) : '' ?>"
                                           class="px-3 py-1 text-sm rounded-md transition-all duration-200 <?= $i === $page ? 'bg-blue-600 text-white' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700' ?>">
                                            <?= $i ?>
                                        </a>
                                    <?php endfor; ?>
                                </div>

                                <?php if ($page < $totalPages): ?>
                                    <a href="?page=<?= ($page + 1) ?><?= !empty($search) ? '&search=' . urlencode($search) : '' ?><?= !empty($department_filter) ? '&department=' . urlencode($department_filter) : '' ?><?= !empty($status_filter) ? '&status=' . urlencode($status_filter) : '' ?>" class="btn btn-outline btn-sm hover:shadow-md transition-all duration-200">
                                        Next <i class="fas fa-chevron-right ml-1"></i>
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
                <?php else: ?>
                    <div class="p-12 text-center">
                        <?php if (!empty($search) || !empty($department_filter) || !empty($status_filter)): ?>
                            <div class="max-w-md mx-auto">
                                <div class="w-20 h-20 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 rounded-2xl flex items-center justify-center mx-auto mb-6">
                                    <i class="fas fa-search text-gray-400 text-2xl"></i>
                                </div>
                                <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-3">No matching results</h3>
                                <p class="text-gray-500 dark:text-gray-400 mb-6">We couldn't find any faculty matching your search criteria. Try adjusting your filters or search terms.</p>
                                <div class="flex flex-col sm:flex-row gap-3 justify-center">
                                    <a href="list_faculty.php" class="btn btn-outline hover:shadow-md transition-all duration-200">
                                        <i class="fas fa-times mr-2"></i> Clear Filters
                                    </a>
                                    <a href="add_faculty.php" class="btn btn-primary shadow-lg hover:shadow-xl transition-all duration-200">
                                        <i class="fas fa-user-plus mr-2"></i> Add New Faculty
                                    </a>
                                </div>
                            </div>
                        <?php else: ?>
                            <div class="max-w-md mx-auto">
                                <div class="w-20 h-20 bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-900/30 dark:to-blue-800/30 rounded-2xl flex items-center justify-center mx-auto mb-6">
                                    <i class="fas fa-chalkboard-teacher text-blue-600 dark:text-blue-400 text-2xl"></i>
                                </div>
                                <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-3">No faculty members yet</h3>
                                <p class="text-gray-500 dark:text-gray-400 mb-6">Get started by adding your first faculty member to begin building your academic team.</p>
                                <div class="flex flex-col sm:flex-row gap-3 justify-center">
                                    <a href="add_faculty.php" class="btn btn-primary shadow-lg hover:shadow-xl transition-all duration-200">
                                        <i class="fas fa-user-plus mr-2"></i> Add First Faculty
                                    </a>
                                    <a href="import_faculty.php" class="btn btn-outline hover:shadow-md transition-all duration-200">
                                        <i class="fas fa-file-import mr-2"></i> Import Faculty
                                    </a>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>
            
            <!-- Delete Confirmation Modal -->
            <div id="deleteModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4">
                    <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">Confirm Deletion</h3>
                    </div>
                    <div class="p-6">
                        <p class="text-gray-700 dark:text-gray-300 mb-4">
                            Are you sure you want to delete faculty member <span id="facultyName" class="font-medium"></span>? This action cannot be undone.
                        </p>
                        <div class="flex justify-end space-x-3">
                            <button type="button" class="btn btn-outline" onclick="closeDeleteModal()">
                                Cancel
                            </button>
                            <a href="#" id="confirmDeleteBtn" class="btn btn-danger">
                                <i class="fas fa-trash-alt mr-2"></i> Delete
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // View toggle functionality
        function toggleView(viewType) {
            const gridView = document.getElementById('facultyGrid');
            const listView = document.getElementById('facultyList');
            const gridBtn = document.getElementById('gridViewBtn');
            const listBtn = document.getElementById('listViewBtn');

            if (viewType === 'grid') {
                gridView.classList.remove('hidden');
                listView.classList.add('hidden');
                gridBtn.classList.add('text-blue-600', 'bg-blue-50', 'dark:bg-blue-900/20');
                gridBtn.classList.remove('text-gray-400');
                listBtn.classList.remove('text-blue-600', 'bg-blue-50', 'dark:bg-blue-900/20');
                listBtn.classList.add('text-gray-400');
                localStorage.setItem('facultyViewType', 'grid');
            } else {
                gridView.classList.add('hidden');
                listView.classList.remove('hidden');
                listBtn.classList.add('text-blue-600', 'bg-blue-50', 'dark:bg-blue-900/20');
                listBtn.classList.remove('text-gray-400');
                gridBtn.classList.remove('text-blue-600', 'bg-blue-50', 'dark:bg-blue-900/20');
                gridBtn.classList.add('text-gray-400');
                localStorage.setItem('facultyViewType', 'list');
            }
        }

        // Load saved view preference
        document.addEventListener('DOMContentLoaded', function() {
            const savedView = localStorage.getItem('facultyViewType') || 'grid';
            toggleView(savedView);
        });

        // Export functionality with current filters
        function exportWithCurrentFilters() {
            const searchInput = document.getElementById('searchInput');
            const departmentFilter = document.getElementById('departmentFilter');
            const statusFilter = document.getElementById('statusFilter');

            const params = new URLSearchParams();

            // Add current filters to export URL
            if (searchInput && searchInput.value.trim()) {
                params.set('search', searchInput.value.trim());
            }

            if (departmentFilter && departmentFilter.value && departmentFilter.value !== 'all') {
                params.set('department', departmentFilter.value);
            }

            if (statusFilter && statusFilter.value && statusFilter.value !== 'all') {
                params.set('status', statusFilter.value);
            }

            // Set default fields for quick export
            params.set('fields[]', 'name');
            params.set('fields[]', 'email');
            params.set('fields[]', 'department');
            params.set('fields[]', 'position');
            params.set('fields[]', 'status');

            // Redirect to export page with current filters
            window.location.href = 'export_faculty.php?' + params.toString();
        }

        // Advanced filters toggle
        function toggleAdvancedFilters() {
            // This can be implemented later for additional filters
            alert('Advanced filters coming soon!');
        }

        // Delete confirmation modal
        function confirmDelete(id, name) {
            document.getElementById('facultyName').textContent = name;
            document.getElementById('confirmDeleteBtn').href = 'delete_faculty.php?id=' + id;
            document.getElementById('deleteModal').classList.remove('hidden');
        }

        function closeDeleteModal() {
            document.getElementById('deleteModal').classList.add('hidden');
        }

        // Close modal when clicking outside
        document.getElementById('deleteModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeDeleteModal();
            }
        });

        // Auto-submit search form with debounce
        let searchTimeout;
        const searchInput = document.querySelector('input[name="search"]');
        if (searchInput) {
            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    this.form.submit();
                }, 500);
            });
        }
    </script>

<?php
// Include the footer
require_once '../includes/footer.php';
?>