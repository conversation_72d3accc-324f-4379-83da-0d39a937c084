<?php
require_once '../includes/db.php';
require_once '../includes/header.php';

if (!isset($_SESSION['alumni_id'])) {
    header("Location: ../auth/alumni_login.php");
    exit;
}

$alumni_id = $_SESSION['alumni_id'];
$error = $success = '';

// Add availability
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['day_of_week'])) {
    $day = $_POST['day_of_week'];
    $start = $_POST['start_time'];
    $end = $_POST['end_time'];

    if ($day && $start && $end) {
        $stmt = $conn->prepare("INSERT INTO alumni_availability (alumni_id, day_of_week, start_time, end_time) VALUES (?, ?, ?, ?)");
        $stmt->execute([$alumni_id, $day, $start, $end]);
        $success = "Availability added.";
    } else {
        $error = "All fields are required.";
    }
}

// Delete availability
if (isset($_GET['delete'])) {
    $id = $_GET['delete'];
    $stmt = $conn->prepare("DELETE FROM alumni_availability WHERE id = ? AND alumni_id = ?");
    $stmt->execute([$id, $alumni_id]);
    $success = "Slot deleted.";
}

// Fetch existing availability
$stmt = $conn->prepare("SELECT * FROM alumni_availability WHERE alumni_id = ? ORDER BY FIELD(day_of_week, 'Monday','Tuesday','Wednesday','Thursday','Friday','Saturday','Sunday')");
$stmt->execute([$alumni_id]);
$slots = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<div class="container mt-4">
    <h3>🕒 Manage Your Weekly Availability</h3>
    <p class="text-muted">Set slots when you're open for student mentorship.</p>

    <?php if ($success): ?><div class="alert alert-success"><?= $success ?></div><?php endif; ?>
    <?php if ($error): ?><div class="alert alert-danger"><?= $error ?></div><?php endif; ?>

    <form method="POST" class="card p-3 mb-4 shadow-sm">
        <div class="row g-2">
            <div class="col-md-4">
                <label>Day of Week</label>
                <select name="day_of_week" class="form-select" required>
                    <option value="">Select</option>
                    <?php foreach (['Monday','Tuesday','Wednesday','Thursday','Friday','Saturday','Sunday'] as $day): ?>
                        <option value="<?= $day ?>"><?= $day ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-3">
                <label>Start Time</label>
                <input type="time" name="start_time" class="form-control" required>
            </div>
            <div class="col-md-3">
                <label>End Time</label>
                <input type="time" name="end_time" class="form-control" required>
            </div>
            <div class="col-md-2 d-flex align-items-end">
                <button class="btn btn-success w-100">+ Add</button>
            </div>
        </div>
    </form>

    <h5>📋 Your Current Slots</h5>
    <?php if ($slots): ?>
        <ul class="list-group">
            <?php foreach ($slots as $s): ?>
                <li class="list-group-item d-flex justify-content-between align-items-center">
                    <?= $s['day_of_week'] ?>: <?= date("g:i A", strtotime($s['start_time'])) ?> - <?= date("g:i A", strtotime($s['end_time'])) ?>
                    <a href="?delete=<?= $s['id'] ?>" class="btn btn-sm btn-outline-danger">Remove</a>
                </li>
            <?php endforeach; ?>
        </ul>
    <?php else: ?>
        <p class="text-muted small">No slots added yet.</p>
    <?php endif; ?>
</div>

<?php require_once '../includes/footer.php'; ?>
