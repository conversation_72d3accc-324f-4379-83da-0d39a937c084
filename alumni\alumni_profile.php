<?php
require_once '../includes/db.php';
require_once '../includes/header.php';

if (!isset($_SESSION['alumni_id'])) {
    header("Location: ../auth/alumni_login.php");
    exit;
}

$alumni_id = $_SESSION['alumni_id'];

// Fetch alumni profile data
$stmt = $conn->prepare("SELECT * FROM alumni WHERE id = ?");
$stmt->execute([$alumni_id]);
$alumni = $stmt->fetch(PDO::FETCH_ASSOC);

// Photo fallback
$photo = (!empty($alumni['profile_photo']) && file_exists("../uploads/{$alumni['profile_photo']}"))
    ? $alumni['profile_photo']
    : 'profile.gif';

// Handle form submission
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Process form data
    $full_name = trim($_POST['full_name']);
    $email = trim($_POST['email']);
    $phone = trim($_POST['phone']);
    $graduation_year = trim($_POST['graduation_year']);
    $degree = trim($_POST['degree']);
    $major = trim($_POST['major']);
    $current_position = trim($_POST['current_position']);
    $company = trim($_POST['company']);
    $location = trim($_POST['location']);
    $industry = trim($_POST['industry']);
    $bio = trim($_POST['bio']);
    $linkedin_url = trim($_POST['linkedin_url']);
    $skills = trim($_POST['skills']);
    
    // Validate required fields
    if (empty($full_name) || empty($email)) {
        $error_message = "Full name and email are required.";
    } else {
        // Handle profile photo upload
        $profile_photo = $alumni['profile_photo']; // Default to existing photo
        
        if (isset($_FILES['profile_photo']) && $_FILES['profile_photo']['error'] === UPLOAD_ERR_OK) {
            $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];
            $file_type = $_FILES['profile_photo']['type'];
            
            if (in_array($file_type, $allowed_types)) {
                $file_name = 'alumni_' . $alumni_id . '_' . time() . '.' . pathinfo($_FILES['profile_photo']['name'], PATHINFO_EXTENSION);
                $upload_path = '../uploads/' . $file_name;
                
                if (move_uploaded_file($_FILES['profile_photo']['tmp_name'], $upload_path)) {
                    $profile_photo = $file_name;
                } else {
                    $error_message = "Failed to upload profile photo.";
                }
            } else {
                $error_message = "Invalid file type. Please upload JPG, PNG, or GIF.";
            }
        }
        
        if (empty($error_message)) {
            // Update alumni profile
            $update_stmt = $conn->prepare("
                UPDATE alumni SET 
                full_name = ?, 
                email = ?, 
                phone = ?, 
                graduation_year = ?, 
                degree = ?, 
                major = ?, 
                current_position = ?, 
                company = ?, 
                location = ?, 
                industry = ?, 
                bio = ?, 
                linkedin_url = ?,
                skills = ?,
                profile_photo = ? 
                WHERE id = ?
            ");
            
            $update_stmt->execute([
                $full_name, 
                $email, 
                $phone, 
                $graduation_year, 
                $degree, 
                $major, 
                $current_position, 
                $company, 
                $location, 
                $industry, 
                $bio, 
                $linkedin_url,
                $skills,
                $profile_photo, 
                $alumni_id
            ]);
            
            if ($update_stmt->rowCount() > 0) {
                $success_message = "Profile updated successfully!";
                
                // Refresh alumni data
                $stmt->execute([$alumni_id]);
                $alumni = $stmt->fetch(PDO::FETCH_ASSOC);
                
                // Update photo variable
                $photo = (!empty($alumni['profile_photo']) && file_exists("../uploads/{$alumni['profile_photo']}"))
                    ? $alumni['profile_photo']
                    : 'profile.gif';
            } else {
                $error_message = "No changes made or update failed.";
            }
        }
    }
}

// Parse skills into array
$skills_array = !empty($alumni['skills']) ? explode(',', $alumni['skills']) : [];
?>

<!-- LinkedIn-style Profile Page -->
<div class="max-w-5xl mx-auto">
    <?php if (!empty($success_message)): ?>
        <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-5" role="alert">
            <p><?= $success_message ?></p>
        </div>
    <?php endif; ?>
    
    <?php if (!empty($error_message)): ?>
        <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-5" role="alert">
            <p><?= $error_message ?></p>
        </div>
    <?php endif; ?>
    
    <!-- Profile Header -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 overflow-hidden mb-5">
        <!-- Cover Photo -->
        <div class="h-40 bg-gradient-to-r from-[#0a66c2] to-[#0073b1] w-full relative">
            <div class="absolute bottom-4 right-4">
                <button type="button" class="bg-white hover:bg-gray-100 text-gray-800 font-medium py-1.5 px-3 rounded-md text-sm flex items-center shadow-sm">
                    <i class="fas fa-camera mr-2"></i> Edit Cover Photo
                </button>
            </div>
        </div>
        
        <!-- Profile Info -->
        <div class="px-6 pb-6 relative">
            <div class="flex flex-col md:flex-row">
                <!-- Profile Photo -->
                <div class="relative -mt-16 md:-mt-20 flex-shrink-0">
                    <img src="../uploads/<?= htmlspecialchars($photo) ?>" class="w-32 h-32 md:w-40 md:h-40 rounded-full border-4 border-white dark:border-gray-800 object-cover" alt="Profile Photo">
                    <div class="absolute bottom-0 right-0 bg-white dark:bg-gray-800 rounded-full p-1.5 border border-gray-200 dark:border-gray-700">
                        <label for="profile_photo_upload" class="cursor-pointer text-gray-600 dark:text-gray-400 hover:text-[#0a66c2]">
                            <i class="fas fa-camera"></i>
                            <span class="sr-only">Upload Photo</span>
                        </label>
                    </div>
                </div>
                
                <div class="mt-4 md:mt-0 md:ml-6 flex-grow">
                    <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                        <div>
                            <h1 class="text-2xl font-bold text-gray-900 dark:text-white"><?= htmlspecialchars($alumni['full_name'] ?? '') ?></h1>
                            <p class="text-lg text-gray-700 dark:text-gray-300"><?= htmlspecialchars($alumni['current_position'] ?? '') ?> at <?= htmlspecialchars($alumni['company'] ?? '') ?></p>
                            <p class="text-gray-600 dark:text-gray-400 mt-1"><?= htmlspecialchars($alumni['location'] ?? '') ?></p>
                            <div class="mt-2 flex items-center text-sm text-gray-500 dark:text-gray-400">
                                <span class="mr-4"><?= htmlspecialchars($alumni['degree'] ?? '') ?> in <?= htmlspecialchars($alumni['major'] ?? '') ?>, Class of <?= htmlspecialchars($alumni['graduation_year'] ?? '') ?></span>
                            </div>
                        </div>
                        
                        <div class="mt-4 md:mt-0">
                            <button type="button" class="btn-primary" data-toggle-edit-form>
                                <i class="fas fa-pen mr-2"></i> Edit Profile
                            </button>
                        </div>
                    </div>
                    
                    <?php if (!empty($alumni['bio'])): ?>
                    <div class="mt-4 text-gray-700 dark:text-gray-300">
                        <?= nl2br(htmlspecialchars($alumni['bio'] ?? '')) ?>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Profile Content -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-5">
        <!-- Left Column -->
        <div class="lg:col-span-2">
            <!-- About Section -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 overflow-hidden mb-5">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-xl font-bold text-gray-900 dark:text-white">About</h2>
                        <button type="button" class="text-[#0a66c2] hover:underline" data-toggle-edit-form>
                            <i class="fas fa-pen"></i>
                        </button>
                    </div>
                    
                    <div class="text-gray-700 dark:text-gray-300">
                        <?= !empty($alumni['bio']) ? nl2br(htmlspecialchars($alumni['bio'])) : '<p class="text-gray-500 dark:text-gray-400 italic">Add a bio to tell people more about yourself.</p>' ?>
                    </div>
                </div>
            </div>
            
            <!-- Experience Section -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 overflow-hidden mb-5">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-xl font-bold text-gray-900 dark:text-white">Experience</h2>
                        <button type="button" class="text-[#0a66c2] hover:underline">
                            <i class="fas fa-plus"></i>
                        </button>
                    </div>
                    
                    <?php if (!empty($alumni['current_position']) && !empty($alumni['company'])): ?>
                    <div class="flex mb-6">
                        <div class="flex-shrink-0 mr-4">
                            <div class="w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded flex items-center justify-center">
                                <i class="fas fa-building text-gray-500 dark:text-gray-400"></i>
                            </div>
                        </div>
                        <div>
                            <h3 class="font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($alumni['current_position']) ?></h3>
                            <p class="text-gray-700 dark:text-gray-300"><?= htmlspecialchars($alumni['company']) ?></p>
                            <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">2020 - Present</p>
                            <p class="text-sm text-gray-500 dark:text-gray-400"><?= htmlspecialchars($alumni['location'] ?? '') ?></p>
                        </div>
                    </div>
                    <?php else: ?>
                    <p class="text-gray-500 dark:text-gray-400 italic">Add your work experience to showcase your professional journey.</p>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Education Section -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 overflow-hidden mb-5">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-xl font-bold text-gray-900 dark:text-white">Education</h2>
                        <button type="button" class="text-[#0a66c2] hover:underline">
                            <i class="fas fa-plus"></i>
                        </button>
                    </div>
                    
                    <?php if (!empty($alumni['degree']) && !empty($alumni['graduation_year'])): ?>
                    <div class="flex">
                        <div class="flex-shrink-0 mr-4">
                            <div class="w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded flex items-center justify-center">
                                <i class="fas fa-university text-gray-500 dark:text-gray-400"></i>
                            </div>
                        </div>
                        <div>
                            <h3 class="font-medium text-gray-900 dark:text-white">Your College</h3>
                            <p class="text-gray-700 dark:text-gray-300"><?= htmlspecialchars($alumni['degree']) ?> in <?= htmlspecialchars($alumni['major'] ?? '') ?></p>
                            <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Class of <?= htmlspecialchars($alumni['graduation_year']) ?></p>
                        </div>
                    </div>
                    <?php else: ?>
                    <p class="text-gray-500 dark:text-gray-400 italic">Add your education details to showcase your academic background.</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- Right Column -->
        <div class="lg:col-span-1">
            <!-- Skills Section -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 overflow-hidden mb-5">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-xl font-bold text-gray-900 dark:text-white">Skills</h2>
                        <button type="button" class="text-[#0a66c2] hover:underline" data-toggle-edit-form>
                            <i class="fas fa-pen"></i>
                        </button>
                </div>
                
                    <?php if (!empty($skills_array)): ?>
                    <div class="flex flex-wrap gap-2">
                        <?php foreach ($skills_array as $skill): ?>
                            <span class="bg-[#e7f3ff] text-[#0a66c2] dark:bg-blue-900 dark:text-blue-200 px-3 py-1 rounded-full text-sm"><?= htmlspecialchars(trim($skill)) ?></span>
                        <?php endforeach; ?>
                    </div>
                    <?php else: ?>
                    <p class="text-gray-500 dark:text-gray-400 italic">Add skills to showcase your expertise.</p>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Contact Information -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 overflow-hidden mb-5">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-xl font-bold text-gray-900 dark:text-white">Contact Info</h2>
                        <button type="button" class="text-[#0a66c2] hover:underline" data-toggle-edit-form>
                            <i class="fas fa-pen"></i>
                        </button>
                </div>
                
                    <ul class="space-y-3">
                        <?php if (!empty($alumni['email'])): ?>
                        <li class="flex items-center">
                            <i class="fas fa-envelope text-gray-500 dark:text-gray-400 w-6"></i>
                            <span class="ml-2 text-gray-700 dark:text-gray-300"><?= htmlspecialchars($alumni['email']) ?></span>
                        </li>
                        <?php endif; ?>
                        
                        <?php if (!empty($alumni['phone'])): ?>
                        <li class="flex items-center">
                            <i class="fas fa-phone text-gray-500 dark:text-gray-400 w-6"></i>
                            <span class="ml-2 text-gray-700 dark:text-gray-300"><?= htmlspecialchars($alumni['phone']) ?></span>
                        </li>
                        <?php endif; ?>
                        
                        <?php if (!empty($alumni['linkedin_url'])): ?>
                        <li class="flex items-center">
                            <i class="fab fa-linkedin text-gray-500 dark:text-gray-400 w-6"></i>
                            <a href="<?= htmlspecialchars($alumni['linkedin_url']) ?>" target="_blank" class="ml-2 text-[#0a66c2] hover:underline">LinkedIn Profile</a>
                        </li>
                        <?php endif; ?>
                        
                        <?php if (empty($alumni['email']) && empty($alumni['phone']) && empty($alumni['linkedin_url'])): ?>
                        <li class="text-gray-500 dark:text-gray-400 italic">Add your contact information to help others connect with you.</li>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>
            
            <!-- Availability Section -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 overflow-hidden mb-5">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-xl font-bold text-gray-900 dark:text-white">Availability</h2>
                        <a href="availability.php" class="text-[#0a66c2] hover:underline">
                            <i class="fas fa-pen"></i>
                        </a>
                    </div>
                    
                    <div class="flex items-center mb-3">
                        <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                        <span class="text-gray-700 dark:text-gray-300">Available for mentoring</span>
            </div>
            
                    <a href="availability.php" class="btn-outline text-sm py-1.5 flex items-center justify-center mt-2">
                        <i class="far fa-calendar-alt mr-2"></i> Set Availability
                    </a>
                </div>
            </div>
            </div>
        </div>
        
    <!-- Edit Profile Form (Hidden by default) -->
    <div id="editProfileForm" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-3xl w-full max-h-[90vh] overflow-y-auto">
            <div class="flex justify-between items-center p-4 border-b border-gray-200 dark:border-gray-700">
                <h2 class="text-xl font-bold text-gray-900 dark:text-white">Edit Profile</h2>
                <button type="button" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300" data-close-edit-form>
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <form method="POST" enctype="multipart/form-data" class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Personal Information -->
                    <div class="md:col-span-2">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">Personal Information</h3>
                    </div>
                    
                    <div class="form-group">
                        <label for="full_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Full Name *</label>
                        <input type="text" id="full_name" name="full_name" class="form-input" value="<?= htmlspecialchars($alumni['full_name'] ?? '') ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Email *</label>
                        <input type="email" id="email" name="email" class="form-input" value="<?= htmlspecialchars($alumni['email'] ?? '') ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Phone</label>
                        <input type="text" id="phone" name="phone" class="form-input" value="<?= htmlspecialchars($alumni['phone'] ?? '') ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="location" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Location</label>
                        <input type="text" id="location" name="location" class="form-input" value="<?= htmlspecialchars($alumni['location'] ?? '') ?>" placeholder="City, Country">
                    </div>
                    
                    <div class="form-group">
                        <label for="profile_photo" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Profile Photo</label>
                        <input type="file" id="profile_photo_upload" name="profile_photo" class="form-input" accept="image/jpeg,image/png,image/gif">
                    </div>
                    
                    <div class="form-group">
                        <label for="linkedin_url" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">LinkedIn URL</label>
                        <input type="url" id="linkedin_url" name="linkedin_url" class="form-input" value="<?= htmlspecialchars($alumni['linkedin_url'] ?? '') ?>" placeholder="https://linkedin.com/in/yourprofile">
                    </div>
                    
                    <!-- Education -->
                    <div class="md:col-span-2 mt-4">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">Education</h3>
                    </div>
                    
                    <div class="form-group">
                        <label for="graduation_year" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Graduation Year</label>
                        <input type="text" id="graduation_year" name="graduation_year" class="form-input" value="<?= htmlspecialchars($alumni['graduation_year'] ?? '') ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="degree" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Degree</label>
                        <input type="text" id="degree" name="degree" class="form-input" value="<?= htmlspecialchars($alumni['degree'] ?? '') ?>" placeholder="Bachelor of Science, Master of Arts, etc.">
                    </div>
                    
                    <div class="form-group md:col-span-2">
                        <label for="major" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Major/Field of Study</label>
                        <input type="text" id="major" name="major" class="form-input" value="<?= htmlspecialchars($alumni['major'] ?? '') ?>">
                    </div>
                    
                    <!-- Professional Information -->
                    <div class="md:col-span-2 mt-4">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">Professional Information</h3>
                    </div>
                    
                    <div class="form-group">
                        <label for="current_position" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Current Position</label>
                        <input type="text" id="current_position" name="current_position" class="form-input" value="<?= htmlspecialchars($alumni['current_position'] ?? '') ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="company" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Company</label>
                        <input type="text" id="company" name="company" class="form-input" value="<?= htmlspecialchars($alumni['company'] ?? '') ?>">
                    </div>
                    
                    <div class="form-group md:col-span-2">
                        <label for="industry" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Industry</label>
                        <input type="text" id="industry" name="industry" class="form-input" value="<?= htmlspecialchars($alumni['industry'] ?? '') ?>">
                </div>
                
                    <div class="form-group md:col-span-2">
                        <label for="skills" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Skills (comma separated)</label>
                        <input type="text" id="skills" name="skills" class="form-input" value="<?= htmlspecialchars($alumni['skills'] ?? '') ?>" placeholder="Leadership, Communication, Project Management">
                </div>
                
                    <div class="form-group md:col-span-2">
                        <label for="bio" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Bio</label>
                        <textarea id="bio" name="bio" rows="4" class="form-input" placeholder="Tell us about yourself, your experience, and what you're passionate about."><?= htmlspecialchars($alumni['bio'] ?? '') ?></textarea>
                    </div>
                </div>
                
                <div class="flex justify-end mt-6 space-x-3">
                    <button type="button" class="btn-secondary" data-close-edit-form>Cancel</button>
                    <button type="submit" class="btn-primary">Save Changes</button>
                </div>
            </form>
        </div>
        </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Edit form toggle
    const editButtons = document.querySelectorAll('[data-toggle-edit-form]');
    const editForm = document.getElementById('editProfileForm');
    const closeButtons = document.querySelectorAll('[data-close-edit-form]');
    
    editButtons.forEach(button => {
        button.addEventListener('click', () => {
            editForm.classList.remove('hidden');
            document.body.classList.add('overflow-hidden');
        });
    });
    
    closeButtons.forEach(button => {
        button.addEventListener('click', () => {
            editForm.classList.add('hidden');
            document.body.classList.remove('overflow-hidden');
        });
    });
    
    // Close modal when clicking outside
    editForm.addEventListener('click', (e) => {
        if (e.target === editForm) {
            editForm.classList.add('hidden');
            document.body.classList.remove('overflow-hidden');
        }
    });
    
    // Close on escape key
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && !editForm.classList.contains('hidden')) {
            editForm.classList.add('hidden');
            document.body.classList.remove('overflow-hidden');
        }
    });
});
</script>

<?php require_once '../includes/footer.php'; ?>