<?php
// Determine user role and set appropriate variables if not already set
if (!isset($role)) {
    $role = '';
}
if (!isset($user_id)) {
    $user_id = null;
}
if (!isset($user_name)) {
    $user_name = '';
}
if (!isset($dashboard_url)) {
    $dashboard_url = '';
}

// Only set these variables if they haven't been set already
if (empty($role)) {
    if (isset($_SESSION['superadmin_id'])) {
        $role = 'superadmin';
        $user_id = $_SESSION['superadmin_id'];
        $user_name = $_SESSION['superadmin_name'];
        $dashboard_url = '/dashboards/superadmin_dashboard.php';
    } elseif (isset($_SESSION['collegeadmin_id'])) {
        $role = 'collegeadmin';
        $user_id = $_SESSION['collegeadmin_id'];
        $user_name = $_SESSION['collegeadmin_name'];
        $dashboard_url = '/dashboards/collegeadmin_dashboard.php';
    } elseif (isset($_SESSION['faculty_id'])) {
        $role = 'faculty';
        $user_id = $_SESSION['faculty_id'];
        $user_name = $_SESSION['faculty_name'];
        $dashboard_url = '/faculty/faculty_dashboard.php';
    } elseif (isset($_SESSION['student_id'])) {
        $role = 'student';
        $user_id = $_SESSION['student_id'];
        $user_name = $_SESSION['student_name'];
        $dashboard_url = '/student/student_dashboard.php';
    } elseif (isset($_SESSION['alumni_id'])) {
        $role = 'alumni';
        $user_id = $_SESSION['alumni_id'];
        $user_name = $_SESSION['alumni_name'];
        $dashboard_url = '/alumni/alumni_dashboard.php';
    }
}

// Determine active page if not already set
if (!isset($current_page)) {
    $current_page = basename($_SERVER['PHP_SELF']);
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= ucfirst($role) ?> - Mentoshri Platform</title>
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js"></script>
    <!-- Google Fonts - Inter -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        'primary': '#2563eb',
                        'primary-dark': '#1d4ed8',
                        'primary-light': '#dbeafe',
                        'accent': '#f59e0b',
                        'accent-light': '#fef3c7',
                        'success': '#10b981',
                        'success-light': '#d1fae5',
                        'danger': '#ef4444',
                        'danger-light': '#fee2e2',
                        'warning': '#f59e0b',
                        'warning-light': '#fef3c7',
                    },
                    fontFamily: {
                        'sans': ['Inter', 'sans-serif'],
                    },
                }
            }
        }
    </script>
    <!-- Design System -->
    <link rel="stylesheet" href="/assets/css/design-system.css">
    <!-- Custom styles -->
    <style>
        .nav-item {
            position: relative;
            transition: all 0.2s ease;
            color: #4b5563; /* text-gray-600 */
        }
        .dark .nav-item {
            color: #e5e7eb; /* text-gray-200 */
        }
        .nav-item.active {
            color: #2563eb;
            font-weight: 500;
        }
        .dark .nav-item.active {
            color: #3b82f6; /* text-blue-500 */
        }
        .nav-item.active::after {
            content: '';
            position: absolute;
            bottom: -0.5rem;
            left: 0;
            width: 100%;
            height: 2px;
            background-color: #2563eb;
            border-radius: 2px;
        }
        .dark .nav-item.active::after {
            background-color: #3b82f6; /* bg-blue-500 */
        }
        .nav-item:hover:not(.active) {
            color: #4b5563;
        }
        .dark .nav-item:hover:not(.active) {
            color: #f9fafb; /* text-gray-50 */
        }
        .sidenav-item {
            transition: all 0.2s ease;
            border-radius: 0.375rem;
        }
        .sidenav-item:hover {
            background-color: #f3f4f6;
        }
        .sidenav-item.active {
            background-color: #dbeafe;
            color: #2563eb;
            font-weight: 500;
        }
        .dark .sidenav-item:hover {
            background-color: #374151;
        }
        .dark .sidenav-item.active {
            background-color: #1e40af;
            color: white;
        }
        
        /* FontAwesome icon styles for sidebar */
        .sidenav-item i {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 1.25rem;
            height: 1.25rem;
            min-width: 1.25rem;
            margin-right: 0.75rem;
            font-size: 1rem;
        }
    </style>
</head>
<body class="font-sans bg-gray-50 dark:bg-gray-900 min-h-screen flex flex-col">
    <!-- Top Navigation Bar -->
    <header class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50">
        <div class="max-w-full mx-auto px-4">
        <div class="flex items-center justify-between h-16">
                <!-- Logo & Branding -->
            <div class="flex items-center">
                    <a href="<?= $dashboard_url ?>" class="flex-shrink-0 flex items-center">
                        <div class="bg-primary rounded-lg w-8 h-8 flex items-center justify-center text-white font-bold text-xl mr-2">M</div>
                        <span class="text-gray-900 dark:text-white font-bold text-xl hidden md:inline-block">Mentoshri <span class="text-primary dark:text-blue-400 font-normal">Platform</span></span>
                    </a>
            </div>
            
                <!-- Main Navigation (Desktop) -->
                <nav class="hidden md:flex space-x-8">
                    <?php if ($role === 'superadmin'): ?>
                        <a href="/dashboards/superadmin_dashboard.php" class="nav-item py-2 <?= $current_page === 'superadmin_dashboard.php' ? 'active' : '' ?>">
                            <span>Dashboard</span>
                        </a>
                        <a href="/dashboards/list_colleges.php" class="nav-item py-2 <?= $current_page === 'list_colleges.php' || $current_page === 'create_college.php' ? 'active' : '' ?>">
                            <span>Colleges</span>
                        </a>
                        <a href="/dashboards/list_admins.php" class="nav-item py-2 <?= $current_page === 'list_admins.php' ? 'active' : '' ?>">
                            <span>Admins</span>
                        </a>
                        <a href="/dashboards/platform_analytics.php" class="nav-item py-2 <?= $current_page === 'platform_analytics.php' ? 'active' : '' ?>">
                            <span>Analytics</span>
                        </a>
                    <?php elseif ($role === 'collegeadmin'): ?>
                        <a href="/dashboards/collegeadmin_dashboard.php" class="nav-item py-2 <?= $current_page === 'collegeadmin_dashboard.php' ? 'active' : '' ?>">
                            <span>Dashboard</span>
                        </a>
                        <a href="/dashboards/list_faculty.php" class="nav-item py-2 <?= $current_page === 'list_faculty.php' ? 'active' : '' ?>">
                            <span>Faculty</span>
                        </a>
                        <a href="/dashboards/list_students.php" class="nav-item py-2 <?= $current_page === 'list_students.php' ? 'active' : '' ?>">
                            <span>Students</span>
                        </a>
                        <a href="/dashboards/list_alumni.php" class="nav-item py-2 <?= $current_page === 'list_alumni.php' ? 'active' : '' ?>">
                            <span>Alumni</span>
                    </a>
                    <?php elseif ($role === 'faculty'): ?>
                        <a href="/faculty/faculty_dashboard.php" class="nav-item py-2 <?= $current_page === 'faculty_dashboard.php' ? 'active' : '' ?>">
                            <span>Dashboard</span>
                        </a>
                        <a href="/faculty/approve_students.php" class="nav-item py-2 <?= $current_page === 'approve_students.php' ? 'active' : '' ?>">
                            <span>Students</span>
                        </a>
                        <a href="/faculty/invite_alumni.php" class="nav-item py-2 <?= $current_page === 'invite_alumni.php' ? 'active' : '' ?>">
                            <span>Alumni</span>
                        </a>
                    <?php elseif ($role === 'student'): ?>
                        <a href="/student/student_dashboard.php" class="nav-item py-2 <?= $current_page === 'student_dashboard.php' ? 'active' : '' ?>">
                            <span>Dashboard</span>
                        </a>
                        <a href="/student/view_alumni.php" class="nav-item py-2 <?= $current_page === 'view_alumni.php' ? 'active' : '' ?>">
                            <span>Alumni</span>
                        </a>
                        <a href="/student/request_mentor.php" class="nav-item py-2 <?= $current_page === 'request_mentor.php' ? 'active' : '' ?>">
                            <span>Mentorship</span>
                        </a>
                        <a href="/events/event_list.php" class="nav-item py-2 <?= $current_page === 'event_list.php' ? 'active' : '' ?>">
                            <span>Events</span>
                        </a>
                    <?php elseif ($role === 'alumni'): ?>
                        <a href="/alumni/alumni_dashboard.php" class="nav-item py-2 <?= $current_page === 'alumni_dashboard.php' ? 'active' : '' ?>">
                            <span>Dashboard</span>
                        </a>
                        <a href="/alumni/incoming_requests.php" class="nav-item py-2 <?= $current_page === 'incoming_requests.php' ? 'active' : '' ?>">
                            <span>Mentorship</span>
                        </a>
                        <a href="/alumni/availability.php" class="nav-item py-2 <?= $current_page === 'availability.php' ? 'active' : '' ?>">
                            <span>Availability</span>
                        </a>
                        <a href="/events/event_list.php" class="nav-item py-2 <?= $current_page === 'event_list.php' ? 'active' : '' ?>">
                            <span>Events</span>
                    </a>
                <?php endif; ?>
            </nav>
            
                <!-- Right Side Controls -->
                <div class="flex items-center space-x-4">
                    <!-- Search Button -->
                    <?php if (in_array($role, ['collegeadmin', 'faculty', 'student', 'alumni'])): ?>
                    <button data-search-modal class="text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary-light p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                    </button>
                    <?php endif; ?>
                    
                    <!-- Notifications -->
                    <div class="relative">
                        <button class="text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary-light p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                            </svg>
                        </button>
                    </div>
                    
                    <!-- Messages -->
                    <?php if (in_array($role, ['student', 'alumni', 'faculty'])): ?>
                    <a href="/chat/chat.php" class="text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary-light p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                        </svg>
                    </a>
                    <?php endif; ?>
                    
                    <!-- Dark Mode Toggle -->
                    <button id="themeToggle" class="text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary-light p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 dark:hidden" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
                        </svg>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 hidden dark:block" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                        </svg>
                    </button>
                    
                    <!-- User Menu -->
                    <div class="relative" id="userMenu">
                        <button id="userMenuButton" class="flex items-center text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary-light">
                            <div class="w-8 h-8 rounded-full bg-primary-light text-primary flex items-center justify-center font-medium mr-2">
                                <?= substr($user_name, 0, 1) ?>
                            </div>
                            <span class="hidden sm:inline-block"><?= htmlspecialchars($user_name) ?></span>
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </button>
                        
                        <!-- Dropdown Menu -->
                        <div id="userMenuDropdown" class="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg py-1 border border-gray-200 dark:border-gray-700 hidden transition-all duration-200 opacity-0 transform origin-top-right scale-95">
                            <div class="px-4 py-2 border-b border-gray-200 dark:border-gray-700">
                                <p class="text-sm font-medium text-gray-700 dark:text-gray-300">Signed in as</p>
                                <p class="text-sm font-bold text-gray-900 dark:text-white"><?= ucfirst($role) ?></p>
                                    </div>
                            <a href="<?= $role ?>/<?= $role ?>_profile.php" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                <i class="fas fa-user mr-2 text-gray-400 dark:text-gray-500"></i> Profile
                            </a>
                            <a href="/settings.php" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                <i class="fas fa-cog mr-2 text-gray-400 dark:text-gray-500"></i> Settings
                            </a>
                            <div class="border-t border-gray-200 dark:border-gray-700"></div>
                            <a href="/auth/logout.php" class="block px-4 py-2 text-sm text-red-600 dark:text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20">
                                <i class="fas fa-sign-out-alt mr-2"></i> Sign out
                            </a>
                                    </div>
                                </div>
                                
                    <!-- Mobile Menu Button -->
                    <button id="mobileMenuButton" class="md:hidden text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 focus:outline-none">
                        <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>
                </div>
                            </div>
                            
            <!-- Mobile Navigation Menu -->
            <div id="mobileMenu" class="md:hidden hidden pb-3">
                <div class="pt-2 space-y-1">
                                <?php if ($role === 'superadmin'): ?>
                        <a href="/dashboards/superadmin_dashboard.php" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 <?= $current_page === 'superadmin_dashboard.php' ? 'bg-gray-100 dark:bg-gray-700 text-primary dark:text-primary-light' : '' ?>">
                            <i class="fas fa-th-large mr-2"></i> Dashboard
                        </a>
                        <a href="/dashboards/list_colleges.php" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 <?= $current_page === 'list_colleges.php' ? 'bg-gray-100 dark:bg-gray-700 text-primary dark:text-primary-light' : '' ?>">
                            <i class="fas fa-university mr-2"></i> Colleges
                        </a>
                        <a href="/dashboards/list_admins.php" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 <?= $current_page === 'list_admins.php' ? 'bg-gray-100 dark:bg-gray-700 text-primary dark:text-primary-light' : '' ?>">
                            <i class="fas fa-user-shield mr-2"></i> Admins
                        </a>
                        <a href="/dashboards/platform_analytics.php" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 <?= $current_page === 'platform_analytics.php' ? 'bg-gray-100 dark:bg-gray-700 text-primary dark:text-primary-light' : '' ?>">
                            <i class="fas fa-chart-line mr-2"></i> Analytics
                        </a>
                        <a href="/dashboards/system_check.php" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 <?= $current_page === 'system_check.php' ? 'bg-gray-100 dark:bg-gray-700 text-primary dark:text-primary-light' : '' ?>">
                            <i class="fas fa-sync-alt mr-2"></i> System Check
                        </a>
                                <?php elseif ($role === 'collegeadmin'): ?>
                        <!-- College Admin Mobile Menu Items -->
                        <a href="/dashboards/collegeadmin_dashboard.php" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 <?= $current_page === 'collegeadmin_dashboard.php' ? 'bg-gray-100 dark:bg-gray-700 text-primary dark:text-primary-light' : '' ?>">
                            <i class="fas fa-tachometer-alt mr-2"></i> Dashboard
                        </a>
                        <a href="/dashboards/list_faculty.php" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 <?= $current_page === 'list_faculty.php' ? 'bg-gray-100 dark:bg-gray-700 text-primary dark:text-primary-light' : '' ?>">
                            <i class="fas fa-chalkboard-teacher mr-2"></i> Faculty
                        </a>
                        <a href="/dashboards/list_students.php" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 <?= $current_page === 'list_students.php' ? 'bg-gray-100 dark:bg-gray-700 text-primary dark:text-primary-light' : '' ?>">
                            <i class="fas fa-user-graduate mr-2"></i> Students
                        </a>
                        <a href="/dashboards/list_alumni.php" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 <?= $current_page === 'list_alumni.php' ? 'bg-gray-100 dark:bg-gray-700 text-primary dark:text-primary-light' : '' ?>">
                            <i class="fas fa-user-tie mr-2"></i> Alumni
                        </a>
                        <a href="/campaigns/manage_campaigns.php" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 <?= $current_page === 'manage_campaigns.php' ? 'bg-gray-100 dark:bg-gray-700 text-primary dark:text-primary-light' : '' ?>">
                            <i class="fas fa-gift mr-2"></i> Campaigns
                        </a>
                        <a href="/events/event_list.php" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 <?= $current_page === 'event_list.php' ? 'bg-gray-100 dark:bg-gray-700 text-primary dark:text-primary-light' : '' ?>">
                            <i class="fas fa-calendar-alt mr-2"></i> Events
                        </a>
                                <?php elseif ($role === 'faculty'): ?>
                        <!-- Faculty Mobile Menu Items -->
                                <?php elseif ($role === 'student'): ?>
                        <!-- Student Mobile Menu Items -->
                                <?php elseif ($role === 'alumni'): ?>
                        <!-- Alumni Mobile Menu Items -->
                                <?php endif; ?>
                                </div>
                            </div>
                        </div>
    </header>

    <!-- Main Content Area -->
    <div class="flex flex-1 overflow-hidden">
        <?php if ($role): // Only show sidebar if user is logged in ?>
        <!-- Sidebar Navigation -->
        <aside id="sidebar" class="hidden lg:block w-64 border-r border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 overflow-y-auto">
            <div class="flex items-center justify-between px-6 pt-6 pb-4">
                <div>
                    <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-1"><?= ucfirst($role) ?></h2>
                    <p class="text-sm text-gray-500 dark:text-gray-400">
                        <?php
                        echo $role === 'superadmin' ? 'Platform Management' :
                            ($role === 'collegeadmin' ? 'College Management' :
                            ($role === 'faculty' ? 'Faculty Portal' :
                            ($role === 'student' ? 'Student Portal' : 'Alumni Portal')));
                        ?>
                    </p>
                    </div>
            </div>
            
            <nav class="mt-4 px-3 space-y-1">
                <?php include_once "sidebar_{$role}.php"; ?>
            </nav>
        </aside>
        <?php endif; ?>

        <!-- Main Content -->
        <main class="flex-1 bg-gray-50 dark:bg-gray-900 overflow-auto p-6">
        
<script>
// User dropdown menu functionality
document.addEventListener('DOMContentLoaded', function() {
    const userMenuButton = document.getElementById('userMenuButton');
    const userMenuDropdown = document.getElementById('userMenuDropdown');
    const userMenu = document.getElementById('userMenu');
    
    if (userMenuButton && userMenuDropdown && userMenu) {
        // Function to show the dropdown
        function showDropdown() {
            userMenuDropdown.classList.remove('hidden', 'opacity-0', 'scale-95');
            userMenuDropdown.classList.add('opacity-100', 'scale-100');
        }
        
        // Function to hide the dropdown
        function hideDropdown() {
            userMenuDropdown.classList.add('opacity-0', 'scale-95');
            setTimeout(() => {
                userMenuDropdown.classList.add('hidden');
            }, 200);
        }
        
        // Toggle dropdown on button click
        userMenuButton.addEventListener('click', function(e) {
            e.stopPropagation();
            if (userMenuDropdown.classList.contains('hidden')) {
                showDropdown();
            } else {
                hideDropdown();
            }
        });
        
        // Show dropdown on hover (only on non-touch devices)
        if (window.matchMedia('(hover: hover)').matches) {
            userMenu.addEventListener('mouseenter', showDropdown);
            userMenu.addEventListener('mouseleave', hideDropdown);
        }
        
        // Hide dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (!userMenu.contains(e.target)) {
                hideDropdown();
            }
        });
    }
    
    // Theme toggle functionality
    const themeToggle = document.getElementById('themeToggle');
    
    if (themeToggle) {
        themeToggle.addEventListener('click', function() {
            document.documentElement.classList.toggle('dark');
            localStorage.setItem('theme', document.documentElement.classList.contains('dark') ? 'dark' : 'light');
        });
    }
    
    // Set the initial theme based on user preference or system preference
    if (localStorage.theme === 'dark' || (!('theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
        document.documentElement.classList.add('dark');
    } else {
        document.documentElement.classList.remove('dark');
    }
    
    // Mobile menu functionality
    const mobileMenuButton = document.getElementById('mobileMenuButton');
    const mobileMenu = document.getElementById('mobileMenu');
    
    if (mobileMenuButton && mobileMenu) {
        mobileMenuButton.addEventListener('click', function() {
            mobileMenu.classList.toggle('hidden');
        });
    }
});
</script>
