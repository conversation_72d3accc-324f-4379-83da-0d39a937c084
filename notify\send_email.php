<?php
use <PERSON><PERSON><PERSON>ailer\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ailer\PHPMailer\Exception;

require __DIR__ . '/../vendor/autoload.php'; // Ensure PHPMailer is installed via Composer

function sendEmail($to, $toName, $subject, $bodyHtml) {
    $mail = new PHPMailer(true);

    try {
        // SMTP Configuration
        $mail->isSMTP();
        $mail->Host = 'smtp.gmail.com';
        $mail->SMTPAuth = true;
        $mail->Username = '<EMAIL>'; // Your Gmail
        $mail->Password = 'xoqrvepcjjpuycgo'; // App Password
        $mail->SMTPSecure = 'tls';
        $mail->Port = 587;

        // Email Setup
        $mail->setFrom('<EMAIL>', 'Connect My Students');
        $mail->addAddress($to, $toName);
        $mail->isHTML(true);
        $mail->Subject = $subject;
        $mail->Body = $bodyHtml;

        $mail->send();
        return true;
    } catch (Exception $e) {
        error_log("Mail Error: " . $mail->ErrorInfo);
        return false;
    }
}
?>
