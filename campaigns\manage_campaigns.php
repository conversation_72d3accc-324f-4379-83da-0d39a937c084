<?php
require_once '../includes/db.php';
require_once '../includes/campaign_helpers.php';
require_once '../notify/send_email.php';

// Check session
if (!isset($_SESSION['faculty_id']) && !isset($_SESSION['collegeadmin_id'])) {
    // Redirect to appropriate login page instead of logout
    if (isset($_SERVER['HTTP_REFERER']) && strpos($_SERVER['HTTP_REFERER'], 'collegeadmin') !== false) {
        header("Location: ../auth/collegeadmin_login.php");
    } else if (isset($_SERVER['HTTP_REFERER']) && strpos($_SERVER['HTTP_REFERER'], 'faculty') !== false) {
        header("Location: ../auth/faculty_login.php");
    } else {
        header("Location: ../auth/collegeadmin_login.php");
    }
    exit;
}

$college_id = $_SESSION['college_id'] ?? null;

// Set role explicitly for the sidebar
if (isset($_SESSION['collegeadmin_id'])) {
    $role = 'collegeadmin';
    $user_id = $_SESSION['collegeadmin_id'];
    $user_name = $_SESSION['collegeadmin_name'] ?? 'Admin';
    $is_admin = true;
    $is_faculty = false;
} elseif (isset($_SESSION['faculty_id'])) {
    $role = 'faculty';
    $user_id = $_SESSION['faculty_id'];
    $user_name = $_SESSION['faculty_name'] ?? 'Faculty';
    $is_admin = false;
    $is_faculty = true;
}
$current_page = basename($_SERVER['PHP_SELF']);

if (!$user_id || !$college_id) {
    header("Location: ../auth/collegeadmin_login.php");
    exit;
}

// Handle Approval/Rejection/Closure
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $campaign_id = $_POST['campaign_id'];
    $action = $_POST['action'];

    if ($action === 'approve') {
        $stmt = $conn->prepare("UPDATE deals SET status = 'approved', approval_remark = NULL WHERE id = ?");
        $stmt->execute([$campaign_id]);
    } elseif ($action === 'reject') {
        $remark = trim($_POST['remark']);
        $stmt = $conn->prepare("UPDATE deals SET status = 'rejected', approval_remark = ? WHERE id = ?");
        $stmt->execute([$remark, $campaign_id]);
    } elseif ($action === 'close') {
        $stmt = $conn->prepare("UPDATE deals SET status = 'closed' WHERE id = ?");
        $stmt->execute([$campaign_id]);
    }
}

// Fetch campaigns for this college with creator names
$stmt = $conn->prepare("
    SELECT d.*, 
        CASE 
            WHEN d.creator_role = 'faculty' THEN f.name 
            WHEN d.creator_role = 'admin' THEN ca.name 
            ELSE 'Unknown' 
        END AS creator_name 
    FROM deals d 
    LEFT JOIN faculties f ON d.created_by_user_id = f.id AND d.creator_role = 'faculty' 
    LEFT JOIN college_admins ca ON d.created_by_user_id = ca.id AND d.creator_role = 'admin' 
    WHERE d.college_id = ? 
    ORDER BY d.created_at DESC
");
$stmt->execute([$college_id]);
$campaigns = $stmt->fetchAll(PDO::FETCH_ASSOC);

require_once '../includes/header.php';
?>

<!-- Main Content -->
<main class="flex-grow py-8 px-4">
    <div class="container mx-auto">
        <!-- Breadcrumb -->
        <nav class="mb-6 text-sm" aria-label="Breadcrumb">
            <ol class="flex items-center space-x-2">
                <li>
                    <a href="<?= $role === 'collegeadmin' ? '../dashboards/collegeadmin_dashboard.php' : '../dashboards/faculty_dashboard.php' ?>" class="text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary-light">
                        <i class="fas fa-home"></i>
                        <span class="sr-only">Dashboard</span>
                    </a>
                </li>
                <li class="flex items-center">
                    <i class="fas fa-chevron-right text-gray-400 text-xs mx-1"></i>
                    <span class="text-gray-700 dark:text-gray-300">Manage Campaigns</span>
                </li>
            </ol>
        </nav>

        <div class="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                    <i class="fas fa-bullhorn text-primary mr-3"></i>
                    Manage Campaigns
                </h1>
                <p class="text-gray-600 dark:text-gray-400 mt-1">
                    Review, approve, and manage fundraising campaigns
                </p>
            </div>
            <div class="mt-4 md:mt-0">
                <a href="create_campaign.php" class="btn btn-primary flex items-center">
                    <i class="fas fa-plus mr-2"></i> Create Campaign
                </a>
            </div>
        </div>

        <?php if (empty($campaigns)): ?>
            <div class="bg-gray-50 dark:bg-gray-700/30 border border-gray-200 dark:border-gray-700 rounded-lg p-6 text-center">
                <div class="flex flex-col items-center">
                    <div class="h-16 w-16 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center text-gray-400 dark:text-gray-500 mb-4">
                        <i class="fas fa-bullhorn text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-1">No Campaigns Found</h3>
                    <p class="text-gray-500 dark:text-gray-400">
                        There are no campaigns created yet. Click the button above to create your first campaign.
                    </p>
                </div>
            </div>
        <?php else: ?>
            <div class="space-y-6">
                <?php foreach ($campaigns as $campaign): ?>
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 overflow-hidden">
                        <div class="md:flex">
                            <div class="md:w-1/3 lg:w-1/4">
                                <?php if ($campaign['thumbnail_path'] && file_exists('../' . $campaign['thumbnail_path'])): ?>
                                    <img src="../<?= htmlspecialchars($campaign['thumbnail_path']) ?>" 
                                        alt="<?= htmlspecialchars($campaign['title']) ?>" 
                                        class="h-48 md:h-full w-full object-cover">
                                <?php else: ?>
                                    <div class="h-48 md:h-full w-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center text-gray-400 dark:text-gray-500">
                                        <i class="fas fa-image text-4xl"></i>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="md:w-2/3 lg:w-3/4 p-6">
                                <div class="flex flex-col h-full">
                                    <div class="flex flex-wrap items-center gap-2 mb-2">
                                        <h2 class="text-xl font-bold text-gray-900 dark:text-white mr-2">
                                            <?= htmlspecialchars($campaign['title']) ?>
                                        </h2>
                                        <?php if ($campaign['status'] === 'pending'): ?>
                                            <span class="px-2 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-500">
                                                Pending
                                            </span>
                                        <?php elseif ($campaign['status'] === 'approved'): ?>
                                            <span class="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-500">
                                                Approved
                                            </span>
                                        <?php elseif ($campaign['status'] === 'rejected'): ?>
                                            <span class="px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-500">
                                                Rejected
                                            </span>
                                        <?php elseif ($campaign['status'] === 'closed'): ?>
                                            <span class="px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-400">
                                                Closed
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-md text-sm font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400">
                                            <?= ucfirst($campaign['category']) ?>
                                        </span>
                                        
                                        <?php 
                                        $types = explode(',', $campaign['contribution_types']);
                                        foreach ($types as $type): 
                                            $color = match($type) {
                                                'monetary' => 'green',
                                                'in-kind' => 'purple',
                                                'service' => 'orange',
                                                default => 'gray'
                                            };
                                        ?>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-md text-sm font-medium bg-<?= $color ?>-100 text-<?= $color ?>-800 dark:bg-<?= $color ?>-900/30 dark:text-<?= $color ?>-400 ml-2">
                                                <?= ucfirst($type) ?>
                                            </span>
                                        <?php endforeach; ?>
                                    </div>
                                    
                                    <p class="text-gray-600 dark:text-gray-300 mb-4 flex-grow">
                                        <?= nl2br(htmlspecialchars(substr($campaign['description'], 0, 200))) ?>
                                        <?= strlen($campaign['description']) > 200 ? '...' : '' ?>
                                    </p>
                                    
                                    <div class="mt-auto">
                                        <div class="text-sm text-gray-500 dark:text-gray-400 mb-4">
                                            <span class="flex items-center">
                                                <i class="fas fa-user mr-2"></i>
                                                <?= htmlspecialchars($campaign['creator_name']) ?> (<?= ucfirst($campaign['creator_role']) ?>)
                                            </span>
                                            <span class="flex items-center mt-1">
                                                <i class="fas fa-calendar-alt mr-2"></i>
                                                <?= date("F j, Y", strtotime($campaign['created_at'])) ?>
                                            </span>
                                        </div>
                                        
                                        <?php if ($campaign['status'] === 'pending' && $is_admin): ?>
                                            <form method="POST" class="flex flex-wrap gap-2">
                                                <input type="hidden" name="campaign_id" value="<?= $campaign['id'] ?>">
                                                <button name="action" value="approve" class="btn btn-sm btn-success">
                                                    <i class="fas fa-check mr-1"></i> Approve
                                                </button>
                                                <button type="button" class="btn btn-sm btn-danger" 
                                                    onclick="toggleRemark('remark-<?= $campaign['id'] ?>')">
                                                    <i class="fas fa-times mr-1"></i> Reject
                                                </button>
                                                <div id="remark-<?= $campaign['id'] ?>" class="hidden mt-2 w-full">
                                                    <textarea name="remark" class="form-input w-full mb-2" 
                                                        placeholder="Reason for rejection" rows="2"></textarea>
                                                    <button name="action" value="reject" class="btn btn-sm btn-outline-danger">
                                                        Confirm Reject
                                                    </button>
                                                </div>
                                            </form>
                                        <?php elseif ($campaign['status'] === 'approved'): ?>
                                            <div class="flex flex-wrap gap-2">
                                                <?php if ($is_admin): ?>
                                                <form method="POST">
                                                    <input type="hidden" name="campaign_id" value="<?= $campaign['id'] ?>">
                                                    <button name="action" value="close" class="btn btn-sm btn-warning">
                                                        <i class="fas fa-archive mr-1"></i> Close Campaign
                                                    </button>
                                                </form>
                                                <?php endif; ?>
                                                <a href="view_contributions.php?campaign_id=<?= $campaign['id'] ?>" class="btn btn-sm btn-outline-info">
                                                    <i class="fas fa-list-alt mr-1"></i> View Contributions
                                                </a>
                                                <?php if ($campaign['creator_role'] === $role && $campaign['created_by_user_id'] == $user_id): ?>
                                                <a href="edit_campaign.php?campaign_id=<?= $campaign['id'] ?>" class="btn btn-sm btn-outline">
                                                    <i class="fas fa-edit mr-1"></i> Edit
                                                </a>
                                                <?php endif; ?>
                                            </div>
                                        <?php elseif ($campaign['status'] === 'rejected' && !empty($campaign['approval_remark'])): ?>
                                            <div class="bg-red-50 dark:bg-red-900/20 border-l-4 border-red-500 text-red-700 dark:text-red-400 p-3 text-sm">
                                                <strong>Rejection Reason:</strong> <?= htmlspecialchars($campaign['approval_remark']) ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
</main>

<script>
function toggleRemark(id) {
    const el = document.getElementById(id);
    el.classList.toggle('hidden');
}
</script>

<?php require_once '../includes/footer.php'; ?>
