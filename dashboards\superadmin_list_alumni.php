<?php
// Include session configuration
require_once '../includes/session_config.php';
require_once '../includes/db.php';
require_once '../includes/header.php';

// Ensure the user is logged in as a super admin
if (!isset($_SESSION['superadmin_id'])) {
    header("Location: ../auth/superadmin_login.php");
    exit;
}

// Pagination setup
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 15; // Items per page
$offset = ($page - 1) * $limit;

// Search and filter functionality
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$college_filter = isset($_GET['college_id']) ? (int)$_GET['college_id'] : 0;
$industry_filter = isset($_GET['industry']) ? trim($_GET['industry']) : '';
$graduation_year_filter = isset($_GET['graduation_year']) ? trim($_GET['graduation_year']) : '';
$verified_filter = isset($_GET['verified']) ? $_GET['verified'] : '';

// Build query conditions
$conditions = ["1=1"]; // Always true condition to start with
$params = [];

if (!empty($search)) {
    $conditions[] = "(full_name LIKE ? OR email LIKE ? OR company LIKE ? OR job_title LIKE ?)";
    $searchParam = "%$search%";
    $params = array_merge($params, [$searchParam, $searchParam, $searchParam, $searchParam]);
}

if ($college_filter > 0) {
    $conditions[] = "college_id = ?";
    $params[] = $college_filter;
}

if (!empty($industry_filter)) {
    $conditions[] = "industry = ?";
    $params[] = $industry_filter;
}

if (!empty($graduation_year_filter)) {
    $conditions[] = "graduation_year = ?";
    $params[] = $graduation_year_filter;
}

if ($verified_filter !== '') {
    $conditions[] = "verified = ?";
    $params[] = $verified_filter;
}

$whereClause = implode(' AND ', $conditions);

// Count total alumni for pagination
$countSql = "SELECT COUNT(*) FROM alumni WHERE $whereClause";
$countStmt = $conn->prepare($countSql);
$countStmt->execute($params);
$totalAlumni = $countStmt->fetchColumn();
$totalPages = ceil($totalAlumni / $limit);

// Fetch alumni with pagination and filters
$sql = "SELECT a.*, c.name as college_name 
        FROM alumni a
        LEFT JOIN colleges c ON a.college_id = c.id
        WHERE $whereClause 
        ORDER BY a.created_at DESC 
        LIMIT $limit OFFSET $offset";
$stmt = $conn->prepare($sql);
$stmt->execute($params);
$alumni_list = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get all colleges for filter
$collegeStmt = $conn->prepare("SELECT id, name FROM colleges ORDER BY name ASC");
$collegeStmt->execute();
$colleges = $collegeStmt->fetchAll(PDO::FETCH_ASSOC);

// Get industries for filter
$industryStmt = $conn->prepare("SELECT DISTINCT industry FROM alumni WHERE industry IS NOT NULL AND industry != '' ORDER BY industry ASC");
$industryStmt->execute();
$industries = $industryStmt->fetchAll(PDO::FETCH_COLUMN);

// Get graduation years for filter
$yearStmt = $conn->prepare("SELECT DISTINCT graduation_year FROM alumni WHERE graduation_year IS NOT NULL AND graduation_year != '' ORDER BY graduation_year DESC");
$yearStmt->execute();
$graduation_years = $yearStmt->fetchAll(PDO::FETCH_COLUMN);

// Get statistics
$totalVerifiedStmt = $conn->prepare("SELECT COUNT(*) FROM alumni WHERE verified = 1");
$totalVerifiedStmt->execute();
$totalVerified = $totalVerifiedStmt->fetchColumn();

$totalUnverifiedStmt = $conn->prepare("SELECT COUNT(*) FROM alumni WHERE verified = 0");
$totalUnverifiedStmt->execute();
$totalUnverified = $totalUnverifiedStmt->fetchColumn();

// Count alumni who are mentoring students
$mentorCount = 0;
try {
    $mentorStmt = $conn->prepare("SELECT COUNT(DISTINCT alumni_id) FROM mentorships WHERE status = 'active'");
    $mentorStmt->execute();
    $mentorCount = $mentorStmt->fetchColumn();
} catch (PDOException $e) {
    // Table might not exist, ignore
}
?>

<!-- Main Content -->
<div class="container mx-auto px-4 py-8 max-w-7xl">
    <!-- Page Header -->
    <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                <span class="bg-purple-500/10 dark:bg-purple-500/20 p-2 rounded-md mr-3">
                    <i class="fas fa-user-tie text-purple-600 dark:text-purple-400"></i>
                </span>
                Alumni Directory
            </h1>
            <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">View and manage all alumni across all colleges</p>
        </div>
        <div class="mt-4 md:mt-0 flex flex-wrap gap-2">
            <a href="superadmin_dashboard.php" class="btn btn-outline flex items-center">
                <i class="fas fa-arrow-left mr-2"></i> Back to Dashboard
            </a>
            <a href="#" class="btn btn-outline flex items-center">
                <i class="fas fa-file-export mr-2"></i> Export Data
            </a>
        </div>
    </div>
    
    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <!-- Total Alumni -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400 mr-4">
                    <i class="fas fa-users"></i>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Alumni</p>
                    <p class="text-2xl font-semibold text-gray-900 dark:text-white"><?= number_format($totalAlumni) ?></p>
                </div>
            </div>
        </div>
        
        <!-- Verified Alumni -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 mr-4">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Verified Alumni</p>
                    <p class="text-2xl font-semibold text-gray-900 dark:text-white"><?= number_format($totalVerified) ?></p>
                </div>
            </div>
        </div>
        
        <!-- Mentoring Alumni -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 mr-4">
                    <i class="fas fa-handshake"></i>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Active Mentors</p>
                    <p class="text-2xl font-semibold text-gray-900 dark:text-white"><?= number_format($mentorCount) ?></p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Filters & Search -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6 mb-8">
        <div class="flex flex-col md:flex-row md:items-end md:justify-between gap-4">
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 flex-grow">
                <!-- Search Input -->
                <div>
                    <label for="search" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Search</label>
                    <div class="relative">
                        <input type="text" id="search" class="form-input pl-10 w-full" placeholder="Name, email, company..." value="<?= htmlspecialchars($search) ?>">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-search text-gray-400 text-sm"></i>
                        </div>
                    </div>
                </div>
                
                <!-- College Filter -->
                <div>
                    <label for="college-filter" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">College</label>
                    <select id="college-filter" class="form-input">
                        <option value="">All Colleges</option>
                        <?php foreach ($colleges as $college): ?>
                            <option value="<?= $college['id'] ?>" <?= $college_filter == $college['id'] ? 'selected' : '' ?>>
                                <?= htmlspecialchars($college['name']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <!-- Industry Filter -->
                <div>
                    <label for="industry-filter" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Industry</label>
                    <select id="industry-filter" class="form-input">
                        <option value="">All Industries</option>
                        <?php foreach ($industries as $industry): ?>
                            <option value="<?= $industry ?>" <?= $industry_filter === $industry ? 'selected' : '' ?>>
                                <?= htmlspecialchars($industry) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <!-- Graduation Year Filter -->
                <div>
                    <label for="graduation-year-filter" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Graduation Year</label>
                    <select id="graduation-year-filter" class="form-input">
                        <option value="">All Years</option>
                        <?php foreach ($graduation_years as $year): ?>
                            <option value="<?= $year ?>" <?= $graduation_year_filter === $year ? 'selected' : '' ?>>
                                <?= htmlspecialchars($year) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
            </div>
            
            <div class="flex space-x-2">
                <button id="reset-filters" class="btn btn-outline">
                    <i class="fas fa-times mr-2"></i> Clear
                </button>
                <button id="apply-filters" class="btn btn-primary">
                    <i class="fas fa-filter mr-2"></i> Apply Filters
                </button>
            </div>
        </div>
    </div>
    
    <!-- Alumni Table -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 overflow-hidden mb-6">
        <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4 flex justify-between items-center">
            <h2 class="font-semibold text-gray-800 dark:text-white flex items-center">
                <i class="fas fa-list mr-2 text-primary"></i> Alumni List
                <span class="ml-2 px-2.5 py-0.5 bg-gray-100 dark:bg-gray-700 rounded-full text-xs font-medium text-gray-800 dark:text-gray-300">
                    <?= $totalAlumni ?> total
                </span>
            </h2>
            <div class="flex items-center">
                <span class="text-sm text-gray-600 dark:text-gray-400 mr-2">Status:</span>
                <select id="verified-filter" class="form-input py-1 pl-2 pr-8 text-sm">
                    <option value="">All Alumni</option>
                    <option value="1" <?= $verified_filter === '1' ? 'selected' : '' ?>>Verified</option>
                    <option value="0" <?= $verified_filter === '0' ? 'selected' : '' ?>>Pending</option>
                </select>
            </div>
        </div>
        
        <div class="overflow-x-auto">
            <?php if (count($alumni_list) > 0): ?>
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead>
                        <tr>
                            <th class="px-6 py-3 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Name</th>
                            <th class="px-6 py-3 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">College</th>
                            <th class="px-6 py-3 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Company & Position</th>
                            <th class="px-6 py-3 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Industry</th>
                            <th class="px-6 py-3 bg-gray-50 dark:bg-gray-700 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 bg-gray-50 dark:bg-gray-700 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                        <?php foreach ($alumni_list as $alumni): ?>
                            <tr class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="h-10 w-10 flex-shrink-0">
                                            <?php if (!empty($alumni['profile_image']) && file_exists('../uploads/' . $alumni['profile_image'])): ?>
                                                <img class="h-10 w-10 rounded-full object-cover" src="../uploads/<?= $alumni['profile_image'] ?>" alt="<?= htmlspecialchars($alumni['full_name']) ?>">
                                            <?php else: ?>
                                                <div class="h-10 w-10 rounded-full bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center">
                                                    <span class="font-medium text-purple-600 dark:text-purple-400">
                                                        <?= strtoupper(substr($alumni['full_name'] ?? 'A', 0, 1)) ?>
                                                    </span>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($alumni['full_name']) ?></div>
                                            <div class="text-sm text-gray-500 dark:text-gray-400"><?= htmlspecialchars($alumni['email']) ?></div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900 dark:text-white"><?= htmlspecialchars($alumni['college_name']) ?></div>
                                    <div class="text-xs text-gray-500 dark:text-gray-400">
                                        <?= !empty($alumni['graduation_year']) ? 'Class of ' . htmlspecialchars($alumni['graduation_year']) : 'Graduation year not set' ?>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900 dark:text-white">
                                        <?= !empty($alumni['company']) ? htmlspecialchars($alumni['company']) : 'Not specified' ?>
                                    </div>
                                    <div class="text-sm text-gray-500 dark:text-gray-400">
                                        <?= !empty($alumni['job_title']) ? htmlspecialchars($alumni['job_title']) : 'Position not specified' ?>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900 dark:text-white">
                                        <?= !empty($alumni['industry']) ? htmlspecialchars($alumni['industry']) : 'Not specified' ?>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-center">
                                    <?php if ($alumni['verified'] == 1): ?>
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400">
                                            Verified
                                        </span>
                                    <?php else: ?>
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400">
                                            Pending
                                        </span>
                                    <?php endif; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
                                    <a href="superadmin_view_alumni.php?id=<?= $alumni['id'] ?>" class="text-primary hover:text-primary-dark dark:hover:text-primary-light mr-3">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="superadmin_edit_alumni.php?id=<?= $alumni['id'] ?>" class="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-300 mr-3">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
                
                <!-- Pagination -->
                <?php if ($totalPages > 1): ?>
                <div class="px-6 py-4 flex items-center justify-between border-t border-gray-200 dark:border-gray-700">
                    <div class="text-sm text-gray-500 dark:text-gray-400">
                        Showing <?= $offset + 1 ?> to <?= min($offset + $limit, $totalAlumni) ?> of <?= $totalAlumni ?> alumni
                    </div>
                    <div class="flex space-x-2">
                        <?php if ($page > 1): ?>
                            <a href="?page=<?= $page - 1 ?>&search=<?= urlencode($search) ?>&college_id=<?= $college_filter ?>&industry=<?= urlencode($industry_filter) ?>&graduation_year=<?= urlencode($graduation_year_filter) ?>&verified=<?= $verified_filter ?>" class="px-3 py-1 rounded border border-gray-300 dark:border-gray-600 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700">
                                Previous
                            </a>
                        <?php else: ?>
                            <span class="px-3 py-1 rounded border border-gray-200 dark:border-gray-700 text-sm font-medium text-gray-400 dark:text-gray-600 cursor-not-allowed">
                                Previous
                            </span>
                        <?php endif; ?>
                        
                        <?php if ($page < $totalPages): ?>
                            <a href="?page=<?= $page + 1 ?>&search=<?= urlencode($search) ?>&college_id=<?= $college_filter ?>&industry=<?= urlencode($industry_filter) ?>&graduation_year=<?= urlencode($graduation_year_filter) ?>&verified=<?= $verified_filter ?>" class="px-3 py-1 rounded border border-gray-300 dark:border-gray-600 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700">
                                Next
                            </a>
                        <?php else: ?>
                            <span class="px-3 py-1 rounded border border-gray-200 dark:border-gray-700 text-sm font-medium text-gray-400 dark:text-gray-600 cursor-not-allowed">
                                Next
                            </span>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>
            <?php else: ?>
                <div class="text-center py-12">
                    <div class="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-user-tie text-gray-400 text-2xl"></i>
                    </div>
                    <h3 class="mt-2 text-lg font-medium text-gray-900 dark:text-white">No alumni found</h3>
                    <p class="mt-1 text-gray-500 dark:text-gray-400">
                        <?php if (!empty($search) || $college_filter > 0 || !empty($industry_filter) || !empty($graduation_year_filter) || $verified_filter !== ''): ?>
                            Try adjusting your filters or search criteria.
                        <?php else: ?>
                            There are no alumni registered in the system yet.
                        <?php endif; ?>
                    </p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('search');
    const collegeFilter = document.getElementById('college-filter');
    const industryFilter = document.getElementById('industry-filter');
    const graduationYearFilter = document.getElementById('graduation-year-filter');
    const verifiedFilter = document.getElementById('verified-filter');
    const applyFiltersBtn = document.getElementById('apply-filters');
    const resetFiltersBtn = document.getElementById('reset-filters');
    
    // Apply filters
    applyFiltersBtn.addEventListener('click', function() {
        applyFilters();
    });
    
    // Reset filters
    resetFiltersBtn.addEventListener('click', function() {
        searchInput.value = '';
        collegeFilter.value = '';
        industryFilter.value = '';
        graduationYearFilter.value = '';
        verifiedFilter.value = '';
        applyFilters();
    });
    
    // Listen for Enter key in search field
    searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            applyFilters();
        }
    });
    
    // Apply filters when verified filter changes
    verifiedFilter.addEventListener('change', function() {
        applyFilters();
    });
    
    function applyFilters() {
        let url = window.location.pathname + '?';
        
        if (searchInput.value) {
            url += 'search=' + encodeURIComponent(searchInput.value) + '&';
        }
        
        if (collegeFilter.value) {
            url += 'college_id=' + encodeURIComponent(collegeFilter.value) + '&';
        }
        
        if (industryFilter.value) {
            url += 'industry=' + encodeURIComponent(industryFilter.value) + '&';
        }
        
        if (graduationYearFilter.value) {
            url += 'graduation_year=' + encodeURIComponent(graduationYearFilter.value) + '&';
        }
        
        if (verifiedFilter.value) {
            url += 'verified=' + encodeURIComponent(verifiedFilter.value) + '&';
        }
        
        // Remove trailing &
        if (url.endsWith('&')) {
            url = url.slice(0, -1);
        }
        
        window.location.href = url;
    }
});
</script> 