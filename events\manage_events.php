<?php
ini_set('display_errors', 1);
error_reporting(E_ALL);
require_once '../includes/db.php';
require_once '../includes/header.php';
require_once '../vendor/phpmailer/phpmailer/src/PHPMailer.php';
require_once '../vendor/phpmailer/phpmailer/src/SMTP.php';
require_once '../vendor/phpmailer/phpmailer/src/Exception.php';
require_once '../vendor/autoload.php';

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;


if (!isset($_SESSION['faculty_id']) && !isset($_SESSION['alumni_id'])) {
    header("Location: ../auth/logout.php");
    exit;
}

$college_id = $_SESSION['college_id'];
$current_id = $_SESSION['faculty_id'] ?? $_SESSION['alumni_id'];
$current_role = isset($_SESSION['faculty_id']) ? 'faculty' : 'alumni';

// Fetch events created by current user
$stmt = $conn->prepare("SELECT * FROM events WHERE college_id = ? AND created_by_id = ? AND created_by_role = ? ORDER BY event_date ASC");
$stmt->execute([$college_id, $current_id, $current_role]);
$events = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Cancel Event
if (isset($_GET['cancel']) && is_numeric($_GET['cancel'])) {
    $event_id = intval($_GET['cancel']);

    $fetchStmt = $conn->prepare("SELECT title, created_by_id, created_by_role, google_event_id FROM events WHERE id = ?");
    $fetchStmt->execute([$event_id]);
    $event = $fetchStmt->fetch(PDO::FETCH_ASSOC);

    if ($event && $event['created_by_id'] == $current_id && $event['created_by_role'] == $current_role) {
        $title = $event['title'];
        $google_event_id = $event['google_event_id'];

        // Delete from Google Calendar
        if ($google_event_id && isset($_SESSION['google_access_token'])) {
            $client = new Google_Client();
            $client->setAuthConfig('../includes/google_credentials.json');
            $client->addScope(Google_Service_Calendar::CALENDAR);
            $client->setAccessToken($_SESSION['google_access_token']);

            $service = new Google_Service_Calendar($client);
            try {
                $service->events->delete('primary', $google_event_id);
            } catch (Exception $e) {
                // Ignore deletion errors
            }
        }

        // Delete from local database
        // Delete attendees first
        $deleteAttendees = $conn->prepare("DELETE FROM event_attendees WHERE event_id = ?");
        $deleteAttendees->execute([$event_id]);
        
        // Then delete event
        $deleteStmt = $conn->prepare("DELETE FROM events WHERE id = ?");
        $deleteStmt->execute([$event_id]);

        // Notify all users
        $users = [];

        $studentStmt = $conn->prepare("SELECT email FROM students WHERE college_id = ? AND verified = 1");
        $studentStmt->execute([$college_id]);
        $users = array_merge($users, $studentStmt->fetchAll(PDO::FETCH_COLUMN));

        $alumniStmt = $conn->prepare("SELECT email FROM alumni WHERE college_id = ? AND verified = 1");
        $alumniStmt->execute([$college_id]);
        $users = array_merge($users, $alumniStmt->fetchAll(PDO::FETCH_COLUMN));

        $facultyStmt = $conn->prepare("SELECT email FROM faculties WHERE college_id = ?");
        $facultyStmt->execute([$college_id]);
        $users = array_merge($users, $facultyStmt->fetchAll(PDO::FETCH_COLUMN));

        $mail = new PHPMailer(true);
        try {
            $mail->isSMTP();
            $mail->Host = 'smtp.gmail.com';
            $mail->SMTPAuth = true;
            $mail->Username = '<EMAIL>';
            $mail->Password = 'xoqrvepcjjpuycgo';
            $mail->SMTPSecure = 'tls';
            $mail->Port = 587;

            $mail->setFrom('<EMAIL>', 'Connect My Students');
            $mail->isHTML(true);
            $mail->Subject = "🚫 Event Cancelled: " . $title;
            $mail->Body = "
                <h3>🚫 Event Cancellation Notice</h3>
                <p><strong>Event:</strong> {$title}</p>
                <p>We regret to inform you that this event has been cancelled.</p><br>
                <a href='https://darkviolet-vulture-501696.hostingersite.com/events/event_list.php' style='background-color: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>View Events</a>
            ";

            foreach ($users as $email) {
                $mail->addAddress($email);
            }

            $mail->send();
        } catch (Exception $e) {
            // Silent fail
        }

        $_SESSION['flash_message'] = "Event cancelled successfully.";
    } else {
        $_SESSION['flash_message'] = "Unauthorized action.";
    }

    header("Location: manage_events.php");
    exit;
}
?>

<!-- HTML Output -->
<div class="container mt-5">
    <h2 class="mb-4">Manage Your Events</h2>

    <?php if (isset($_SESSION['flash_message'])): ?>
        <div class="alert alert-success"><?= $_SESSION['flash_message']; unset($_SESSION['flash_message']); ?></div>
    <?php endif; ?>

    <?php if (count($events) > 0): ?>
        <div class="table-responsive card shadow p-3">
            <table class="table table-bordered">
                <thead class="table-light">
                    <tr>
                        <th>Title</th>
                        <th>Speaker</th>
                        <th>Date</th>
                        <th>Time</th>
                        <th>Participants</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($events as $e): ?>
                        <tr>
                            <td><?= htmlspecialchars($e['title']) ?></td>
                            <td><?= htmlspecialchars($e['speaker_name']) ?></td>
                            <td><?= htmlspecialchars($e['event_date']) ?></td>
                            <td><?= htmlspecialchars(date("g:i A", strtotime($e['event_time']))) ?></td>
                            <td>
                                <?php
                                $participantStmt = $conn->prepare("SELECT COUNT(*) FROM event_attendees WHERE event_id = ?");
                                $participantStmt->execute([$e['id']]);
                                echo $participantStmt->fetchColumn();
                                ?>
                            </td>
                            <td>
                                <a href="view_participants.php?event_id=<?= $e['id'] ?>" class="btn btn-outline-primary btn-sm">View Participants</a>
                                <a href="?cancel=<?= $e['id'] ?>" class="btn btn-danger btn-sm" onclick="return confirm('Are you sure you want to cancel this event?')">Cancel</a>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php else: ?>
        <div class="alert alert-info">You have not created any events yet.</div>
    <?php endif; ?>
</div>

<?php require_once '../includes/footer.php'; ?>
