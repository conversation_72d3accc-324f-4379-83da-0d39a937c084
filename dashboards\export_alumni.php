<?php
// Include session configuration
require_once '../includes/session_config.php';
require_once '../includes/db.php';
require_once '../includes/db_schema_check.php';

if (!isset($_SESSION['collegeadmin_id'])) {
    header("Location: ../auth/collegeadmin_login.php");
    exit;
}

$college_id = $_SESSION['college_id'];
$admin_name = $_SESSION['collegeadmin_name'] ?? 'Admin';

// Set role explicitly for the sidebar
$role = 'collegeadmin';
$user_id = $_SESSION['collegeadmin_id'];
$user_name = $_SESSION['collegeadmin_name'];
$current_page = basename($_SERVER['PHP_SELF']);

// Get college details
$collegeStmt = $conn->prepare("SELECT name FROM colleges WHERE id = ?");
$collegeStmt->execute([$college_id]);
$college = $collegeStmt->fetch(PDO::FETCH_ASSOC);

// Handle export request
if (isset($_GET['export']) && $_GET['export'] === 'csv') {
    // Get alumni data
    $stmt = $conn->prepare("SELECT * FROM alumni WHERE college_id = ? ORDER BY full_name ASC");
    $stmt->execute([$college_id]);
    $alumni = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Set headers for CSV download
    header('Content-Type: text/csv');
    header('Content-Disposition: attachment; filename="alumni_export_' . date('Y-m-d') . '.csv"');
    
    // Create file pointer
    $output = fopen('php://output', 'w');
    
    // Add CSV headers
    fputcsv($output, [
        'ID',
        'Full Name',
        'Email',
        'Phone',
        'Graduation Year',
        'Department',
        'Company',
        'Position',
        'Industry',
        'LinkedIn',
        'Website',
        'Verified',
        'Created At'
    ]);
    
    // Add data rows
    foreach ($alumni as $alumnus) {
        fputcsv($output, [
            $alumnus['id'],
            $alumnus['full_name'],
            $alumnus['email'],
            $alumnus['phone'] ?? '',
            $alumnus['graduation_year'] ?? '',
            $alumnus['department'] ?? '',
            $alumnus['company'] ?? '',
            $alumnus['position'] ?? '',
            $alumnus['industry'] ?? '',
            $alumnus['linkedin'] ?? '',
            $alumnus['website'] ?? '',
            $alumnus['verified'] ? 'Yes' : 'No',
            $alumnus['created_at']
        ]);
    }
    
    fclose($output);
    exit;
}

// Get statistics
$totalStmt = $conn->prepare("SELECT COUNT(*) FROM alumni WHERE college_id = ?");
$totalStmt->execute([$college_id]);
$total_alumni = $totalStmt->fetchColumn();

$verifiedStmt = $conn->prepare("SELECT COUNT(*) FROM alumni WHERE college_id = ? AND verified = 1");
$verifiedStmt->execute([$college_id]);
$verified_alumni = $verifiedStmt->fetchColumn();

$pendingStmt = $conn->prepare("SELECT COUNT(*) FROM alumni WHERE college_id = ? AND verified = 0");
$pendingStmt->execute([$college_id]);
$pending_alumni = $pendingStmt->fetchColumn();

// Include the header
require_once '../includes/header.php';
?>

    <!-- Main Content -->
    <main class="flex-grow py-6 px-4">
        <div class="max-w-7xl mx-auto">
            <!-- Page Header -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8">
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="bg-gradient-to-br from-blue-500 to-blue-600 p-3 rounded-xl shadow-lg">
                            <i class="fas fa-download text-white text-xl"></i>
                        </div>
                        <div>
                            <h1 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                                Export Alumni Data
                            </h1>
                            <nav class="text-sm text-gray-600 dark:text-gray-400 mt-1" aria-label="Breadcrumb">
                                <ol class="flex items-center space-x-2">
                                    <li>
                                        <a href="collegeadmin_dashboard.php" class="hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
                                            Dashboard
                                        </a>
                                    </li>
                                    <li class="flex items-center">
                                        <i class="fas fa-chevron-right text-gray-400 text-xs mx-2"></i>
                                        <a href="list_alumni.php" class="hover:text-blue-600 dark:hover:text-blue-400 transition-colors">Alumni Management</a>
                                    </li>
                                    <li class="flex items-center">
                                        <i class="fas fa-chevron-right text-gray-400 text-xs mx-2"></i>
                                        <span class="text-gray-900 dark:text-white font-medium">Export Data</span>
                                    </li>
                                </ol>
                            </nav>
                            <p class="text-gray-600 dark:text-gray-400 mt-2">
                                Download alumni data from <span class="font-medium text-blue-600 dark:text-blue-400"><?= htmlspecialchars($college['name'] ?? 'your college') ?></span>
                            </p>
                        </div>
                    </div>
                    <div class="mt-4 lg:mt-0 flex flex-wrap gap-3">
                        <a href="list_alumni.php" class="inline-flex items-center px-4 py-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-medium rounded-lg transition-colors">
                            <i class="fas fa-arrow-left mr-2"></i> Back to Alumni
                        </a>
                        <a href="invite_alumni.php" class="inline-flex items-center px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white font-medium rounded-lg transition-colors">
                            <i class="fas fa-user-plus mr-2"></i> Invite Alumni
                        </a>
                    </div>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                    <div class="flex items-center">
                        <div class="bg-blue-100 dark:bg-blue-900/30 p-3 rounded-lg">
                            <i class="fas fa-users text-blue-600 dark:text-blue-400 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm text-gray-600 dark:text-gray-400">Total Alumni</p>
                            <p class="text-2xl font-bold text-gray-900 dark:text-white"><?= number_format($total_alumni) ?></p>
                        </div>
                    </div>
                </div>
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                    <div class="flex items-center">
                        <div class="bg-green-100 dark:bg-green-900/30 p-3 rounded-lg">
                            <i class="fas fa-check-circle text-green-600 dark:text-green-400 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm text-gray-600 dark:text-gray-400">Verified Alumni</p>
                            <p class="text-2xl font-bold text-gray-900 dark:text-white"><?= number_format($verified_alumni) ?></p>
                        </div>
                    </div>
                </div>
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                    <div class="flex items-center">
                        <div class="bg-yellow-100 dark:bg-yellow-900/30 p-3 rounded-lg">
                            <i class="fas fa-clock text-yellow-600 dark:text-yellow-400 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm text-gray-600 dark:text-gray-400">Pending Verification</p>
                            <p class="text-2xl font-bold text-gray-900 dark:text-white"><?= number_format($pending_alumni) ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Export Options -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- CSV Export -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                    <div class="flex items-center mb-4">
                        <div class="bg-green-100 dark:bg-green-900/30 p-3 rounded-lg mr-4">
                            <i class="fas fa-file-csv text-green-600 dark:text-green-400 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">CSV Export</h3>
                            <p class="text-sm text-gray-600 dark:text-gray-400">Download alumni data in CSV format</p>
                        </div>
                    </div>
                    
                    <div class="space-y-3 mb-6">
                        <div class="flex items-center text-sm text-gray-600 dark:text-gray-400">
                            <i class="fas fa-check text-green-600 mr-2"></i>
                            <span>All alumni information</span>
                        </div>
                        <div class="flex items-center text-sm text-gray-600 dark:text-gray-400">
                            <i class="fas fa-check text-green-600 mr-2"></i>
                            <span>Contact details and professional info</span>
                        </div>
                        <div class="flex items-center text-sm text-gray-600 dark:text-gray-400">
                            <i class="fas fa-check text-green-600 mr-2"></i>
                            <span>Verification status</span>
                        </div>
                        <div class="flex items-center text-sm text-gray-600 dark:text-gray-400">
                            <i class="fas fa-check text-green-600 mr-2"></i>
                            <span>Compatible with Excel and Google Sheets</span>
                        </div>
                    </div>
                    
                    <a href="?export=csv" class="w-full inline-flex items-center justify-center px-4 py-3 bg-green-600 hover:bg-green-700 text-white font-medium rounded-lg transition-colors">
                        <i class="fas fa-download mr-2"></i> Download CSV
                    </a>
                </div>

                <!-- Export Instructions -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                    <div class="flex items-center mb-4">
                        <div class="bg-blue-100 dark:bg-blue-900/30 p-3 rounded-lg mr-4">
                            <i class="fas fa-info-circle text-blue-600 dark:text-blue-400 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Export Information</h3>
                            <p class="text-sm text-gray-600 dark:text-gray-400">Important details about the export</p>
                        </div>
                    </div>
                    
                    <div class="space-y-4">
                        <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
                            <h4 class="font-medium text-blue-900 dark:text-blue-100 mb-2">Data Included</h4>
                            <ul class="text-sm text-blue-800 dark:text-blue-200 space-y-1">
                                <li>• Personal information (name, email, phone)</li>
                                <li>• Academic details (graduation year, department)</li>
                                <li>• Professional information (company, position, industry)</li>
                                <li>• Social profiles (LinkedIn, website)</li>
                                <li>• Account status and creation date</li>
                            </ul>
                        </div>
                        
                        <div class="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-4">
                            <h4 class="font-medium text-yellow-900 dark:text-yellow-100 mb-2">Privacy Notice</h4>
                            <p class="text-sm text-yellow-800 dark:text-yellow-200">
                                This export contains personal information. Please handle the data responsibly and in accordance with your institution's privacy policies.
                            </p>
                        </div>
                        
                        <div class="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
                            <h4 class="font-medium text-green-900 dark:text-green-100 mb-2">File Format</h4>
                            <p class="text-sm text-green-800 dark:text-green-200">
                                The CSV file can be opened in Excel, Google Sheets, or any spreadsheet application. The filename includes today's date for easy organization.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

<script>
function toggleDropdown(id) {
    const dropdown = document.getElementById(id);
    dropdown.classList.toggle('hidden');
}

// Close dropdown when clicking outside
document.addEventListener('click', function(event) {
    const dropdowns = document.querySelectorAll('[id$="Dropdown"]');
    dropdowns.forEach(dropdown => {
        if (!dropdown.contains(event.target) && !event.target.closest('button[onclick*="' + dropdown.id + '"]')) {
            dropdown.classList.add('hidden');
        }
    });
});
</script>

<?php require_once '../includes/footer.php'; ?>
