<?php
require_once '../includes/db.php';

$error = $success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $full_name = trim($_POST['full_name']);
    $username = trim($_POST['username']);
    $email = trim($_POST['email']);
    $password = $_POST['password'];
    $roll_number = trim($_POST['roll_number']);
    $department = trim($_POST['department']);
    $year = intval($_POST['year']);
    $division = trim($_POST['division']);
    $college_id = intval($_POST['college_id']); // will be selected from dropdown

    if (!$full_name || !$email || !$password || !$roll_number || !$department || !$year || !$division || !$college_id) {
        $error = "All fields are required.";
    } else {
        $hashed = password_hash($password, PASSWORD_DEFAULT);
        $stmt = $conn->prepare("INSERT INTO students (college_id, full_name, username, email, password, roll_number, department, year, division) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
        $stmt->execute([$college_id, $full_name, $username, $email, $hashed, $roll_number, $department, $year, $division]);
        $success = "Registration successful! Awaiting faculty approval.";
    }
}

// Fetch colleges for dropdown
$colleges = $conn->query("SELECT id, name FROM colleges ORDER BY name ASC")->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Student Registration - Mentoshri</title>
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts - Inter -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        'primary': '#0a66c2',
                        'primary-dark': '#004182',
                        'primary-light': '#e7f3ff',
                    },
                    fontFamily: {
                        'sans': ['Inter', 'sans-serif'],
                    },
                }
            }
        }
    </script>
    <!-- Design System -->
    <link rel="stylesheet" href="../assets/css/design-system.css">
</head>
<body class="font-sans bg-gray-50 dark:bg-gray-900 min-h-screen flex flex-col">
    <!-- Header -->
    <header class="bg-white dark:bg-gray-800 shadow-sm">
        <div class="container mx-auto px-4 py-3 flex items-center justify-between">
            <div class="flex items-center">
                <a href="../index.php" class="text-primary dark:text-primary font-bold text-2xl">Mentoshri</a>
            </div>
            
            <div class="flex items-center space-x-4">
                <button id="themeToggle" class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700">
                    <i class="fas fa-moon dark:hidden"></i>
                    <i class="fas fa-sun hidden dark:block"></i>
                </button>
                <a href="../index.php" class="text-gray-600 dark:text-gray-300 hover:text-primary dark:hover:text-primary">
                    <i class="fas fa-arrow-left mr-2"></i> Back to Home
                </a>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="flex-grow py-12 px-4">
        <div class="max-w-2xl mx-auto">
            <div class="text-center mb-8">
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Student Registration</h1>
                <p class="text-gray-600 dark:text-gray-400 mt-2">Create your student account to connect with alumni mentors</p>
            </div>
            
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 p-6">
                <?php if ($error): ?>
                    <div class="bg-red-50 dark:bg-red-900/30 border-l-4 border-red-500 text-red-700 dark:text-red-400 p-4 mb-6" role="alert">
                        <p><?= htmlspecialchars($error) ?></p>
                    </div>
                <?php elseif ($success): ?>
                    <div class="bg-green-50 dark:bg-green-900/30 border-l-4 border-green-500 text-green-700 dark:text-green-400 p-4 mb-6" role="alert">
                        <p><?= htmlspecialchars($success) ?></p>
                        <a href="student_login.php" class="inline-block mt-3 btn btn-primary py-1.5 px-4 text-sm">Proceed to Login</a>
                    </div>
                <?php endif; ?>

                <form method="post" class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Personal Information -->
                        <div class="md:col-span-2">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">Personal Information</h3>
                        </div>
                        
                        <div class="form-group">
                            <label for="full_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Full Name</label>
                            <input type="text" id="full_name" name="full_name" class="form-input" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="username" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Username</label>
                            <input type="text" id="username" name="username" class="form-input" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Email</label>
                            <input type="email" id="email" name="email" class="form-input" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Password</label>
                            <input type="password" id="password" name="password" class="form-input" required>
                            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Password must be at least 8 characters long</p>
                        </div>
                        
                        <!-- Academic Information -->
                        <div class="md:col-span-2 pt-4">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">Academic Information</h3>
                        </div>
                        
                        <div class="form-group">
                            <label for="roll_number" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Enrollment Number</label>
                            <input type="text" id="roll_number" name="roll_number" class="form-input" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="department" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Department</label>
                            <input type="text" id="department" name="department" class="form-input" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="year" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Year</label>
                            <input type="number" id="year" name="year" min="1" max="6" class="form-input" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="division" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Division</label>
                            <input type="text" id="division" name="division" class="form-input" required>
                        </div>
                        
                        <div class="form-group md:col-span-2">
                            <label for="college_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Select College</label>
                            <select id="college_id" name="college_id" class="form-input" required>
                                <option value="">-- Select College --</option>
                                <?php foreach ($colleges as $college): ?>
                                    <option value="<?= $college['id'] ?>"><?= htmlspecialchars($college['name']) ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    
                    <div class="md:col-span-2 pt-4">
                        <div class="flex items-center">
                            <input type="checkbox" id="terms" name="terms" class="h-4 w-4 text-primary border-gray-300 rounded" required>
                            <label for="terms" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                                I agree to the <a href="#" class="text-primary hover:underline">Terms of Service</a> and <a href="#" class="text-primary hover:underline">Privacy Policy</a>
                            </label>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary w-full py-2.5">Register</button>
                </form>
                
                <div class="mt-6 text-center">
                    <p class="text-gray-600 dark:text-gray-400">
                        Already have an account? 
                        <a href="student_login.php" class="text-primary font-medium hover:underline">Sign In</a>
                    </p>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 py-4 mt-8">
        <div class="container mx-auto px-4">
            <div class="text-center text-gray-600 dark:text-gray-400 text-sm">
                &copy; <?= date('Y') ?> Mentoshri. All rights reserved.
            </div>
        </div>
    </footer>

    <script>
        // Theme Toggle
        const themeToggle = document.getElementById('themeToggle');
        const htmlElement = document.documentElement;
        
        // Check for saved theme preference
        if (localStorage.theme === 'dark' || 
            (!('theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
            htmlElement.classList.add('dark');
        } else {
            htmlElement.classList.remove('dark');
        }
        
        // Toggle theme
        themeToggle.addEventListener('click', () => {
            if (htmlElement.classList.contains('dark')) {
                htmlElement.classList.remove('dark');
                localStorage.theme = 'light';
            } else {
                htmlElement.classList.add('dark');
                localStorage.theme = 'dark';
            }
        });
    </script>
</body>
</html>
