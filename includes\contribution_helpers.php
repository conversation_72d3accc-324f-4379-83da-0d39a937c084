<?php
// contribution_helpers.php

require_once 'db.php';

function getPledgesByCampaign(PDO $conn, int $campaign_id): array {
    $stmt = $conn->prepare("
        SELECT p.*, 
               a.full_name, 
               a.email
        FROM alumni_pledges p
        JOIN alumni a ON a.id = p.alumni_id
        WHERE p.deal_id = ?
        ORDER BY p.created_at DESC
    ");
    $stmt->execute([$campaign_id]);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function getPledgesByAlumni(PDO $conn, int $alumni_id): array {
    $stmt = $conn->prepare("
        SELECT p.*, d.title AS campaign_title, d.category, d.college_id, d.contact_name, d.contact_email
        FROM alumni_pledges p
        JOIN deals d ON p.deal_id = d.id
        WHERE p.alumni_id = ?
        ORDER BY p.created_at DESC
    ");
    $stmt->execute([$alumni_id]);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function getPledgeStatusBadge(string $status): string {
    $map = [
        'pending'     => 'warning',
        'in-progress' => 'info',
        'fulfilled'   => 'success',
        'declined'    => 'danger'
    ];

    $color = $map[$status] ?? 'secondary';
    return "<span class='badge bg-$color text-uppercase'>$status</span>";
}

function getApprovedCampaignById(PDO $conn, int $campaign_id): ?array {
    $stmt = $conn->prepare("SELECT * FROM deals WHERE id = ? AND status = 'approved'");
    $stmt->execute([$campaign_id]);
    return $stmt->fetch(PDO::FETCH_ASSOC) ?: null;
}

function getAlumniName(PDO $conn, int $alumni_id): string {
    $stmt = $conn->prepare("SELECT full_name FROM alumni WHERE id = ?");
    $stmt->execute([$alumni_id]);
    $row = $stmt->fetch(PDO::FETCH_ASSOC);
    return $row['full_name'] ?? 'An Alumni';
}

function savePledge(PDO $conn, array $data): void {
    $stmt = $conn->prepare("INSERT INTO alumni_pledges 
        (deal_id, alumni_id, pledge_type, amount, description, allow_contact, contact_preference, is_anonymous, status)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'pending')");
    $stmt->execute([
        $data['deal_id'],
        $data['alumni_id'],
        $data['pledge_type'],
        $data['amount'],
        $data['description'],
        $data['allow_contact'],
        $data['contact_method'],
        $data['is_anonymous']
    ]);
}
?>
