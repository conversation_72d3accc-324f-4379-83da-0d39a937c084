<?php
require_once '../includes/db.php';

// Check session
if (isset($_SESSION['faculty_id'])) {
    $role = 'faculty';
    $user_id = $_SESSION['faculty_id'];
    $user_name = $_SESSION['faculty_name'] ?? 'Faculty';
    $uploader_id = $user_id;
    $college_id = $_SESSION['college_id'];
} elseif (isset($_SESSION['alumni_id'])) {
    $role = 'alumni';
    $user_id = $_SESSION['alumni_id'];
    $user_name = $_SESSION['alumni_name'] ?? 'Alumni';
    $uploader_id = $user_id;
    $college_id = $_SESSION['college_id'];
} elseif (isset($_SESSION['collegeadmin_id'])) {
    $role = 'collegeadmin';
    $user_id = $_SESSION['collegeadmin_id'];
    $user_name = $_SESSION['collegeadmin_name'] ?? 'Admin';
    $uploader_id = $user_id;
    $college_id = $_SESSION['college_id'];
} else {
    // Redirect to appropriate login page instead of logout
    if (isset($_SERVER['HTTP_REFERER'])) {
        if (strpos($_SERVER['HTTP_REFERER'], 'collegeadmin') !== false) {
            header("Location: ../auth/collegeadmin_login.php");
        } else if (strpos($_SERVER['HTTP_REFERER'], 'faculty') !== false) {
            header("Location: ../auth/faculty_login.php");
        } else if (strpos($_SERVER['HTTP_REFERER'], 'alumni') !== false) {
            header("Location: ../auth/alumni_login.php");
        } else {
            header("Location: ../auth/collegeadmin_login.php");
        }
    } else {
        header("Location: ../auth/collegeadmin_login.php");
    }
    exit;
}
$current_page = basename($_SERVER['PHP_SELF']);

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['resource_file'])) {
    $title = trim($_POST['title']);
    $file = $_FILES['resource_file'];

    if ($file['error'] == 0) {
        $filename = time() . '_' . basename($file['name']);
        $target = "../uploads/" . $filename;

        if (move_uploaded_file($file['tmp_name'], $target)) {
            $stmt = $conn->prepare("INSERT INTO resources (uploaded_by, role, college_id, title, file_path) VALUES (?, ?, ?, ?, ?)");
            $stmt->execute([$uploader_id, $role, $college_id, $title, $filename]);
            $success = "Resource uploaded successfully!";
        } else {
            $error = "Failed to upload file.";
        }
    } else {
        $error = "File upload error.";
    }
}

require_once '../includes/header.php';
?>

<!-- Main Content -->
<main class="flex-grow py-8 px-4">
    <div class="container mx-auto">
        <!-- Breadcrumb -->
        <nav class="mb-6 text-sm" aria-label="Breadcrumb">
            <ol class="flex items-center space-x-2">
                <li>
                    <a href="<?php
                        if ($role === 'collegeadmin') echo '../dashboards/collegeadmin_dashboard.php';
                        elseif ($role === 'faculty') echo '../dashboards/faculty_dashboard.php';
                        elseif ($role === 'alumni') echo '../alumni/alumni_dashboard.php';
                        else echo '../index.php';
                    ?>" class="text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary-light">
                        <i class="fas fa-home"></i>
                        <span class="sr-only">Dashboard</span>
                    </a>
                </li>
                <li class="flex items-center">
                    <i class="fas fa-chevron-right text-gray-400 text-xs mx-1"></i>
                    <a href="resource_list.php" class="text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary-light">
                        Resources
                    </a>
                </li>
                <li class="flex items-center">
                    <i class="fas fa-chevron-right text-gray-400 text-xs mx-1"></i>
                    <span class="text-gray-700 dark:text-gray-300">Upload Resource</span>
                </li>
            </ol>
        </nav>

        <div class="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                    <i class="fas fa-upload text-primary mr-3"></i>
                    Upload New Resource
                </h1>
                <p class="text-gray-600 dark:text-gray-400 mt-1">
                    Share documents, presentations, and other resources with the community
                </p>
            </div>
            <div class="mt-4 md:mt-0">
                <a href="resource_list.php" class="btn btn-outline flex items-center">
                    <i class="fas fa-list mr-2"></i> View All Resources
                </a>
            </div>
        </div>

        <?php if (isset($error)): ?>
            <div class="bg-red-50 dark:bg-red-900/30 border-l-4 border-red-500 text-red-700 dark:text-red-400 p-4 mb-6 flex justify-between items-center" role="alert">
                <div class="flex items-center">
                    <i class="fas fa-exclamation-circle mr-2"></i>
                    <span><?= htmlspecialchars($error) ?></span>
                </div>
                <button type="button" class="text-red-700 dark:text-red-400 hover:opacity-75" onclick="this.parentElement.style.display='none';">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        <?php elseif (isset($success)): ?>
            <div class="bg-green-50 dark:bg-green-900/30 border-l-4 border-green-500 text-green-700 dark:text-green-400 p-4 mb-6 flex justify-between items-center" role="alert">
                <div class="flex items-center">
                    <i class="fas fa-check-circle mr-2"></i>
                    <span><?= htmlspecialchars($success) ?></span>
                </div>
                <button type="button" class="text-green-700 dark:text-green-400 hover:opacity-75" onclick="this.parentElement.style.display='none';">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        <?php endif; ?>

        <!-- Upload Form -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700">
            <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                <h2 class="text-lg font-semibold text-gray-800 dark:text-white flex items-center">
                    <i class="fas fa-file-upload text-primary mr-2"></i> Resource Details
                </h2>
                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    Fill in the details below to upload a new resource
                </p>
            </div>
            
            <div class="p-6">
                <form method="post" enctype="multipart/form-data" id="uploadForm">
                    <div class="mb-6">
                        <label for="title" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Resource Title <span class="text-red-500">*</span>
                        </label>
                        <input type="text" id="title" name="title" 
                            class="form-input w-full" 
                            placeholder="Enter a descriptive title for the resource" required>
                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                            Choose a clear, descriptive title to help others find this resource.
                        </p>
                    </div>
                    
                    <div class="mb-6">
                        <label for="resource_file" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Select File <span class="text-red-500">*</span>
                        </label>
                        <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 dark:border-gray-600 border-dashed rounded-md">
                            <div class="space-y-1 text-center">
                                <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48" aria-hidden="true">
                                    <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                </svg>
                                <div class="flex text-sm text-gray-600 dark:text-gray-400">
                                    <label for="resource_file" class="relative cursor-pointer bg-white dark:bg-gray-700 rounded-md font-medium text-primary hover:text-primary-dark px-3 py-2">
                                        <span>Upload a file</span>
                                        <input id="resource_file" name="resource_file" type="file" class="sr-only" required>
                                    </label>
                                    <p class="pl-1 pt-2">or drag and drop</p>
                                </div>
                                <p class="text-xs text-gray-500 dark:text-gray-400">
                                    PDF, DOCX, PPTX, XLSX, ZIP up to 10MB
                                </p>
                                <p id="file-name" class="text-sm text-gray-700 dark:text-gray-300 mt-2 hidden"></p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="flex items-center justify-end space-x-3">
                        <button type="reset" class="btn btn-outline" onclick="resetFileInput()">
                            <i class="fas fa-redo mr-2"></i> Reset
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-cloud-upload-alt mr-2"></i> Upload Resource
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Tips Section -->
        <div class="mt-6 bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
            <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4 flex items-center">
                <i class="fas fa-lightbulb text-yellow-500 mr-2"></i> Resource Upload Tips
            </h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="flex items-start">
                    <div class="flex-shrink-0 h-10 w-10 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center text-blue-600 dark:text-blue-400">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <div class="ml-4">
                        <h4 class="text-sm font-medium text-gray-900 dark:text-white">Use Descriptive Titles</h4>
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                            Choose clear titles that describe the content and purpose of your resource.
                        </p>
                    </div>
                </div>
                
                <div class="flex items-start">
                    <div class="flex-shrink-0 h-10 w-10 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center text-green-600 dark:text-green-400">
                        <i class="fas fa-file-pdf"></i>
                    </div>
                    <div class="ml-4">
                        <h4 class="text-sm font-medium text-gray-900 dark:text-white">Prefer PDF Format</h4>
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                            When possible, convert documents to PDF to ensure compatibility for all users.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

<script>
    // File input handling
    const fileInput = document.getElementById('resource_file');
    const fileNameDisplay = document.getElementById('file-name');
    
    fileInput.addEventListener('change', function() {
        if (this.files.length > 0) {
            fileNameDisplay.textContent = 'Selected file: ' + this.files[0].name;
            fileNameDisplay.classList.remove('hidden');
        } else {
            fileNameDisplay.classList.add('hidden');
        }
    });
    
    // Drag and drop functionality
    const dropZone = fileInput.closest('div.border-dashed');
    
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        dropZone.addEventListener(eventName, preventDefaults, false);
    });
    
    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }
    
    ['dragenter', 'dragover'].forEach(eventName => {
        dropZone.addEventListener(eventName, highlight, false);
    });
    
    ['dragleave', 'drop'].forEach(eventName => {
        dropZone.addEventListener(eventName, unhighlight, false);
    });
    
    function highlight() {
        dropZone.classList.add('border-primary', 'bg-primary-light', 'dark:bg-primary-dark/10');
    }
    
    function unhighlight() {
        dropZone.classList.remove('border-primary', 'bg-primary-light', 'dark:bg-primary-dark/10');
    }
    
    dropZone.addEventListener('drop', handleDrop, false);
    
    function handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;
        
        fileInput.files = files;
        
        if (files.length > 0) {
            fileNameDisplay.textContent = 'Selected file: ' + files[0].name;
            fileNameDisplay.classList.remove('hidden');
        }
    }
    
    function resetFileInput() {
        fileInput.value = '';
        fileNameDisplay.classList.add('hidden');
    }
</script>

<?php require_once '../includes/footer.php'; ?>
