<?php
require_once '../includes/db.php';

$role = $_POST['role'] ?? '';
$user_id = $_POST['user_id'] ?? 0;

if (!in_array($role, ['student', 'alumni']) || !$user_id) {
    echo json_encode([]);
    exit;
}

$opposite_role = $role === 'student' ? 'alumni' : 'student';
$table = $role === 'student' ? 'alumni' : 'students';
$id_field = $opposite_role . '_id';

$stmt = $conn->prepare("
    SELECT DISTINCT m.$id_field as chat_partner_id, t.full_name, t.profile_photo
    FROM messages m
    JOIN $table t ON m.$id_field = t.id
    WHERE m.sender_id = ? AND m.sender_role = ?
       OR m.receiver_id = ? AND m.sender_role = ?
    ORDER BY m.created_at DESC
");
$stmt->execute([$user_id, $role, $user_id, $opposite_role]);
$conversations = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo json_encode($conversations);
?>
