<?php
session_start();
require_once '../includes/db.php';

if (!isset($_SESSION['superadmin_id'])) {
    header("Location: ../auth/superadmin_login.php");
    exit;
}

$college_id = $_GET['id'] ?? null;
if (!$college_id) {
    $_SESSION['flash_message'] = "Invalid college ID.";
    header("Location: superadmin_dashboard.php");
    exit;
}

// Fetch college details
$stmt = $conn->prepare("SELECT * FROM colleges WHERE id = ?");
$stmt->execute([$college_id]);
$college = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$college) {
    $_SESSION['flash_message'] = "College not found.";
    header("Location: superadmin_dashboard.php");
    exit;
}

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = trim($_POST['name']);
    $slug = trim($_POST['slug']);

    if (!$name || !$slug) {
        $error = "Both name and slug are required.";
    } else {
        // Check if slug already exists for another college
        $check = $conn->prepare("SELECT COUNT(*) FROM colleges WHERE slug = ? AND id != ?");
        $check->execute([$slug, $college_id]);
        $exists = $check->fetchColumn();
        
        if ($exists) {
            $error = "A college with this slug already exists.";
        } else {
            $update = $conn->prepare("UPDATE colleges SET name = ?, slug = ?, updated_at = NOW() WHERE id = ?");
            $update->execute([$name, $slug, $college_id]);
            
            $success = "College updated successfully.";
            $_SESSION['flash_message'] = "College updated successfully.";
            
            // Refresh data
            $stmt->execute([$college_id]);
            $college = $stmt->fetch(PDO::FETCH_ASSOC);
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit College - Mentoshri</title>
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts - Inter -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        'primary': '#0a66c2',
                        'primary-dark': '#004182',
                        'primary-light': '#e7f3ff',
                    },
                    fontFamily: {
                        'sans': ['Inter', 'sans-serif'],
                    },
                }
            }
        }
    </script>
    <!-- Design System -->
    <link rel="stylesheet" href="../assets/css/design-system.css">
</head>
<body class="font-sans bg-gray-50 dark:bg-gray-900 min-h-screen flex flex-col">
    <!-- Header -->
    <header class="bg-white dark:bg-gray-800 shadow-sm">
        <div class="container mx-auto px-4 py-3 flex items-center justify-between">
            <div class="flex items-center">
                <a href="superadmin_dashboard.php" class="text-primary font-bold text-2xl">Mentoshri</a>
            </div>
            
            <div class="flex items-center space-x-4">
                <button id="themeToggle" class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700">
                    <i class="fas fa-moon dark:hidden"></i>
                    <i class="fas fa-sun hidden dark:block"></i>
                </button>
                <a href="superadmin_dashboard.php" class="text-gray-600 dark:text-gray-300 hover:text-primary dark:hover:text-primary">
                    <i class="fas fa-arrow-left mr-2"></i> Back to Dashboard
                </a>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="flex-grow py-8 px-4">
        <div class="max-w-2xl mx-auto">
            <div class="mb-6">
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Edit College</h1>
                <p class="text-gray-600 dark:text-gray-400 mt-1">Update details for <?= htmlspecialchars($college['name']) ?></p>
            </div>
            
            <?php if ($error): ?>
                <div class="bg-red-50 dark:bg-red-900/30 border-l-4 border-red-500 text-red-700 dark:text-red-400 p-4 mb-6" role="alert">
                    <p><?= htmlspecialchars($error) ?></p>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="bg-green-50 dark:bg-green-900/30 border-l-4 border-green-500 text-green-700 dark:text-green-400 p-4 mb-6" role="alert">
                    <p><?= htmlspecialchars($success) ?></p>
                </div>
            <?php endif; ?>
            
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700">
                <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4 flex justify-between items-center">
                    <h2 class="font-semibold text-gray-800 dark:text-white">College Details</h2>
                    <span class="badge badge-primary">ID: <?= $college['id'] ?></span>
                </div>
                
                <div class="p-6">
                    <form method="post" id="collegeForm">
                        <div class="space-y-6">
                            <div class="form-group">
                                <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">College Name <span class="text-red-500">*</span></label>
                                <input 
                                    type="text" 
                                    id="name" 
                                    name="name" 
                                    class="form-input" 
                                    value="<?= htmlspecialchars($college['name']) ?>"
                                    placeholder="Enter college name"
                                    required
                                >
                                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Full official name of the college</p>
                            </div>
                            
                            <div class="form-group">
                                <label for="slug" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Slug <span class="text-red-500">*</span></label>
                                <div class="relative">
                                    <input 
                                        type="text" 
                                        id="slug" 
                                        name="slug" 
                                        class="form-input" 
                                        value="<?= htmlspecialchars($college['slug']) ?>"
                                        placeholder="college-slug"
                                        required
                                    >
                                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                        <button type="button" id="generateSlug" class="text-primary text-sm hover:text-primary-dark">
                                            Generate
                                        </button>
                                    </div>
                                </div>
                                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">URL-friendly identifier (lowercase, no spaces)</p>
                            </div>
                            
                            <div class="form-group">
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Created At</label>
                                <div class="form-input bg-gray-100 dark:bg-gray-700 cursor-not-allowed">
                                    <?= date('F j, Y', strtotime($college['created_at'])) ?>
                                </div>
                            </div>
                            
                            <div class="flex justify-between items-center pt-4">
                                <a href="view_college.php?id=<?= $college['id'] ?>" class="btn btn-outline">
                                    Cancel
                                </a>
                                <div class="flex space-x-3">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save mr-2"></i> Update College
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Danger Zone -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 mt-6">
                <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
                    <h2 class="font-semibold text-red-600 dark:text-red-400">Danger Zone</h2>
                </div>
                
                <div class="p-6">
                    <div class="flex items-center justify-between p-4 border border-red-200 dark:border-red-900/50 rounded-lg bg-red-50 dark:bg-red-900/20">
                        <div>
                            <h3 class="font-medium text-red-800 dark:text-red-300">Delete College</h3>
                            <p class="text-sm text-red-600 dark:text-red-400 mt-1">
                                This action cannot be undone. All associated data will be permanently deleted.
                            </p>
                        </div>
                        <button type="button" id="deleteCollegeBtn" class="btn bg-white dark:bg-gray-800 text-red-600 dark:text-red-400 border border-red-300 dark:border-red-700 hover:bg-red-50 dark:hover:bg-red-900/30">
                            Delete College
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" class="modal-backdrop hidden">
        <div class="modal dark:bg-gray-800 dark:border-gray-700" role="dialog">
            <div class="modal-header dark:border-gray-700">
                <h3 class="text-xl font-semibold text-gray-800 dark:text-white">Confirm Deletion</h3>
                <button id="closeDeleteModal" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="p-4 mb-4 bg-yellow-50 dark:bg-yellow-900/20 border-l-4 border-yellow-500 text-yellow-700 dark:text-yellow-400">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm">
                                You are about to delete <strong><?= htmlspecialchars($college['name']) ?></strong>. This action cannot be undone.
                            </p>
                        </div>
                    </div>
                </div>
                <p class="mb-4 text-gray-700 dark:text-gray-300">
                    Please type <strong class="font-semibold"><?= htmlspecialchars($college['slug']) ?></strong> to confirm.
                </p>
                <div class="form-group">
                    <input 
                        type="text" 
                        id="confirmSlug" 
                        class="form-input" 
                        placeholder="Enter college slug to confirm"
                    >
                </div>
            </div>
            <div class="modal-footer dark:border-gray-700">
                <button id="cancelDelete" class="btn btn-outline">Cancel</button>
                <form action="delete_college.php" method="post">
                    <input type="hidden" name="college_id" value="<?= $college['id'] ?>">
                    <button type="submit" id="confirmDelete" class="btn bg-red-600 hover:bg-red-700 text-white" disabled>
                        Delete Permanently
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 py-4 mt-auto">
        <div class="container mx-auto px-4">
            <div class="text-center text-gray-600 dark:text-gray-400 text-sm">
                &copy; <?= date('Y') ?> Mentoshri. All rights reserved.
            </div>
        </div>
    </footer>

    <script>
        // Theme Toggle
        const themeToggle = document.getElementById('themeToggle');
        const htmlElement = document.documentElement;
        
        // Check for saved theme preference or use system preference
        if (localStorage.theme === 'dark' || 
            (!('theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
            htmlElement.classList.add('dark');
        } else {
            htmlElement.classList.remove('dark');
        }
        
        // Toggle theme
        themeToggle.addEventListener('click', () => {
            if (htmlElement.classList.contains('dark')) {
                htmlElement.classList.remove('dark');
                localStorage.theme = 'light';
            } else {
                htmlElement.classList.add('dark');
                localStorage.theme = 'dark';
            }
        });
        
        // Generate slug from college name
        document.getElementById('generateSlug').addEventListener('click', function() {
            const collegeName = document.getElementById('name').value;
            if (collegeName) {
                const slug = collegeName
                    .toLowerCase()
                    .replace(/[^\w\s-]/g, '')  // Remove special characters
                    .replace(/\s+/g, '-')      // Replace spaces with hyphens
                    .replace(/-+/g, '-');      // Replace multiple hyphens with single hyphen
                
                document.getElementById('slug').value = slug;
            }
        });
        
        // Form validation
        document.getElementById('collegeForm').addEventListener('submit', function(e) {
            const name = document.getElementById('name').value.trim();
            const slug = document.getElementById('slug').value.trim();
            
            if (!name || !slug) {
                e.preventDefault();
                alert('Please fill in all required fields.');
            }
        });
        
        // Delete Modal
        const deleteModal = document.getElementById('deleteModal');
        const deleteCollegeBtn = document.getElementById('deleteCollegeBtn');
        const closeDeleteModal = document.getElementById('closeDeleteModal');
        const cancelDelete = document.getElementById('cancelDelete');
        const confirmDelete = document.getElementById('confirmDelete');
        const confirmSlug = document.getElementById('confirmSlug');
        const collegeSlug = "<?= htmlspecialchars($college['slug']) ?>";
        
        deleteCollegeBtn.addEventListener('click', () => {
            deleteModal.classList.remove('hidden');
        });
        
        closeDeleteModal.addEventListener('click', () => {
            deleteModal.classList.add('hidden');
            confirmSlug.value = '';
        });
        
        cancelDelete.addEventListener('click', () => {
            deleteModal.classList.add('hidden');
            confirmSlug.value = '';
        });
        
        confirmSlug.addEventListener('input', () => {
            if (confirmSlug.value === collegeSlug) {
                confirmDelete.disabled = false;
            } else {
                confirmDelete.disabled = true;
            }
        });
    </script>
</body>
</html> 