/* Mentoshri Design System CSS */

/* Base Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    font-weight: 500;
    border-radius: 0.375rem;
    transition: all 0.2s ease-in-out;
    cursor: pointer;
}

.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

.btn-primary {
    background-color: #2563eb;
    color: white;
    border: 1px solid #2563eb;
}

.btn-primary:hover {
    background-color: #1d4ed8;
    border-color: #1d4ed8;
}

.btn-secondary {
    background-color: #6b7280;
    color: white;
    border: 1px solid #6b7280;
}

.btn-secondary:hover {
    background-color: #4b5563;
    border-color: #4b5563;
}

.btn-outline {
    background-color: transparent;
    color: #2563eb;
    border: 1px solid #d1d5db;
}

.btn-outline:hover {
    background-color: #f3f4f6;
    border-color: #2563eb;
}

.dark .btn-outline {
    color: #93c5fd;
    border-color: #4b5563;
}

.dark .btn-outline:hover {
    background-color: #374151;
    border-color: #93c5fd;
}

/* Modal Styles */
.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 50;
    transition: opacity 0.3s ease;
}

.modal {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    border: 1px solid #e5e7eb;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid #e5e7eb;
}

.modal-body {
    padding: 1rem;
}

.modal-footer {
    padding: 1rem;
    border-top: 1px solid #e5e7eb;
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
}

/* Form Elements */
.form-input {
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    background-color: white;
    color: #1f2937;
    transition: all 0.2s ease;
}

.form-input:focus {
    outline: none;
    border-color: #2563eb;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.dark .form-input {
    background-color: #1f2937;
    border-color: #4b5563;
    color: #e5e7eb;
}

.dark .form-input:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Badges */
.badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    font-weight: 500;
    border-radius: 9999px;
}

.badge-green {
    background-color: #d1fae5;
    color: #047857;
}

.dark .badge-green {
    background-color: rgba(4, 120, 87, 0.2);
    color: #34d399;
}

.badge-blue {
    background-color: #dbeafe;
    color: #1e40af;
}

.dark .badge-blue {
    background-color: rgba(30, 64, 175, 0.2);
    color: #60a5fa;
}

.badge-red {
    background-color: #fee2e2;
    color: #b91c1c;
}

.dark .badge-red {
    background-color: rgba(185, 28, 28, 0.2);
    color: #f87171;
}

/* Sidebar Styles */
#sidebar {
    border-right: 1px solid var(--sidebar-border, #e5e7eb);
}

/* Make sure icons are always visible */
#sidebar .sidenav-item i {
    display: inline-flex !important;
    align-items: center;
    justify-content: center;
    width: 1.5rem;
    height: 1.5rem;
    min-width: 1.5rem;
    margin-right: 0.75rem;
    font-size: 1rem;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Add divider between sidebar sections */
#sidebar .mb-6:not(:last-child) {
    border-bottom: 1px solid var(--sidebar-border, #e5e7eb);
    padding-bottom: 1rem;
    margin-bottom: 1rem;
}

/* Card Styles */
.card {
    background-color: white;
    border-radius: 0.5rem;
    border: 1px solid #e5e7eb;
    overflow: hidden;
    transition: all 0.2s ease;
}

.dark .card {
    background-color: #1f2937;
    border-color: #374151;
}

.card-header {
    padding: 1rem;
    border-bottom: 1px solid #e5e7eb;
    background-color: #f9fafb;
}

.dark .card-header {
    border-color: #374151;
    background-color: #111827;
}

.card-body {
    padding: 1rem;
}

.card-footer {
    padding: 1rem;
    border-top: 1px solid #e5e7eb;
    background-color: #f9fafb;
}

.dark .card-footer {
    border-color: #374151;
    background-color: #111827;
}

/* Table Styles */
.table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

.table {
    width: 100%;
    border-collapse: collapse;
}

.table th {
    background-color: #f9fafb;
    font-weight: 500;
    text-align: left;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #e5e7eb;
}

.dark .table th {
    background-color: #111827;
    border-color: #374151;
}

.table td {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #e5e7eb;
}

.dark .table td {
    border-color: #374151;
}

.table tr:hover {
    background-color: #f3f4f6;
}

.dark .table tr:hover {
    background-color: #1f2937;
}

/* Alert Styles */
.alert {
    padding: 1rem;
    border-radius: 0.375rem;
    margin-bottom: 1rem;
}

.alert-success {
    background-color: #d1fae5;
    border: 1px solid #a7f3d0;
    color: #047857;
}

.dark .alert-success {
    background-color: rgba(4, 120, 87, 0.2);
    border-color: rgba(167, 243, 208, 0.2);
    color: #34d399;
}

.alert-danger {
    background-color: #fee2e2;
    border: 1px solid #fecaca;
    color: #b91c1c;
}

.dark .alert-danger {
    background-color: rgba(185, 28, 28, 0.2);
    border-color: rgba(254, 202, 202, 0.2);
    color: #f87171;
}

.alert-warning {
    background-color: #fef3c7;
    border: 1px solid #fde68a;
    color: #92400e;
}

.dark .alert-warning {
    background-color: rgba(146, 64, 14, 0.2);
    border-color: rgba(253, 230, 138, 0.2);
    color: #fbbf24;
}

.alert-info {
    background-color: #dbeafe;
    border: 1px solid #bfdbfe;
    color: #1e40af;
}

.dark .alert-info {
    background-color: rgba(30, 64, 175, 0.2);
    border-color: rgba(191, 219, 254, 0.2);
    color: #60a5fa;
} 