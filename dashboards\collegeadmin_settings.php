<?php
require_once '../includes/db.php';

// Check session
if (!isset($_SESSION['collegeadmin_id'])) {
    header("Location: ../auth/collegeadmin_login.php");
    exit;
}

$college_id = $_SESSION['college_id'];

// Set role explicitly for the sidebar
$role = 'collegeadmin';
$user_id = $_SESSION['collegeadmin_id'];
$user_name = $_SESSION['collegeadmin_name'] ?? 'Admin';
$current_page = basename($_SERVER['PHP_SELF']);

// Get college details
$collegeStmt = $conn->prepare("SELECT * FROM colleges WHERE id = ?");
$collegeStmt->execute([$college_id]);
$college = $collegeStmt->fetch(PDO::FETCH_ASSOC);

// Get current settings
$settingsStmt = $conn->prepare("SELECT * FROM college_settings WHERE college_id = ?");
$settingsStmt->execute([$college_id]);
$settings = $settingsStmt->fetch(PDO::FETCH_ASSOC);

// If no settings exist, create default settings
if (!$settings) {
    $defaultSettings = [
        'email_notifications' => 1,
        'sms_notifications' => 0,
        'mentor_approval_required' => 1,
        'allow_alumni_registration' => 1,
        'allow_student_registration' => 0,
        'max_mentees_per_mentor' => 5,
        'theme_color' => 'blue',
        'logo_path' => null,
        'privacy_policy' => 'Default privacy policy for ' . $college['name'],
        'terms_of_service' => 'Default terms of service for ' . $college['name']
    ];
    
    $insertStmt = $conn->prepare("INSERT INTO college_settings 
        (college_id, email_notifications, sms_notifications, mentor_approval_required, 
        allow_alumni_registration, allow_student_registration, max_mentees_per_mentor,
        theme_color, logo_path, privacy_policy, terms_of_service) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
    
    $insertStmt->execute([
        $college_id, 
        $defaultSettings['email_notifications'], 
        $defaultSettings['sms_notifications'],
        $defaultSettings['mentor_approval_required'],
        $defaultSettings['allow_alumni_registration'],
        $defaultSettings['allow_student_registration'],
        $defaultSettings['max_mentees_per_mentor'],
        $defaultSettings['theme_color'],
        $defaultSettings['logo_path'],
        $defaultSettings['privacy_policy'],
        $defaultSettings['terms_of_service']
    ]);
    
    // Retrieve newly created settings
    $settingsStmt->execute([$college_id]);
    $settings = $settingsStmt->fetch(PDO::FETCH_ASSOC);
}

$success = '';
$error = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // General settings
    $emailNotifications = isset($_POST['email_notifications']) ? 1 : 0;
    $smsNotifications = isset($_POST['sms_notifications']) ? 1 : 0;
    $mentorApprovalRequired = isset($_POST['mentor_approval_required']) ? 1 : 0;
    $allowAlumniRegistration = isset($_POST['allow_alumni_registration']) ? 1 : 0;
    $allowStudentRegistration = isset($_POST['allow_student_registration']) ? 1 : 0;
    $maxMenteesPerMentor = intval($_POST['max_mentees_per_mentor'] ?? 5);
    $themeColor = $_POST['theme_color'] ?? 'blue';
    
    // Content settings
    $privacyPolicy = trim($_POST['privacy_policy'] ?? '');
    $termsOfService = trim($_POST['terms_of_service'] ?? '');
    
    // Logo upload handling
    $logoPath = $settings['logo_path'];
    if (!empty($_FILES['logo']['name'])) {
        $uploadDir = '../uploads/logos/';
        if (!file_exists($uploadDir)) {
            mkdir($uploadDir, 0777, true);
        }
        
        $fileName = $college_id . '_' . time() . '_' . basename($_FILES['logo']['name']);
        $targetFile = $uploadDir . $fileName;
        
        $imageFileType = strtolower(pathinfo($targetFile, PATHINFO_EXTENSION));
        
        // Check if image file is an actual image
        $check = getimagesize($_FILES['logo']['tmp_name']);
        if ($check === false) {
            $error = "File is not an image.";
        } 
        // Check file size (max 2MB)
        elseif ($_FILES['logo']['size'] > 2000000) {
            $error = "File is too large. Maximum size is 2MB.";
        }
        // Allow only certain file formats
        elseif (!in_array($imageFileType, ['jpg', 'jpeg', 'png', 'gif'])) {
            $error = "Only JPG, JPEG, PNG & GIF files are allowed.";
        }
        // Try to upload file
        elseif (move_uploaded_file($_FILES['logo']['tmp_name'], $targetFile)) {
            $logoPath = 'uploads/logos/' . $fileName;
        } else {
            $error = "There was an error uploading your file.";
        }
    }
    
    if (empty($error)) {
        try {
            // Update settings
            $updateStmt = $conn->prepare("UPDATE college_settings SET 
                email_notifications = ?,
                sms_notifications = ?,
                mentor_approval_required = ?,
                allow_alumni_registration = ?,
                allow_student_registration = ?,
                max_mentees_per_mentor = ?,
                theme_color = ?,
                logo_path = ?,
                privacy_policy = ?,
                terms_of_service = ?,
                updated_at = NOW()
                WHERE college_id = ?");
                
            $updateStmt->execute([
                $emailNotifications,
                $smsNotifications,
                $mentorApprovalRequired,
                $allowAlumniRegistration,
                $allowStudentRegistration,
                $maxMenteesPerMentor,
                $themeColor,
                $logoPath,
                $privacyPolicy,
                $termsOfService,
                $college_id
            ]);
            
            $success = "Settings updated successfully.";
            
            // Refresh settings
            $settingsStmt->execute([$college_id]);
            $settings = $settingsStmt->fetch(PDO::FETCH_ASSOC);
            
        } catch (PDOException $e) {
            $error = "Database error: " . $e->getMessage();
        }
    }
}

require_once '../includes/header.php';
?>

<!-- Main Content -->
<main class="flex-grow py-8 px-4">
    <div class="container mx-auto">
        <!-- Breadcrumb -->
        <nav class="mb-6 text-sm" aria-label="Breadcrumb">
            <ol class="flex items-center space-x-2">
                <li>
                    <a href="collegeadmin_dashboard.php" class="text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary-light">
                        <i class="fas fa-home"></i>
                        <span class="sr-only">Dashboard</span>
                    </a>
                </li>
                <li class="flex items-center">
                    <i class="fas fa-chevron-right text-gray-400 text-xs mx-1"></i>
                    <span class="text-gray-700 dark:text-gray-300">College Settings</span>
                </li>
            </ol>
        </nav>

        <div class="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                    <i class="fas fa-cog text-primary mr-3"></i>
                    College Settings
                </h1>
                <p class="text-gray-600 dark:text-gray-400 mt-1">
                    Configure settings for <?= htmlspecialchars($college['name']) ?>
                </p>
            </div>
        </div>

        <?php if ($success): ?>
            <div class="bg-green-50 dark:bg-green-900/30 border-l-4 border-green-500 text-green-700 dark:text-green-400 p-4 mb-6 flex justify-between items-center" role="alert">
                <div class="flex items-center">
                    <i class="fas fa-check-circle mr-2"></i>
                    <span><?= htmlspecialchars($success) ?></span>
                </div>
                <button type="button" class="text-green-700 dark:text-green-400 hover:opacity-75" onclick="this.parentElement.style.display='none';">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="bg-red-50 dark:bg-red-900/30 border-l-4 border-red-500 text-red-700 dark:text-red-400 p-4 mb-6 flex justify-between items-center" role="alert">
                <div class="flex items-center">
                    <i class="fas fa-exclamation-circle mr-2"></i>
                    <span><?= htmlspecialchars($error) ?></span>
                </div>
                <button type="button" class="text-red-700 dark:text-red-400 hover:opacity-75" onclick="this.parentElement.style.display='none';">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        <?php endif; ?>

        <form method="POST" enctype="multipart/form-data" class="space-y-6">
            <!-- General Settings Card -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 overflow-hidden">
                <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                    <h2 class="text-lg font-semibold text-gray-800 dark:text-white">
                        General Settings
                    </h2>
                </div>
                <div class="p-6 space-y-6">
                    <!-- Notifications -->
                    <div>
                        <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                            Notifications
                        </h3>
                        <div class="space-y-3">
                            <label class="flex items-center">
                                <input type="checkbox" name="email_notifications" class="form-checkbox" 
                                    <?= $settings['email_notifications'] ? 'checked' : '' ?>>
                                <span class="ml-2 text-gray-700 dark:text-gray-300">Enable email notifications</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="sms_notifications" class="form-checkbox" 
                                    <?= $settings['sms_notifications'] ? 'checked' : '' ?>>
                                <span class="ml-2 text-gray-700 dark:text-gray-300">Enable SMS notifications</span>
                            </label>
                        </div>
                    </div>

                    <!-- Registration Settings -->
                    <div>
                        <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                            Registration Settings
                        </h3>
                        <div class="space-y-3">
                            <label class="flex items-center">
                                <input type="checkbox" name="allow_alumni_registration" class="form-checkbox" 
                                    <?= $settings['allow_alumni_registration'] ? 'checked' : '' ?>>
                                <span class="ml-2 text-gray-700 dark:text-gray-300">Allow alumni self-registration</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="allow_student_registration" class="form-checkbox" 
                                    <?= $settings['allow_student_registration'] ? 'checked' : '' ?>>
                                <span class="ml-2 text-gray-700 dark:text-gray-300">Allow student self-registration</span>
                            </label>
                        </div>
                    </div>

                    <!-- Mentorship Settings -->
                    <div>
                        <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                            Mentorship Settings
                        </h3>
                        <div class="space-y-3">
                            <label class="flex items-center">
                                <input type="checkbox" name="mentor_approval_required" class="form-checkbox" 
                                    <?= $settings['mentor_approval_required'] ? 'checked' : '' ?>>
                                <span class="ml-2 text-gray-700 dark:text-gray-300">Require admin approval for mentor-mentee relationships</span>
                            </label>
                            <div>
                                <label for="max_mentees" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    Maximum mentees per mentor
                                </label>
                                <input type="number" id="max_mentees" name="max_mentees_per_mentor" 
                                    class="form-input w-full sm:w-32" 
                                    value="<?= $settings['max_mentees_per_mentor'] ?>" 
                                    min="1" max="20">
                            </div>
                        </div>
                    </div>

                    <!-- Theme Settings -->
                    <div>
                        <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                            Theme Settings
                        </h3>
                        <div class="space-y-3">
                            <div>
                                <label for="theme_color" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    Primary Color Theme
                                </label>
                                <select id="theme_color" name="theme_color" class="form-select w-full sm:w-auto">
                                    <option value="blue" <?= $settings['theme_color'] === 'blue' ? 'selected' : '' ?>>Blue</option>
                                    <option value="green" <?= $settings['theme_color'] === 'green' ? 'selected' : '' ?>>Green</option>
                                    <option value="purple" <?= $settings['theme_color'] === 'purple' ? 'selected' : '' ?>>Purple</option>
                                    <option value="red" <?= $settings['theme_color'] === 'red' ? 'selected' : '' ?>>Red</option>
                                    <option value="orange" <?= $settings['theme_color'] === 'orange' ? 'selected' : '' ?>>Orange</option>
                                </select>
                            </div>
                            <div>
                                <label for="logo" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    College Logo
                                </label>
                                <div class="flex items-center space-x-4">
                                    <?php if (!empty($settings['logo_path']) && file_exists('../' . $settings['logo_path'])): ?>
                                        <div class="h-16 w-16 bg-gray-100 dark:bg-gray-700 rounded overflow-hidden">
                                            <img src="../<?= $settings['logo_path'] ?>" alt="College Logo" class="h-full w-full object-contain">
                                        </div>
                                    <?php endif; ?>
                                    <input type="file" id="logo" name="logo" class="form-input" accept="image/*">
                                </div>
                                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                    Recommended size: 200x200 pixels. Max file size: 2MB.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Legal Settings Card -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 overflow-hidden">
                <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                    <h2 class="text-lg font-semibold text-gray-800 dark:text-white">
                        Legal Documents
                    </h2>
                </div>
                <div class="p-6 space-y-6">
                    <div>
                        <label for="privacy_policy" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Privacy Policy
                        </label>
                        <textarea id="privacy_policy" name="privacy_policy" rows="6" 
                            class="form-input w-full"><?= htmlspecialchars($settings['privacy_policy']) ?></textarea>
                    </div>
                    <div>
                        <label for="terms_of_service" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Terms of Service
                        </label>
                        <textarea id="terms_of_service" name="terms_of_service" rows="6" 
                            class="form-input w-full"><?= htmlspecialchars($settings['terms_of_service']) ?></textarea>
                    </div>
                </div>
            </div>

            <!-- Submit Button -->
            <div class="flex justify-end">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save mr-2"></i> Save Settings
                </button>
            </div>
        </form>
    </div>
</main>

<?php require_once '../includes/footer.php'; ?> 