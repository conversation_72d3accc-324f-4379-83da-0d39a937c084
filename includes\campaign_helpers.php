<?php
// campaign_helpers.php

require_once 'db.php';

/**
 * Get a campaign by its ID.
 */
function getCampaignById(PDO $conn, int $campaign_id): ?array {
    $stmt = $conn->prepare("SELECT * FROM deals WHERE id = ?");
    $stmt->execute([$campaign_id]);
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

/**
 * Get all campaigns by a specific college.
 */
function getCampaignsByCollege(PDO $conn, int $college_id): array {
    $stmt = $conn->prepare("SELECT * FROM deals WHERE college_id = ? ORDER BY created_at DESC");
    $stmt->execute([$college_id]);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * Get all campaigns created by a specific user with a given role.
 */
function getCampaignsByUser(PDO $conn, int $user_id, string $role): array {
    $stmt = $conn->prepare("SELECT * FROM deals WHERE created_by_user_id = ? AND creator_role = ? ORDE<PERSON> BY created_at DESC");
    $stmt->execute([$user_id, $role]);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * Get all active campaigns (approved and not expired).
 */
function getActiveCampaigns(PDO $conn): array {
    $stmt = $conn->prepare("
        SELECT d.*, c.name AS college_name
        FROM deals d
        JOIN colleges c ON d.college_id = c.id
        WHERE d.status = 'approved'
        AND (d.end_date IS NULL OR d.end_date >= CURDATE())
        ORDER BY d.created_at DESC
    ");
    $stmt->execute();
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * Get status badge HTML for a campaign.
 */
function getCampaignStatusBadge(string $status): string {
    $colors = [
        'pending' => 'warning',
        'approved' => 'success',
        'rejected' => 'danger',
        'closed'   => 'secondary'
    ];
    $color = $colors[$status] ?? 'dark';
    return "<span class='badge bg-$color text-uppercase'>$status</span>";
}

/**
 * Format contribution types with emojis.
 */
function formatContributionTypes(string $types): string {
    $map = [
        'monetary' => 'Monetary 💰',
        'in-kind'  => 'In-Kind 🎁',
        'service'  => 'Service 🙋‍♂️'
    ];
    $values = explode(',', $types);
    return implode(', ', array_map(fn($v) => $map[trim($v)] ?? ucfirst(trim($v)), $values));
}
