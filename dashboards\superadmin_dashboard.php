<?php
require_once '../includes/session_config.php';
require_once '../includes/db.php';

if (!isset($_SESSION['superadmin_id'])) {
    header("Location: ../auth/superadmin_login.php");
    exit;
}

$superadmin_id = $_SESSION['superadmin_id'];
$superadmin_name = $_SESSION['superadmin_name'];

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    if ($_POST['action'] === 'create_college') {
        $college_name = trim($_POST['college_name']);
        $slug = trim($_POST['slug']);
        
        if (!empty($college_name) && !empty($slug)) {
            try {
                $sql = "INSERT INTO colleges (name, slug, created_at) VALUES (?, ?, NOW())";
                $stmt = $conn->prepare($sql);
                $stmt->execute([$college_name, $slug]);
                echo json_encode(['success' => true, 'message' => 'College created successfully!']);
            } catch (PDOException $e) {
                echo json_encode(['success' => false, 'message' => 'Error creating college: ' . $e->getMessage()]);
            }
        } else {
            echo json_encode(['success' => false, 'message' => 'All fields are required']);
        }
        exit;
    }
    
    if ($_POST['action'] === 'assign_admin') {
        $college_id = $_POST['college_id'];
        $name = trim($_POST['name']);
        $email = trim($_POST['email']);
        $password = password_hash($_POST['password'], PASSWORD_DEFAULT);
        
        if (!empty($college_id) && !empty($name) && !empty($email) && !empty($_POST['password'])) {
            try {
                $sql = "INSERT INTO college_admins (college_id, name, email, password, created_at) VALUES (?, ?, ?, ?, NOW())";
                $stmt = $conn->prepare($sql);
                $stmt->execute([$college_id, $name, $email, $password]);
                echo json_encode(['success' => true, 'message' => 'Admin assigned successfully!']);
            } catch (PDOException $e) {
                echo json_encode(['success' => false, 'message' => 'Error assigning admin: ' . $e->getMessage()]);
            }
        } else {
            echo json_encode(['success' => false, 'message' => 'All fields are required']);
        }
        exit;
    }
}

// Fetch platform statistics with growth calculations
$sql = "SELECT COUNT(*) FROM colleges";
$stmt = $conn->prepare($sql);
$stmt->execute();
$totalColleges = $stmt->fetchColumn();

$sql = "SELECT COUNT(*) FROM colleges WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH)";
$stmt = $conn->prepare($sql);
$stmt->execute();
$newCollegesMonth = $stmt->fetchColumn();
$collegeGrowth = $totalColleges > 0 ? round(($newCollegesMonth / max($totalColleges - $newCollegesMonth, 1)) * 100, 1) : 0;

$sql = "SELECT COUNT(*) FROM college_admins";
$stmt = $conn->prepare($sql);
$stmt->execute();
$totalAdmins = $stmt->fetchColumn();

$sql = "SELECT COUNT(*) FROM college_admins WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH)";
$stmt = $conn->prepare($sql);
$stmt->execute();
$newAdminsMonth = $stmt->fetchColumn();
$adminGrowth = $totalAdmins > 0 ? round(($newAdminsMonth / max($totalAdmins - $newAdminsMonth, 1)) * 100, 1) : 0;

$sql = "SELECT COUNT(*) FROM students";
$stmt = $conn->prepare($sql);
$stmt->execute();
$totalStudents = $stmt->fetchColumn();

$sql = "SELECT COUNT(*) FROM students WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH)";
$stmt = $conn->prepare($sql);
$stmt->execute();
$newStudentsMonth = $stmt->fetchColumn();
$studentGrowth = $totalStudents > 0 ? round(($newStudentsMonth / max($totalStudents - $newStudentsMonth, 1)) * 100, 1) : 0;

$sql = "SELECT COUNT(*) FROM alumni";
$stmt = $conn->prepare($sql);
$stmt->execute();
$totalAlumni = $stmt->fetchColumn();

$sql = "SELECT COUNT(*) FROM alumni WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH)";
$stmt = $conn->prepare($sql);
$stmt->execute();
$newAlumniMonth = $stmt->fetchColumn();
$alumniGrowth = $totalAlumni > 0 ? round(($newAlumniMonth / max($totalAlumni - $newAlumniMonth, 1)) * 100, 1) : 0;

$sql = "SELECT COUNT(*) FROM faculties";
$stmt = $conn->prepare($sql);
$stmt->execute();
$totalFaculty = $stmt->fetchColumn();

$sql = "SELECT COUNT(*) FROM faculties WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH)";
$stmt = $conn->prepare($sql);
$stmt->execute();
$newFacultyMonth = $stmt->fetchColumn();
$facultyGrowth = $totalFaculty > 0 ? round(($newFacultyMonth / max($totalFaculty - $newFacultyMonth, 1)) * 100, 1) : 0;

// Fetch recent activity
$sql = "SELECT c.name as title, c.created_at, 'college' as type, 'College created' as description FROM colleges c 
        UNION ALL 
        SELECT ca.name as title, ca.created_at, 'admin' as type, 'Admin assigned' as description FROM college_admins ca
        ORDER BY created_at DESC LIMIT 10";
$stmt = $conn->prepare($sql);
$stmt->execute();
$recentActivity = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Fetch colleges for dropdowns and tables
$sql = "SELECT * FROM colleges ORDER BY name ASC";
$stmt = $conn->prepare($sql);
$stmt->execute();
$colleges = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Fetch recent colleges (last 5)
$sql = "SELECT * FROM colleges ORDER BY created_at DESC LIMIT 5";
$stmt = $conn->prepare($sql);
$stmt->execute();
$recentColleges = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Fetch recent admins with college names
$sql = "SELECT ca.*, c.name as college_name FROM college_admins ca 
        JOIN colleges c ON ca.college_id = c.id 
        ORDER BY ca.created_at DESC LIMIT 5";
$stmt = $conn->prepare($sql);
$stmt->execute();
$recentAdmins = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Fetch all admins for the table
$sql = "SELECT ca.*, c.name as college_name FROM college_admins ca 
        JOIN colleges c ON ca.college_id = c.id 
        ORDER BY ca.created_at DESC";
$stmt = $conn->prepare($sql);
$stmt->execute();
$admins = $stmt->fetchAll(PDO::FETCH_ASSOC);

require_once '../includes/header.php';
?>

<!-- Dashboard Header -->
<div class="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 p-6 mb-6">
    <div class="flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                <span class="bg-primary/10 dark:bg-primary/20 p-2 rounded-lg mr-3">
                    <i class="fas fa-tachometer-alt text-primary"></i>
                </span>
                Dashboard Overview
            </h1>
            <p class="text-gray-600 dark:text-gray-400 mt-1">Welcome back, <span class="font-medium text-primary"><?= htmlspecialchars($_SESSION['superadmin_name']) ?></span></p>
        </div>
        <div class="mt-4 md:mt-0 flex gap-3">
            <button onclick="openCreateCollegeModal()" class="btn btn-primary flex items-center text-sm">
                <i class="fas fa-plus-circle mr-2"></i> Add College
            </button>
            <button onclick="openAssignAdminModal()" class="btn btn-outline flex items-center text-sm">
                <i class="fas fa-user-plus mr-2"></i> Assign Admin
            </button>
        </div>
    </div>
    
    <!-- System Status Indicators -->
    <div class="mt-4 grid grid-cols-2 md:grid-cols-4 gap-3">
        <div class="bg-success-light dark:bg-success/20 px-3 py-2 rounded-md flex items-center">
            <i class="fas fa-database text-success mr-2"></i>
            <span class="text-sm font-medium text-success">Database: Online</span>
        </div>
        <div class="bg-success-light dark:bg-success/20 px-3 py-2 rounded-md flex items-center">
            <i class="fas fa-envelope text-success mr-2"></i>
            <span class="text-sm font-medium text-success">Email Service: Active</span>
        </div>
        <div class="bg-success-light dark:bg-success/20 px-3 py-2 rounded-md flex items-center">
            <i class="fas fa-hdd text-success mr-2"></i>
            <span class="text-sm font-medium text-success">Storage: 78% Available</span>
        </div>
        <div class="bg-success-light dark:bg-success/20 px-3 py-2 rounded-md flex items-center">
            <i class="fas fa-clock text-success mr-2"></i>
            <span class="text-sm font-medium text-success">Last Backup: <?= date('M d, Y', strtotime('-1 day')) ?></span>
        </div>
    </div>
</div>

<?php if (isset($_SESSION['flash_message'])): ?>
    <div class="bg-blue-50 dark:bg-blue-900/30 border-l-4 border-blue-500 text-blue-700 dark:text-blue-400 p-4 mb-6 flex justify-between items-center" role="alert">
        <p><?= $_SESSION['flash_message']; unset($_SESSION['flash_message']); ?></p>
        <button type="button" class="text-blue-700 dark:text-blue-400 hover:text-blue-900" onclick="this.parentElement.style.display='none';">
            <i class="fas fa-times"></i>
        </button>
    </div>
<?php endif; ?>

<!-- Analytics Overview -->
<div class="mb-6">
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-lg font-semibold text-gray-800 dark:text-white flex items-center">
            <i class="fas fa-chart-bar mr-2 text-primary"></i> Platform Analytics
        </h2>
        <div class="flex text-sm">
            <a href="platform_analytics.php?period=weekly" class="px-3 py-1 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 rounded-l-md transition">Weekly</a>
            <a href="platform_analytics.php" class="px-3 py-1 bg-primary text-white rounded-r-md transition">Monthly</a>
        </div>
    </div>
    
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4">
        <!-- Total Colleges -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 transition-all duration-300 hover:shadow-md">
            <div class="flex items-center">
                <div class="bg-primary-light dark:bg-primary-dark/30 p-3 rounded-full">
                    <i class="fas fa-university text-primary text-lg"></i>
                </div>
                <div class="ml-4">
                    <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Colleges</h3>
                    <div class="flex items-baseline">
                        <p class="text-xl font-bold text-gray-900 dark:text-white"><?= $totalColleges ?></p>
                        <span class="ml-2 flex items-center <?= $collegeGrowth > 0 ? 'text-green-500' : 'text-red-500' ?>">
                            <i class="fas fa-<?= $collegeGrowth > 0 ? 'arrow-up' : 'arrow-down' ?> text-xs mr-1"></i>
                            <?= abs($collegeGrowth) ?>%
                        </span>
                    </div>
                </div>
            </div>
            <div class="mt-3">
                <div class="w-full bg-gray-200 dark:bg-gray-700 h-1 rounded-full overflow-hidden">
                    <div class="bg-primary h-full rounded-full" style="width: <?= min(100, max(10, $collegeGrowth * 2)) ?>%"></div>
                </div>
                <div class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                    <?= $newCollegesMonth ?> new this month
                </div>
            </div>
        </div>
        
        <!-- Total Admins -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 transition-all duration-300 hover:shadow-md">
            <div class="flex items-center">
                <div class="bg-blue-100 dark:bg-blue-900/30 p-3 rounded-full">
                    <i class="fas fa-user-shield text-blue-600 dark:text-blue-400 text-lg"></i>
                </div>
                <div class="ml-4">
                    <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Admins</h3>
                    <div class="flex items-baseline">
                        <p class="text-xl font-bold text-gray-900 dark:text-white"><?= $totalAdmins ?></p>
                        <span class="ml-2 flex items-center <?= $adminGrowth > 0 ? 'text-green-500' : 'text-red-500' ?>">
                            <i class="fas fa-<?= $adminGrowth > 0 ? 'arrow-up' : 'arrow-down' ?> text-xs mr-1"></i>
                            <?= abs($adminGrowth) ?>%
                        </span>
                    </div>
                </div>
            </div>
            <div class="mt-3">
                <div class="w-full bg-gray-200 dark:bg-gray-700 h-1 rounded-full overflow-hidden">
                    <div class="bg-blue-600 dark:bg-blue-500 h-full rounded-full" style="width: <?= min(100, max(10, $adminGrowth * 2)) ?>%"></div>
                </div>
                <div class="mt-1 flex justify-between items-center text-xs">
                    <span class="text-gray-500 dark:text-gray-400"><?= $newAdminsMonth ?> new this month</span>
                    <a href="list_admins.php" class="text-blue-600 dark:text-blue-400 hover:underline flex items-center">
                        View <i class="fas fa-chevron-right ml-1 text-xs"></i>
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Total Students -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 transition-all duration-300 hover:shadow-md">
            <div class="flex items-center">
                <div class="bg-green-100 dark:bg-green-900/30 p-3 rounded-full">
                    <i class="fas fa-user-graduate text-green-600 dark:text-green-400 text-lg"></i>
                </div>
                <div class="ml-4">
                    <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Students</h3>
                    <div class="flex items-baseline">
                        <p class="text-xl font-bold text-gray-900 dark:text-white"><?= $totalStudents ?></p>
                        <span class="ml-2 flex items-center <?= $studentGrowth > 0 ? 'text-green-500' : 'text-red-500' ?>">
                            <i class="fas fa-<?= $studentGrowth > 0 ? 'arrow-up' : 'arrow-down' ?> text-xs mr-1"></i>
                            <?= abs($studentGrowth) ?>%
                        </span>
                    </div>
                </div>
            </div>
            <div class="mt-3">
                <div class="w-full bg-gray-200 dark:bg-gray-700 h-1 rounded-full overflow-hidden">
                    <div class="bg-green-600 dark:bg-green-500 h-full rounded-full" style="width: <?= min(100, max(10, $studentGrowth * 2)) ?>%"></div>
                </div>
                <div class="mt-1 flex justify-between items-center text-xs">
                    <span class="text-gray-500 dark:text-gray-400"><?= $newStudentsMonth ?> new this month</span>
                    <a href="superadmin_list_students.php" class="text-green-600 dark:text-green-400 hover:underline flex items-center">
                        View <i class="fas fa-chevron-right ml-1 text-xs"></i>
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Total Alumni -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 transition-all duration-300 hover:shadow-md">
            <div class="flex items-center">
                <div class="bg-purple-100 dark:bg-purple-900/30 p-3 rounded-full">
                    <i class="fas fa-user-tie text-purple-600 dark:text-purple-400 text-lg"></i>
                </div>
                <div class="ml-4">
                    <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Alumni</h3>
                    <div class="flex items-baseline">
                        <p class="text-xl font-bold text-gray-900 dark:text-white"><?= $totalAlumni ?></p>
                        <span class="ml-2 flex items-center <?= $alumniGrowth > 0 ? 'text-green-500' : 'text-red-500' ?>">
                            <i class="fas fa-<?= $alumniGrowth > 0 ? 'arrow-up' : 'arrow-down' ?> text-xs mr-1"></i>
                            <?= abs($alumniGrowth) ?>%
                        </span>
                    </div>
                </div>
            </div>
            <div class="mt-3">
                <div class="w-full bg-gray-200 dark:bg-gray-700 h-1 rounded-full overflow-hidden">
                    <div class="bg-purple-600 dark:bg-purple-500 h-full rounded-full" style="width: <?= min(100, max(10, $alumniGrowth * 2)) ?>%"></div>
                </div>
                <div class="mt-1 flex justify-between items-center text-xs">
                    <span class="text-gray-500 dark:text-gray-400"><?= $newAlumniMonth ?> new this month</span>
                    <a href="superadmin_list_alumni.php" class="text-purple-600 dark:text-purple-400 hover:underline flex items-center">
                        View <i class="fas fa-chevron-right ml-1 text-xs"></i>
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Total Faculty -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 transition-all duration-300 hover:shadow-md">
            <div class="flex items-center">
                <div class="bg-orange-100 dark:bg-orange-900/30 p-3 rounded-full">
                    <i class="fas fa-chalkboard-teacher text-orange-600 dark:text-orange-400 text-lg"></i>
                </div>
                <div class="ml-4">
                    <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Faculty</h3>
                    <div class="flex items-baseline">
                        <p class="text-xl font-bold text-gray-900 dark:text-white"><?= $totalFaculty ?></p>
                        <span class="ml-2 flex items-center <?= $facultyGrowth > 0 ? 'text-green-500' : 'text-red-500' ?>">
                            <i class="fas fa-<?= $facultyGrowth > 0 ? 'arrow-up' : 'arrow-down' ?> text-xs mr-1"></i>
                            <?= abs($facultyGrowth) ?>%
                        </span>
                    </div>
                </div>
            </div>
            <div class="mt-3">
                <div class="w-full bg-gray-200 dark:bg-gray-700 h-1 rounded-full overflow-hidden">
                    <div class="bg-orange-500 dark:bg-orange-500 h-full rounded-full" style="width: <?= min(100, max(10, $facultyGrowth * 2)) ?>%"></div>
                </div>
                <div class="mt-1 flex justify-between items-center text-xs">
                    <span class="text-gray-500 dark:text-gray-400"><?= $newFacultyMonth ?> new this month</span>
                    <a href="superadmin_list_faculty.php" class="text-orange-600 dark:text-orange-400 hover:underline flex items-center">
                        View <i class="fas fa-chevron-right ml-1 text-xs"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Content Grid -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
    <!-- Left Column -->
    <div class="lg:col-span-2">
        <!-- Recent Colleges -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 mb-6">
            <div class="border-b border-gray-200 dark:border-gray-700 px-4 py-3 flex justify-between items-center">
                <h2 class="font-semibold text-gray-800 dark:text-white flex items-center">
                    <span class="bg-blue-500/10 dark:bg-blue-500/20 p-1.5 rounded-md mr-2">
                        <i class="fas fa-university text-primary text-sm"></i>
                    </span>
                    Recent Colleges
                </h2>
                <div class="flex items-center gap-2">
                    <button onclick="openCreateCollegeModal()" class="p-1.5 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 rounded-full text-gray-600 dark:text-gray-300">
                        <i class="fas fa-plus text-xs"></i>
                    </button>
                    <a href="list_colleges.php" class="text-sm text-primary hover:underline flex items-center">
                        View All <i class="fas fa-chevron-right ml-1 text-xs"></i>
                    </a>
                </div>
            </div>
            <div class="p-4">
                <?php if (count($recentColleges) > 0): ?>
                    <div class="space-y-3">
                        <?php foreach ($recentColleges as $college): ?>
                            <div class="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-200">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-primary to-primary-dark/80 flex items-center justify-center text-white shadow-sm">
                                        <i class="fas fa-university"></i>
                                    </div>
                                    <div class="ml-3">
                                        <h4 class="font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($college['name']) ?></h4>
                                        <div class="flex items-center space-x-2 mt-1">
                                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
                                                <?= htmlspecialchars($college['slug']) ?>
                                            </span>
                                            <span class="text-xs text-gray-500 dark:text-gray-400">
                                                <i class="far fa-clock mr-1"></i> <?= date('M j, Y', strtotime($college['created_at'])) ?>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="flex space-x-1">
                                    <a href="edit_college.php?id=<?= $college['id'] ?>" class="p-1.5 bg-gray-100 hover:bg-primary/10 dark:bg-gray-700 dark:hover:bg-gray-600 rounded-md text-primary">
                                        <i class="fas fa-edit text-sm"></i>
                                    </a>
                                    <a href="assign_admin.php?college_id=<?= $college['id'] ?>" class="p-1.5 bg-gray-100 hover:bg-blue-100 dark:bg-gray-700 dark:hover:bg-gray-600 rounded-md text-blue-600 dark:text-blue-400">
                                        <i class="fas fa-user-plus text-sm"></i>
                                    </a>
                                    <a href="view_college.php?id=<?= $college['id'] ?>" class="p-1.5 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 rounded-md text-gray-600 dark:text-gray-400">
                                        <i class="fas fa-eye text-sm"></i>
                                    </a>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="flex flex-col items-center justify-center py-8 text-center">
                        <div class="w-16 h-16 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center text-gray-400 dark:text-gray-500 mb-4">
                            <i class="fas fa-university text-xl"></i>
                        </div>
                        <p class="text-gray-500 dark:text-gray-400">No colleges created yet.</p>
                        <button onclick="openCreateCollegeModal()" class="mt-4 btn btn-primary text-sm">
                            <i class="fas fa-plus mr-1"></i> Add Your First College
                        </button>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- All Colleges Table -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 mb-6">
            <div class="border-b border-gray-200 dark:border-gray-700 px-4 py-3 flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3">
                <h2 class="font-semibold text-gray-800 dark:text-white flex items-center">
                    <span class="bg-primary/10 dark:bg-primary/20 p-1.5 rounded-md mr-2">
                        <i class="fas fa-table text-primary text-sm"></i>
                    </span>
                    Colleges Management
                </h2>
                <div class="flex items-center gap-3">
                    <div class="relative">
                        <input type="text" id="collegeSearch" class="form-input py-1.5 pl-8 text-sm rounded-md border-gray-300 dark:border-gray-600 focus:border-primary focus:ring focus:ring-primary/30" placeholder="Search colleges...">
                        <div class="absolute inset-y-0 left-0 pl-2.5 flex items-center pointer-events-none">
                            <i class="fas fa-search text-gray-400 text-sm"></i>
                        </div>
                    </div>
                    <button onclick="openCreateCollegeModal()" class="btn btn-primary text-sm flex items-center py-1.5 px-3">
                        <i class="fas fa-plus mr-1"></i> Add College
                    </button>
                </div>
            </div>
            <div class="p-4 overflow-x-auto">
                <table id="collegeTable" class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead>
                        <tr>
                            <th class="px-4 py-3 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider rounded-tl-md">Name</th>
                            <th class="px-4 py-3 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Slug</th>
                            <th class="px-4 py-3 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Created</th>
                            <th class="px-4 py-3 bg-gray-50 dark:bg-gray-700 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider rounded-tr-md">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                        <?php 
                        // Show only the first 5 colleges in this table
                        $displayColleges = array_slice($colleges, 0, 5);
                        foreach ($displayColleges as $college): 
                        ?>
                            <tr class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                                <td class="px-4 py-3 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-primary/10 dark:bg-primary/20 rounded-md flex items-center justify-center text-primary mr-3">
                                            <i class="fas fa-university text-sm"></i>
                                        </div>
                                        <span class="font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($college['name']) ?></span>
                                    </div>
                                </td>
                                <td class="px-4 py-3 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                                        <?= htmlspecialchars($college['slug']) ?>
                                    </span>
                                </td>
                                <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                    <i class="far fa-calendar-alt mr-1"></i> <?= date('M j, Y', strtotime($college['created_at'])) ?>
                                </td>
                                <td class="px-4 py-3 whitespace-nowrap text-sm text-center">
                                    <div class="flex items-center justify-center space-x-2">
                                        <a href="assign_admin.php?college_id=<?= $college['id'] ?>" class="p-1.5 bg-blue-100 hover:bg-blue-200 dark:bg-blue-900/30 dark:hover:bg-blue-800/50 rounded text-blue-600 dark:text-blue-400 transition-colors" title="Assign Admin">
                                            <i class="fas fa-user-plus text-xs"></i>
                                        </a>
                                        <a href="edit_college.php?id=<?= $college['id'] ?>" class="p-1.5 bg-primary/10 hover:bg-primary/20 dark:bg-primary/20 dark:hover:bg-primary/30 rounded text-primary transition-colors" title="Edit College">
                                            <i class="fas fa-edit text-xs"></i>
                                        </a>
                                        <a href="view_college.php?id=<?= $college['id'] ?>" class="p-1.5 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 rounded text-gray-600 dark:text-gray-400 transition-colors" title="View Details">
                                            <i class="fas fa-eye text-xs"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                        <?php if (count($displayColleges) === 0): ?>
                            <tr>
                                <td colspan="4" class="px-4 py-6 text-center text-gray-500 dark:text-gray-400">
                                    No colleges found. Click "Add College" to create one.
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
                <div class="mt-4 flex justify-between items-center text-sm text-gray-500 dark:text-gray-400">
                    <div>Showing <?= count($displayColleges) ?> of <?= count($colleges) ?> colleges</div>
                    <a href="list_colleges.php" class="text-primary hover:underline flex items-center">
                        View All Colleges <i class="fas fa-chevron-right ml-1 text-xs"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Right Column -->
    <div>
        <!-- Recent Activity -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 mb-6">
            <div class="border-b border-gray-200 dark:border-gray-700 px-4 py-3 flex justify-between items-center">
                <h2 class="font-semibold text-gray-800 dark:text-white flex items-center">
                    <span class="bg-purple-500/10 dark:bg-purple-500/20 p-1.5 rounded-md mr-2">
                        <i class="fas fa-history text-purple-600 dark:text-purple-400 text-sm"></i>
                    </span>
                    Platform Activity
                </h2>
                <div class="flex gap-1">
                    <button class="p-1.5 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 rounded text-gray-500 dark:text-gray-400 transition-colors">
                        <i class="fas fa-sync-alt text-xs"></i>
                    </button>
                </div>
            </div>
            <div class="p-4">
                <?php if (count($recentActivity) > 0): ?>
                    <div class="relative">
                        <!-- Timeline line -->
                        <div class="absolute top-0 bottom-0 left-4 w-0.5 bg-gray-200 dark:bg-gray-700"></div>
                        
                        <div class="space-y-4">
                            <?php foreach ($recentActivity as $index => $activity): ?>
                                <?php if ($index < 5): // Limit to 5 activities ?>
                                <div class="flex relative">
                                    <!-- Timeline dot -->
                                    <div class="absolute left-4 transform -translate-x-1/2 -translate-y-1/3">
                                        <div class="w-8 h-8 rounded-full border-4 border-white dark:border-gray-800 <?= $activity['type'] === 'college' ? 'bg-blue-100 dark:bg-blue-800 text-blue-600 dark:text-blue-400' : 'bg-green-100 dark:bg-green-800 text-green-600 dark:text-green-400' ?> flex items-center justify-center">
                                            <i class="fas <?= $activity['type'] === 'college' ? 'fa-university' : 'fa-user-shield' ?> text-xs"></i>
                                        </div>
                                    </div>
                                    
                                    <!-- Content -->
                                    <div class="ml-10 bg-white dark:bg-gray-800 p-3 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-shadow w-full">
                                        <div class="flex justify-between items-start">
                                            <h4 class="font-medium text-gray-900 dark:text-white text-sm"><?= htmlspecialchars($activity['title']) ?></h4>
                                            <span class="px-2 py-0.5 rounded-full text-xs <?= $activity['type'] === 'college' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300' : 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300' ?>">
                                                <?= ucfirst($activity['type']) ?>
                                            </span>
                                        </div>
                                        <p class="text-xs text-gray-600 dark:text-gray-400 mt-1"><?= $activity['description'] ?></p>
                                        <div class="flex items-center justify-between mt-2">
                                            <p class="text-xs text-gray-400 dark:text-gray-500 flex items-center">
                                                <i class="far fa-clock mr-1"></i> <?= date('M j, H:i', strtotime($activity['created_at'])) ?>
                                            </p>
                                            <?php if ($activity['type'] === 'college'): ?>
                                                <a href="view_college.php?name=<?= urlencode($activity['title']) ?>" class="text-xs text-primary hover:underline">View</a>
                                            <?php elseif ($activity['type'] === 'admin'): ?>
                                                <a href="list_admins.php?search=<?= urlencode($activity['title']) ?>" class="text-xs text-primary hover:underline">View</a>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    
                    <div class="flex justify-center mt-4">
                        <a href="activity_log.php" class="text-sm text-primary hover:underline flex items-center">
                            View All Activity <i class="fas fa-chevron-right ml-1 text-xs"></i>
                        </a>
                    </div>
                <?php else: ?>
                    <div class="flex flex-col items-center justify-center py-8 text-center">
                        <div class="w-16 h-16 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center text-gray-400 dark:text-gray-500 mb-4">
                            <i class="fas fa-history text-xl"></i>
                        </div>
                        <p class="text-gray-500 dark:text-gray-400">No recent activity found</p>
                        <p class="text-sm text-gray-400 dark:text-gray-500 mt-1">New actions will appear here</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Recent Admins -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 mb-6">
            <div class="border-b border-gray-200 dark:border-gray-700 px-4 py-3 flex justify-between items-center">
                <h2 class="font-semibold text-gray-800 dark:text-white flex items-center">
                    <span class="bg-blue-500/10 dark:bg-blue-500/20 p-1.5 rounded-md mr-2">
                        <i class="fas fa-user-shield text-blue-600 dark:text-blue-400 text-sm"></i>
                    </span>
                    Recent Admins
                </h2>
                <a href="list_admins.php" class="text-sm text-primary hover:underline">View All</a>
            </div>
            <div class="p-4">
                <?php if (count($recentAdmins) > 0): ?>
                    <div class="space-y-3">
                        <?php foreach ($recentAdmins as $admin): ?>
                            <div class="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                                <div class="flex items-center">
                                    <div class="w-9 h-9 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center text-blue-600 dark:text-blue-400 mr-3">
                                        <i class="fas fa-user-shield"></i>
                                    </div>
                                    <div>
                                        <h4 class="font-medium text-gray-900 dark:text-white text-sm"><?= htmlspecialchars($admin['name']) ?></h4>
                                        <p class="text-xs text-gray-500 dark:text-gray-400"><?= htmlspecialchars($admin['college_name']) ?></p>
                                    </div>
                                </div>
                                <div class="flex space-x-1">
                                    <a href="edit_collegeadmin.php?id=<?= $admin['id'] ?>" class="p-1.5 bg-gray-100 hover:bg-primary/10 dark:bg-gray-700 dark:hover:bg-gray-600 rounded-md text-primary">
                                        <i class="fas fa-edit text-xs"></i>
                                    </a>
                                    <a href="reset_admin_password.php?id=<?= $admin['id'] ?>" class="p-1.5 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 rounded-md text-gray-600 dark:text-gray-400">
                                        <i class="fas fa-key text-xs"></i>
                                    </a>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <p class="text-gray-500 dark:text-gray-400 text-center py-4">No college admins have been assigned yet.</p>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- System Health Overview -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
            <div class="border-b border-gray-200 dark:border-gray-700 px-4 py-3 flex justify-between items-center">
                <h2 class="font-semibold text-gray-800 dark:text-white flex items-center">
                    <span class="bg-green-500/10 dark:bg-green-500/20 p-1.5 rounded-md mr-2">
                        <i class="fas fa-server text-green-600 dark:text-green-400 text-sm"></i>
                    </span>
                    System Health
                </h2>
                <a href="system_check.php" class="text-sm text-primary hover:underline">Details</a>
            </div>
            <div class="p-4">
                <div class="space-y-3">
                    <div class="flex justify-between items-center p-3 rounded-lg bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800">
                        <div class="flex items-center">
                            <i class="fas fa-database text-green-600 dark:text-green-400 mr-3"></i>
                            <span class="text-gray-700 dark:text-gray-300 text-sm">Database</span>
                        </div>
                        <span class="px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-200">Online</span>
                    </div>
                    <div class="flex justify-between items-center p-3 rounded-lg bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800">
                        <div class="flex items-center">
                            <i class="fas fa-hdd text-green-600 dark:text-green-400 mr-3"></i>
                            <span class="text-gray-700 dark:text-gray-300 text-sm">Storage</span>
                        </div>
                        <span class="px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-200">85% Free</span>
                    </div>
                    <div class="flex justify-between items-center p-3 rounded-lg bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800">
                        <div class="flex items-center">
                            <i class="fas fa-envelope text-green-600 dark:text-green-400 mr-3"></i>
                            <span class="text-gray-700 dark:text-gray-300 text-sm">Email Service</span>
                        </div>
                        <span class="px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-200">Operational</span>
                    </div>
                    <div class="flex justify-between items-center p-3 rounded-lg bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800">
                        <div class="flex items-center">
                            <i class="fas fa-clock text-blue-600 dark:text-blue-400 mr-3"></i>
                            <span class="text-gray-700 dark:text-gray-300 text-sm">Last Backup</span>
                        </div>
                        <span class="text-gray-500 dark:text-gray-400 text-xs"><?= date('M j, H:i', strtotime('-1 day')) ?></span>
                    </div>
                </div>

                <div class="mt-4 grid grid-cols-2 gap-3">
                    <a href="system_check.php" class="btn btn-outline flex justify-center items-center text-sm py-2">
                        <i class="fas fa-sync-alt mr-2"></i> System Check
                    </a>
                    <a href="system_logs.php" class="btn btn-outline flex justify-center items-center text-sm py-2">
                        <i class="fas fa-file-alt mr-2"></i> View Logs
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create College Modal -->
<div id="createCollegeModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div class="mt-3">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">Create New College</h3>
                <button onclick="closeCreateCollegeModal()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="createCollegeForm">
                <div class="mb-4">
                    <label for="modal_college_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">College Name</label>
                    <input type="text" id="modal_college_name" name="college_name" class="form-input" required>
                </div>
                <div class="mb-4">
                    <label for="modal_slug" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Slug (internal reference)</label>
                    <input type="text" id="modal_slug" name="slug" class="form-input" required>
                </div>
                <div class="flex justify-between items-center">
                    <button type="button" onclick="closeCreateCollegeModal()" class="btn btn-outline">
                        Cancel
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-plus-circle mr-2"></i> Create College
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Assign Admin Modal -->
<div id="assignAdminModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div class="mt-3">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">Assign Admin to College</h3>
                <button onclick="closeAssignAdminModal()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="assignAdminForm">
                <div class="mb-4">
                    <label for="modal_college_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Select College</label>
                    <select id="modal_college_id" name="college_id" class="form-input" required>
                        <option value="">-- Choose a college --</option>
                        <?php foreach ($colleges as $college): ?>
                            <option value="<?= $college['id'] ?>"><?= htmlspecialchars($college['name']) ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="mb-4">
                    <label for="modal_admin_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Admin Name</label>
                    <input type="text" id="modal_admin_name" name="name" class="form-input" required>
                </div>
                <div class="mb-4">
                    <label for="modal_admin_email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Email (Username)</label>
                    <input type="email" id="modal_admin_email" name="email" class="form-input" required>
                </div>
                <div class="mb-4">
                    <label for="modal_admin_password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Password</label>
                    <input type="password" id="modal_admin_password" name="password" class="form-input" required>
                </div>
                <div class="flex justify-between items-center">
                    <button type="button" onclick="closeAssignAdminModal()" class="btn btn-outline">
                        Cancel
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-user-plus mr-2"></i> Assign Admin
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Alert Container -->
<div id="alertContainer" class="fixed top-4 right-4 z-50"></div>

<script>
// Modal functions
function openCreateCollegeModal() {
    document.getElementById('createCollegeModal').classList.remove('hidden');
}

function closeCreateCollegeModal() {
    document.getElementById('createCollegeModal').classList.add('hidden');
    document.getElementById('createCollegeForm').reset();
}

function openAssignAdminModal() {
    document.getElementById('assignAdminModal').classList.remove('hidden');
}

function closeAssignAdminModal() {
    document.getElementById('assignAdminModal').classList.add('hidden');
    document.getElementById('assignAdminForm').reset();
}

// Auto-generate slug from college name
document.getElementById('modal_college_name').addEventListener('input', function() {
    const slug = this.value.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-+|-+$/g, '');
    document.getElementById('modal_slug').value = slug;
});

// Form submissions
document.getElementById('createCollegeForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    formData.append('action', 'create_college');
    
    fetch(window.location.href, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        showAlert(data.message, data.success ? 'success' : 'error');
        if (data.success) {
            closeCreateCollegeModal();
            setTimeout(() => window.location.reload(), 1500);
        }
    })
    .catch(error => {
        showAlert('An error occurred. Please try again.', 'error');
    });
});

document.getElementById('assignAdminForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    formData.append('action', 'assign_admin');
    
    fetch(window.location.href, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        showAlert(data.message, data.success ? 'success' : 'error');
        if (data.success) {
            closeAssignAdminModal();
            setTimeout(() => window.location.reload(), 1500);
        }
    })
    .catch(error => {
        showAlert('An error occurred. Please try again.', 'error');
    });
});

// Alert function
function showAlert(message, type) {
    const alertContainer = document.getElementById('alertContainer');
    const alertId = 'alert-' + Date.now();
    
    const alertHTML = `
        <div id="${alertId}" class="mb-4 p-4 rounded-lg shadow-lg transform translate-x-full transition-transform duration-300 ${
            type === 'success' ? 'bg-green-100 border border-green-400 text-green-700' : 
            'bg-red-100 border border-red-400 text-red-700'
        }">
            <div class="flex justify-between items-center">
                <div class="flex items-center">
                    <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'} mr-2"></i>
                    <span>${message}</span>
                </div>
                <button onclick="closeAlert('${alertId}')" class="ml-4 text-sm font-medium hover:opacity-75">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    `;
    
    alertContainer.insertAdjacentHTML('beforeend', alertHTML);
    
    // Animate in
    setTimeout(() => {
        document.getElementById(alertId).classList.remove('translate-x-full');
    }, 100);
    
    // Auto close after 5 seconds
    setTimeout(() => closeAlert(alertId), 5000);
}

function closeAlert(alertId) {
    const alert = document.getElementById(alertId);
    if (alert) {
        alert.classList.add('translate-x-full');
        setTimeout(() => alert.remove(), 300);
    }
}

// Close modals when clicking outside
window.addEventListener('click', function(e) {
    if (e.target.classList.contains('fixed') && e.target.classList.contains('inset-0')) {
        closeCreateCollegeModal();
        closeAssignAdminModal();
    }
});

// Search functionality for college table
if (document.getElementById('collegeSearch')) {
    document.getElementById('collegeSearch').addEventListener('keyup', function () {
        let filter = this.value.toLowerCase();
        let rows = document.querySelectorAll('#collegeTable tbody tr');

        rows.forEach(row => {
            let collegeName = row.querySelector('td:nth-child(1)').textContent.toLowerCase();
            let slug = row.querySelector('td:nth-child(2)').textContent.toLowerCase();
            row.style.display = (collegeName.includes(filter) || slug.includes(filter)) ? '' : 'none';
        });
    });
}
</script>

<?php require_once '../includes/footer.php'; ?>
