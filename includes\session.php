<?php
// Include session configuration
require_once __DIR__ . '/session_config.php';

/**
 * Enforce login by role.
 * Redirects to role-specific login page if session is missing.
 */
function require_login($role = 'guest') {
    $redirects = [
        'superadmin' => '/auth/superadmin_login.php',
        'collegeadmin' => '/auth/collegeadmin_login.php',
        'faculty' => '/auth/faculty_login.php',
        'student' => '/auth/student_login.php',
        'alumni' => '/auth/alumni_login.php',
    ];

    $session_keys = [
        'superadmin' => 'superadmin_id',
        'collegeadmin' => 'collegeadmin_id',
        'faculty' => 'faculty_id',
        'student' => 'student_id',
        'alumni' => 'alumni_id',
    ];

    if (!isset($_SESSION[$session_keys[$role]])) {
        header("Location: " . $redirects[$role]);
        exit;
    }
}
