<?php
require_once '../includes/db.php';
require_once '../includes/header.php';
require_once '../notify/send_email.php';

if (!isset($_SESSION['faculty_id'])) {
    header("Location: ../auth/faculty_login.php");
    exit;
}

$college_id = $_SESSION['college_id'];

if (isset($_GET['approve'])) {
    $id = intval($_GET['approve']);

    $stmt = $conn->prepare("UPDATE alumni SET verified = 1 WHERE id = ? AND college_id = ?");
    $stmt->execute([$id, $college_id]);

    $getAlumni = $conn->prepare("SELECT full_name, email FROM alumni WHERE id = ?");
    $getAlumni->execute([$id]);
    $alumni = $getAlumni->fetch(PDO::FETCH_ASSOC);

    if ($alumni) {
        $subject = "Your Alumni Account Has Been Approved";
        $body = "
            <p>Dear {$alumni['full_name']},</p>
            <p>Your alumni account has been approved. You can now log in and respond to student mentorship requests.</p>
            <p><a href='https://darkviolet-vulture-501696.hostingersite.com/auth/alumni_login.php'>Login Here</a></p>
            <p>Best regards,<br>Connect My Students</p>
        ";
        sendEmail($alumni['email'], $alumni['full_name'], $subject, $body);
    }

    $_SESSION['flash_message'] = "Alumni approved successfully.";
    header("Location: verify_alumni.php");
    exit;
}

if (isset($_GET['reject'])) {
    $id = intval($_GET['reject']);
    $stmt = $conn->prepare("DELETE FROM alumni WHERE id = ? AND college_id = ?");
    $stmt->execute([$id, $college_id]);
    $_SESSION['flash_message'] = "Alumni rejected and removed.";
    header("Location: verify_alumni.php");
    exit;
}

$stmt = $conn->prepare("SELECT * FROM alumni WHERE verified = 0 AND college_id = ? ORDER BY created_at DESC");
$stmt->execute([$college_id]);
$alumni_list = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<h2 class="mb-4">Verify Alumni Profiles</h2>

<?php if (isset($_SESSION['flash_message'])): ?>
    <div class="alert alert-info"><?= $_SESSION['flash_message']; unset($_SESSION['flash_message']); ?></div>
<?php endif; ?>

<?php if (count($alumni_list) > 0): ?>
    <div class="card">
        <div class="card-header bg-dark text-white">Pending Alumni</div>
        <div class="card-body">
            <table class="table table-bordered table-hover table-striped">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Full Name</th>
                        <th>Email</th>
                        <th>Industry</th>
                        <th>Joined On</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($alumni_list as $alumni): ?>
                        <tr>
                            <td><?= $alumni['id'] ?></td>
                            <td><?= htmlspecialchars($alumni['full_name']) ?></td>
                            <td><?= htmlspecialchars($alumni['email']) ?></td>
                            <td><?= htmlspecialchars($alumni['industry']) ?></td>
                            <td><?= $alumni['created_at'] ?></td>
                            <td>
                                <a href="?approve=<?= $alumni['id'] ?>" class="btn btn-sm btn-success">Approve</a>
                                <a href="?reject=<?= $alumni['id'] ?>" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to reject this alumni?');">Reject</a>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
<?php else: ?>
    <div class="alert alert-secondary">No pending alumni to verify.</div>
<?php endif; ?>

<?php require_once '../includes/footer.php'; ?>
