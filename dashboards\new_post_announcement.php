<?php
// Include session configuration
require_once '../includes/session_config.php';
require_once '../includes/db.php';
require_once '../includes/db_schema_check.php';

if (!isset($_SESSION['collegeadmin_id'])) {
    header("Location: ../auth/collegeadmin_login.php");
    exit;
}

$college_id = $_SESSION['college_id'];
$admin_name = $_SESSION['collegeadmin_name'] ?? 'Admin';

// Set role explicitly for the sidebar
$role = 'collegeadmin';
$user_id = $_SESSION['collegeadmin_id'];
$user_name = $_SESSION['collegeadmin_name'];
$current_page = basename($_SERVER['PHP_SELF']);

// Get college details
$collegeStmt = $conn->prepare("SELECT name FROM colleges WHERE id = ?");
$collegeStmt->execute([$college_id]);
$college = $collegeStmt->fetch(PDO::FETCH_ASSOC);

$error = '';
$success = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $title = trim($_POST['title'] ?? '');
    $content = trim($_POST['content'] ?? '');
    $audience = $_POST['audience'] ?? [];
    $is_important = isset($_POST['is_important']) ? 1 : 0;
    $expires_at = $_POST['expires_at'] ?? null;
    
    // Validation
    if (empty($title)) {
        $error = "Announcement title is required.";
    } elseif (empty($content)) {
        $error = "Announcement content is required.";
    } elseif (empty($audience)) {
        $error = "Please select at least one target audience.";
    } else {
        try {
            // Check if announcements table exists, create if not
            try {
                $conn->query("SELECT 1 FROM announcements LIMIT 1");
            } catch (PDOException $e) {
                // Create announcements table
                $conn->exec("CREATE TABLE IF NOT EXISTS announcements (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    college_id INT NOT NULL,
                    title VARCHAR(255) NOT NULL,
                    content TEXT NOT NULL,
                    audience JSON NOT NULL,
                    is_important BOOLEAN DEFAULT FALSE,
                    expires_at DATETIME NULL,
                    created_by INT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX idx_college_id (college_id),
                    INDEX idx_created_at (created_at),
                    INDEX idx_is_important (is_important)
                )");
            }
            
            // Insert announcement
            $stmt = $conn->prepare("
                INSERT INTO announcements (college_id, title, content, audience, is_important, expires_at, created_by) 
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $college_id,
                $title,
                $content,
                json_encode($audience),
                $is_important,
                $expires_at ?: null,
                $_SESSION['collegeadmin_id']
            ]);
            
            $success = "Announcement posted successfully! It will be visible to the selected audience.";
            
            // Clear form data
            $title = $content = '';
            $audience = [];
            $is_important = 0;
            $expires_at = '';
            
        } catch (PDOException $e) {
            $error = "Database error: " . $e->getMessage();
        }
    }
}

// Get recent announcements count
$recentCount = 0;
try {
    $countStmt = $conn->prepare("SELECT COUNT(*) FROM announcements WHERE college_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)");
    $countStmt->execute([$college_id]);
    $recentCount = $countStmt->fetchColumn();
} catch (PDOException $e) {
    // Table doesn't exist yet
    $recentCount = 0;
}

// Include the header
require_once '../includes/header.php';
?>

    <!-- Main Content -->
    <main class="flex-grow py-6 px-4">
        <div class="max-w-7xl mx-auto">
            <!-- Modern Page Header with Gradient -->
            <div class="relative bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-800 rounded-2xl shadow-xl overflow-hidden mb-8">
                <!-- Background Pattern -->
                <div class="absolute inset-0 bg-black/10">
                    <div class="absolute inset-0" style="background-image: radial-gradient(circle at 1px 1px, rgba(255,255,255,0.15) 1px, transparent 0); background-size: 20px 20px;"></div>
                </div>
                
                <!-- Content -->
                <div class="relative p-8">
                    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                        <div class="flex items-center space-x-6">
                            <div class="bg-white/20 backdrop-blur-sm p-4 rounded-2xl shadow-lg">
                                <i class="fas fa-bullhorn text-white text-3xl"></i>
                            </div>
                            <div>
                                <h1 class="text-3xl font-bold text-white mb-2">
                                    Create Announcement
                                </h1>
                                <nav class="text-blue-100 text-sm mb-3" aria-label="Breadcrumb">
                                    <ol class="flex items-center space-x-2">
                                        <li>
                                            <a href="collegeadmin_dashboard.php" class="hover:text-white transition-colors">
                                                <i class="fas fa-home mr-1"></i>Dashboard
                                            </a>
                                        </li>
                                        <li class="flex items-center">
                                            <i class="fas fa-chevron-right text-blue-300 text-xs mx-2"></i>
                                            <span class="text-white font-medium">Communication</span>
                                        </li>
                                        <li class="flex items-center">
                                            <i class="fas fa-chevron-right text-blue-300 text-xs mx-2"></i>
                                            <span class="text-white font-medium">Post Announcement</span>
                                        </li>
                                    </ol>
                                </nav>
                                <p class="text-blue-100 text-lg">
                                    Share important updates with your college community
                                </p>
                            </div>
                        </div>
                        <div class="mt-6 lg:mt-0 flex flex-wrap gap-3">
                            <a href="bulk_email_students.php" class="inline-flex items-center px-6 py-3 bg-white/20 hover:bg-white/30 backdrop-blur-sm text-white font-medium rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl">
                                <i class="fas fa-envelope mr-2"></i> Bulk Email
                            </a>
                            <a href="collegeadmin_dashboard.php" class="inline-flex items-center px-6 py-3 bg-white/10 hover:bg-white/20 backdrop-blur-sm text-white font-medium rounded-xl transition-all duration-200 border border-white/20">
                                <i class="fas fa-arrow-left mr-2"></i> Dashboard
                            </a>
                        </div>
                    </div>
                    
                    <!-- Quick Stats -->
                    <div class="mt-8 grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20">
                            <div class="flex items-center">
                                <div class="bg-green-500/20 p-2 rounded-lg">
                                    <i class="fas fa-chart-line text-green-300"></i>
                                </div>
                                <div class="ml-3">
                                    <p class="text-blue-100 text-sm">This Week</p>
                                    <p class="text-white text-xl font-bold"><?= $recentCount ?></p>
                                </div>
                            </div>
                        </div>
                        <div class="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20">
                            <div class="flex items-center">
                                <div class="bg-blue-500/20 p-2 rounded-lg">
                                    <i class="fas fa-users text-blue-300"></i>
                                </div>
                                <div class="ml-3">
                                    <p class="text-blue-100 text-sm">Reach</p>
                                    <p class="text-white text-xl font-bold">All Users</p>
                                </div>
                            </div>
                        </div>
                        <div class="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20">
                            <div class="flex items-center">
                                <div class="bg-purple-500/20 p-2 rounded-lg">
                                    <i class="fas fa-clock text-purple-300"></i>
                                </div>
                                <div class="ml-3">
                                    <p class="text-blue-100 text-sm">Status</p>
                                    <p class="text-white text-xl font-bold">Live</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Alert Messages -->
            <?php if ($error): ?>
                <div class="bg-red-50 dark:bg-red-900/50 border-l-4 border-red-500 rounded-r-xl p-6 mb-6 shadow-lg">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <div class="bg-red-100 dark:bg-red-800 p-2 rounded-full">
                                <i class="fas fa-exclamation-triangle text-red-600 dark:text-red-400"></i>
                            </div>
                        </div>
                        <div class="ml-4">
                            <h3 class="text-lg font-semibold text-red-800 dark:text-red-200">Error</h3>
                            <p class="text-red-700 dark:text-red-300 mt-1"><?= htmlspecialchars($error) ?></p>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <?php if ($success): ?>
                <div class="bg-green-50 dark:bg-green-900/50 border-l-4 border-green-500 rounded-r-xl p-6 mb-6 shadow-lg">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <div class="bg-green-100 dark:bg-green-800 p-2 rounded-full">
                                <i class="fas fa-check-circle text-green-600 dark:text-green-400"></i>
                            </div>
                        </div>
                        <div class="ml-4">
                            <h3 class="text-lg font-semibold text-green-800 dark:text-green-200">Success</h3>
                            <p class="text-green-700 dark:text-green-300 mt-1"><?= htmlspecialchars($success) ?></p>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
