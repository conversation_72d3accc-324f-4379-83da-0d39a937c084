<?php
// Include session configuration
require_once '../includes/session_config.php';
require_once '../includes/db.php';
require_once '../includes/db_schema_check.php';
require_once '../notify/send_email.php';

if (!isset($_SESSION['collegeadmin_id'])) {
    header("Location: ../auth/collegeadmin_login.php");
    exit;
}

$college_id = $_SESSION['college_id'];
$admin_name = $_SESSION['collegeadmin_name'] ?? 'Admin';

// Set role explicitly for the sidebar
$role = 'collegeadmin';
$user_id = $_SESSION['collegeadmin_id'];
$user_name = $_SESSION['collegeadmin_name'];
$current_page = basename($_SERVER['PHP_SELF']);

// Get college details
$collegeStmt = $conn->prepare("SELECT name FROM colleges WHERE id = ?");
$collegeStmt->execute([$college_id]);
$college = $collegeStmt->fetch(PDO::FETCH_ASSOC);

$error = '';
$success = '';

// Create email_templates table if it doesn't exist
try {
    $conn->exec("CREATE TABLE IF NOT EXISTS email_templates (
        id INT AUTO_INCREMENT PRIMARY KEY,
        college_id INT NOT NULL,
        name VARCHAR(255) NOT NULL,
        subject VARCHAR(255) NOT NULL,
        content TEXT NOT NULL,
        template_type ENUM('system', 'custom') DEFAULT 'custom',
        created_by INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_college_id (college_id),
        INDEX idx_template_type (template_type)
    )");
} catch (PDOException $e) {
    // Table creation failed, continue anyway
}

// Handle template creation
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_template'])) {
    $template_name = trim($_POST['template_name'] ?? '');
    $template_subject = trim($_POST['template_subject'] ?? '');
    $template_content = trim($_POST['template_content'] ?? '');

    if (empty($template_name) || empty($template_subject) || empty($template_content)) {
        $error = "All template fields are required.";
    } else {
        try {
            $stmt = $conn->prepare("INSERT INTO email_templates (college_id, name, subject, content, template_type, created_by) VALUES (?, ?, ?, ?, 'custom', ?)");
            $stmt->execute([$college_id, $template_name, $template_subject, $template_content, $_SESSION['collegeadmin_id']]);
            $success = "Email template created successfully!";
        } catch (PDOException $e) {
            $error = "Failed to create template: " . $e->getMessage();
        }
    }
}

// Handle email sending
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['send_email'])) {
    $template_id = $_POST['template_id'] ?? '';
    $recipients = $_POST['recipients'] ?? [];
    $custom_subject = trim($_POST['custom_subject'] ?? '');
    $custom_content = trim($_POST['custom_content'] ?? '');

    if (empty($recipients)) {
        $error = "Please select at least one recipient.";
    } elseif (empty($custom_subject) || empty($custom_content)) {
        $error = "Please provide both subject and content for the email.";
    } else {
        // Get recipient details - build query based on available columns
        $recipientIds = implode(',', array_map('intval', $recipients));

        // Check what columns exist in the students table
        $columnsCheck = $conn->query("SHOW COLUMNS FROM students");
        $availableColumns = $columnsCheck->fetchAll(PDO::FETCH_COLUMN);

        // Build select clause based on available columns
        $selectFields = ['id']; // Always include id
        $fieldMapping = [
            'full_name' => ['full_name', 'name', 'student_name'],
            'email' => ['email', 'student_email'],
            'student_id' => ['student_id', 'registration_number', 'roll_number', 'student_number'],
            'department' => ['department', 'dept', 'course'],
            'year' => ['year', 'academic_year', 'class_year', 'semester']
        ];

        foreach ($fieldMapping as $standardField => $possibleColumns) {
            foreach ($possibleColumns as $column) {
                if (in_array($column, $availableColumns)) {
                    $selectFields[] = "$column as $standardField";
                    break;
                }
            }
        }

        $selectClause = implode(', ', $selectFields);
        $recipientStmt = $conn->prepare("SELECT $selectClause FROM students WHERE id IN ($recipientIds)");
        $recipientStmt->execute();
        $recipientStudents = $recipientStmt->fetchAll(PDO::FETCH_ASSOC);

        if (empty($recipientStudents)) {
            $error = "No valid recipients found.";
        } else {
            // Email sending configuration
            $emailsSent = 0;
            $emailsFailed = 0;
            $failedEmails = [];

            // Get college email settings
            $fromEmail = "noreply@" . ($_SERVER['HTTP_HOST'] ?? 'college.edu');
            $fromName = $college['name'] ?? 'College Administration';
            $replyTo = $fromEmail;

            // Personalization data
            $personalizeData = [
                'college_name' => $college['name'] ?? 'Your College',
                'admin_name' => $admin_name,
                'college_email' => $fromEmail
            ];

            foreach ($recipientStudents as $student) {
                try {
                    // Personalize subject and content for this student
                    $personalizedSubject = $custom_subject;
                    $personalizedContent = $custom_content;

                    // Student-specific data with fallbacks
                    $studentData = array_merge($personalizeData, [
                        'student_name' => $student['full_name'] ?? $student['name'] ?? 'Student',
                        'student_email' => $student['email'] ?? $student['student_email'] ?? '',
                        'student_id' => $student['student_id'] ?? $student['registration_number'] ?? $student['roll_number'] ?? $student['id'] ?? 'N/A',
                        'department' => $student['department'] ?? $student['dept'] ?? $student['course'] ?? 'General',
                        'year' => $student['year'] ?? $student['academic_year'] ?? $student['class_year'] ?? 'N/A'
                    ]);

                    // Replace shortcodes
                    foreach ($studentData as $key => $value) {
                        $placeholder = '{{' . $key . '}}';
                        $personalizedSubject = str_replace($placeholder, $value, $personalizedSubject);
                        $personalizedContent = str_replace($placeholder, $value, $personalizedContent);
                    }

                    // Convert plain text to HTML for better formatting
                    $htmlContent = nl2br(htmlspecialchars($personalizedContent));
                    $htmlContent = '<div style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">' . $htmlContent . '</div>';

                    // Send email using PHPMailer SMTP function
                    $emailSent = sendEmail(
                        $student['email'],
                        $student['full_name'] ?? 'Student',
                        $personalizedSubject,
                        $htmlContent
                    );

                    if ($emailSent) {
                        $emailsSent++;

                        // Log successful email
                        try {
                            $logStmt = $conn->prepare("INSERT INTO email_logs (college_id, recipient_id, recipient_email, subject, status, sent_at, sent_by) VALUES (?, ?, ?, ?, 'sent', NOW(), ?)");
                            $logStmt->execute([$college_id, $student['id'], $student['email'], $personalizedSubject, $_SESSION['collegeadmin_id']]);
                        } catch (PDOException $e) {
                            // Log table might not exist, continue anyway
                        }
                    } else {
                        $emailsFailed++;
                        $failedEmails[] = $student['email'];

                        // Log failed email
                        try {
                            $logStmt = $conn->prepare("INSERT INTO email_logs (college_id, recipient_id, recipient_email, subject, status, sent_at, sent_by, error_message) VALUES (?, ?, ?, ?, 'failed', NOW(), ?, 'PHPMailer SMTP failed')");
                            $logStmt->execute([$college_id, $student['id'], $student['email'], $personalizedSubject, $_SESSION['collegeadmin_id']]);
                        } catch (PDOException $e) {
                            // Log table might not exist, continue anyway
                        }
                    }

                    // Small delay to prevent overwhelming the SMTP server
                    usleep(200000); // 0.2 second delay for SMTP

                } catch (Exception $e) {
                    $emailsFailed++;
                    $failedEmails[] = $student['email'];

                    // Log exception
                    try {
                        $logStmt = $conn->prepare("INSERT INTO email_logs (college_id, recipient_id, recipient_email, subject, status, sent_at, sent_by, error_message) VALUES (?, ?, ?, ?, 'failed', NOW(), ?, ?)");
                        $logStmt->execute([$college_id, $student['id'], $student['email'], $personalizedSubject ?? 'Unknown', $_SESSION['collegeadmin_id'], $e->getMessage()]);
                    } catch (PDOException $e2) {
                        // Log table might not exist, continue anyway
                    }
                }
            }

            // Create email logs table if it doesn't exist
            try {
                $conn->exec("CREATE TABLE IF NOT EXISTS email_logs (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    college_id INT NOT NULL,
                    recipient_id INT,
                    recipient_email VARCHAR(255) NOT NULL,
                    subject VARCHAR(255) NOT NULL,
                    status ENUM('sent', 'failed') NOT NULL,
                    sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    sent_by INT NOT NULL,
                    error_message TEXT NULL,
                    INDEX idx_college_id (college_id),
                    INDEX idx_status (status),
                    INDEX idx_sent_at (sent_at)
                )");
            } catch (PDOException $e) {
                // Table creation failed, continue anyway
            }

            // Set success/error message
            if ($emailsSent > 0 && $emailsFailed == 0) {
                $success = "All emails sent successfully! $emailsSent emails delivered.";
            } elseif ($emailsSent > 0 && $emailsFailed > 0) {
                $success = "Partially successful: $emailsSent emails sent, $emailsFailed failed.";
                if (!empty($failedEmails)) {
                    $error = "Failed to send to: " . implode(', ', array_slice($failedEmails, 0, 5)) . ($emailsFailed > 5 ? " and " . ($emailsFailed - 5) . " more" : "");
                }
            } else {
                $error = "Failed to send any emails. Please check your server's mail configuration.";
            }
        }
    }
}

// Get system templates (default ones)
$system_templates = [
    [
        'id' => 'welcome',
        'name' => 'Welcome Message',
        'subject' => 'Welcome to {{college_name}}!',
        'content' => "Dear {{student_name}},\n\nWelcome to {{college_name}}! We're excited to have you as part of our academic community.\n\nYour student ID: {{student_id}}\nDepartment: {{department}}\nYear: {{year}}\n\nIf you have any questions, please don't hesitate to contact us.\n\nBest regards,\n{{admin_name}}\n{{college_name}} Administration",
        'template_type' => 'system'
    ],
    [
        'id' => 'exam_reminder',
        'name' => 'Exam Reminder',
        'subject' => 'Important: Upcoming Exams - {{college_name}}',
        'content' => "Dear {{student_name}},\n\nThis is a reminder about your upcoming examinations.\n\nStudent Details:\n- Name: {{student_name}}\n- Student ID: {{student_id}}\n- Department: {{department}}\n- Year: {{year}}\n\nPlease ensure you are well-prepared and arrive on time for all examinations.\n\nBest of luck!\n{{admin_name}}\n{{college_name}}",
        'template_type' => 'system'
    ],
    [
        'id' => 'event_invitation',
        'name' => 'Event Invitation',
        'subject' => 'You are Invited: Special Event at {{college_name}}',
        'content' => "Dear {{student_name}},\n\nWe're pleased to invite you to our upcoming event!\n\nStudent Information:\n- Name: {{student_name}}\n- Email: {{student_email}}\n- Department: {{department}}\n- Year: {{year}}\n\nThis event will be a great opportunity to network and learn. We hope to see you there!\n\nWarm regards,\n{{admin_name}}\n{{college_name}} Events Team",
        'template_type' => 'system'
    ],
    [
        'id' => 'fee_reminder',
        'name' => 'Fee Payment Reminder',
        'subject' => 'Fee Payment Reminder - {{college_name}}',
        'content' => "Dear {{student_name}},\n\nThis is a friendly reminder regarding your fee payment.\n\nStudent Details:\n- Name: {{student_name}}\n- Student ID: {{student_id}}\n- Department: {{department}}\n- Year: {{year}}\n- Email: {{student_email}}\n\nPlease ensure timely payment to avoid any inconvenience.\n\nThank you,\n{{admin_name}}\n{{college_name}} Accounts Department",
        'template_type' => 'system'
    ],
    [
        'id' => 'achievement',
        'name' => 'Achievement Congratulations',
        'subject' => 'Congratulations on Your Achievement! - {{college_name}}',
        'content' => "Dear {{student_name}},\n\nCongratulations on your outstanding achievement!\n\nStudent Information:\n- Name: {{student_name}}\n- Student ID: {{student_id}}\n- Department: {{department}}\n- Year: {{year}}\n\nYour hard work and dedication have paid off. We're proud to have you as part of {{college_name}}.\n\nKeep up the excellent work!\n\nProud regards,\n{{admin_name}}\n{{college_name}} Administration",
        'template_type' => 'system'
    ]
];

// Get custom templates from database
$custom_templates = [];
try {
    $stmt = $conn->prepare("SELECT * FROM email_templates WHERE college_id = ? AND template_type = 'custom' ORDER BY created_at DESC");
    $stmt->execute([$college_id]);
    $custom_templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $custom_templates = [];
}

// Get students for recipient selection
$students = [];
$debug_info = '';
try {
    // First, let's check if students table exists and what columns it has
    $tableCheck = $conn->query("SHOW TABLES LIKE 'students'");
    if ($tableCheck->rowCount() == 0) {
        $debug_info = "Students table does not exist";
    } else {
        // Check table structure
        $columnsCheck = $conn->query("SHOW COLUMNS FROM students");
        $columns = $columnsCheck->fetchAll(PDO::FETCH_COLUMN);

        // Build query based on available columns
        $selectFields = [];
        $availableFields = ['id', 'full_name', 'email', 'student_id', 'department', 'year'];

        foreach ($availableFields as $field) {
            if (in_array($field, $columns)) {
                $selectFields[] = $field;
            }
        }

        // Fallback if standard fields don't exist
        if (empty($selectFields)) {
            $selectFields = ['id'];
            if (in_array('name', $columns)) $selectFields[] = 'name as full_name';
            if (in_array('email', $columns)) $selectFields[] = 'email';
        }

        $selectClause = implode(', ', $selectFields);

        // Try different query variations
        $queries = [];

        // Query 1: With college_id and verified
        if (in_array('college_id', $columns) && in_array('verified', $columns)) {
            $queries[] = "SELECT $selectClause FROM students WHERE college_id = ? AND verified = 1 ORDER BY " . (in_array('full_name', $selectFields) ? 'full_name' : 'id') . " ASC";
        }

        // Query 2: With college_id only
        if (in_array('college_id', $columns)) {
            $queries[] = "SELECT $selectClause FROM students WHERE college_id = ? ORDER BY " . (in_array('full_name', $selectFields) ? 'full_name' : 'id') . " ASC";
        }

        // Query 3: With verified only
        if (in_array('verified', $columns)) {
            $queries[] = "SELECT $selectClause FROM students WHERE verified = 1 ORDER BY " . (in_array('full_name', $selectFields) ? 'full_name' : 'id') . " ASC";
        }

        // Query 4: All students
        $queries[] = "SELECT $selectClause FROM students ORDER BY " . (in_array('full_name', $selectFields) ? 'full_name' : 'id') . " ASC";

        // Try each query until we find students
        foreach ($queries as $index => $query) {
            try {
                if ($index < 2) {
                    // Queries with college_id parameter
                    $stmt = $conn->prepare($query);
                    $stmt->execute([$college_id]);
                } else {
                    // Queries without parameters
                    $stmt = $conn->prepare($query);
                    $stmt->execute();
                }

                $students = $stmt->fetchAll(PDO::FETCH_ASSOC);

                if (!empty($students)) {
                    $debug_info = "Query " . ($index + 1) . " successful: " . count($students) . " students found";
                    break;
                }
            } catch (PDOException $e) {
                continue; // Try next query
            }
        }

        // Get total count for debugging
        try {
            $countStmt = $conn->query("SELECT COUNT(*) FROM students");
            $totalStudents = $countStmt->fetchColumn();
            $debug_info .= " | Total in DB: $totalStudents | College ID: $college_id";
        } catch (PDOException $e) {
            $debug_info .= " | Could not get total count";
        }

        // Add column info to debug
        $debug_info .= " | Available columns: " . implode(', ', $columns);
    }
} catch (PDOException $e) {
    $debug_info = "Database error: " . $e->getMessage();
    $students = [];
}

// Include the header
require_once '../includes/header.php';
?>

    <!-- Main Content -->
    <main class="flex-grow py-6 px-4">
        <div class="max-w-7xl mx-auto">
            <!-- Modern Page Header with Gradient -->
            <div class="relative bg-gradient-to-br from-purple-600 via-purple-700 to-indigo-800 rounded-2xl shadow-xl overflow-hidden mb-8">
                <!-- Background Pattern -->
                <div class="absolute inset-0 bg-black/10">
                    <div class="absolute inset-0" style="background-image: radial-gradient(circle at 1px 1px, rgba(255,255,255,0.15) 1px, transparent 0); background-size: 20px 20px;"></div>
                </div>

                <!-- Content -->
                <div class="relative p-8">
                    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                        <div class="flex items-center space-x-6">
                            <div class="bg-white/20 backdrop-blur-sm p-4 rounded-2xl shadow-lg">
                                <i class="fas fa-envelope text-white text-3xl"></i>
                            </div>
                            <div>
                                <h1 class="text-3xl font-bold text-white mb-2">
                                    Bulk Email System
                                </h1>
                                <nav class="text-purple-100 text-sm mb-3" aria-label="Breadcrumb">
                                    <ol class="flex items-center space-x-2">
                                        <li>
                                            <a href="collegeadmin_dashboard.php" class="hover:text-white transition-colors">
                                                <i class="fas fa-home mr-1"></i>Dashboard
                                            </a>
                                        </li>
                                        <li class="flex items-center">
                                            <i class="fas fa-chevron-right text-purple-300 text-xs mx-2"></i>
                                            <span class="text-white font-medium">Communication</span>
                                        </li>
                                        <li class="flex items-center">
                                            <i class="fas fa-chevron-right text-purple-300 text-xs mx-2"></i>
                                            <span class="text-white font-medium">Bulk Email</span>
                                        </li>
                                    </ol>
                                </nav>
                                <p class="text-purple-100 text-lg">
                                    Send personalized emails to students, faculty, and alumni
                                </p>
                            </div>
                        </div>
                        <div class="mt-6 lg:mt-0 flex flex-wrap gap-3">
                            <a href="post_announcement.php" class="inline-flex items-center px-6 py-3 bg-white/20 hover:bg-white/30 backdrop-blur-sm text-white font-medium rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl">
                                <i class="fas fa-bullhorn mr-2"></i> Post Announcement
                            </a>
                            <a href="collegeadmin_dashboard.php" class="inline-flex items-center px-6 py-3 bg-white/10 hover:bg-white/20 backdrop-blur-sm text-white font-medium rounded-xl transition-all duration-200 border border-white/20">
                                <i class="fas fa-arrow-left mr-2"></i> Dashboard
                            </a>
                        </div>
                    </div>

                    <!-- Quick Stats -->
                    <div class="mt-8 grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div class="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20">
                            <div class="flex items-center">
                                <div class="bg-blue-500/20 p-2 rounded-lg">
                                    <i class="fas fa-users text-blue-300"></i>
                                </div>
                                <div class="ml-3">
                                    <p class="text-purple-100 text-sm">Total Students</p>
                                    <p class="text-white text-xl font-bold"><?= count($students) ?></p>
                                </div>
                            </div>
                        </div>
                        <div class="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20">
                            <div class="flex items-center">
                                <div class="bg-green-500/20 p-2 rounded-lg">
                                    <i class="fas fa-envelope text-green-300"></i>
                                </div>
                                <div class="ml-3">
                                    <p class="text-purple-100 text-sm">System Templates</p>
                                    <p class="text-white text-xl font-bold"><?= count($system_templates) ?></p>
                                </div>
                            </div>
                        </div>
                        <div class="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20">
                            <div class="flex items-center">
                                <div class="bg-yellow-500/20 p-2 rounded-lg">
                                    <i class="fas fa-edit text-yellow-300"></i>
                                </div>
                                <div class="ml-3">
                                    <p class="text-purple-100 text-sm">Custom Templates</p>
                                    <p class="text-white text-xl font-bold"><?= count($custom_templates) ?></p>
                                </div>
                            </div>
                        </div>
                        <div class="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20">
                            <div class="flex items-center">
                                <div class="bg-purple-500/20 p-2 rounded-lg">
                                    <i class="fas fa-paper-plane text-purple-300"></i>
                                </div>
                                <div class="ml-3">
                                    <p class="text-purple-100 text-sm">Ready to Send</p>
                                    <p class="text-white text-xl font-bold" id="selectedCount">0</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Alert Messages -->
            <?php if ($error): ?>
                <div class="bg-red-50 dark:bg-red-900/50 border-l-4 border-red-500 rounded-r-xl p-6 mb-6 shadow-lg">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <div class="bg-red-100 dark:bg-red-800 p-2 rounded-full">
                                <i class="fas fa-exclamation-triangle text-red-600 dark:text-red-400"></i>
                            </div>
                        </div>
                        <div class="ml-4">
                            <h3 class="text-lg font-semibold text-red-800 dark:text-red-200">Error</h3>
                            <p class="text-red-700 dark:text-red-300 mt-1"><?= htmlspecialchars($error) ?></p>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <?php if ($success): ?>
                <div class="bg-green-50 dark:bg-green-900/50 border-l-4 border-green-500 rounded-r-xl p-6 mb-6 shadow-lg">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <div class="bg-green-100 dark:bg-green-800 p-2 rounded-full">
                                <i class="fas fa-check-circle text-green-600 dark:text-green-400"></i>
                            </div>
                        </div>
                        <div class="ml-4">
                            <h3 class="text-lg font-semibold text-green-800 dark:text-green-200">Success</h3>
                            <p class="text-green-700 dark:text-green-300 mt-1"><?= htmlspecialchars($success) ?></p>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Email Configuration Notice -->
            <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-xl p-6 mb-8">
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <div class="bg-blue-100 dark:bg-blue-900/30 p-2 rounded-lg">
                            <i class="fas fa-server text-blue-600 dark:text-blue-400"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-2">📧 Professional Email System</h3>
                        <p class="text-blue-800 dark:text-blue-200 mb-3">
                            <strong>✅ PROFESSIONAL SMTP EMAIL SENDING!</strong> This system uses PHPMailer with Gmail SMTP for reliable email delivery.
                        </p>
                        <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-blue-200 dark:border-blue-700">
                            <h4 class="font-semibold text-gray-900 dark:text-white mb-2">Professional SMTP Configuration:</h4>
                            <ul class="text-sm text-gray-700 dark:text-gray-300 space-y-1">
                                <li><strong>From Email:</strong> <EMAIL></li>
                                <li><strong>From Name:</strong> Connect My Students</li>
                                <li><strong>SMTP Server:</strong> Gmail SMTP (smtp.gmail.com:587)</li>
                                <li><strong>Method:</strong> PHPMailer with TLS encryption</li>
                                <li><strong>Features:</strong> HTML emails, personalization, delivery tracking, error handling</li>
                                <li><strong>Status:</strong> <span class="text-green-600 font-semibold">✅ Professional SMTP Active</span></li>
                            </ul>
                        </div>
                        <div class="mt-4 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 rounded-lg">
                            <p class="text-sm text-green-800 dark:text-green-200">
                                <i class="fas fa-check-circle mr-1"></i>
                                <strong>Production Ready:</strong> Using professional Gmail SMTP with PHPMailer for reliable email delivery and better inbox placement.
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Email Templates Section -->
            <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 overflow-hidden mb-8">
                <div class="bg-gradient-to-r from-purple-50 to-indigo-50 dark:from-gray-700 dark:to-gray-800 p-6 border-b border-gray-200 dark:border-gray-700">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="bg-purple-100 dark:bg-purple-900/30 p-3 rounded-xl mr-4">
                                <i class="fas fa-envelope-open-text text-purple-600 dark:text-purple-400 text-xl"></i>
                            </div>
                            <div>
                                <h2 class="text-xl font-bold text-gray-900 dark:text-white">Email Templates</h2>
                                <p class="text-gray-600 dark:text-gray-400 mt-1">Choose from pre-built templates or create your own</p>
                            </div>
                        </div>
                        <button onclick="showCreateTemplateModal()" class="inline-flex items-center px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white font-medium rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl">
                            <i class="fas fa-plus mr-2"></i> Create Template
                        </button>
                    </div>
                </div>

                <div class="p-8">
                    <!-- Template Instructions -->
                    <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-xl p-6 mb-8">
                        <div class="flex items-start">
                            <div class="flex-shrink-0">
                                <div class="bg-blue-100 dark:bg-blue-900/30 p-2 rounded-lg">
                                    <i class="fas fa-info-circle text-blue-600 dark:text-blue-400"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <h3 class="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-3">Template Personalization Guide</h3>
                                <p class="text-blue-800 dark:text-blue-200 mb-4">Use these shortcodes in your templates to automatically insert student data:</p>
                                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                    <div class="bg-white dark:bg-gray-800 rounded-lg p-3 border border-blue-200 dark:border-blue-700">
                                        <code class="text-purple-600 dark:text-purple-400 font-mono text-sm">{{student_name}}</code>
                                        <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">Student's full name</p>
                                    </div>
                                    <div class="bg-white dark:bg-gray-800 rounded-lg p-3 border border-blue-200 dark:border-blue-700">
                                        <code class="text-purple-600 dark:text-purple-400 font-mono text-sm">{{student_email}}</code>
                                        <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">Student's email address</p>
                                    </div>
                                    <div class="bg-white dark:bg-gray-800 rounded-lg p-3 border border-blue-200 dark:border-blue-700">
                                        <code class="text-purple-600 dark:text-purple-400 font-mono text-sm">{{student_id}}</code>
                                        <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">Student ID number</p>
                                    </div>
                                    <div class="bg-white dark:bg-gray-800 rounded-lg p-3 border border-blue-200 dark:border-blue-700">
                                        <code class="text-purple-600 dark:text-purple-400 font-mono text-sm">{{department}}</code>
                                        <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">Student's department</p>
                                    </div>
                                    <div class="bg-white dark:bg-gray-800 rounded-lg p-3 border border-blue-200 dark:border-blue-700">
                                        <code class="text-purple-600 dark:text-purple-400 font-mono text-sm">{{year}}</code>
                                        <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">Academic year</p>
                                    </div>
                                    <div class="bg-white dark:bg-gray-800 rounded-lg p-3 border border-blue-200 dark:border-blue-700">
                                        <code class="text-purple-600 dark:text-purple-400 font-mono text-sm">{{college_name}}</code>
                                        <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">College name</p>
                                    </div>
                                    <div class="bg-white dark:bg-gray-800 rounded-lg p-3 border border-blue-200 dark:border-blue-700">
                                        <code class="text-purple-600 dark:text-purple-400 font-mono text-sm">{{admin_name}}</code>
                                        <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">Your name</p>
                                    </div>
                                    <div class="bg-white dark:bg-gray-800 rounded-lg p-3 border border-blue-200 dark:border-blue-700">
                                        <code class="text-purple-600 dark:text-purple-400 font-mono text-sm">{{college_email}}</code>
                                        <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">College contact email</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- System Templates -->
                    <div class="mb-8">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                            <i class="fas fa-cogs text-green-600 mr-2"></i>
                            System Templates (<?= count($system_templates) ?>)
                        </h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            <?php foreach ($system_templates as $template): ?>
                                <div class="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-gray-700 dark:to-gray-800 border-2 border-green-200 dark:border-green-800 rounded-xl p-6 hover:shadow-lg transition-all duration-200 group">
                                    <div class="flex items-start justify-between mb-4">
                                        <div class="bg-green-100 dark:bg-green-900/30 p-2 rounded-lg">
                                            <i class="fas fa-envelope text-green-600 dark:text-green-400"></i>
                                        </div>
                                        <span class="bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 text-xs font-medium px-2 py-1 rounded-full">System</span>
                                    </div>
                                    <h4 class="font-semibold text-gray-900 dark:text-white mb-2"><?= htmlspecialchars($template['name']) ?></h4>
                                    <p class="text-sm text-gray-600 dark:text-gray-400 mb-4 line-clamp-2"><?= htmlspecialchars($template['subject']) ?></p>
                                    <div class="flex gap-2">
                                        <button onclick="previewTemplate('<?= $template['id'] ?>', 'system')" class="flex-1 bg-green-600 hover:bg-green-700 text-white text-sm font-medium py-2 px-3 rounded-lg transition-colors">
                                            <i class="fas fa-eye mr-1"></i> Preview
                                        </button>
                                        <button onclick="useTemplate('<?= $template['id'] ?>', 'system')" class="flex-1 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium py-2 px-3 rounded-lg transition-colors">
                                            <i class="fas fa-paper-plane mr-1"></i> Use
                                        </button>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>

                    <!-- Custom Templates -->
                    <div class="mb-8">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                            <i class="fas fa-user-edit text-purple-600 mr-2"></i>
                            Your Custom Templates (<?= count($custom_templates) ?>)
                        </h3>
                        <?php if (count($custom_templates) > 0): ?>
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                <?php foreach ($custom_templates as $template): ?>
                                    <div class="bg-gradient-to-br from-purple-50 to-indigo-50 dark:from-gray-700 dark:to-gray-800 border-2 border-purple-200 dark:border-purple-800 rounded-xl p-6 hover:shadow-lg transition-all duration-200 group">
                                        <div class="flex items-start justify-between mb-4">
                                            <div class="bg-purple-100 dark:bg-purple-900/30 p-2 rounded-lg">
                                                <i class="fas fa-edit text-purple-600 dark:text-purple-400"></i>
                                            </div>
                                            <span class="bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-200 text-xs font-medium px-2 py-1 rounded-full">Custom</span>
                                        </div>
                                        <h4 class="font-semibold text-gray-900 dark:text-white mb-2"><?= htmlspecialchars($template['name']) ?></h4>
                                        <p class="text-sm text-gray-600 dark:text-gray-400 mb-4 line-clamp-2"><?= htmlspecialchars($template['subject']) ?></p>
                                        <div class="flex gap-2">
                                            <button onclick="previewTemplate('<?= $template['id'] ?>', 'custom')" class="flex-1 bg-purple-600 hover:bg-purple-700 text-white text-sm font-medium py-2 px-3 rounded-lg transition-colors">
                                                <i class="fas fa-eye mr-1"></i> Preview
                                            </button>
                                            <button onclick="editTemplate('<?= $template['id'] ?>')" class="bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium py-2 px-3 rounded-lg transition-colors">
                                                <i class="fas fa-edit mr-1"></i> Edit
                                            </button>
                                            <button onclick="useTemplate('<?= $template['id'] ?>', 'custom')" class="flex-1 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium py-2 px-3 rounded-lg transition-colors">
                                                <i class="fas fa-paper-plane mr-1"></i> Use
                                            </button>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-12 bg-gray-50 dark:bg-gray-700 rounded-xl border-2 border-dashed border-gray-300 dark:border-gray-600">
                                <div class="w-16 h-16 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <i class="fas fa-plus text-purple-600 dark:text-purple-400 text-2xl"></i>
                                </div>
                                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Custom Templates Yet</h3>
                                <p class="text-gray-600 dark:text-gray-400 mb-4">Create your first custom email template to get started</p>
                                <button onclick="showCreateTemplateModal()" class="inline-flex items-center px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white font-medium rounded-lg transition-colors">
                                    <i class="fas fa-plus mr-2"></i> Create Your First Template
                                </button>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Email Composition Section -->
            <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 overflow-hidden mb-8">
                <div class="bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-gray-700 dark:to-gray-800 p-6 border-b border-gray-200 dark:border-gray-700">
                    <div class="flex items-center">
                        <div class="bg-blue-100 dark:bg-blue-900/30 p-3 rounded-xl mr-4">
                            <i class="fas fa-paper-plane text-blue-600 dark:text-blue-400 text-xl"></i>
                        </div>
                        <div>
                            <h2 class="text-xl font-bold text-gray-900 dark:text-white">Compose & Send Email</h2>
                            <p class="text-gray-600 dark:text-gray-400 mt-1">Select recipients and compose your email</p>
                        </div>
                    </div>
                </div>

                <div class="p-8">
                    <form method="POST" action="" id="emailForm" class="space-y-8">
                        <input type="hidden" name="send_email" value="1">
                        <input type="hidden" id="selectedTemplate" name="template_id" value="">

                        <!-- Step 1: Select Recipients -->
                        <div class="bg-gray-50 dark:bg-gray-700 rounded-xl p-6">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                                <span class="bg-blue-600 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-3">1</span>
                                Select Recipients
                            </h3>

                            <!-- Quick Selection Buttons -->
                            <div class="flex flex-wrap gap-3 mb-6">
                                <button type="button" onclick="selectAllStudents()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors">
                                    <i class="fas fa-check-double mr-2"></i> Select All Students
                                </button>
                                <button type="button" onclick="clearSelection()" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors">
                                    <i class="fas fa-times mr-2"></i> Clear Selection
                                </button>
                                <button type="button" onclick="selectByDepartment()" class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-colors">
                                    <i class="fas fa-filter mr-2"></i> Select by Department
                                </button>
                                <button type="button" onclick="selectByYear()" class="bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-lg transition-colors">
                                    <i class="fas fa-calendar mr-2"></i> Select by Year
                                </button>
                            </div>

                            <!-- Students List -->
                            <div class="max-h-96 overflow-y-auto border border-gray-200 dark:border-gray-600 rounded-lg">
                                <div class="bg-gray-100 dark:bg-gray-600 p-3 border-b border-gray-200 dark:border-gray-600 sticky top-0">
                                    <div class="flex items-center">
                                        <input type="checkbox" id="selectAllCheckbox" onchange="toggleAllStudents()" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500">
                                        <label for="selectAllCheckbox" class="ml-3 text-sm font-medium text-gray-700 dark:text-gray-300">
                                            Select All (<span id="totalStudents"><?= count($students) ?></span> students)
                                        </label>
                                    </div>
                                </div>
                                <div class="divide-y divide-gray-200 dark:divide-gray-600">
                                    <?php if (count($students) > 0): ?>
                                        <?php foreach ($students as $student): ?>
                                            <label class="flex items-center p-4 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors">
                                                <input type="checkbox" name="recipients[]" value="<?= $student['id'] ?>"
                                                    class="student-checkbox w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                                                    data-department="<?= htmlspecialchars($student['department'] ?? 'Unknown') ?>"
                                                    data-year="<?= htmlspecialchars($student['year'] ?? 'Unknown') ?>"
                                                    onchange="updateSelectedCount()">
                                                <div class="ml-4 flex-1">
                                                    <div class="flex items-center justify-between">
                                                        <div>
                                                            <h4 class="text-sm font-medium text-gray-900 dark:text-white">
                                                                <?= htmlspecialchars($student['full_name'] ?? 'Unknown Name') ?>
                                                            </h4>
                                                            <p class="text-sm text-gray-600 dark:text-gray-400">
                                                                <?= htmlspecialchars($student['email'] ?? 'No email') ?>
                                                            </p>
                                                        </div>
                                                        <div class="text-right">
                                                            <p class="text-xs text-gray-500 dark:text-gray-400">
                                                                ID: <?= htmlspecialchars($student['student_id'] ?? 'Unknown') ?>
                                                            </p>
                                                            <p class="text-xs text-gray-500 dark:text-gray-400">
                                                                <?= htmlspecialchars($student['department'] ?? 'Unknown') ?> - Year <?= htmlspecialchars($student['year'] ?? 'Unknown') ?>
                                                            </p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </label>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <div class="p-8 text-center">
                                            <div class="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                                                <i class="fas fa-user-graduate text-gray-400 text-2xl"></i>
                                            </div>
                                            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Students Found</h3>
                                            <p class="text-gray-600 dark:text-gray-400 mb-4">
                                                There are no students available for this college or they haven't been verified yet.
                                            </p>
                                            <div class="space-y-2 text-sm text-gray-500 dark:text-gray-400">
                                                <p>Possible reasons:</p>
                                                <ul class="list-disc list-inside space-y-1">
                                                    <li>No students have been added to this college</li>
                                                    <li>Students are not verified yet</li>
                                                    <li>Database connection issue</li>
                                                    <li>College ID mismatch</li>
                                                </ul>
                                            </div>
                                            <div class="mt-6 flex flex-wrap gap-3 justify-center">
                                                <a href="add_student.php" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors">
                                                    <i class="fas fa-plus mr-2"></i> Add Students
                                                </a>
                                                <a href="list_students.php" class="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white font-medium rounded-lg transition-colors">
                                                    <i class="fas fa-list mr-2"></i> View All Students
                                                </a>
                                                <a href="verify_student.php" class="inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white font-medium rounded-lg transition-colors">
                                                    <i class="fas fa-check mr-2"></i> Verify Students
                                                </a>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <div class="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                                <p class="text-sm text-blue-800 dark:text-blue-200">
                                    <i class="fas fa-info-circle mr-2"></i>
                                    Selected: <span id="selectedStudentCount" class="font-bold">0</span> students
                                </p>
                            </div>
                        </div>

                        <!-- Step 2: Choose Template or Compose -->
                        <div class="bg-gray-50 dark:bg-gray-700 rounded-xl p-6">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                                <span class="bg-blue-600 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-3">2</span>
                                Choose Template or Compose Email
                            </h3>

                            <div class="space-y-6">
                                <!-- Template Selection -->
                                <div>
                                    <label class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                                        Selected Template (Optional)
                                    </label>
                                    <div id="selectedTemplateDisplay" class="bg-white dark:bg-gray-800 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-4 text-center">
                                        <i class="fas fa-envelope-open text-gray-400 text-2xl mb-2"></i>
                                        <p class="text-gray-500 dark:text-gray-400">No template selected. Choose a template above or compose manually below.</p>
                                    </div>
                                </div>

                                <!-- Manual Composition -->
                                <div class="grid grid-cols-1 gap-6">
                                    <div>
                                        <label for="email_subject" class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                                            Email Subject <span class="text-red-500">*</span>
                                        </label>
                                        <input type="text" id="email_subject" name="custom_subject" required
                                            class="w-full px-4 py-3 border-2 border-gray-200 dark:border-gray-600 rounded-xl focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 dark:bg-gray-700 dark:text-white transition-all duration-200"
                                            placeholder="Enter email subject...">
                                    </div>

                                    <div>
                                        <label for="email_content" class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                                            Email Content <span class="text-red-500">*</span>
                                        </label>
                                        <textarea id="email_content" name="custom_content" rows="8" required
                                            class="w-full px-4 py-3 border-2 border-gray-200 dark:border-gray-600 rounded-xl focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 dark:bg-gray-700 dark:text-white transition-all duration-200 resize-none"
                                            placeholder="Enter email content... Use {{shortcodes}} for personalization"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Step 3: Send Email -->
                        <div class="bg-gray-50 dark:bg-gray-700 rounded-xl p-6">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                                <span class="bg-blue-600 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-3">3</span>
                                Send Email
                            </h3>

                            <div class="flex flex-col sm:flex-row gap-4">
                                <button type="button" onclick="previewEmail()" class="flex-1 bg-gray-600 hover:bg-gray-700 text-white font-semibold py-4 px-8 rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl flex items-center justify-center">
                                    <i class="fas fa-eye mr-3"></i>
                                    Preview Email
                                </button>
                                <button type="submit" class="flex-1 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold py-4 px-8 rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 flex items-center justify-center">
                                    <i class="fas fa-paper-plane mr-3"></i>
                                    Send to <span id="sendButtonCount">0</span> Students
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </main>

<!-- Create Template Modal -->
<div id="createTemplateModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
    <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <h3 class="text-xl font-bold text-gray-900 dark:text-white">Create New Email Template</h3>
                <button onclick="hideCreateTemplateModal()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
        </div>
        <form method="POST" action="" class="p-6">
            <input type="hidden" name="create_template" value="1">

            <div class="space-y-6">
                <div>
                    <label for="template_name" class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                        Template Name <span class="text-red-500">*</span>
                    </label>
                    <input type="text" id="template_name" name="template_name" required
                        class="w-full px-4 py-3 border-2 border-gray-200 dark:border-gray-600 rounded-xl focus:ring-4 focus:ring-purple-500/20 focus:border-purple-500 dark:bg-gray-700 dark:text-white transition-all duration-200"
                        placeholder="Enter template name...">
                </div>

                <div>
                    <label for="template_subject" class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                        Email Subject <span class="text-red-500">*</span>
                    </label>
                    <input type="text" id="template_subject" name="template_subject" required
                        class="w-full px-4 py-3 border-2 border-gray-200 dark:border-gray-600 rounded-xl focus:ring-4 focus:ring-purple-500/20 focus:border-purple-500 dark:bg-gray-700 dark:text-white transition-all duration-200"
                        placeholder="Enter email subject...">
                </div>

                <div>
                    <label for="template_content" class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                        Email Content <span class="text-red-500">*</span>
                    </label>
                    <textarea id="template_content" name="template_content" rows="8" required
                        class="w-full px-4 py-3 border-2 border-gray-200 dark:border-gray-600 rounded-xl focus:ring-4 focus:ring-purple-500/20 focus:border-purple-500 dark:bg-gray-700 dark:text-white transition-all duration-200 resize-none"
                        placeholder="Enter email content... Use {{shortcodes}} for personalization"></textarea>
                </div>
            </div>

            <div class="flex gap-4 mt-8">
                <button type="submit" class="flex-1 bg-purple-600 hover:bg-purple-700 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl">
                    <i class="fas fa-save mr-2"></i> Create Template
                </button>
                <button type="button" onclick="hideCreateTemplateModal()" class="flex-1 bg-gray-600 hover:bg-gray-700 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200">
                    <i class="fas fa-times mr-2"></i> Cancel
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Template Preview Modal -->
<div id="previewModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
    <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <h3 class="text-xl font-bold text-gray-900 dark:text-white">Template Preview</h3>
                <button onclick="hidePreviewModal()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
        </div>
        <div class="p-6">
            <div id="previewContent" class="space-y-4">
                <!-- Preview content will be loaded here -->
            </div>
        </div>
    </div>
</div>

<!-- Email Preview Modal -->
<div id="emailPreviewModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
    <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <h3 class="text-xl font-bold text-gray-900 dark:text-white">Email Preview</h3>
                <button onclick="hideEmailPreviewModal()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
        </div>
        <div class="p-6">
            <div id="emailPreviewContent" class="space-y-4">
                <!-- Email preview content will be loaded here -->
            </div>
        </div>
    </div>
</div>

<!-- Department Filter Modal -->
<div id="departmentModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
    <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl max-w-md w-full">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <h3 class="text-xl font-bold text-gray-900 dark:text-white">Select by Department</h3>
                <button onclick="hideDepartmentModal()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
        </div>
        <div class="p-6">
            <div id="departmentList" class="space-y-2">
                <!-- Department options will be loaded here -->
            </div>
            <div class="flex gap-3 mt-6">
                <button onclick="applyDepartmentFilter()" class="flex-1 bg-purple-600 hover:bg-purple-700 text-white font-medium py-2 px-4 rounded-lg transition-colors">
                    Apply Filter
                </button>
                <button onclick="hideDepartmentModal()" class="flex-1 bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-lg transition-colors">
                    Cancel
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Year Filter Modal -->
<div id="yearModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
    <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl max-w-md w-full">
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <h3 class="text-xl font-bold text-gray-900 dark:text-white">Select by Year</h3>
                <button onclick="hideYearModal()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
        </div>
        <div class="p-6">
            <div id="yearList" class="space-y-2">
                <!-- Year options will be loaded here -->
            </div>
            <div class="flex gap-3 mt-6">
                <button onclick="applyYearFilter()" class="flex-1 bg-orange-600 hover:bg-orange-700 text-white font-medium py-2 px-4 rounded-lg transition-colors">
                    Apply Filter
                </button>
                <button onclick="hideYearModal()" class="flex-1 bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-lg transition-colors">
                    Cancel
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Template data for JavaScript
const systemTemplates = <?= json_encode($system_templates) ?>;
const customTemplates = <?= json_encode($custom_templates) ?>;

// Modal functions
function showCreateTemplateModal() {
    document.getElementById('createTemplateModal').classList.remove('hidden');
    document.body.style.overflow = 'hidden';
}

function hideCreateTemplateModal() {
    document.getElementById('createTemplateModal').classList.add('hidden');
    document.body.style.overflow = 'auto';
}

function showPreviewModal() {
    document.getElementById('previewModal').classList.remove('hidden');
    document.body.style.overflow = 'hidden';
}

function hidePreviewModal() {
    document.getElementById('previewModal').classList.add('hidden');
    document.body.style.overflow = 'auto';
}

// Template functions
function previewTemplate(templateId, type) {
    let template = null;

    if (type === 'system') {
        template = systemTemplates.find(t => t.id === templateId);
    } else {
        template = customTemplates.find(t => t.id == templateId);
    }

    if (template) {
        const previewContent = document.getElementById('previewContent');
        previewContent.innerHTML = `
            <div class="bg-gray-50 dark:bg-gray-700 rounded-xl p-6">
                <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">${template.name}</h4>
                <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Subject:</label>
                        <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                            <code class="text-sm">${template.subject}</code>
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Content:</label>
                        <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                            <pre class="text-sm whitespace-pre-wrap">${template.content}</pre>
                        </div>
                    </div>
                </div>
            </div>
        `;
        showPreviewModal();
    }
}

function useTemplate(templateId, type) {
    let template = null;

    if (type === 'system') {
        template = systemTemplates.find(t => t.id === templateId);
    } else {
        template = customTemplates.find(t => t.id == templateId);
    }

    if (template) {
        // Fill the form with template data
        document.getElementById('selectedTemplate').value = templateId;
        document.getElementById('email_subject').value = template.subject;
        document.getElementById('email_content').value = template.content;

        // Update the selected template display
        const display = document.getElementById('selectedTemplateDisplay');
        display.innerHTML = `
            <div class="bg-green-50 dark:bg-green-900/20 border-2 border-green-200 dark:border-green-700 rounded-lg p-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="bg-green-100 dark:bg-green-900/30 p-2 rounded-lg mr-3">
                            <i class="fas fa-envelope text-green-600 dark:text-green-400"></i>
                        </div>
                        <div>
                            <h4 class="font-semibold text-green-900 dark:text-green-100">${template.name}</h4>
                            <p class="text-sm text-green-700 dark:text-green-300">${template.subject}</p>
                        </div>
                    </div>
                    <button onclick="clearTemplate()" class="text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-200">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        `;

        // Scroll to email composition section
        document.querySelector('[data-step="2"]')?.scrollIntoView({ behavior: 'smooth' });

        // Show success message
        showNotification('Template selected successfully!', 'success');
    }
}

function clearTemplate() {
    document.getElementById('selectedTemplate').value = '';
    document.getElementById('email_subject').value = '';
    document.getElementById('email_content').value = '';

    const display = document.getElementById('selectedTemplateDisplay');
    display.innerHTML = `
        <i class="fas fa-envelope-open text-gray-400 text-2xl mb-2"></i>
        <p class="text-gray-500 dark:text-gray-400">No template selected. Choose a template above or compose manually below.</p>
    `;
}

function editTemplate(templateId) {
    // This would open the edit template modal
    alert('Edit template functionality would be implemented here.');
}

// Close modals when clicking outside
document.addEventListener('click', function(e) {
    if (e.target.id === 'createTemplateModal') {
        hideCreateTemplateModal();
    }
    if (e.target.id === 'previewModal') {
        hidePreviewModal();
    }
});

// Student selection functions
function selectAllStudents() {
    const checkboxes = document.querySelectorAll('.student-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
    });
    document.getElementById('selectAllCheckbox').checked = true;
    updateSelectedCount();
}

function clearSelection() {
    const checkboxes = document.querySelectorAll('.student-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
    document.getElementById('selectAllCheckbox').checked = false;
    updateSelectedCount();
}

function toggleAllStudents() {
    const selectAll = document.getElementById('selectAllCheckbox');
    const checkboxes = document.querySelectorAll('.student-checkbox');

    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
    updateSelectedCount();
}

function updateSelectedCount() {
    const selectedCheckboxes = document.querySelectorAll('.student-checkbox:checked');
    const count = selectedCheckboxes.length;

    document.getElementById('selectedStudentCount').textContent = count;
    document.getElementById('selectedCount').textContent = count;
    document.getElementById('sendButtonCount').textContent = count;

    // Update select all checkbox state
    const totalCheckboxes = document.querySelectorAll('.student-checkbox');
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');

    if (count === 0) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = false;
    } else if (count === totalCheckboxes.length) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = true;
    } else {
        selectAllCheckbox.indeterminate = true;
    }
}

// Department and Year filter functions
function selectByDepartment() {
    const students = document.querySelectorAll('.student-checkbox');
    const departments = [...new Set(Array.from(students).map(s => s.dataset.department))];

    const departmentList = document.getElementById('departmentList');
    departmentList.innerHTML = departments.map(dept => `
        <label class="flex items-center p-3 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg cursor-pointer">
            <input type="checkbox" value="${dept}" class="department-filter w-4 h-4 text-purple-600 bg-gray-100 border-gray-300 rounded focus:ring-purple-500">
            <span class="ml-3 text-sm font-medium text-gray-700 dark:text-gray-300">${dept}</span>
        </label>
    `).join('');

    showDepartmentModal();
}

function selectByYear() {
    const students = document.querySelectorAll('.student-checkbox');
    const years = [...new Set(Array.from(students).map(s => s.dataset.year))].sort();

    const yearList = document.getElementById('yearList');
    yearList.innerHTML = years.map(year => `
        <label class="flex items-center p-3 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg cursor-pointer">
            <input type="checkbox" value="${year}" class="year-filter w-4 h-4 text-orange-600 bg-gray-100 border-gray-300 rounded focus:ring-orange-500">
            <span class="ml-3 text-sm font-medium text-gray-700 dark:text-gray-300">Year ${year}</span>
        </label>
    `).join('');

    showYearModal();
}

function applyDepartmentFilter() {
    const selectedDepts = Array.from(document.querySelectorAll('.department-filter:checked')).map(cb => cb.value);
    const students = document.querySelectorAll('.student-checkbox');

    students.forEach(student => {
        if (selectedDepts.includes(student.dataset.department)) {
            student.checked = true;
        }
    });

    updateSelectedCount();
    hideDepartmentModal();
    showNotification(`Selected students from ${selectedDepts.length} department(s)`, 'success');
}

function applyYearFilter() {
    const selectedYears = Array.from(document.querySelectorAll('.year-filter:checked')).map(cb => cb.value);
    const students = document.querySelectorAll('.student-checkbox');

    students.forEach(student => {
        if (selectedYears.includes(student.dataset.year)) {
            student.checked = true;
        }
    });

    updateSelectedCount();
    hideYearModal();
    showNotification(`Selected students from ${selectedYears.length} year(s)`, 'success');
}

// Modal functions for filters
function showDepartmentModal() {
    document.getElementById('departmentModal').classList.remove('hidden');
    document.body.style.overflow = 'hidden';
}

function hideDepartmentModal() {
    document.getElementById('departmentModal').classList.add('hidden');
    document.body.style.overflow = 'auto';
}

function showYearModal() {
    document.getElementById('yearModal').classList.remove('hidden');
    document.body.style.overflow = 'hidden';
}

function hideYearModal() {
    document.getElementById('yearModal').classList.add('hidden');
    document.body.style.overflow = 'auto';
}

function showEmailPreviewModal() {
    document.getElementById('emailPreviewModal').classList.remove('hidden');
    document.body.style.overflow = 'hidden';
}

function hideEmailPreviewModal() {
    document.getElementById('emailPreviewModal').classList.add('hidden');
    document.body.style.overflow = 'auto';
}

// Email preview function
function previewEmail() {
    const subject = document.getElementById('email_subject').value;
    const content = document.getElementById('email_content').value;
    const selectedCount = document.querySelectorAll('.student-checkbox:checked').length;

    if (!subject || !content) {
        showNotification('Please enter both subject and content to preview', 'error');
        return;
    }

    if (selectedCount === 0) {
        showNotification('Please select at least one recipient', 'error');
        return;
    }

    // Sample student data for preview
    const sampleStudent = {
        student_name: 'John Doe',
        student_email: '<EMAIL>',
        student_id: 'STU001',
        department: 'Computer Science',
        year: '2',
        college_name: '<?= htmlspecialchars($college['name'] ?? 'Your College') ?>',
        admin_name: '<?= htmlspecialchars($admin_name) ?>'
    };

    // Replace shortcodes with sample data
    let previewSubject = subject;
    let previewContent = content;

    Object.keys(sampleStudent).forEach(key => {
        const placeholder = `{{${key}}}`;
        previewSubject = previewSubject.replace(new RegExp(placeholder, 'g'), sampleStudent[key]);
        previewContent = previewContent.replace(new RegExp(placeholder, 'g'), sampleStudent[key]);
    });

    const previewContentDiv = document.getElementById('emailPreviewContent');
    previewContentDiv.innerHTML = `
        <div class="bg-gray-50 dark:bg-gray-700 rounded-xl p-6">
            <div class="mb-4">
                <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Email Preview</h4>
                <p class="text-sm text-gray-600 dark:text-gray-400">This is how the email will look for recipients (showing sample data)</p>
            </div>
            <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600 overflow-hidden">
                <div class="bg-gray-100 dark:bg-gray-700 p-4 border-b border-gray-200 dark:border-gray-600">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-600 dark:text-gray-400">To: ${selectedCount} recipients</p>
                            <p class="text-sm text-gray-600 dark:text-gray-400">From: ${sampleStudent.admin_name}</p>
                        </div>
                        <span class="bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 text-xs font-medium px-2 py-1 rounded-full">Preview</span>
                    </div>
                </div>
                <div class="p-6">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Subject:</label>
                        <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                            <p class="font-medium">${previewSubject}</p>
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Content:</label>
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                            <pre class="whitespace-pre-wrap font-sans">${previewContent}</pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    showEmailPreviewModal();
}

// Notification function
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full`;

    if (type === 'success') {
        notification.className += ' bg-green-500 text-white';
    } else if (type === 'error') {
        notification.className += ' bg-red-500 text-white';
    } else {
        notification.className += ' bg-blue-500 text-white';
    }

    notification.innerHTML = `
        <div class="flex items-center">
            <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'exclamation-triangle' : 'info'} mr-2"></i>
            <span>${message}</span>
        </div>
    `;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);

    // Remove after 3 seconds
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Form validation
document.getElementById('emailForm').addEventListener('submit', function(e) {
    const selectedStudents = document.querySelectorAll('.student-checkbox:checked');
    const subject = document.getElementById('email_subject').value.trim();
    const content = document.getElementById('email_content').value.trim();

    if (selectedStudents.length === 0) {
        e.preventDefault();
        showNotification('Please select at least one recipient', 'error');
        return false;
    }

    if (!subject || !content) {
        e.preventDefault();
        showNotification('Please enter both subject and content', 'error');
        return false;
    }

    // Show confirmation
    if (!confirm(`Are you sure you want to send this email to ${selectedStudents.length} students?`)) {
        e.preventDefault();
        return false;
    }

    // Show loading state
    const submitBtn = e.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Sending...';
    submitBtn.disabled = true;

    return true;
});

// Escape key to close modals
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        hideCreateTemplateModal();
        hidePreviewModal();
        hideEmailPreviewModal();
        hideDepartmentModal();
        hideYearModal();
    }
});

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    updateSelectedCount();
});
</script>

<?php require_once '../includes/footer.php'; ?>