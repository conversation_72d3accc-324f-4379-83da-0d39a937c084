<?php
use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\Exception;

require_once '../includes/db.php';
require_once '../includes/header.php';
require '../vendor/autoload.php'; // Make sure this path is correct for PHPMailer

error_reporting(E_ALL);
ini_set('display_errors', 1);

$error = $success = '';
$role = $_GET['role'] ?? '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = trim($_POST['email']);
    $role = $_POST['role'];

    if (!in_array($role, ['students', 'alumni'])) {
        $error = "Invalid role.";
    } else {
        $table = $role;
        $stmt = $conn->prepare("SELECT id FROM $table WHERE email = ?");
        $stmt->execute([$email]);
        $user = $stmt->fetch();

        if ($user) {
            $token = bin2hex(random_bytes(32));
            $conn->prepare("INSERT INTO password_resets (email, token, role) VALUES (?, ?, ?)")
                ->execute([$email, $token, $role]);

            $resetLink = "https://darkviolet-vulture-501696.hostingersite.com/auth/reset_password.php?token=$token";

            // Send email using Gmail SMTP
            $mail = new PHPMailer(true);
            try {
                $mail->isSMTP();
                $mail->Host = 'smtp.gmail.com';
                $mail->SMTPAuth = true;
                $mail->Username = '<EMAIL>';
                $mail->Password = 'xoqrvepcjjpuycgo'; // App Password (no spaces)
                $mail->SMTPSecure = 'tls';
                $mail->Port = 587;

                $mail->setFrom('<EMAIL>', 'Connect My Students');
                $mail->addAddress($email);
                $mail->isHTML(true);
                $mail->Subject = '🔐 Reset Your Password';
                $mail->Body = "Hi,<br><br>You requested a password reset.<br>Click the link below:<br><br><a href='$resetLink'>$resetLink</a><br><br>If you didn’t request this, please ignore this email.";

                $mail->send();
                $success = "Password reset link has been sent to your email.";
            } catch (Exception $e) {
                $error = "Failed to send email. Mailer Error: {$mail->ErrorInfo}";
            }
        } else {
            $error = "Email not found.";
        }
    }
}
?>

<div class="container mt-5">
    <h3>Forgot Password</h3>
    <?php if ($error): ?><div class="alert alert-danger"><?= $error ?></div><?php endif; ?>
    <?php if ($success): ?><div class="alert alert-success"><?= $success ?></div><?php endif; ?>

    <form method="POST">
        <input type="hidden" name="role" value="<?= htmlspecialchars($role) ?>">
        <div class="mb-3">
            <label>Email</label>
            <input type="email" name="email" required class="form-control">
        </div>
        <button class="btn btn-primary">Send Reset Link</button>
    </form>
    <br> <a href='https://darkviolet-vulture-501696.hostingersite.com/'> Login Here </a>
</div>

<?php require_once '../includes/footer.php'; ?>
