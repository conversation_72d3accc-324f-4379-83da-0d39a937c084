<?php
session_start();
require_once '../includes/db.php';

if (!isset($_SESSION['superadmin_id'])) {
    header("Location: ../auth/superadmin_login.php");
    exit;
}

$admin_id = $_GET['id'] ?? null;
if (!$admin_id) {
    $_SESSION['flash_message'] = "Invalid admin ID.";
    header("Location: superadmin_dashboard.php");
    exit;
}

// Fetch the admin's data
$stmt = $conn->prepare("SELECT ca.*, c.name AS college_name 
                       FROM college_admins ca 
                       JOIN colleges c ON ca.college_id = c.id 
                       WHERE ca.id = ?");
$stmt->execute([$admin_id]);
$admin = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$admin) {
    $_SESSION['flash_message'] = "College admin not found.";
    header("Location: superadmin_dashboard.php");
    exit;
}

$error = '';
$success = '';
$newPassword = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action']) && $_POST['action'] === 'generate') {
        // Generate a random password
        $length = 12;
        $chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+";
        $newPassword = "";
        for ($i = 0; $i < $length; $i++) {
            $newPassword .= $chars[rand(0, strlen($chars) - 1)];
        }
    } elseif (isset($_POST['action']) && $_POST['action'] === 'reset') {
        $password = $_POST['password'];
        
        if (empty($password)) {
            $error = "Password cannot be empty.";
        } elseif (strlen($password) < 8) {
            $error = "Password must be at least 8 characters long.";
        } else {
            $hashed = password_hash($password, PASSWORD_DEFAULT);
            $update = $conn->prepare("UPDATE college_admins SET password = ? WHERE id = ?");
            $update->execute([$hashed, $admin_id]);
            
            $success = "Password has been reset successfully.";
            $_SESSION['flash_message'] = "Password for " . htmlspecialchars($admin['name']) . " has been reset successfully.";
            
            // Redirect after successful password reset
            header("Location: superadmin_dashboard.php");
            exit;
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Admin Password - Mentoshri</title>
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts - Inter -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        'primary': '#0a66c2',
                        'primary-dark': '#004182',
                        'primary-light': '#e7f3ff',
                    },
                    fontFamily: {
                        'sans': ['Inter', 'sans-serif'],
                    },
                }
            }
        }
    </script>
    <!-- Design System -->
    <link rel="stylesheet" href="../assets/css/design-system.css">
</head>
<body class="font-sans bg-gray-50 dark:bg-gray-900 min-h-screen flex flex-col">
    <!-- Header -->
    <header class="bg-white dark:bg-gray-800 shadow-sm">
        <div class="container mx-auto px-4 py-3 flex items-center justify-between">
            <div class="flex items-center">
                <a href="superadmin_dashboard.php" class="text-primary font-bold text-2xl">Mentoshri</a>
            </div>
            
            <div class="flex items-center space-x-4">
                <button id="themeToggle" class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700">
                    <i class="fas fa-moon dark:hidden"></i>
                    <i class="fas fa-sun hidden dark:block"></i>
                </button>
                <a href="superadmin_dashboard.php" class="text-gray-600 dark:text-gray-300 hover:text-primary dark:hover:text-primary">
                    <i class="fas fa-arrow-left mr-2"></i> Back to Dashboard
                </a>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="flex-grow py-8 px-4">
        <div class="max-w-2xl mx-auto">
            <div class="mb-6">
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Reset Admin Password</h1>
                <p class="text-gray-600 dark:text-gray-400 mt-1">Set a new password for <?= htmlspecialchars($admin['name']) ?></p>
            </div>
            
            <?php if ($error): ?>
                <div class="bg-red-50 dark:bg-red-900/30 border-l-4 border-red-500 text-red-700 dark:text-red-400 p-4 mb-6" role="alert">
                    <p><?= htmlspecialchars($error) ?></p>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="bg-green-50 dark:bg-green-900/30 border-l-4 border-green-500 text-green-700 dark:text-green-400 p-4 mb-6" role="alert">
                    <p><?= htmlspecialchars($success) ?></p>
                </div>
            <?php endif; ?>
            
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700">
                <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
                    <h2 class="font-semibold text-gray-800 dark:text-white">Admin Information</h2>
                </div>
                
                <div class="p-6">
                    <div class="bg-gray-50 dark:bg-gray-700/30 rounded-lg p-4 mb-6">
                        <div class="flex items-center">
                            <div class="w-12 h-12 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center text-blue-600 dark:text-blue-400 mr-4">
                                <i class="fas fa-user-shield text-xl"></i>
                            </div>
                            <div>
                                <h3 class="font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($admin['name']) ?></h3>
                                <p class="text-sm text-gray-500 dark:text-gray-400">
                                    <span class="inline-block bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 text-xs px-2 py-0.5 rounded">
                                        <?= htmlspecialchars($admin['college_name']) ?>
                                    </span>
                                    <span class="ml-2"><?= htmlspecialchars($admin['email']) ?></span>
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="space-y-6">
                        <div class="bg-yellow-50 dark:bg-yellow-900/20 border-l-4 border-yellow-500 text-yellow-700 dark:text-yellow-400 p-4">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm">
                                        You are about to reset the password for this admin. The admin will need to use the new password for their next login.
                                    </p>
                                </div>
                            </div>
                        </div>
                        
                        <form method="post" id="passwordForm">
                            <div class="form-group">
                                <label for="password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">New Password <span class="text-red-500">*</span></label>
                                <div class="relative">
                                    <input 
                                        type="text" 
                                        id="password" 
                                        name="password" 
                                        class="form-input pr-24" 
                                        value="<?= htmlspecialchars($newPassword) ?>"
                                        placeholder="Enter new password"
                                        required
                                        minlength="8"
                                    >
                                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                        <button type="submit" name="action" value="generate" class="text-primary text-sm hover:text-primary-dark">
                                            Generate Strong
                                        </button>
                                    </div>
                                </div>
                                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Minimum 8 characters recommended</p>
                            </div>
                            
                            <div class="mt-6 flex justify-between items-center">
                                <a href="edit_collegeadmin.php?id=<?= $admin['id'] ?>" class="btn btn-outline">
                                    Cancel
                                </a>
                                <button type="submit" name="action" value="reset" class="btn btn-primary">
                                    <i class="fas fa-key mr-2"></i> Reset Password
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 py-4 mt-auto">
        <div class="container mx-auto px-4">
            <div class="text-center text-gray-600 dark:text-gray-400 text-sm">
                &copy; <?= date('Y') ?> Mentoshri. All rights reserved.
            </div>
        </div>
    </footer>

    <script>
        // Theme Toggle
        const themeToggle = document.getElementById('themeToggle');
        const htmlElement = document.documentElement;
        
        // Check for saved theme preference or use system preference
        if (localStorage.theme === 'dark' || 
            (!('theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
            htmlElement.classList.add('dark');
        } else {
            htmlElement.classList.remove('dark');
        }
        
        // Toggle theme
        themeToggle.addEventListener('click', () => {
            if (htmlElement.classList.contains('dark')) {
                htmlElement.classList.remove('dark');
                localStorage.theme = 'light';
            } else {
                htmlElement.classList.add('dark');
                localStorage.theme = 'dark';
            }
        });
        
        // Copy password to clipboard when generated
        document.addEventListener('DOMContentLoaded', function() {
            const passwordInput = document.getElementById('password');
            if (passwordInput.value) {
                navigator.clipboard.writeText(passwordInput.value).then(() => {
                    alert('Password copied to clipboard!');
                });
            }
        });
    </script>
</body>
</html> 