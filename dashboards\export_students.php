<?php
// Include session configuration
require_once '../includes/session_config.php';
require_once '../includes/db.php';
require_once '../includes/db_schema_check.php';

if (!isset($_SESSION['collegeadmin_id'])) {
    header("Location: ../auth/collegeadmin_login.php");
    exit;
}

$college_id = $_SESSION['college_id'];
$admin_name = $_SESSION['collegeadmin_name'];

// Set role explicitly for the sidebar
$role = 'collegeadmin';
$user_id = $_SESSION['collegeadmin_id'];
$user_name = $_SESSION['collegeadmin_name'];
$current_page = basename($_SERVER['PHP_SELF']);

$error = '';
$success = '';

// Get college details
$collegeStmt = $conn->prepare("SELECT name FROM colleges WHERE id = ?");
$collegeStmt->execute([$college_id]);
$college = $collegeStmt->fetch(PDO::FETCH_ASSOC);

// Get departments for filter
$deptStmt = $conn->prepare("SELECT DISTINCT department FROM students WHERE college_id = ? ORDER BY department ASC");
$deptStmt->execute([$college_id]);
$departments = $deptStmt->fetchAll(PDO::FETCH_COLUMN);

// Get years for filter
$yearStmt = $conn->prepare("SELECT DISTINCT year FROM students WHERE college_id = ? ORDER BY year ASC");
$yearStmt->execute([$college_id]);
$years = $yearStmt->fetchAll(PDO::FETCH_COLUMN);

// Get student statistics
$statsStmt = $conn->prepare("
    SELECT
        COUNT(*) as total_students,
        COUNT(CASE WHEN verified = 1 THEN 1 END) as verified_students,
        COUNT(CASE WHEN verified = 0 THEN 1 END) as pending_students,
        COUNT(DISTINCT department) as total_departments
    FROM students
    WHERE college_id = ?
");
$statsStmt->execute([$college_id]);
$stats = $statsStmt->fetch(PDO::FETCH_ASSOC);

// Handle export request
if (isset($_GET['export']) && $_GET['export'] === 'csv') {
    // Build query based on filters
    $whereConditions = ["college_id = ?"];
    $params = [$college_id];

    // Apply filters
    if (!empty($_GET['department']) && $_GET['department'] !== 'all') {
        $whereConditions[] = "department = ?";
        $params[] = $_GET['department'];
    }

    if (!empty($_GET['year']) && $_GET['year'] !== 'all') {
        $whereConditions[] = "year = ?";
        $params[] = $_GET['year'];
    }

    if (!empty($_GET['verified']) && $_GET['verified'] !== 'all') {
        $whereConditions[] = "verified = ?";
        $params[] = $_GET['verified'];
    }

    if (!empty($_GET['search'])) {
        $whereConditions[] = "(full_name LIKE ? OR email LIKE ? OR roll_number LIKE ?)";
        $searchTerm = '%' . $_GET['search'] . '%';
        $params[] = $searchTerm;
        $params[] = $searchTerm;
        $params[] = $searchTerm;
    }

    $whereClause = implode(' AND ', $whereConditions);

    // Select fields to export
    $fields = $_GET['fields'] ?? ['full_name', 'email', 'department', 'year', 'roll_number', 'verified'];
    $selectedFields = array_intersect($fields, ['full_name', 'email', 'department', 'year', 'roll_number', 'division', 'verified', 'created_at']);

    if (empty($selectedFields)) {
        $selectedFields = ['full_name', 'email', 'department', 'year'];
    }

    $sql = "SELECT " . implode(', ', $selectedFields) . " FROM students WHERE $whereClause ORDER BY full_name ASC";
    $stmt = $conn->prepare($sql);
    $stmt->execute($params);
    $student_data = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Generate CSV
    $filename = 'students_export_' . date('Y-m-d_H-i-s') . '.csv';

    header('Content-Type: text/csv');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
    header('Expires: 0');

    $output = fopen('php://output', 'w');

    // Write header row
    $headers = [];
    foreach ($selectedFields as $field) {
        $headers[] = ucwords(str_replace('_', ' ', $field));
    }
    fputcsv($output, $headers);

    // Write data rows
    foreach ($student_data as $row) {
        $csvRow = [];
        foreach ($selectedFields as $field) {
            $value = $row[$field] ?? '';

            // Format specific fields
            if ($field === 'created_at' && !empty($value)) {
                $value = date('Y-m-d H:i:s', strtotime($value));
            } elseif ($field === 'verified') {
                $value = $value ? 'Verified' : 'Pending';
            }

            $csvRow[] = $value;
        }
        fputcsv($output, $csvRow);
    }

    fclose($output);
    exit;
}

// Set dashboard URL for header
$dashboard_url = 'collegeadmin_dashboard.php';

// Include the header
require_once '../includes/header.php';
?>

    <!-- Main Content -->
    <main class="flex-grow py-6 px-4">
        <div class="max-w-7xl mx-auto">
            <!-- Page Header -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8">
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="bg-gradient-to-br from-green-500 to-green-600 p-3 rounded-xl shadow-lg">
                            <i class="fas fa-file-export text-white text-xl"></i>
                        </div>
                        <div>
                            <h1 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                                Export Student Data
                            </h1>
                            <p class="text-gray-600 dark:text-gray-400 mt-1">
                                Export student data from <span class="font-medium text-green-600 dark:text-green-400"><?= htmlspecialchars($college['name'] ?? 'your college') ?></span>
                                <span class="mx-2">•</span>
                                <span class="text-sm"><?= date('l, F j, Y') ?></span>
                            </p>
                        </div>
                    </div>
                    <div class="mt-6 lg:mt-0 flex flex-wrap gap-3">
                        <a href="list_students.php" class="btn btn-outline flex items-center text-sm hover:shadow-md transition-all duration-200">
                            <i class="fas fa-arrow-left mr-2"></i> Back to Student List
                        </a>
                        <a href="import_students.php" class="btn btn-outline flex items-center text-sm hover:shadow-md transition-all duration-200">
                            <i class="fas fa-file-import mr-2"></i> Import Students
                        </a>
                    </div>
                </div>
            </div>

            <!-- Filters Applied Notice -->
            <?php if (!empty($_GET['search']) || (!empty($_GET['department']) && $_GET['department'] !== 'all') || (!empty($_GET['year']) && $_GET['year'] !== 'all') || (!empty($_GET['verified']) && $_GET['verified'] !== 'all')): ?>
                <div class="bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-800 rounded-xl p-4 mb-6">
                    <div class="flex items-center">
                        <i class="fas fa-info-circle text-blue-500 mr-3"></i>
                        <div>
                            <h4 class="font-medium text-blue-800 dark:text-blue-300">Filters Applied from Student List</h4>
                            <p class="text-sm text-blue-700 dark:text-blue-400 mt-1">
                                The export form has been pre-filled with your current search and filter settings.
                                <?php if (!empty($_GET['search'])): ?>
                                    <span class="font-medium">Search: "<?= htmlspecialchars($_GET['search']) ?>"</span>
                                <?php endif; ?>
                                <?php if (!empty($_GET['department']) && $_GET['department'] !== 'all'): ?>
                                    <span class="font-medium">Department: <?= htmlspecialchars($_GET['department']) ?></span>
                                <?php endif; ?>
                                <?php if (!empty($_GET['year']) && $_GET['year'] !== 'all'): ?>
                                    <span class="font-medium">Year: <?= htmlspecialchars($_GET['year']) ?></span>
                                <?php endif; ?>
                                <?php if (!empty($_GET['verified']) && $_GET['verified'] !== 'all'): ?>
                                    <span class="font-medium">Status: <?= $_GET['verified'] == '1' ? 'Verified' : 'Pending' ?></span>
                                <?php endif; ?>
                            </p>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Statistics Cards -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-users text-blue-600 dark:text-blue-400 text-xl"></i>
                        </div>
                        <div>
                            <p class="text-2xl font-bold text-gray-900 dark:text-white"><?= $stats['total_students'] ?></p>
                            <p class="text-sm text-gray-600 dark:text-gray-400">Total Students</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-user-check text-green-600 dark:text-green-400 text-xl"></i>
                        </div>
                        <div>
                            <p class="text-2xl font-bold text-gray-900 dark:text-white"><?= $stats['verified_students'] ?></p>
                            <p class="text-sm text-gray-600 dark:text-gray-400">Verified Students</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-user-clock text-orange-600 dark:text-orange-400 text-xl"></i>
                        </div>
                        <div>
                            <p class="text-2xl font-bold text-gray-900 dark:text-white"><?= $stats['pending_students'] ?></p>
                            <p class="text-sm text-gray-600 dark:text-gray-400">Pending Verification</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-building text-purple-600 dark:text-purple-400 text-xl"></i>
                        </div>
                        <div>
                            <p class="text-2xl font-bold text-gray-900 dark:text-white"><?= $stats['total_departments'] ?></p>
                            <p class="text-sm text-gray-600 dark:text-gray-400">Departments</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content Grid -->
            <div class="grid grid-cols-1 xl:grid-cols-4 gap-8">
                <!-- Left Column - Export Form (3/4 width) -->
                <div class="xl:col-span-3">
                    <!-- Export Configuration -->
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 mb-8">
                        <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
                            <h2 class="text-lg font-semibold text-gray-800 dark:text-white flex items-center">
                                <i class="fas fa-cog mr-3 text-blue-600"></i> Export Configuration
                            </h2>
                            <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">Configure your export settings and filters</p>
                        </div>

                        <form id="exportForm" method="GET" class="p-6 space-y-6">
                            <input type="hidden" name="export" value="csv">

                            <!-- Filters Section -->
                            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                                <div class="form-group">
                                    <label for="department" class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                                        Department Filter
                                    </label>
                                    <select id="department" name="department" class="form-select w-full rounded-lg border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                        <option value="all">All Departments</option>
                                        <?php foreach ($departments as $dept): ?>
                                            <option value="<?= htmlspecialchars($dept) ?>" <?= (isset($_GET['department']) && $_GET['department'] === $dept) ? 'selected' : '' ?>><?= htmlspecialchars($dept) ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="year" class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                                        Academic Year
                                    </label>
                                    <select id="year" name="year" class="form-select w-full rounded-lg border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                        <option value="all">All Years</option>
                                        <?php foreach ($years as $year): ?>
                                            <option value="<?= htmlspecialchars($year) ?>" <?= (isset($_GET['year']) && $_GET['year'] == $year) ? 'selected' : '' ?>>Year <?= htmlspecialchars($year) ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="verified" class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                                        Verification Status
                                    </label>
                                    <select id="verified" name="verified" class="form-select w-full rounded-lg border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                        <option value="all" <?= (!isset($_GET['verified']) || $_GET['verified'] === 'all') ? 'selected' : '' ?>>All Status</option>
                                        <option value="1" <?= (isset($_GET['verified']) && $_GET['verified'] === '1') ? 'selected' : '' ?>>Verified Only</option>
                                        <option value="0" <?= (isset($_GET['verified']) && $_GET['verified'] === '0') ? 'selected' : '' ?>>Pending Only</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="search" class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                                        Search Filter
                                    </label>
                                    <input type="text" id="search" name="search" class="form-input w-full rounded-lg border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="Search by name, email, or roll number" value="<?= htmlspecialchars($_GET['search'] ?? '') ?>">
                                </div>
                            </div>

                            <!-- Field Selection -->
                            <div class="form-group">
                                <label class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                                    Select Fields to Export
                                </label>
                                <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                                    <label class="flex items-center">
                                        <input type="checkbox" name="fields[]" value="full_name" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" checked>
                                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Full Name</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="fields[]" value="email" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" checked>
                                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Email</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="fields[]" value="roll_number" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" checked>
                                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Roll Number</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="fields[]" value="department" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" checked>
                                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Department</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="fields[]" value="year" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" checked>
                                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Academic Year</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="fields[]" value="division" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Division</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="fields[]" value="verified" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" checked>
                                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Verification Status</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="fields[]" value="created_at" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Registration Date</span>
                                    </label>
                                </div>
                            </div>

                            <!-- Export Button -->
                            <div class="flex justify-end pt-4 border-t border-gray-200 dark:border-gray-700">
                                <button type="submit" class="btn btn-primary shadow-lg hover:shadow-xl transition-all duration-200">
                                    <i class="fas fa-download mr-2"></i> Export to CSV
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Right Column - Sidebar (1/4 width) -->
                <div class="xl:col-span-1">
                    <div class="space-y-6">
                        <!-- Quick Actions -->
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                            <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
                                <h3 class="text-lg font-semibold text-gray-800 dark:text-white flex items-center">
                                    <i class="fas fa-bolt mr-3 text-blue-600"></i> Quick Actions
                                </h3>
                            </div>
                            <div class="p-6 space-y-3">
                                <a href="list_students.php" class="flex items-center p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                                    <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-list text-blue-600 dark:text-blue-400"></i>
                                    </div>
                                    <div>
                                        <p class="font-medium text-gray-900 dark:text-white">All Students</p>
                                        <p class="text-sm text-gray-500 dark:text-gray-400">View student list</p>
                                    </div>
                                </a>

                                <a href="add_student.php" class="flex items-center p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                                    <div class="w-10 h-10 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-user-plus text-green-600 dark:text-green-400"></i>
                                    </div>
                                    <div>
                                        <p class="font-medium text-gray-900 dark:text-white">Add Student</p>
                                        <p class="text-sm text-gray-500 dark:text-gray-400">Add new student</p>
                                    </div>
                                </a>

                                <a href="import_students.php" class="flex items-center p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                                    <div class="w-10 h-10 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-file-import text-orange-600 dark:text-orange-400"></i>
                                    </div>
                                    <div>
                                        <p class="font-medium text-gray-900 dark:text-white">Import Students</p>
                                        <p class="text-sm text-gray-500 dark:text-gray-400">Bulk import students</p>
                                    </div>
                                </a>

                                <a href="verify_student.php" class="flex items-center p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                                    <div class="w-10 h-10 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-user-check text-purple-600 dark:text-purple-400"></i>
                                    </div>
                                    <div>
                                        <p class="font-medium text-gray-900 dark:text-white">Verify Students</p>
                                        <p class="text-sm text-gray-500 dark:text-gray-400">Approve registrations</p>
                                    </div>
                                </a>
                            </div>
                        </div>

                        <!-- Quick Export -->
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                            <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
                                <h3 class="text-lg font-semibold text-gray-800 dark:text-white flex items-center">
                                    <i class="fas fa-history mr-3 text-gray-600"></i> Quick Export
                                </h3>
                            </div>
                            <div class="p-6">
                                <div class="space-y-3">
                                    <a href="export_students.php?export=csv&department=all&year=all&verified=1&fields[]=full_name&fields[]=email&fields[]=department&fields[]=year&fields[]=roll_number&fields[]=verified"
                                       class="block p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
                                        <div class="flex items-center">
                                            <i class="fas fa-download text-green-600 mr-3"></i>
                                            <div>
                                                <p class="font-medium text-gray-900 dark:text-white text-sm">Verified Students</p>
                                                <p class="text-xs text-gray-500 dark:text-gray-400">Basic info for verified students</p>
                                            </div>
                                        </div>
                                    </a>

                                    <a href="export_students.php?export=csv&department=all&year=all&verified=all&fields[]=full_name&fields[]=email&fields[]=department&fields[]=year&fields[]=roll_number&fields[]=division&fields[]=verified&fields[]=created_at"
                                       class="block p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
                                        <div class="flex items-center">
                                            <i class="fas fa-download text-blue-600 mr-3"></i>
                                            <div>
                                                <p class="font-medium text-gray-900 dark:text-white text-sm">Complete Export</p>
                                                <p class="text-xs text-gray-500 dark:text-gray-400">All students with full details</p>
                                            </div>
                                        </div>
                                    </a>

                                    <a href="export_students.php?export=csv&department=all&year=all&verified=0&fields[]=full_name&fields[]=email&fields[]=department&fields[]=year&fields[]=roll_number"
                                       class="block p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
                                        <div class="flex items-center">
                                            <i class="fas fa-download text-orange-600 mr-3"></i>
                                            <div>
                                                <p class="font-medium text-gray-900 dark:text-white text-sm">Pending Students</p>
                                                <p class="text-xs text-gray-500 dark:text-gray-400">Students awaiting verification</p>
                                            </div>
                                        </div>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Form validation
        document.getElementById('exportForm').addEventListener('submit', function(e) {
            const checkboxes = document.querySelectorAll('input[name="fields[]"]:checked');

            if (checkboxes.length === 0) {
                e.preventDefault();
                alert('Please select at least one field to export.');
                return false;
            }
        });

        // Select/Deselect all fields
        function toggleAllFields(checked) {
            const checkboxes = document.querySelectorAll('input[name="fields[]"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = checked;
            });
        }

        // Add select all/none buttons
        document.addEventListener('DOMContentLoaded', function() {
            const fieldsContainer = document.querySelector('.grid.grid-cols-2.md\\:grid-cols-4.gap-3');
            if (fieldsContainer) {
                const buttonContainer = document.createElement('div');
                buttonContainer.className = 'col-span-full flex space-x-2 mb-2';
                buttonContainer.innerHTML = `
                    <button type="button" onclick="toggleAllFields(true)" class="text-xs text-blue-600 hover:text-blue-800">Select All</button>
                    <span class="text-gray-400">|</span>
                    <button type="button" onclick="toggleAllFields(false)" class="text-xs text-blue-600 hover:text-blue-800">Select None</button>
                `;
                fieldsContainer.insertBefore(buttonContainer, fieldsContainer.firstChild);
            }
        });

        // Theme toggle functionality
        const themeToggleBtn = document.getElementById('themeToggle');

        function getThemePreference() {
            return localStorage.getItem('color-theme') || 'light';
        }

        function setThemePreference(theme) {
            localStorage.setItem('color-theme', theme);
            if (theme === 'dark') {
                document.documentElement.classList.add('dark');
            } else {
                document.documentElement.classList.remove('dark');
            }
        }

        // Set initial theme
        setThemePreference(getThemePreference());

        // Toggle theme when button is clicked
        if (themeToggleBtn) {
            themeToggleBtn.addEventListener('click', function() {
                const currentTheme = getThemePreference();
                const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
                setThemePreference(newTheme);
            });
        }

        // Notifications button
        document.getElementById('notificationsBtn')?.addEventListener('click', function() {
            alert('Notifications feature coming soon!');
        });
    </script>

<?php
// Include the footer
require_once '../includes/footer.php';
?>