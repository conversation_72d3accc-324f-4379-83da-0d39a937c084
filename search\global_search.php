<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

session_start();
require_once '../includes/db.php';
require_once '../includes/header.php';

$keyword = trim($_GET['q'] ?? '');

// Determine role
$role = '';
if (isset($_SESSION['collegeadmin_id'])) {
    $role = 'collegeadmin';
} elseif (isset($_SESSION['faculty_id'])) {
    $role = 'faculty';
} elseif (isset($_SESSION['student_id'])) {
    $role = 'student';
} elseif (isset($_SESSION['alumni_id'])) {
    $role = 'alumni';
} elseif (isset($_SESSION['superadmin_id'])) {
    $role = 'superadmin';
}

$results = ['students' => [], 'alumni' => [], 'events' => []];

if ($keyword && $role) {
    $like = '%' . $keyword . '%';

    // Search students (College Admin & Faculty only)
    if (in_array($role, ['collegeadmin', 'faculty'])) {
        try {
            $stmt = $conn->prepare("SELECT id, full_name, email, department FROM students WHERE full_name LIKE ? OR email LIKE ? OR username LIKE ? OR skills LIKE ? OR department LIKE ?");
            $stmt->execute([$like, $like, $like, $like, $like]);
            $results['students'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Student Search Error: " . $e->getMessage());
        }
    }

    // Search alumni (College Admin, Faculty, Student)
    if (in_array($role, ['collegeadmin', 'faculty', 'student'])) {
        try {
            $stmt = $conn->prepare("SELECT id, full_name, email, company, industry FROM alumni WHERE full_name LIKE ? OR email LIKE ? OR username LIKE ? OR skills LIKE ? OR company LIKE ?");
            $stmt->execute([$like, $like, $like, $like, $like]);
            $results['alumni'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Alumni Search Error: " . $e->getMessage());
        }
    }

    // Search events (Everyone)
    if (in_array($role, ['collegeadmin', 'faculty', 'student', 'alumni'])) {
        try {
            $stmt = $conn->prepare("SELECT id, title, speaker_name, event_date FROM events WHERE title LIKE ? OR speaker_name LIKE ?");
            $stmt->execute([$like, $like]);
            $results['events'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Event Search Error: " . $e->getMessage());
        }
    }
}
?>

<div class="container mt-4">
    <h3>🔍 Global Search</h3>
    <form method="get" class="mb-4">
        <input type="text" name="q" class="form-control" placeholder="Search students, alumni, events..." value="<?= htmlspecialchars($keyword) ?>">
    </form>

    <?php if ($keyword): ?>
        <h5>Results for "<strong><?= htmlspecialchars($keyword) ?></strong>"</h5>

        <?php if ($results['students']): ?>
            <div class="mb-4">
                <h6>👨‍🎓 Students</h6>
                <ul class="list-group">
                    <?php foreach ($results['students'] as $s): ?>
                        <li class="list-group-item"><?= htmlspecialchars($s['full_name']) ?> (<?= htmlspecialchars($s['department']) ?>)</li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>

        <?php if ($results['alumni']): ?>
            <div class="mb-4">
                <h6>🎓 Alumni</h6>
                <ul class="list-group">
                    <?php foreach ($results['alumni'] as $a): ?>
                        <li class="list-group-item"><?= htmlspecialchars($a['full_name']) ?> – <?= htmlspecialchars($a['company']) ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>

        <?php if ($results['events']): ?>
            <div class="mb-4">
                <h6>📅 Events</h6>
                <ul class="list-group">
                    <?php foreach ($results['events'] as $e): ?>
                        <li class="list-group-item"><?= htmlspecialchars($e['title']) ?> – <?= htmlspecialchars($e['event_date']) ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>

        <?php if (!$results['students'] && !$results['alumni'] && !$results['events']): ?>
            <div class="alert alert-warning">No matching results found.</div>
        <?php endif; ?>
    <?php endif; ?>
</div>

<?php require_once '../includes/footer.php'; ?>
