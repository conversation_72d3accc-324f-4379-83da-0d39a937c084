<?php 
// Check if $role is defined
$role = isset($role) ? $role : '';

// Only show search modal for specific roles
if (in_array($role, ['collegeadmin', 'faculty', 'student', 'alumni'])): ?>
<!-- Search Modal -->
<div id="searchModal" class="fixed inset-0 z-50 hidden overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity" id="searchModalOverlay"></div>
        
        <div class="relative bg-white dark:bg-gray-800 rounded-lg max-w-2xl w-full mx-auto shadow-xl">
            <form action="/search/global_search.php" method="get">
                <div class="flex justify-between items-center p-4 border-b dark:border-gray-700">
                    <h3 class="text-lg font-semibold">Search</h3>
                    <button type="button" id="closeSearchModal" class="text-gray-500 hover:text-gray-700">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <div class="p-6">
                    <div class="search-box w-full">
                        <i class="fas fa-search text-gray-500 dark:text-gray-400 mr-2"></i>
                        <input type="text" class="search-input" 
                           name="q" 
                           placeholder="<?php
                                echo $role === 'collegeadmin' ? 'Search students, alumni, events...' :
                                    ($role === 'faculty' ? 'Search students, alumni, events...' :
                                    ($role === 'student' ? 'Search alumni, events...' :
                                    'Search events...'));
                           ?>" required>
                    </div>
                </div>
                
                <div class="flex justify-end p-4 border-t dark:border-gray-700">
                    <button type="button" id="cancelSearchModal" class="btn-secondary mr-2">Cancel</button>
                    <button type="submit" class="btn-primary">Search</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    // Search modal functionality
    document.addEventListener('DOMContentLoaded', function() {
        const searchButtons = document.querySelectorAll('[data-search-modal]');
        const searchModal = document.getElementById('searchModal');
        const closeSearchModal = document.getElementById('closeSearchModal');
        const cancelSearchModal = document.getElementById('cancelSearchModal');
        const searchModalOverlay = document.getElementById('searchModalOverlay');
        
        function openSearchModal() {
            searchModal.classList.remove('hidden');
        }
        
        function closeModal() {
            searchModal.classList.add('hidden');
        }
        
        searchButtons.forEach(button => {
            button.addEventListener('click', openSearchModal);
        });
        
        if (closeSearchModal) closeSearchModal.addEventListener('click', closeModal);
        if (cancelSearchModal) cancelSearchModal.addEventListener('click', closeModal);
        if (searchModalOverlay) searchModalOverlay.addEventListener('click', closeModal);
    });
</script>
<?php endif; ?>

</div> <!-- /.container -->

<!-- LinkedIn-style Footer -->
<footer class="bg-white dark:bg-gray-800 mt-10 border-t border-[#e0e0e0] dark:border-gray-700 py-8">
    <div class="max-w-7xl mx-auto px-4">
        <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
            <div>
                <div class="mb-3">
                    <span class="font-bold text-[#0a66c2] dark:text-[#0a66c2] text-xl">Mentoshri</span>
                </div>
                <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
                    Connecting alumni and students for mentorship and growth.
                </p>
                <div class="flex space-x-3">
                    <a href="#" class="text-gray-500 hover:text-[#0a66c2]">
                        <i class="fab fa-facebook-f"></i>
                    </a>
                    <a href="#" class="text-gray-500 hover:text-[#0a66c2]">
                        <i class="fab fa-twitter"></i>
                    </a>
                    <a href="#" class="text-gray-500 hover:text-[#0a66c2]">
                        <i class="fab fa-linkedin-in"></i>
                    </a>
                    <a href="#" class="text-gray-500 hover:text-[#0a66c2]">
                        <i class="fab fa-instagram"></i>
                    </a>
                </div>
            </div>
            
            <div>
                <h4 class="font-medium text-gray-900 dark:text-white mb-4">Navigation</h4>
                <ul class="space-y-2 text-sm">
                    <li><a href="<?= isset($dashboardUrl) ? $dashboardUrl : '#' ?>" class="text-gray-600 dark:text-gray-400 hover:text-[#0a66c2] dark:hover:text-[#0a66c2]">Home</a></li>
                    <li><a href="/events/event_list.php" class="text-gray-600 dark:text-gray-400 hover:text-[#0a66c2] dark:hover:text-[#0a66c2]">Events</a></li>
                    <li><a href="/resources/resource_list.php" class="text-gray-600 dark:text-gray-400 hover:text-[#0a66c2] dark:hover:text-[#0a66c2]">Resources</a></li>
                    <li><a href="/chat/chat.php" class="text-gray-600 dark:text-gray-400 hover:text-[#0a66c2] dark:hover:text-[#0a66c2]">Messages</a></li>
                </ul>
            </div>
            
            <div>
                <h4 class="font-medium text-gray-900 dark:text-white mb-4">Resources</h4>
                <ul class="space-y-2 text-sm">
                    <li><a href="#" class="text-gray-600 dark:text-gray-400 hover:text-[#0a66c2] dark:hover:text-[#0a66c2]">Help Center</a></li>
                    <li><a href="#" class="text-gray-600 dark:text-gray-400 hover:text-[#0a66c2] dark:hover:text-[#0a66c2]">Privacy & Terms</a></li>
                    <li><a href="#" class="text-gray-600 dark:text-gray-400 hover:text-[#0a66c2] dark:hover:text-[#0a66c2]">Accessibility</a></li>
                    <li><a href="#" class="text-gray-600 dark:text-gray-400 hover:text-[#0a66c2] dark:hover:text-[#0a66c2]">About</a></li>
                </ul>
            </div>
            
            <div>
                <h4 class="font-medium text-gray-900 dark:text-white mb-4">Settings</h4>
                <ul class="space-y-2 text-sm">
                    <li>
                        <button id="themeToggle" class="flex items-center justify-between w-full text-gray-600 dark:text-gray-400 hover:text-[#0a66c2] dark:hover:text-[#0a66c2]">
                            <span>Dark Mode</span>
                            <span class="relative inline-block w-10 h-5 rounded-full bg-gray-200 dark:bg-[#0a66c2] transition-colors">
                                <span class="absolute left-0.5 dark:left-5 top-0.5 w-4 h-4 rounded-full bg-white transition-all"></span>
                            </span>
                        </button>
                    </li>
                    <li><a href="#" class="text-gray-600 dark:text-gray-400 hover:text-[#0a66c2] dark:hover:text-[#0a66c2]">Language</a></li>
                </ul>
            </div>
        </div>
        
        <div class="text-center text-sm text-gray-600 dark:text-gray-400 mt-8 pt-6 border-t border-[#e0e0e0] dark:border-gray-700">
            <div class="flex flex-wrap justify-center gap-x-6 gap-y-2 mb-4">
                <a href="#" class="text-gray-600 dark:text-gray-400 hover:text-[#0a66c2] dark:hover:text-[#0a66c2] text-sm">About</a>
                <a href="#" class="text-gray-600 dark:text-gray-400 hover:text-[#0a66c2] dark:hover:text-[#0a66c2] text-sm">Privacy</a>
                <a href="#" class="text-gray-600 dark:text-gray-400 hover:text-[#0a66c2] dark:hover:text-[#0a66c2] text-sm">Terms</a>
                <a href="#" class="text-gray-600 dark:text-gray-400 hover:text-[#0a66c2] dark:hover:text-[#0a66c2] text-sm">Help Center</a>
            </div>
            &copy; <?= date('Y') ?> Mentoshri. All rights reserved.
        </div>
    </div>
</footer>

<!-- Theme Toggle Script -->
<script>
    // Theme toggle functionality
    document.addEventListener('DOMContentLoaded', function() {
        const themeToggles = document.querySelectorAll('#themeToggle');
        
        // Check for saved theme preference or respect OS preference
        if (localStorage.theme === 'dark' || 
            (!('theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
            document.documentElement.classList.add('dark');
        } else {
            document.documentElement.classList.remove('dark');
        }
        
        // Toggle theme
        themeToggles.forEach(function(themeToggle) {
            themeToggle.addEventListener('click', function() {
                if (document.documentElement.classList.contains('dark')) {
                    document.documentElement.classList.remove('dark');
                    localStorage.theme = 'light';
                } else {
                    document.documentElement.classList.add('dark');
                    localStorage.theme = 'dark';
                }
            });
        });
    });
</script>

<!-- Custom JS -->
<script src="/assets/js/script.js"></script>
</body>
</html>
