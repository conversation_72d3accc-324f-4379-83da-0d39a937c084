<?php
require_once '../includes/db.php';

// Check session
if (!isset($_SESSION['collegeadmin_id'])) {
    header("Location: ../auth/collegeadmin_login.php");
    exit;
}

$college_id = $_SESSION['college_id'];

// Set role explicitly for the sidebar
$role = 'collegeadmin';
$user_id = $_SESSION['collegeadmin_id'];
$user_name = $_SESSION['collegeadmin_name'] ?? 'Admin';
$current_page = basename($_SERVER['PHP_SELF']);

// Get admin details
$stmt = $conn->prepare("SELECT * FROM college_admins WHERE id = ?");
$stmt->execute([$user_id]);
$admin = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$admin) {
    header("Location: ../auth/logout.php");
    exit;
}

// Get college details
$collegeStmt = $conn->prepare("SELECT * FROM colleges WHERE id = ?");
$collegeStmt->execute([$college_id]);
$college = $collegeStmt->fetch(PDO::FETCH_ASSOC);

$success = '';
$error = '';

// Handle form submission for profile update
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = trim($_POST['name']);
    $email = trim($_POST['email']);
    $phone = trim($_POST['phone'] ?? '');
    $current_password = trim($_POST['current_password'] ?? '');
    $new_password = trim($_POST['new_password'] ?? '');
    $confirm_password = trim($_POST['confirm_password'] ?? '');

    // Validate inputs
    if (empty($name) || empty($email)) {
        $error = "Name and email are required fields.";
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = "Please enter a valid email address.";
    } else {
        try {
            // Start transaction
            $conn->beginTransaction();
            
            // Check if email is already in use by another admin
            $emailCheck = $conn->prepare("SELECT id FROM college_admins WHERE email = ? AND id != ?");
            $emailCheck->execute([$email, $user_id]);
            if ($emailCheck->rowCount() > 0) {
                $error = "Email address is already in use by another administrator.";
            } else {
                // Update basic info
                $updateStmt = $conn->prepare("UPDATE college_admins SET name = ?, email = ?, phone = ? WHERE id = ?");
                $updateStmt->execute([$name, $email, $phone, $user_id]);
                
                // Handle password change if requested
                if (!empty($current_password) && !empty($new_password)) {
                    // Verify current password
                    if (!password_verify($current_password, $admin['password'])) {
                        $error = "Current password is incorrect.";
                    } elseif ($new_password !== $confirm_password) {
                        $error = "New passwords do not match.";
                    } elseif (strlen($new_password) < 8) {
                        $error = "New password must be at least 8 characters long.";
                    } else {
                        // Update password
                        $hashedPassword = password_hash($new_password, PASSWORD_DEFAULT);
                        $passwordStmt = $conn->prepare("UPDATE college_admins SET password = ? WHERE id = ?");
                        $passwordStmt->execute([$hashedPassword, $user_id]);
                    }
                }
                
                if (empty($error)) {
                    // Commit changes
                    $conn->commit();
                    $success = "Profile updated successfully.";
                    
                    // Update session variables
                    $_SESSION['collegeadmin_name'] = $name;
                    
                    // Refresh admin data
                    $stmt->execute([$user_id]);
                    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
                }
            }
        } catch (PDOException $e) {
            $conn->rollBack();
            $error = "Database error: " . $e->getMessage();
        }
    }
}

require_once '../includes/header.php';
?>

<!-- Main Content -->
<main class="flex-grow py-8 px-4">
    <div class="container mx-auto">
        <!-- Breadcrumb -->
        <nav class="mb-6 text-sm" aria-label="Breadcrumb">
            <ol class="flex items-center space-x-2">
                <li>
                    <a href="collegeadmin_dashboard.php" class="text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary-light">
                        <i class="fas fa-home"></i>
                        <span class="sr-only">Dashboard</span>
                    </a>
                </li>
                <li class="flex items-center">
                    <i class="fas fa-chevron-right text-gray-400 text-xs mx-1"></i>
                    <span class="text-gray-700 dark:text-gray-300">My Profile</span>
                </li>
            </ol>
        </nav>

        <div class="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                    <i class="fas fa-user-circle text-primary mr-3"></i>
                    My Profile
                </h1>
                <p class="text-gray-600 dark:text-gray-400 mt-1">
                    View and edit your account information
                </p>
            </div>
        </div>

        <?php if ($success): ?>
            <div class="bg-green-50 dark:bg-green-900/30 border-l-4 border-green-500 text-green-700 dark:text-green-400 p-4 mb-6 flex justify-between items-center" role="alert">
                <div class="flex items-center">
                    <i class="fas fa-check-circle mr-2"></i>
                    <span><?= htmlspecialchars($success) ?></span>
                </div>
                <button type="button" class="text-green-700 dark:text-green-400 hover:opacity-75" onclick="this.parentElement.style.display='none';">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="bg-red-50 dark:bg-red-900/30 border-l-4 border-red-500 text-red-700 dark:text-red-400 p-4 mb-6 flex justify-between items-center" role="alert">
                <div class="flex items-center">
                    <i class="fas fa-exclamation-circle mr-2"></i>
                    <span><?= htmlspecialchars($error) ?></span>
                </div>
                <button type="button" class="text-red-700 dark:text-red-400 hover:opacity-75" onclick="this.parentElement.style.display='none';">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        <?php endif; ?>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <!-- Profile Summary Card -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
                <div class="flex flex-col items-center text-center">
                    <div class="h-24 w-24 rounded-full bg-primary-light dark:bg-primary-dark/30 flex items-center justify-center text-primary dark:text-primary-light text-4xl mb-4">
                        <i class="fas fa-user-shield"></i>
                    </div>
                    <h2 class="text-xl font-bold text-gray-900 dark:text-white">
                        <?= htmlspecialchars($admin['name']) ?>
                    </h2>
                    <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                        College Administrator
                    </p>
                    <div class="mt-4 w-full">
                        <div class="border-t border-gray-200 dark:border-gray-700 pt-4">
                            <div class="flex items-center justify-center mb-2">
                                <i class="fas fa-building text-gray-500 dark:text-gray-400 mr-2"></i>
                                <span class="text-gray-700 dark:text-gray-300"><?= htmlspecialchars($college['name']) ?></span>
                            </div>
                            <div class="flex items-center justify-center mb-2">
                                <i class="fas fa-envelope text-gray-500 dark:text-gray-400 mr-2"></i>
                                <span class="text-gray-700 dark:text-gray-300"><?= htmlspecialchars($admin['email']) ?></span>
                            </div>
                            <?php if (!empty($admin['phone'])): ?>
                            <div class="flex items-center justify-center">
                                <i class="fas fa-phone text-gray-500 dark:text-gray-400 mr-2"></i>
                                <span class="text-gray-700 dark:text-gray-300"><?= htmlspecialchars($admin['phone']) ?></span>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Profile Edit Form -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6 md:col-span-2">
                <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4">
                    Edit Profile Information
                </h3>
                <form method="POST" id="profileForm">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Full Name <span class="text-red-500">*</span>
                            </label>
                            <input type="text" id="name" name="name" 
                                class="form-input w-full" 
                                value="<?= htmlspecialchars($admin['name']) ?>" required>
                        </div>
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Email Address <span class="text-red-500">*</span>
                            </label>
                            <input type="email" id="email" name="email" 
                                class="form-input w-full" 
                                value="<?= htmlspecialchars($admin['email']) ?>" required>
                        </div>
                    </div>

                    <div class="mb-6">
                        <label for="phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Phone Number
                        </label>
                        <input type="tel" id="phone" name="phone" 
                            class="form-input w-full" 
                            value="<?= htmlspecialchars($admin['phone'] ?? '') ?>">
                    </div>

                    <h4 class="text-md font-semibold text-gray-800 dark:text-white mb-4 border-t border-gray-200 dark:border-gray-700 pt-4">
                        Change Password (leave blank to keep current password)
                    </h4>

                    <div class="mb-6">
                        <label for="current_password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Current Password
                        </label>
                        <div class="relative">
                            <input type="password" id="current_password" name="current_password" 
                                class="form-input w-full pr-10">
                            <button type="button" class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-500 dark:text-gray-400 password-toggle" tabindex="-1">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div>
                            <label for="new_password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                New Password
                            </label>
                            <div class="relative">
                                <input type="password" id="new_password" name="new_password" 
                                    class="form-input w-full pr-10">
                                <button type="button" class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-500 dark:text-gray-400 password-toggle" tabindex="-1">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                Minimum 8 characters
                            </p>
                        </div>
                        <div>
                            <label for="confirm_password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Confirm New Password
                            </label>
                            <div class="relative">
                                <input type="password" id="confirm_password" name="confirm_password" 
                                    class="form-input w-full pr-10">
                                <button type="button" class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-500 dark:text-gray-400 password-toggle" tabindex="-1">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="flex items-center justify-end space-x-3">
                        <button type="reset" class="btn btn-outline">
                            <i class="fas fa-undo mr-2"></i> Reset
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save mr-2"></i> Update Profile
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</main>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Password toggle visibility
        const toggleButtons = document.querySelectorAll('.password-toggle');
        toggleButtons.forEach(button => {
            button.addEventListener('click', function() {
                const input = this.parentElement.querySelector('input');
                const icon = this.querySelector('i');
                
                if (input.type === 'password') {
                    input.type = 'text';
                    icon.classList.remove('fa-eye');
                    icon.classList.add('fa-eye-slash');
                } else {
                    input.type = 'password';
                    icon.classList.remove('fa-eye-slash');
                    icon.classList.add('fa-eye');
                }
            });
        });
        
        // Form validation
        const profileForm = document.getElementById('profileForm');
        profileForm.addEventListener('submit', function(e) {
            const newPassword = document.getElementById('new_password').value;
            const confirmPassword = document.getElementById('confirm_password').value;
            const currentPassword = document.getElementById('current_password').value;
            
            // If new password is provided, current password is required
            if (newPassword && !currentPassword) {
                e.preventDefault();
                alert('Please enter your current password to set a new password.');
                return false;
            }
            
            // If new password is provided, confirm password must match
            if (newPassword && newPassword !== confirmPassword) {
                e.preventDefault();
                alert('New password and confirm password do not match.');
                return false;
            }
            
            // If new password is provided, it must be at least 8 characters
            if (newPassword && newPassword.length < 8) {
                e.preventDefault();
                alert('New password must be at least 8 characters long.');
                return false;
            }
            
            return true;
        });
    });
</script>

<?php require_once '../includes/footer.php'; ?> 