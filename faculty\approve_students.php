<?php
require_once '../includes/db.php';
require_once '../includes/header.php';
require_once '../notify/send_email.php';

if (!isset($_SESSION['faculty_id'])) {
    header("Location: ../auth/faculty_login.php");
    exit;
}

$college_id = $_SESSION['college_id'];

if (isset($_GET['approve'])) {
    $id = intval($_GET['approve']);

    $stmt = $conn->prepare("UPDATE students SET verified = 1 WHERE id = ? AND college_id = ?");
    $stmt->execute([$id, $college_id]);

    $getStudent = $conn->prepare("SELECT full_name, email FROM students WHERE id = ?");
    $getStudent->execute([$id]);
    $student = $getStudent->fetch(PDO::FETCH_ASSOC);

    if ($student) {
        $subject = "Your Student Account Has Been Approved";
        $body = "
            <p>Dear {$student['full_name']},</p>
            <p>Your student account has been approved. You can now log in and connect with alumni mentors.</p>
            <p><a href='https://darkviolet-vulture-501696.hostingersite.com/auth/student_login.php'>Login Here</a></p>
            <p>Best regards,<br>Connect My Students</p>
        ";
        sendEmail($student['email'], $student['full_name'], $subject, $body);
    }

    $_SESSION['flash_message'] = "Student approved successfully.";
    header("Location: approve_students.php");
    exit;
}


if (isset($_GET['reject'])) {
    $id = intval($_GET['reject']);
    $stmt = $conn->prepare("DELETE FROM students WHERE id = ? AND college_id = ?");
    $stmt->execute([$id, $college_id]);
    $_SESSION['flash_message'] = "Student rejected and removed.";
    header("Location: approve_students.php");
    exit;
}

$stmt = $conn->prepare("SELECT * FROM students WHERE verified = 0 AND college_id = ? ORDER BY created_at DESC");
$stmt->execute([$college_id]);
$students = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<h2 class="mb-4">Approve Student Registrations</h2>

<?php if (isset($_SESSION['flash_message'])): ?>
    <div class="alert alert-info"><?= $_SESSION['flash_message']; unset($_SESSION['flash_message']); ?></div>
<?php endif; ?>

<?php if (count($students) > 0): ?>
    <div class="card">
        <div class="card-header bg-dark text-white">Pending Students</div>
        <div class="card-body">
            <table class="table table-bordered table-hover table-striped">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Full Name</th>
                        <th>Email</th>
                        <th>Roll No.</th>
                        <th>Department</th>
                        <th>Year</th>
                        <th>Division</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($students as $student): ?>
                        <tr>
                            <td><?= $student['id'] ?></td>
                            <td><?= htmlspecialchars($student['full_name']) ?></td>
                            <td><?= htmlspecialchars($student['email']) ?></td>
                            <td><?= htmlspecialchars($student['roll_number']) ?></td>
                            <td><?= htmlspecialchars($student['department']) ?></td>
                            <td><?= $student['year'] ?></td>
                            <td><?= htmlspecialchars($student['division']) ?></td>
                            <td>
                                <a href="?approve=<?= $student['id'] ?>" class="btn btn-sm btn-success">Approve</a>
                                <a href="?reject=<?= $student['id'] ?>" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to reject this student?');">Reject</a>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
<?php else: ?>
    <div class="alert alert-secondary">No pending student registrations.</div>
<?php endif; ?>

<?php require_once '../includes/footer.php'; ?>
