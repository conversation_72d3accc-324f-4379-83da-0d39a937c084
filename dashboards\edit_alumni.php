<?php
// Include session configuration
require_once '../includes/session_config.php';
require_once '../includes/db.php';
require_once '../includes/db_schema_check.php';

if (!isset($_SESSION['collegeadmin_id'])) {
    header("Location: ../auth/collegeadmin_login.php");
    exit;
}

$college_id = $_SESSION['college_id'];
$admin_name = $_SESSION['collegeadmin_name'] ?? 'Admin';

// Set role explicitly for the sidebar
$role = 'collegeadmin';
$user_id = $_SESSION['collegeadmin_id'];
$user_name = $_SESSION['collegeadmin_name'];
$current_page = basename($_SERVER['PHP_SELF']);

// Get alumni ID from URL
$id = $_GET['id'] ?? null;

if (!$id) {
    $_SESSION['flash_message'] = "Invalid alumni ID.";
    header("Location: list_alumni.php");
    exit;
}

// Get college details
$collegeStmt = $conn->prepare("SELECT name FROM colleges WHERE id = ?");
$collegeStmt->execute([$college_id]);
$college = $collegeStmt->fetch(PDO::FETCH_ASSOC);

// Get alumni details
$stmt = $conn->prepare("SELECT * FROM alumni WHERE id = ? AND college_id = ?");
$stmt->execute([$id, $college_id]);
$alumni = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$alumni) {
    $_SESSION['flash_message'] = "Alumni not found.";
    header("Location: list_alumni.php");
    exit;
}

$error = '';
$success = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $full_name = trim($_POST['full_name'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $phone = trim($_POST['phone'] ?? '');
    $graduation_year = trim($_POST['graduation_year'] ?? '');
    $department = trim($_POST['department'] ?? '');
    $company = trim($_POST['company'] ?? '');
    $position = trim($_POST['position'] ?? '');
    $industry = trim($_POST['industry'] ?? '');
    $linkedin = trim($_POST['linkedin'] ?? '');
    $website = trim($_POST['website'] ?? '');
    $bio = trim($_POST['bio'] ?? '');
    $experience_years = trim($_POST['experience_years'] ?? '');

    // Validation
    if (empty($full_name)) {
        $error = "Full name is required.";
    } elseif (empty($email)) {
        $error = "Email is required.";
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = "Please enter a valid email address.";
    } else {
        // Check if email is already taken by another alumni
        $emailCheckStmt = $conn->prepare("SELECT id FROM alumni WHERE email = ? AND college_id = ? AND id != ?");
        $emailCheckStmt->execute([$email, $college_id, $id]);
        if ($emailCheckStmt->fetch()) {
            $error = "This email is already registered with another alumni account.";
        } else {
            try {
                // Update alumni
                $updateStmt = $conn->prepare("
                    UPDATE alumni SET 
                        full_name = ?, email = ?, phone = ?, graduation_year = ?, department = ?, 
                        company = ?, position = ?, industry = ?, linkedin = ?, website = ?, 
                        bio = ?, experience_years = ?
                    WHERE id = ? AND college_id = ?
                ");
                $updateStmt->execute([
                    $full_name, $email, $phone, $graduation_year, $department,
                    $company, $position, $industry, $linkedin, $website,
                    $bio, $experience_years, $id, $college_id
                ]);

                $success = "Alumni information updated successfully!";
                
                // Refresh alumni data
                $stmt->execute([$id, $college_id]);
                $alumni = $stmt->fetch(PDO::FETCH_ASSOC);
                
            } catch (PDOException $e) {
                $error = "Database error: " . $e->getMessage();
            }
        }
    }
}

// Include the header
require_once '../includes/header.php';
?>

    <!-- Main Content -->
    <main class="flex-grow py-6 px-4">
        <div class="max-w-7xl mx-auto">
            <!-- Page Header -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8">
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="bg-gradient-to-br from-purple-500 to-purple-600 p-3 rounded-xl shadow-lg">
                            <i class="fas fa-user-edit text-white text-xl"></i>
                        </div>
                        <div>
                            <h1 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                                Edit Alumni Profile
                            </h1>
                            <nav class="text-sm text-gray-600 dark:text-gray-400 mt-1" aria-label="Breadcrumb">
                                <ol class="flex items-center space-x-2">
                                    <li>
                                        <a href="collegeadmin_dashboard.php" class="hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
                                            Dashboard
                                        </a>
                                    </li>
                                    <li class="flex items-center">
                                        <i class="fas fa-chevron-right text-gray-400 text-xs mx-2"></i>
                                        <a href="list_alumni.php" class="hover:text-blue-600 dark:hover:text-blue-400 transition-colors">Alumni Management</a>
                                    </li>
                                    <li class="flex items-center">
                                        <i class="fas fa-chevron-right text-gray-400 text-xs mx-2"></i>
                                        <a href="view_alumni.php?id=<?= $id ?>" class="hover:text-blue-600 dark:hover:text-blue-400 transition-colors"><?= htmlspecialchars($alumni['full_name']) ?></a>
                                    </li>
                                    <li class="flex items-center">
                                        <i class="fas fa-chevron-right text-gray-400 text-xs mx-2"></i>
                                        <span class="text-gray-900 dark:text-white font-medium">Edit Profile</span>
                                    </li>
                                </ol>
                            </nav>
                            <p class="text-gray-600 dark:text-gray-400 mt-2">
                                Update alumni information for <span class="font-medium text-purple-600 dark:text-purple-400"><?= htmlspecialchars($alumni['full_name']) ?></span>
                            </p>
                        </div>
                    </div>
                    <div class="mt-4 lg:mt-0 flex flex-wrap gap-3">
                        <a href="view_alumni.php?id=<?= $id ?>" class="inline-flex items-center px-4 py-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-medium rounded-lg transition-colors">
                            <i class="fas fa-arrow-left mr-2"></i> Back to Profile
                        </a>
                        <a href="list_alumni.php" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors">
                            <i class="fas fa-list mr-2"></i> All Alumni
                        </a>
                    </div>
                </div>
            </div>

            <!-- Alert Messages -->
            <?php if ($error): ?>
                <div class="bg-red-50 dark:bg-red-900/50 border border-red-200 dark:border-red-800 rounded-xl p-4 mb-6">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <i class="fas fa-exclamation-circle text-red-600 dark:text-red-400"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-red-800 dark:text-red-200">Error</h3>
                            <p class="text-sm text-red-700 dark:text-red-300 mt-1"><?= htmlspecialchars($error) ?></p>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <?php if ($success): ?>
                <div class="bg-green-50 dark:bg-green-900/50 border border-green-200 dark:border-green-800 rounded-xl p-4 mb-6">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <i class="fas fa-check-circle text-green-600 dark:text-green-400"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-green-800 dark:text-green-200">Success</h3>
                            <p class="text-sm text-green-700 dark:text-green-300 mt-1"><?= htmlspecialchars($success) ?></p>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Main Content Grid -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Left Column: Edit Form -->
                <div class="lg:col-span-2">
                    <form method="post" class="space-y-6">
                        <!-- Personal Information -->
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                                <i class="fas fa-user text-purple-600 mr-2"></i>
                                Personal Information
                            </h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label for="full_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Full Name *</label>
                                    <input type="text" id="full_name" name="full_name" value="<?= htmlspecialchars($alumni['full_name']) ?>" required
                                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:text-white">
                                </div>
                                <div>
                                    <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Email *</label>
                                    <input type="email" id="email" name="email" value="<?= htmlspecialchars($alumni['email']) ?>" required
                                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:text-white">
                                </div>
                                <div>
                                    <label for="phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Phone</label>
                                    <input type="tel" id="phone" name="phone" value="<?= htmlspecialchars($alumni['phone'] ?? '') ?>"
                                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:text-white">
                                </div>
                                <div>
                                    <label for="graduation_year" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Graduation Year</label>
                                    <input type="number" id="graduation_year" name="graduation_year" value="<?= htmlspecialchars($alumni['graduation_year'] ?? '') ?>" min="1950" max="2030"
                                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:text-white">
                                </div>
                                <div class="md:col-span-2">
                                    <label for="department" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Department</label>
                                    <input type="text" id="department" name="department" value="<?= htmlspecialchars($alumni['department'] ?? '') ?>"
                                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:text-white">
                                </div>
                            </div>
                        </div>

                        <!-- Professional Information -->
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                                <i class="fas fa-briefcase text-purple-600 mr-2"></i>
                                Professional Information
                            </h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label for="company" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Company</label>
                                    <input type="text" id="company" name="company" value="<?= htmlspecialchars($alumni['company'] ?? '') ?>"
                                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:text-white">
                                </div>
                                <div>
                                    <label for="position" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Position</label>
                                    <input type="text" id="position" name="position" value="<?= htmlspecialchars($alumni['position'] ?? '') ?>"
                                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:text-white">
                                </div>
                                <div>
                                    <label for="industry" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Industry</label>
                                    <input type="text" id="industry" name="industry" value="<?= htmlspecialchars($alumni['industry'] ?? '') ?>"
                                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:text-white">
                                </div>
                                <div>
                                    <label for="experience_years" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Years of Experience</label>
                                    <input type="number" id="experience_years" name="experience_years" value="<?= htmlspecialchars($alumni['experience_years'] ?? '') ?>" min="0" max="50"
                                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:text-white">
                                </div>
                            </div>
                        </div>

                        <!-- Social Links -->
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                                <i class="fas fa-link text-purple-600 mr-2"></i>
                                Social Links
                            </h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label for="linkedin" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">LinkedIn Profile</label>
                                    <input type="url" id="linkedin" name="linkedin" value="<?= htmlspecialchars($alumni['linkedin'] ?? '') ?>"
                                           placeholder="https://linkedin.com/in/username"
                                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:text-white">
                                </div>
                                <div>
                                    <label for="website" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Website</label>
                                    <input type="url" id="website" name="website" value="<?= htmlspecialchars($alumni['website'] ?? '') ?>"
                                           placeholder="https://example.com"
                                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:text-white">
                                </div>
                            </div>
                        </div>

                        <!-- Bio -->
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                                <i class="fas fa-user-circle text-purple-600 mr-2"></i>
                                About
                            </h3>
                            <div>
                                <label for="bio" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Bio</label>
                                <textarea id="bio" name="bio" rows="4"
                                          placeholder="Tell us about yourself, your achievements, and your professional journey..."
                                          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:text-white"><?= htmlspecialchars($alumni['bio'] ?? '') ?></textarea>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="flex flex-col sm:flex-row gap-3 pt-4">
                            <button type="submit" class="flex-1 bg-purple-600 hover:bg-purple-700 text-white font-medium py-3 px-6 rounded-lg transition-colors">
                                <i class="fas fa-save mr-2"></i> Update Alumni Profile
                            </button>
                            <a href="view_alumni.php?id=<?= $id ?>" class="flex-1 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-medium py-3 px-6 rounded-lg transition-colors text-center">
                                <i class="fas fa-times mr-2"></i> Cancel
                            </a>
                        </div>
                    </form>
                </div>

                <!-- Right Sidebar -->
                <div class="space-y-6">
                    <!-- Current Status -->
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                        <div class="p-4 border-b border-gray-200 dark:border-gray-700">
                            <h3 class="font-semibold text-gray-900 dark:text-white flex items-center">
                                <i class="fas fa-info-circle text-purple-600 mr-2"></i>
                                Current Status
                            </h3>
                        </div>
                        <div class="p-4 space-y-3">
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600 dark:text-gray-400">Verification Status</span>
                                <?php if ($alumni['verified']): ?>
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                        <i class="fas fa-check mr-1"></i> Verified
                                    </span>
                                <?php else: ?>
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                                        <i class="fas fa-clock mr-1"></i> Pending
                                    </span>
                                <?php endif; ?>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600 dark:text-gray-400">Member Since</span>
                                <span class="text-sm font-medium text-gray-900 dark:text-white"><?= date('M Y', strtotime($alumni['created_at'])) ?></span>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                        <div class="p-4 border-b border-gray-200 dark:border-gray-700">
                            <h3 class="font-semibold text-gray-900 dark:text-white flex items-center">
                                <i class="fas fa-bolt text-purple-600 mr-2"></i>
                                Quick Actions
                            </h3>
                        </div>
                        <div class="p-4 space-y-2">
                            <?php if (!$alumni['verified']): ?>
                            <a href="verify_alumni.php?id=<?= $id ?>" class="w-full inline-flex items-center justify-center px-3 py-2 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-lg transition-colors">
                                <i class="fas fa-check-circle mr-2"></i> Verify Alumni
                            </a>
                            <?php endif; ?>
                            <a href="mailto:<?= htmlspecialchars($alumni['email']) ?>" class="w-full inline-flex items-center justify-center px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors">
                                <i class="fas fa-envelope mr-2"></i> Send Email
                            </a>
                            <a href="view_alumni.php?id=<?= $id ?>" class="w-full inline-flex items-center justify-center px-3 py-2 bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium rounded-lg transition-colors">
                                <i class="fas fa-eye mr-2"></i> View Profile
                            </a>
                            <a href="list_alumni.php" class="w-full inline-flex items-center justify-center px-3 py-2 bg-indigo-600 hover:bg-indigo-700 text-white text-sm font-medium rounded-lg transition-colors">
                                <i class="fas fa-list mr-2"></i> All Alumni
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

<script>
function toggleDropdown(id) {
    const dropdown = document.getElementById(id);
    dropdown.classList.toggle('hidden');
}

// Close dropdown when clicking outside
document.addEventListener('click', function(event) {
    const dropdowns = document.querySelectorAll('[id$="Dropdown"]');
    dropdowns.forEach(dropdown => {
        if (!dropdown.contains(event.target) && !event.target.closest('button[onclick*="' + dropdown.id + '"]')) {
            dropdown.classList.add('hidden');
        }
    });
});
</script>

<?php require_once '../includes/footer.php'; ?>
