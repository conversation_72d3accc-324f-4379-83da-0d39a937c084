<?php

ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once '../includes/db.php';
require_once '../includes/header.php';
require_once '../includes/gamify.php';

if (!isset($_SESSION['student_id'])) {
    header("Location: ../auth/student_login.php");
    exit;
}

$student_id = $_SESSION['student_id'];
$success = $error = "";

// Fetch current profile
$stmt = $conn->prepare("SELECT s.*, p.points FROM students s LEFT JOIN user_points p ON s.id = p.user_id AND p.role = 'student' WHERE s.id = ?");
$stmt->execute([$student_id]);
$student = $stmt->fetch(PDO::FETCH_ASSOC);

// Handle profile update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_profile'])) {
    $fields = [
        'full_name' => trim($_POST['full_name']),
        'bio' => trim($_POST['bio']),
        'goals' => trim($_POST['goals']),
        'skills' => trim($_POST['skills']),
        'linkedin' => trim($_POST['linkedin']),
        'github' => trim($_POST['github']),
        'twitter' => trim($_POST['twitter']),
        'website' => trim($_POST['website']),
    ];

    // Handle profile photo
    if (!empty($_FILES['profile_photo']['name'])) {
        $ext = pathinfo($_FILES['profile_photo']['name'], PATHINFO_EXTENSION);
        $photoName = "student_" . $student_id . "." . $ext;
        move_uploaded_file($_FILES['profile_photo']['tmp_name'], "../uploads/$photoName");
        $fields['profile_photo'] = $photoName;
    }

    // Safe for PHP 7.2
    $updates = [];
    foreach (array_keys($fields) as $key) {
        $updates[] = "$key = ?";
    }
    $updateQuery = "UPDATE students SET " . implode(", ", $updates) . " WHERE id = ?";
    $stmt = $conn->prepare($updateQuery);
    $stmt->execute([...array_values($fields), $student_id]);
    
    // Award XP for profile completion
    awardXPForProfileCompletion($conn, $student_id);

    $success = "Profile updated successfully!";
    header("Refresh:0");
}

// Handle password change
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_password'])) {
    $current = $_POST['current_password'];
    $new = $_POST['new_password'];
    if (!password_verify($current, $student['password'])) {
        $error = "Current password is incorrect.";
    } else {
        $hashed = password_hash($new, PASSWORD_DEFAULT);
        $stmt = $conn->prepare("UPDATE students SET password = ? WHERE id = ?");
        $stmt->execute([$hashed, $student_id]);
        $success = "Password updated successfully!";
    }
}

// Profile photo handling
$photo = !empty($student['profile_photo']) && file_exists("../uploads/{$student['profile_photo']}")
    ? $student['profile_photo']
    : 'default.png';
?>

<style>
.editable-section .form-control[readonly] {
    background: #f8f9fa;
    border: none;
}
.editable-section .edit-icon {
    float: right;
    cursor: pointer;
    color: #555;
}
</style>

<h2 class="mb-4">My Profile</h2>
<?php if ($error): ?><div class="alert alert-danger"><?= $error ?></div><?php endif; ?>
<?php if ($success): ?><div class="alert alert-success"><?= $success ?></div><?php endif; ?>

<form method="post" enctype="multipart/form-data" class="card p-4 shadow mb-4" id="profileForm">
    <input type="hidden" name="update_profile" value="1">

    <!-- 1. Profile Header -->
    <div class="row mb-4 align-items-center">
        <div class="col-md-3 text-center">
            <img src="../uploads/<?= $photo ?>" class="rounded-circle border" width="120" height="120">
            <input type="file" name="profile_photo" class="form-control mt-2">
        </div>
        <div class="col-md-9">
            <div class="mb-3">
                <label class="form-label">Full Name</label>
                <input type="text" name="full_name" class="form-control" value="<?= htmlspecialchars($student['full_name'] ?? '') ?>" required>
            </div>
            <div class="editable-section">
                <label class="form-label">Bio</label>
                <textarea name="bio" class="form-control" rows="2"><?= htmlspecialchars($student['bio'] ?? '') ?></textarea>
            </div>
        </div>
    </div>

    <!-- 2. Identity & Academics -->
    <div class="row mb-4">
        <div class="col-md-6">
            <label class="form-label">Email</label>
            <input class="form-control" value="<?= htmlspecialchars($student['email'] ?? '') ?>" readonly>
        </div>
        <div class="col-md-6">
            <label class="form-label">Username</label>
            <input class="form-control" value="<?= htmlspecialchars($student['username'] ?? '') ?>" readonly>
        </div>
        <div class="col-md-3">
            <label class="form-label">Roll Number</label>
            <input class="form-control" value="<?= htmlspecialchars($student['roll_number'] ?? '') ?>" readonly>
        </div>
        <div class="col-md-3">
            <label class="form-label">Department</label>
            <input class="form-control" value="<?= htmlspecialchars($student['department'] ?? '') ?>" readonly>
        </div>
        <div class="col-md-3">
            <label class="form-label">Year</label>
            <input class="form-control" value="<?= htmlspecialchars($student['year'] ?? '') ?>" readonly>
        </div>
        <div class="col-md-3">
            <label class="form-label">Division</label>
            <input class="form-control" value="<?= htmlspecialchars($student['division'] ?? '') ?>" readonly>
        </div>
    </div>

    <!-- 3. Learning & Skills -->
    <div class="row mb-4">
        <div class="col-md-6">
            <label class="form-label">Current Learning Goals</label>
            <textarea name="goals" class="form-control"><?= htmlspecialchars($student['goals'] ?? '') ?></textarea>
        </div>
        <div class="col-md-6">
            <label class="form-label">Skills</label>
            <textarea name="skills" class="form-control"><?= htmlspecialchars($student['skills'] ?? '') ?></textarea>
        </div>
    </div>

    <!-- 4. Social Media -->
    <div class="row mb-4">
        <div class="col-md-3"><label>LinkedIn</label><input name="linkedin" value="<?= htmlspecialchars($student['linkedin'] ?? '') ?>" class="form-control"></div>
        <div class="col-md-3"><label>GitHub</label><input name="github" value="<?= htmlspecialchars($student['github'] ?? '') ?>" class="form-control"></div>
        <div class="col-md-3"><label>Twitter</label><input name="twitter" value="<?= htmlspecialchars($student['twitter'] ?? '') ?>" class="form-control"></div>
        <div class="col-md-3"><label>Website</label><input name="website" value="<?= htmlspecialchars($student['website'] ?? '') ?>" class="form-control"></div>
    </div>

    <button class="btn btn-primary w-100">Update Profile</button>
</form>

<!-- 5. Change Password -->
<form method="post" class="card p-4 shadow" id="passwordForm">
    <input type="hidden" name="update_password" value="1">
    <h5 class="mb-3">Change Password</h5>
    <div class="row">
        <div class="col-md-4"><input type="password" name="current_password" class="form-control" placeholder="Current Password" required></div>
        <div class="col-md-4"><input type="password" name="new_password" class="form-control" placeholder="New Password" required></div>
        <div class="col-md-4"><button class="btn btn-warning w-100">Update Password</button></div>
    </div>
</form>

<!-- 6. Gamification & Points -->
<div class="card p-3 mt-4 text-center shadow-sm">
    <h5>🎮 Gamification</h5>
    <p>You’ve earned <strong><?= $student['points'] ?? 0 ?> XP</strong> as a student!</p>
    <small class="text-muted">Badges coming soon...</small>
</div>

<?php require_once '../includes/footer.php'; ?>
