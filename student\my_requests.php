<?php
require_once '../includes/db.php';
require_once '../includes/header.php';

if (!isset($_SESSION['student_id'])) {
    header("Location: ../auth/student_login.php");
    exit;
}

$student_id = $_SESSION['student_id'];

$stmt = $conn->prepare("SELECT sr.alumni_id, sr.status, sr.message, sr.created_at, a.full_name, a.current_position, a.company, a.profile_photo
    FROM student_requests sr
    JOIN alumni a ON sr.alumni_id = a.id
    WHERE sr.student_id = ?
    ORDER BY sr.created_at DESC");
$stmt->execute([$student_id]);
$requests = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<h2 class="mb-4">📩 My Mentorship Requests</h2>

<?php if ($requests): ?>
    <?php foreach ($requests as $r): ?>
        <div class="card mb-3 shadow-sm">
            <div class="card-body d-flex align-items-center">
                <img src="../uploads/<?= file_exists("../uploads/{$r['profile_photo']}") ? $r['profile_photo'] : 'profile.gif' ?>" width="60" height="60" class="rounded-circle me-3">
                <div class="flex-grow-1">
                    <h5 class="mb-0"><?= htmlspecialchars($r['full_name']) ?></h5>
                    <small class="text-muted"><?= htmlspecialchars($r['current_position']) ?> @ <?= htmlspecialchars($r['company']) ?></small>
                    <p class="mb-1 mt-2"><?= nl2br(htmlspecialchars($r['message'])) ?></p>
                    <span class="badge bg-<?= 
                        $r['status'] === 'accepted' ? 'success' : 
                        ($r['status'] === 'rejected' ? 'danger' : 'secondary') ?>">
                        <?= ucfirst($r['status']) ?>
                    </span>
                </div>
                <?php if ($r['status'] === 'accepted'): ?>
                    <a href="../chat/chat.php?alumni_id=<?= $r['alumni_id'] ?>" class="btn btn-success btn-sm ms-3">Chat</a>
                <?php endif; ?>
            </div>
        </div>
    <?php endforeach; ?>
<?php else: ?>
    <div class="alert alert-info">You haven't sent any mentorship requests yet.</div>
<?php endif; ?>

<?php require_once '../includes/footer.php'; ?>
