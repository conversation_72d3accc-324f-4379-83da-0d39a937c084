<?php
// Include session configuration
require_once '../includes/session_config.php';
require_once '../includes/db.php';
require_once '../includes/db_schema_check.php';

if (!isset($_SESSION['collegeadmin_id'])) {
    header("Location: ../auth/collegeadmin_login.php");
    exit;
}

$college_id = $_SESSION['college_id'];
$admin_name = $_SESSION['collegeadmin_name'] ?? 'Admin';

// Set role explicitly for the sidebar
$role = 'collegeadmin';
$user_id = $_SESSION['collegeadmin_id'];
$user_name = $_SESSION['collegeadmin_name'];
$current_page = basename($_SERVER['PHP_SELF']);

// Get student ID from URL
$id = $_GET['id'] ?? null;

if (!$id) {
    $_SESSION['flash_message'] = "Invalid student ID.";
    header("Location: list_students.php");
    exit;
}

// Get college details
$collegeStmt = $conn->prepare("SELECT name FROM colleges WHERE id = ?");
$collegeStmt->execute([$college_id]);
$college = $collegeStmt->fetch(PDO::FETCH_ASSOC);

// Get student details
$stmt = $conn->prepare("
    SELECT id, college_id, full_name, email, password, roll_number, department, year, division,
           profile_image, verified, status, created_at, profile_photo, bio, skills, goals,
           linkedin, github, twitter, website, phone, address, city, state, country,
           pincode, date_of_birth, gender
    FROM students
    WHERE id = ? AND college_id = ?
");
$stmt->execute([$id, $college_id]);
$student = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$student) {
    $_SESSION['flash_message'] = "Student not found.";
    header("Location: list_students.php");
    exit;
}

// Check if already verified
if ($student['verified']) {
    $_SESSION['flash_message'] = "Student is already verified.";
    header("Location: view_student.php?id=" . $id);
    exit;
}

// Initialize variables
$error = '';
$success = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $notify_student = isset($_POST['notify_student']);
        $verification_notes = trim($_POST['verification_notes'] ?? '');

        // Update student verification status
        $updateStmt = $conn->prepare("UPDATE students SET verified = 1 WHERE id = ? AND college_id = ?");
        $updateStmt->execute([$id, $college_id]);

        // Send email notification if requested
        if ($notify_student) {
            // Email sending logic would go here
            // For now, we'll just set a success message
        }

        $_SESSION['flash_message'] = "Student account has been verified successfully!";
        $_SESSION['flash_type'] = 'success';
        header("Location: view_student.php?id=" . $id);
        exit;

    } catch (PDOException $e) {
        $error = "Error verifying student: " . $e->getMessage();
    }
}

// Set photo path
$photo = $student['profile_photo'] ?? 'default.png';

// Include the header
require_once '../includes/header.php';
?>

    <!-- Main Content -->
    <main class="flex-grow py-6 px-4">
        <div class="max-w-7xl mx-auto">
            <!-- Page Header -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8">
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="bg-gradient-to-br from-green-500 to-green-600 p-3 rounded-xl shadow-lg">
                            <i class="fas fa-user-check text-white text-xl"></i>
                        </div>
                        <div>
                            <h1 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                                Verify Student Account
                            </h1>
                            <nav class="text-sm text-gray-600 dark:text-gray-400 mt-1" aria-label="Breadcrumb">
                                <ol class="flex items-center space-x-2">
                                    <li>
                                        <a href="collegeadmin_dashboard.php" class="hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
                                            Dashboard
                                        </a>
                                    </li>
                                    <li class="flex items-center">
                                        <i class="fas fa-chevron-right text-gray-400 text-xs mx-2"></i>
                                        <a href="list_students.php" class="hover:text-blue-600 dark:hover:text-blue-400 transition-colors">Student Management</a>
                                    </li>
                                    <li class="flex items-center">
                                        <i class="fas fa-chevron-right text-gray-400 text-xs mx-2"></i>
                                        <a href="view_student.php?id=<?= $id ?>" class="hover:text-blue-600 dark:hover:text-blue-400 transition-colors"><?= htmlspecialchars($student['full_name']) ?></a>
                                    </li>
                                    <li class="flex items-center">
                                        <i class="fas fa-chevron-right text-gray-400 text-xs mx-2"></i>
                                        <span class="text-gray-900 dark:text-white font-medium">Verify Student</span>
                                    </li>
                                </ol>
                            </nav>
                        </div>
                    </div>
                    <div class="mt-4 lg:mt-0 flex flex-wrap gap-3">
                        <a href="view_student.php?id=<?= $id ?>" class="inline-flex items-center px-4 py-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-medium rounded-lg transition-colors">
                            <i class="fas fa-arrow-left mr-2"></i> Back to Profile
                        </a>
                        <a href="list_students.php" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors">
                            <i class="fas fa-list mr-2"></i> All Students
                        </a>
                    </div>
                </div>
            </div>

            <!-- Alert Messages -->
            <?php if ($error): ?>
                <div class="bg-red-50 dark:bg-red-900/50 border border-red-200 dark:border-red-800 rounded-xl p-4 mb-6">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <i class="fas fa-exclamation-circle text-red-600 dark:text-red-400"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-red-800 dark:text-red-200">Error</h3>
                            <p class="text-sm text-red-700 dark:text-red-300 mt-1"><?= htmlspecialchars($error) ?></p>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Main Content Grid -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Left Column: Student Profile & Verification Form -->
                <div class="lg:col-span-2 space-y-8">
                    <!-- Student Profile Card -->
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
                        <!-- Cover with Status -->
                        <div class="h-32 bg-gradient-to-br from-blue-600 via-blue-700 to-purple-600 relative">
                            <div class="absolute top-4 right-4">
                                <div class="bg-yellow-500 text-white px-3 py-1 rounded-full text-sm font-medium flex items-center shadow-lg">
                                    <i class="fas fa-clock mr-1"></i> Pending Verification
                                </div>
                            </div>
                        </div>

                        <!-- Profile Content -->
                        <div class="px-6 pb-6 relative">
                            <div class="flex flex-col sm:flex-row sm:items-center -mt-12 relative z-10">
                                <!-- Profile Picture -->
                                <div class="flex-shrink-0 mb-4 sm:mb-0 relative z-20">
                                    <div class="w-24 h-24 rounded-xl border-4 border-white dark:border-gray-800 bg-gray-100 dark:bg-gray-700 flex items-center justify-center overflow-hidden shadow-lg">
                                        <?php if ($photo === 'default.png' || empty($photo)): ?>
                                            <i class="fas fa-user-graduate text-3xl text-gray-400 dark:text-gray-300"></i>
                                        <?php else: ?>
                                            <img src="../uploads/<?= htmlspecialchars($photo) ?>" alt="<?= htmlspecialchars($student['full_name']) ?>" class="w-full h-full object-cover">
                                        <?php endif; ?>
                                    </div>
                                </div>

                                <!-- Student Info -->
                                <div class="sm:ml-6 flex-grow relative z-20 mt-4 sm:mt-0">
                                    <div class="bg-white dark:bg-gray-800 rounded-xl p-4 border-2 border-gray-200 dark:border-gray-600 shadow-sm backdrop-blur-sm bg-opacity-95 dark:bg-opacity-95">
                                        <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-2"><?= htmlspecialchars($student['full_name']) ?></h2>
                                        <p class="text-gray-600 dark:text-gray-400 mb-3"><?= htmlspecialchars($student['email']) ?></p>
                                        <div class="flex flex-wrap gap-2">
                                            <span class="inline-flex items-center px-3 py-1 rounded-lg text-sm font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200">
                                                <i class="fas fa-graduation-cap mr-1"></i>
                                                <?= htmlspecialchars($student['department']) ?>
                                            </span>
                                            <span class="inline-flex items-center px-3 py-1 rounded-lg text-sm font-medium bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200">
                                                <i class="fas fa-calendar-alt mr-1"></i>
                                                Year <?= $student['year'] ?>
                                            </span>
                                            <?php if ($student['division']): ?>
                                            <span class="inline-flex items-center px-3 py-1 rounded-lg text-sm font-medium bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200">
                                                <i class="fas fa-users mr-1"></i>
                                                Division <?= htmlspecialchars($student['division']) ?>
                                            </span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Additional Details -->
                            <div class="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
                                <?php if ($student['roll_number']): ?>
                                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                                    <div class="text-sm text-gray-500 dark:text-gray-400">Roll Number</div>
                                    <div class="font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($student['roll_number']) ?></div>
                                </div>
                                <?php endif; ?>
                                <?php if ($student['phone']): ?>
                                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                                    <div class="text-sm text-gray-500 dark:text-gray-400">Phone</div>
                                    <div class="font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($student['phone']) ?></div>
                                </div>
                                <?php endif; ?>
                                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                                    <div class="text-sm text-gray-500 dark:text-gray-400">Joined</div>
                                    <div class="font-medium text-gray-900 dark:text-white"><?= date('M d, Y', strtotime($student['created_at'])) ?></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Verification Form -->
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                        <div class="p-6">
                            <div class="flex items-center mb-6">
                                <div class="bg-gradient-to-br from-green-500 to-green-600 p-2 rounded-lg mr-3">
                                    <i class="fas fa-check-circle text-white"></i>
                                </div>
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Verify Student Account</h3>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">Grant full platform access to this student</p>
                                </div>
                            </div>

                            <form method="post" class="space-y-6">
                                <!-- Email Notification -->
                                <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800">
                                    <div class="flex items-start">
                                        <input
                                            type="checkbox"
                                            id="notify_student"
                                            name="notify_student"
                                            checked
                                            class="mt-1 h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                                        >
                                        <div class="ml-3">
                                            <label for="notify_student" class="font-medium text-blue-900 dark:text-blue-100 flex items-center">
                                                <i class="fas fa-envelope mr-2"></i>
                                                Send Email Notification
                                            </label>
                                            <p class="text-sm text-blue-700 dark:text-blue-300 mt-1">
                                                Student will receive a welcome email with login instructions and platform access details.
                                            </p>
                                        </div>
                                    </div>
                                </div>

                                <!-- Verification Notes -->
                                <div>
                                    <label for="verification_notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        <i class="fas fa-sticky-note mr-2"></i>
                                        Verification Notes (Optional)
                                    </label>
                                    <textarea
                                        id="verification_notes"
                                        name="verification_notes"
                                        rows="4"
                                        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 dark:bg-gray-700 dark:text-white placeholder-gray-400"
                                        placeholder="Add any welcome message or instructions for the student. This will be included in the verification email..."
                                    ></textarea>
                                    <p class="text-sm text-gray-500 dark:text-gray-400 mt-2">
                                        <i class="fas fa-info-circle mr-1"></i>
                                        These notes will be included in the verification email sent to the student.
                                    </p>
                                </div>

                                <!-- Action Buttons -->
                                <div class="flex flex-col sm:flex-row gap-3 pt-4 border-t border-gray-200 dark:border-gray-700">
                                    <button type="submit" class="flex-1 inline-flex items-center justify-center px-6 py-3 bg-green-600 hover:bg-green-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200">
                                        <i class="fas fa-check-circle mr-2"></i>
                                        Verify Student Account
                                    </button>
                                    <a href="view_student.php?id=<?= $id ?>" class="flex-1 inline-flex items-center justify-center px-6 py-3 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-medium rounded-lg transition-colors">
                                        <i class="fas fa-times mr-2"></i>
                                        Cancel
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Right Sidebar -->
                <div class="space-y-6">
                    <!-- Verification Benefits -->
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                        <div class="p-4 border-b border-gray-200 dark:border-gray-700">
                            <h3 class="font-semibold text-gray-900 dark:text-white flex items-center">
                                <i class="fas fa-unlock-alt text-blue-600 mr-2"></i>
                                Verification Benefits
                            </h3>
                        </div>
                        <div class="p-4">
                            <div class="space-y-3 text-sm">
                                <div class="flex items-center">
                                    <i class="fas fa-check text-green-600 mr-3 w-4"></i>
                                    <span class="text-gray-700 dark:text-gray-300">Full Platform Access</span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-handshake text-purple-600 mr-3 w-4"></i>
                                    <span class="text-gray-700 dark:text-gray-300">Alumni Mentorship</span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-calendar-alt text-orange-600 mr-3 w-4"></i>
                                    <span class="text-gray-700 dark:text-gray-300">Events & Campaigns</span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-book text-indigo-600 mr-3 w-4"></i>
                                    <span class="text-gray-700 dark:text-gray-300">Learning Resources</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Important Notice -->
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                        <div class="p-4 border-b border-gray-200 dark:border-gray-700">
                            <h3 class="font-semibold text-gray-900 dark:text-white flex items-center">
                                <i class="fas fa-exclamation-triangle text-yellow-600 mr-2"></i>
                                Important Notice
                            </h3>
                        </div>
                        <div class="p-4 space-y-3 text-sm">
                            <div class="flex items-start">
                                <i class="fas fa-ban text-red-600 mr-3 mt-0.5 w-4"></i>
                                <span class="text-gray-700 dark:text-gray-300">This action cannot be undone</span>
                            </div>
                            <div class="flex items-start">
                                <i class="fas fa-shield-alt text-blue-600 mr-3 mt-0.5 w-4"></i>
                                <span class="text-gray-700 dark:text-gray-300">Verify student identity first</span>
                            </div>
                            <div class="flex items-start">
                                <i class="fas fa-envelope text-green-600 mr-3 mt-0.5 w-4"></i>
                                <span class="text-gray-700 dark:text-gray-300">Email notification recommended</span>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                        <div class="p-4 border-b border-gray-200 dark:border-gray-700">
                            <h3 class="font-semibold text-gray-900 dark:text-white flex items-center">
                                <i class="fas fa-bolt text-purple-600 mr-2"></i>
                                Quick Actions
                            </h3>
                        </div>
                        <div class="p-4 space-y-2">
                            <a href="edit_student.php?id=<?= $id ?>" class="w-full inline-flex items-center justify-center px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors">
                                <i class="fas fa-edit mr-2"></i> Edit Student
                            </a>
                            <a href="list_students.php" class="w-full inline-flex items-center justify-center px-3 py-2 bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium rounded-lg transition-colors">
                                <i class="fas fa-list mr-2"></i> All Students
                            </a>
                            <a href="collegeadmin_dashboard.php" class="w-full inline-flex items-center justify-center px-3 py-2 bg-indigo-600 hover:bg-indigo-700 text-white text-sm font-medium rounded-lg transition-colors">
                                <i class="fas fa-home mr-2"></i> Dashboard
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

<script>
function toggleDropdown(id) {
    const dropdown = document.getElementById(id);
    dropdown.classList.toggle('hidden');
}

// Close dropdown when clicking outside
document.addEventListener('click', function(event) {
    const dropdowns = document.querySelectorAll('[id$="Dropdown"]');
    dropdowns.forEach(dropdown => {
        if (!dropdown.contains(event.target) && !event.target.closest('button[onclick*="' + dropdown.id + '"]')) {
            dropdown.classList.add('hidden');
        }
    });
});
</script>

<?php require_once '../includes/footer.php'; ?>