<?php
session_start();
require_once '../includes/db.php';
require_once '../includes/db_schema_check.php';
require_once '../notify/send_email.php';

// Ensure only super admin can access this page
if (!isset($_SESSION['superadmin_id'])) {
    header("Location: ../auth/superadmin_login.php");
    exit;
}

$superadmin_id = $_SESSION['superadmin_id'];
$superadmin_name = $_SESSION['superadmin_name'] ?? 'Super Admin';
$id = $_GET['id'] ?? null;
$email_sent = false;

if (!$id) {
    $_SESSION['flash_message'] = "Invalid faculty ID.";
    $_SESSION['flash_message_type'] = "error";
    header("Location: superadmin_list_faculty.php");
    exit;
}

// Get colleges for dropdown
$collegesStmt = $conn->prepare("SELECT id, name FROM colleges ORDER BY name ASC");
$collegesStmt->execute();
$colleges = $collegesStmt->fetchAll(PDO::FETCH_ASSOC);

// Get departments for suggestions - from all colleges for Super Admin
$deptStmt = $conn->prepare("SELECT DISTINCT department FROM faculties WHERE department IS NOT NULL AND department != '' ORDER BY department ASC");
$deptStmt->execute();
$departments = $deptStmt->fetchAll(PDO::FETCH_COLUMN);

// Get faculty details - no college_id restriction for super admin
$stmt = $conn->prepare("
    SELECT f.id, f.college_id, f.name, f.email, f.password, f.department, f.profile_photo,
           f.bio, f.created_at, c.name as college_name
    FROM faculties f
    LEFT JOIN colleges c ON f.college_id = c.id
    WHERE f.id = ?
");
$stmt->execute([$id]);
$faculty = $stmt->fetch(PDO::FETCH_ASSOC);

// Try to get additional columns if they exist
if ($faculty) {
    try {
        $extraStmt = $conn->prepare("SELECT position, phone, status, profile_image, updated_at FROM faculties WHERE id = ? LIMIT 1");
        $extraStmt->execute([$id]);
        $extraData = $extraStmt->fetch(PDO::FETCH_ASSOC);
        if ($extraData) {
            $faculty = array_merge($faculty, $extraData);
        }
    } catch (PDOException $e) {
        // Some columns don't exist, set defaults
        $faculty['position'] = $faculty['position'] ?? null;
        $faculty['phone'] = $faculty['phone'] ?? null;
        $faculty['status'] = $faculty['status'] ?? 'active';
        $faculty['profile_image'] = $faculty['profile_image'] ?? null;
        $faculty['updated_at'] = $faculty['updated_at'] ?? null;
    }
}

if (!$faculty) {
    $_SESSION['flash_message'] = "Faculty not found.";
    $_SESSION['flash_message_type'] = "error";
    header("Location: superadmin_list_faculty.php");
    exit;
}

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $full_name = trim($_POST['full_name']);
    $email = trim($_POST['email']);
    $department = trim($_POST['department']);
    $designation = trim($_POST['designation'] ?? '');
    $college_id = intval($_POST['college_id']);
    $password = $_POST['password'];
    $verified = isset($_POST['verified']) ? 1 : 0;
    $send_credentials = isset($_POST['send_credentials']) ? true : false;

    if (!$full_name || !$email || !$department || !$designation || !$college_id) {
        $error = "Name, email, department, designation, and college are required.";
    } else {
        // Check if email already exists for another faculty
        $check = $conn->prepare("SELECT COUNT(*) FROM faculty WHERE email = ? AND id != ?");
        $check->execute([$email, $id]);
        $exists = $check->fetchColumn();
        
        if ($exists) {
            $error = "A faculty with this email already exists.";
        } else {
            try {
                $password_changed = false;
                
                // Try to update with all fields, fall back to basic fields if some columns don't exist
                try {
                    if ($password) {
                        $hashed = password_hash($password, PASSWORD_DEFAULT);
                        $update = $conn->prepare("UPDATE faculties SET name = ?, email = ?, department = ?, college_id = ?, password = ?, updated_at = NOW() WHERE id = ?");
                        $update->execute([$full_name, $email, $department, $college_id, $hashed, $id]);
                        $password_changed = true;
                    } else {
                        $update = $conn->prepare("UPDATE faculties SET name = ?, email = ?, department = ?, college_id = ?, updated_at = NOW() WHERE id = ?");
                        $update->execute([$full_name, $email, $department, $college_id, $id]);
                    }
                } catch (PDOException $e) {
                    // If updated_at column doesn't exist, try without it
                    if ($password) {
                        $hashed = password_hash($password, PASSWORD_DEFAULT);
                        $update = $conn->prepare("UPDATE faculties SET name = ?, email = ?, department = ?, college_id = ?, password = ? WHERE id = ?");
                        $update->execute([$full_name, $email, $department, $college_id, $hashed, $id]);
                        $password_changed = true;
                    } else {
                        $update = $conn->prepare("UPDATE faculties SET name = ?, email = ?, department = ?, college_id = ? WHERE id = ?");
                        $update->execute([$full_name, $email, $department, $college_id, $id]);
                    }
                }
                
                // Get college name for the email
                $collegeStmt = $conn->prepare("SELECT name FROM colleges WHERE id = ?");
                $collegeStmt->execute([$college_id]);
                $college = $collegeStmt->fetch(PDO::FETCH_ASSOC);
                
                // Send credentials via email if requested
                if ($send_credentials && $password_changed) {
                    $subject = "Your Updated Account Details - " . htmlspecialchars($college['name']);
                    $message = "<p>Dear " . htmlspecialchars($full_name) . ",</p>";
                    $message .= "<p>Your account details have been updated by the system administrator.</p>";
                    $message .= "<p><strong>Email:</strong> " . htmlspecialchars($email) . "<br>";
                    $message .= "<strong>Password:</strong> " . htmlspecialchars($password) . "</p>";
                    $message .= "<p>You can login at: <a href='https://" . $_SERVER['HTTP_HOST'] . "/auth/faculty_login.php'>Faculty Login</a></p>";
                    $message .= "<p>Please keep your credentials secure.</p>";
                    $message .= "<p>Regards,<br>Administration Team<br>" . htmlspecialchars($college['name']) . "</p>";
                    
                    $email_sent = send_email($email, $subject, $message);
                }
                
                // Upload profile photo if provided
                if (isset($_FILES['profile_photo']) && $_FILES['profile_photo']['error'] == 0) {
                    $allowed = ['jpg', 'jpeg', 'png', 'gif'];
                    $filename = $_FILES['profile_photo']['name'];
                    $ext = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
                    
                    if (in_array($ext, $allowed)) {
                        $new_filename = 'faculty_' . $id . '_' . time() . '.' . $ext;
                        $upload_path = '../uploads/' . $new_filename;
                        
                        if (move_uploaded_file($_FILES['profile_photo']['tmp_name'], $upload_path)) {
                            // Update the profile photo in the database
                            try {
                                $photoUpdate = $conn->prepare("UPDATE faculties SET profile_photo = ? WHERE id = ?");
                                $photoUpdate->execute([$new_filename, $id]);
                            } catch (PDOException $e) {
                                // Column might not exist, try profile_image
                                try {
                                    $photoUpdate = $conn->prepare("UPDATE faculties SET profile_image = ? WHERE id = ?");
                                    $photoUpdate->execute([$new_filename, $id]);
                                } catch (PDOException $e2) {
                                    // Neither column exists, skip photo update
                                }
                            }
                        }
                    }
                }
                
                $success = "Faculty information updated successfully.";
                if ($email_sent) {
                    $success .= " Credentials have been sent to the faculty's email.";
                }
                
                // Refresh faculty data
                $stmt = $conn->prepare("
                    SELECT f.id, f.college_id, f.name, f.email, f.password, f.department, f.profile_photo,
                           f.bio, f.created_at, c.name as college_name
                    FROM faculties f
                    LEFT JOIN colleges c ON f.college_id = c.id
                    WHERE f.id = ?
                ");
                $stmt->execute([$id]);
                $faculty = $stmt->fetch(PDO::FETCH_ASSOC);

                // Try to get additional columns if they exist
                if ($faculty) {
                    try {
                        $extraStmt = $conn->prepare("SELECT position, phone, status, profile_image, updated_at FROM faculties WHERE id = ? LIMIT 1");
                        $extraStmt->execute([$id]);
                        $extraData = $extraStmt->fetch(PDO::FETCH_ASSOC);
                        if ($extraData) {
                            $faculty = array_merge($faculty, $extraData);
                        }
                    } catch (PDOException $e) {
                        // Some columns don't exist, set defaults
                        $faculty['position'] = $faculty['position'] ?? null;
                        $faculty['phone'] = $faculty['phone'] ?? null;
                        $faculty['status'] = $faculty['status'] ?? 'active';
                        $faculty['profile_image'] = $faculty['profile_image'] ?? null;
                        $faculty['updated_at'] = $faculty['updated_at'] ?? null;
                    }
                }
                
            } catch (PDOException $e) {
                $error = "Database error: " . $e->getMessage();
            }
        }
    }
}

// Include the header
require_once '../includes/header.php';
?>

<!-- Main Content -->
<div class="container mx-auto px-4 py-8 max-w-6xl">
    <!-- Page Header -->
    <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                <span class="bg-green-500/10 dark:bg-green-500/20 p-2 rounded-md mr-3">
                    <i class="fas fa-user-edit text-green-600 dark:text-green-400"></i>
                </span>
                Edit Faculty
            </h1>
            <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">Update faculty information and credentials</p>
        </div>
        <div class="mt-4 md:mt-0 flex flex-wrap gap-2">
            <a href="superadmin_view_faculty.php?id=<?= $faculty['id'] ?>" class="btn btn-outline flex items-center">
                <i class="fas fa-arrow-left mr-2"></i> Back to Profile
            </a>
            <a href="superadmin_list_faculty.php" class="btn btn-outline flex items-center">
                <i class="fas fa-list mr-2"></i> All Faculty
            </a>
        </div>
    </div>
    
    <?php if ($error): ?>
    <div class="mb-6 bg-red-50 dark:bg-red-900/30 border-l-4 border-red-500 text-red-700 dark:text-red-400 p-4 flex justify-between items-center" role="alert">
        <div>
            <i class="fas fa-exclamation-circle mr-2"></i>
            <?= $error ?>
        </div>
        <button type="button" class="text-red-700 dark:text-red-400 hover:text-red-900" onclick="this.parentElement.style.display='none';">
            <i class="fas fa-times"></i>
        </button>
    </div>
    <?php endif; ?>
    
    <?php if ($success): ?>
    <div class="mb-6 bg-green-50 dark:bg-green-900/30 border-l-4 border-green-500 text-green-700 dark:text-green-400 p-4 flex justify-between items-center" role="alert">
        <div>
            <i class="fas fa-check-circle mr-2"></i>
            <?= $success ?>
        </div>
        <button type="button" class="text-green-700 dark:text-green-400 hover:text-green-900" onclick="this.parentElement.style.display='none';">
            <i class="fas fa-times"></i>
        </button>
    </div>
    <?php endif; ?>
    
    <!-- Faculty Edit Form -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 overflow-hidden">
        <div class="p-6">
            <form action="" method="post" enctype="multipart/form-data" class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Personal Information -->
                    <div class="space-y-6">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2">Personal Information</h3>
                        
                        <div class="form-group">
                            <label for="full_name" class="form-label required">Full Name</label>
                            <input type="text" id="full_name" name="full_name" class="form-input" value="<?= htmlspecialchars($faculty['full_name']) ?>" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="email" class="form-label required">Email Address</label>
                            <input type="email" id="email" name="email" class="form-input" value="<?= htmlspecialchars($faculty['email']) ?>" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="profile_photo" class="form-label">Profile Photo</label>
                            <div class="flex items-center space-x-4">
                                <?php if (!empty($faculty['profile_photo']) && file_exists("../uploads/{$faculty['profile_photo']}")): ?>
                                    <img src="../uploads/<?= $faculty['profile_photo'] ?>" alt="Current profile photo" class="w-16 h-16 rounded-full object-cover">
                                <?php else: ?>
                                    <div class="w-16 h-16 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center text-gray-400">
                                        <i class="fas fa-user text-xl"></i>
                                    </div>
                                <?php endif; ?>
                                <div class="flex-1">
                                    <input type="file" id="profile_photo" name="profile_photo" class="form-input" accept="image/*">
                                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Upload a new photo (JPG, PNG, GIF)</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="password" class="form-label">Password</label>
                            <div class="relative">
                                <input type="password" id="password" name="password" class="form-input pr-10" placeholder="Leave blank to keep current password">
                                <button type="button" class="absolute right-2 top-1/2 -translate-y-1/2 text-gray-500 dark:text-gray-400" onclick="togglePassword('password')">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Leave blank to keep current password</p>
                        </div>
                        
                        <div class="flex items-center space-x-2">
                            <input type="checkbox" id="send_credentials" name="send_credentials" class="form-checkbox">
                            <label for="send_credentials" class="text-sm text-gray-700 dark:text-gray-300">Send credentials to faculty's email</label>
                        </div>
                    </div>
                    
                    <!-- Professional Information -->
                    <div class="space-y-6">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2">Professional Information</h3>
                        
                        <div class="form-group">
                            <label for="college_id" class="form-label required">College</label>
                            <select id="college_id" name="college_id" class="form-select" required>
                                <option value="">Select College</option>
                                <?php foreach ($colleges as $college): ?>
                                    <option value="<?= $college['id'] ?>" <?= $faculty['college_id'] == $college['id'] ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($college['name']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="department" class="form-label required">Department</label>
                            <input type="text" id="department" name="department" class="form-input" value="<?= htmlspecialchars($faculty['department']) ?>" list="department-list" required>
                            <datalist id="department-list">
                                <?php foreach ($departments as $dept): ?>
                                    <option value="<?= htmlspecialchars($dept) ?>">
                                <?php endforeach; ?>
                            </datalist>
                        </div>
                        
                        <div class="form-group">
                            <label for="designation" class="form-label required">Designation</label>
                            <input type="text" id="designation" name="designation" class="form-input" value="<?= htmlspecialchars($faculty['designation'] ?? '') ?>" required>
                        </div>
                        
                        <div class="form-group">
                            <div class="flex items-center space-x-2">
                                <input type="checkbox" id="verified" name="verified" class="form-checkbox" <?= $faculty['verified'] ? 'checked' : '' ?>>
                                <label for="verified" class="text-sm text-gray-700 dark:text-gray-300">Verified Faculty</label>
                            </div>
                            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Verified faculty have full access to all features</p>
                        </div>
                    </div>
                </div>
                
                <div class="flex justify-end space-x-2 pt-4 border-t border-gray-200 dark:border-gray-700">
                    <a href="superadmin_view_faculty.php?id=<?= $faculty['id'] ?>" class="btn btn-outline">Cancel</a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save mr-2"></i> Save Changes
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function togglePassword(id) {
    const input = document.getElementById(id);
    const icon = input.nextElementSibling.querySelector('i');
    
    if (input.type === 'password') {
        input.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        input.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}
</script> 