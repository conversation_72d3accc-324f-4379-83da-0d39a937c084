<?php
require_once '../includes/db.php';

// Check session
if (!isset($_SESSION['faculty_id']) && !isset($_SESSION['alumni_id']) && !isset($_SESSION['collegeadmin_id'])) {
    // Redirect to appropriate login page instead of logout
    if (isset($_SERVER['HTTP_REFERER']) && strpos($_SERVER['HTTP_REFERER'], 'collegeadmin') !== false) {
        header("Location: ../auth/collegeadmin_login.php");
    } else if (isset($_SERVER['HTTP_REFERER']) && strpos($_SERVER['HTTP_REFERER'], 'faculty') !== false) {
        header("Location: ../auth/faculty_login.php");
    } else if (isset($_SERVER['HTTP_REFERER']) && strpos($_SERVER['HTTP_REFERER'], 'alumni') !== false) {
        header("Location: ../auth/alumni_login.php");
    } else {
        header("Location: ../auth/collegeadmin_login.php");
    }
    exit;
}

// Set role explicitly for the sidebar
if (isset($_SESSION['collegeadmin_id'])) {
    $role = 'collegeadmin';
    $user_id = $_SESSION['collegeadmin_id'];
    $user_name = $_SESSION['collegeadmin_name'] ?? 'Admin';
} elseif (isset($_SESSION['faculty_id'])) {
    $role = 'faculty';
    $user_id = $_SESSION['faculty_id'];
    $user_name = $_SESSION['faculty_name'] ?? 'Faculty';
} elseif (isset($_SESSION['alumni_id'])) {
    $role = 'alumni';
    $user_id = $_SESSION['alumni_id'];
    $user_name = $_SESSION['alumni_name'] ?? 'Alumni';
}
$current_page = basename($_SERVER['PHP_SELF']);

$event_id = intval($_GET['event_id'] ?? 0);

// Get event details
$eventStmt = $conn->prepare("SELECT * FROM events WHERE id = ?");
$eventStmt->execute([$event_id]);
$event = $eventStmt->fetch(PDO::FETCH_ASSOC);

// Get attendees
$stmt = $conn->prepare("
    SELECT s.id, s.full_name, s.email, s.department, s.year 
    FROM event_attendees ea 
    JOIN students s ON ea.student_id = s.id 
    WHERE ea.event_id = ?
");
$stmt->execute([$event_id]);
$attendees = $stmt->fetchAll(PDO::FETCH_ASSOC);

require_once '../includes/header.php';
?>

<!-- Main Content -->
<main class="flex-grow py-8 px-4">
    <div class="container mx-auto">
        <!-- Breadcrumb -->
        <nav class="mb-6 text-sm" aria-label="Breadcrumb">
            <ol class="flex items-center space-x-2">
                <li>
                    <a href="<?= $role === 'collegeadmin' ? '../dashboards/collegeadmin_dashboard.php' : '../index.php' ?>" class="text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary-light">
                        <i class="fas fa-home"></i>
                        <span class="sr-only">Dashboard</span>
                    </a>
                </li>
                <li class="flex items-center">
                    <i class="fas fa-chevron-right text-gray-400 text-xs mx-1"></i>
                    <a href="event_list.php" class="text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary-light">
                        Events
                    </a>
                </li>
                <li class="flex items-center">
                    <i class="fas fa-chevron-right text-gray-400 text-xs mx-1"></i>
                    <span class="text-gray-700 dark:text-gray-300">Participants</span>
                </li>
            </ol>
        </nav>

        <div class="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                    <i class="fas fa-users text-primary mr-3"></i>
                    Event Participants
                </h1>
                <?php if ($event): ?>
                <p class="text-gray-600 dark:text-gray-400 mt-1">
                    Registered attendees for: <?= htmlspecialchars($event['title']) ?>
                </p>
                <?php endif; ?>
            </div>
            <div class="mt-4 md:mt-0">
                <a href="event_list.php" class="btn btn-outline flex items-center">
                    <i class="fas fa-arrow-left mr-2"></i> Back to Events
                </a>
            </div>
        </div>

        <!-- Participants Table -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 overflow-hidden">
            <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                <div class="flex justify-between items-center">
                    <h2 class="text-lg font-semibold text-gray-800 dark:text-white">
                        Registered Participants
                    </h2>
                    <span class="badge badge-primary"><?= count($attendees) ?> Attendees</span>
                </div>
            </div>
            
            <?php if ($attendees): ?>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead class="bg-gray-50 dark:bg-gray-700">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Full Name</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Email</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Department</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Year</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                            <?php foreach ($attendees as $student): ?>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($student['full_name']) ?></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500 dark:text-gray-400"><?= htmlspecialchars($student['email']) ?></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500 dark:text-gray-400"><?= htmlspecialchars($student['department']) ?></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500 dark:text-gray-400"><?= htmlspecialchars($student['year']) ?></div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="p-6 text-center">
                    <div class="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-users-slash text-gray-400 text-xl"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Participants Yet</h3>
                    <p class="text-gray-500 dark:text-gray-400">
                        No students have registered for this event yet.
                    </p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</main>

<?php require_once '../includes/footer.php'; ?>
