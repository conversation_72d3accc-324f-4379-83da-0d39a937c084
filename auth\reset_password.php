<?php
require_once '../includes/db.php';
require_once '../includes/header.php';

error_reporting(E_ALL);
ini_set('display_errors', 1);

$token = $_GET['token'] ?? '';
$error = $success = '';

// 1. Validate token from password_resets
$stmt = $conn->prepare("SELECT email, role FROM password_resets WHERE token = ?");
$stmt->execute([$token]);
$reset = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$reset) {
    $error = "Invalid or expired token.";
} elseif ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $new_password = password_hash($_POST['password'], PASSWORD_DEFAULT);
    $email = $reset['email'];
    $role = $reset['role'];

    // 2. Update password in the correct table
    if ($role === 'students') {
        $update = $conn->prepare("UPDATE students SET password = ? WHERE email = ?");
    } elseif ($role === 'alumni') {
        $update = $conn->prepare("UPDATE alumni SET password = ? WHERE email = ?");
    } else {
        $error = "Unknown role.";
    }

    if (isset($update)) {
        if ($update->execute([$new_password, $email])) {
            // 3. Delete reset token after successful update
            $conn->prepare("DELETE FROM password_resets WHERE token = ?")->execute([$token]);
            $success = "✅ Password has been updated. <br><a href='https://darkviolet-vulture-501696.hostingersite.com/'>Login Here</a>";
        } else {
            $error = "Failed to update password. Try again.";
        }
    }
}
?>

<div class="container mt-5">
    <h3>Reset Your Password</h3>
    <?php if ($error): ?><div class="alert alert-danger"><?= $error ?></div><?php endif; ?>
    <?php if ($success): ?><div class="alert alert-success"><?= $success ?></div><?php endif; ?>

    <?php if ($reset && !$success): ?>
    <form method="POST">
        <div class="mb-3">
            <label>New Password</label>
            <input type="password" name="password" class="form-control" required>
        </div>
        <button class="btn btn-success">Reset Password</button>
    </form>
    <?php endif; ?>
</div>

<?php require_once '../includes/footer.php'; ?>
