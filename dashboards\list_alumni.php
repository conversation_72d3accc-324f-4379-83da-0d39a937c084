<?php
// Include session configuration
require_once '../includes/session_config.php';
require_once '../includes/db.php';

if (!isset($_SESSION['collegeadmin_id'])) {
    header("Location: ../auth/collegeadmin_login.php");
    exit;
}

$college_id = $_SESSION['college_id'];
$admin_name = $_SESSION['collegeadmin_name'] ?? 'Admin';

// Set role explicitly for the sidebar
$role = 'collegeadmin';
$user_id = $_SESSION['collegeadmin_id'];
$user_name = $_SESSION['collegeadmin_name'];
$current_page = basename($_SERVER['PHP_SELF']);

// Pagination setup
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 10; // Items per page
$offset = ($page - 1) * $limit;

// Search and filter functionality
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$industry_filter = isset($_GET['industry']) ? trim($_GET['industry']) : '';
$graduation_year_filter = isset($_GET['graduation_year']) ? trim($_GET['graduation_year']) : '';
$verified_filter = isset($_GET['verified']) ? $_GET['verified'] : '';

// Build query conditions
$conditions = ["college_id = ?"];
$params = [$college_id];

if (!empty($search)) {
    $conditions[] = "(full_name LIKE ? OR email LIKE ? OR company LIKE ?)";
    $searchParam = "%$search%";
    $params = array_merge($params, [$searchParam, $searchParam, $searchParam]);
}

if (!empty($industry_filter)) {
    $conditions[] = "industry = ?";
    $params[] = $industry_filter;
}

if (!empty($graduation_year_filter)) {
    $conditions[] = "graduation_year = ?";
    $params[] = $graduation_year_filter;
}

if ($verified_filter !== '') {
    $conditions[] = "verified = ?";
    $params[] = $verified_filter;
}

$whereClause = implode(' AND ', $conditions);

// Count total alumni for pagination
$countSql = "SELECT COUNT(*) FROM alumni WHERE $whereClause";
$countStmt = $conn->prepare($countSql);
$countStmt->execute($params);
$totalAlumni = $countStmt->fetchColumn();
$totalPages = ceil($totalAlumni / $limit);

// Fetch alumni with pagination and filters
$sql = "SELECT * FROM alumni WHERE $whereClause ORDER BY created_at DESC LIMIT $limit OFFSET $offset";
$stmt = $conn->prepare($sql);
$stmt->execute($params);
$alumni_list = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get industries and graduation years for filters
$industryStmt = $conn->prepare("SELECT DISTINCT industry FROM alumni WHERE college_id = ? AND industry IS NOT NULL AND industry != '' ORDER BY industry ASC");
$industryStmt->execute([$college_id]);
$industries = $industryStmt->fetchAll(PDO::FETCH_COLUMN);

$yearStmt = $conn->prepare("SELECT DISTINCT graduation_year FROM alumni WHERE college_id = ? AND graduation_year IS NOT NULL AND graduation_year != '' ORDER BY graduation_year DESC");
$yearStmt->execute([$college_id]);
$graduation_years = $yearStmt->fetchAll(PDO::FETCH_COLUMN);

// Get college details
$collegeStmt = $conn->prepare("SELECT name FROM colleges WHERE id = ?");
$collegeStmt->execute([$college_id]);
$college = $collegeStmt->fetch(PDO::FETCH_ASSOC);

// Count statistics
$verifiedCountStmt = $conn->prepare("SELECT COUNT(*) FROM alumni WHERE college_id = ? AND verified = 1");
$verifiedCountStmt->execute([$college_id]);
$verified_alumni_count = $verifiedCountStmt->fetchColumn();

$pendingCountStmt = $conn->prepare("SELECT COUNT(*) FROM alumni WHERE college_id = ? AND verified = 0");
$pendingCountStmt->execute([$college_id]);
$pending_alumni_count = $pendingCountStmt->fetchColumn();

// Count alumni with mentees
try {
    $mentorCountStmt = $conn->prepare("SELECT COUNT(DISTINCT alumni_id) FROM mentorships 
                                      JOIN alumni ON mentorships.alumni_id = alumni.id 
                                      WHERE alumni.college_id = ?");
    $mentorCountStmt->execute([$college_id]);
    $mentor_count = $mentorCountStmt->fetchColumn();
} catch (PDOException $e) {
    $mentor_count = 0;
}

// Include the header
require_once '../includes/header.php';
?>

    <!-- Main Content -->
    <main class="flex-grow py-6 px-4">
        <div class="max-w-7xl mx-auto">
            <!-- Page Header -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8">
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="bg-gradient-to-br from-purple-500 to-purple-600 p-3 rounded-xl shadow-lg">
                            <i class="fas fa-user-tie text-white text-xl"></i>
                        </div>
                        <div>
                            <h1 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                                Alumni Management
                            </h1>
                            <nav class="text-sm text-gray-600 dark:text-gray-400 mt-1" aria-label="Breadcrumb">
                                <ol class="flex items-center space-x-2">
                                    <li>
                                        <a href="collegeadmin_dashboard.php" class="hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
                                            Dashboard
                                        </a>
                                    </li>
                                    <li class="flex items-center">
                                        <i class="fas fa-chevron-right text-gray-400 text-xs mx-2"></i>
                                        <span class="text-gray-900 dark:text-white font-medium">Alumni Management</span>
                                    </li>
                                </ol>
                            </nav>
                            <p class="text-gray-600 dark:text-gray-400 mt-2">
                                Manage all alumni at <span class="font-medium text-purple-600 dark:text-purple-400"><?= htmlspecialchars($college['name'] ?? 'your college') ?></span>
                            </p>
                        </div>
                    </div>
                    <div class="mt-4 lg:mt-0 flex flex-wrap gap-3">
                        <a href="invite_alumni.php" class="inline-flex items-center px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200">
                            <i class="fas fa-user-plus mr-2"></i> Invite Alumni
                        </a>
                        <a href="export_alumni.php" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200">
                            <i class="fas fa-file-export mr-2"></i> Export Data
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Stats Overview -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 border border-gray-200 dark:border-gray-700">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 mr-4">
                            <i class="fas fa-users"></i>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Alumni</p>
                            <p class="text-xl font-semibold text-gray-800 dark:text-white"><?= $totalAlumni ?></p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 border border-gray-200 dark:border-gray-700">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 mr-4">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Verified Alumni</p>
                            <p class="text-xl font-semibold text-gray-800 dark:text-white"><?= $verified_alumni_count ?></p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 border border-gray-200 dark:border-gray-700">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400 mr-4">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Pending Verification</p>
                            <p class="text-xl font-semibold text-gray-800 dark:text-white"><?= $pending_alumni_count ?></p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 border border-gray-200 dark:border-gray-700">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400 mr-4">
                            <i class="fas fa-hands-helping"></i>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Active Mentors</p>
                            <p class="text-xl font-semibold text-gray-800 dark:text-white"><?= $mentor_count ?></p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Search and Filters -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 mb-6">
                <div class="p-6">
                    <h2 class="text-lg font-semibold text-gray-800 dark:text-white mb-4 flex items-center">
                        <i class="fas fa-filter text-primary mr-2"></i> Search & Filter Alumni
                    </h2>
                    <form method="GET" class="space-y-4">
                        <div class="flex flex-col sm:flex-row gap-4">
                            <div class="flex-grow relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-search text-gray-400"></i>
                                </div>
                                <input 
                                    type="text" 
                                    name="search" 
                                    value="<?= htmlspecialchars($search) ?>" 
                                    class="form-input pl-10 w-full" 
                                    placeholder="Search by name, email or company..."
                                >
                            </div>
                            <div class="flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search mr-2"></i> Search
                                </button>
                                <?php if (!empty($search) || !empty($industry_filter) || !empty($graduation_year_filter) || $verified_filter !== ''): ?>
                                    <a href="list_alumni.php" class="btn btn-outline">
                                        <i class="fas fa-times mr-2"></i> Clear
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <label for="industry" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Industry</label>
                                <select name="industry" id="industry" class="form-select w-full">
                                    <option value="">All Industries</option>
                                    <?php foreach ($industries as $industry): ?>
                                        <option value="<?= htmlspecialchars($industry) ?>" <?= $industry_filter === $industry ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($industry) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div>
                                <label for="graduation_year" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Graduation Year</label>
                                <select name="graduation_year" id="graduation_year" class="form-select w-full">
                                    <option value="">All Years</option>
                                    <?php foreach ($graduation_years as $year): ?>
                                        <option value="<?= htmlspecialchars($year) ?>" <?= $graduation_year_filter === $year ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($year) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div>
                                <label for="verified" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Verification Status</label>
                                <select name="verified" id="verified" class="form-select w-full">
                                    <option value="">All Alumni</option>
                                    <option value="1" <?= $verified_filter === '1' ? 'selected' : '' ?>>Verified</option>
                                    <option value="0" <?= $verified_filter === '0' ? 'selected' : '' ?>>Not Verified</option>
                                </select>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Alumni List -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700">
                <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4 flex flex-wrap justify-between items-center">
                    <h2 class="font-semibold text-gray-800 dark:text-white flex items-center">
                        <i class="fas fa-list text-primary mr-2"></i>
                        Alumni Directory
                        <?php if ($totalAlumni > 0): ?>
                            <span class="ml-2 text-sm text-gray-500 dark:text-gray-400">
                                (<?= $totalAlumni ?> total)
                            </span>
                        <?php endif; ?>
                    </h2>
                    <?php if (!empty($search) || !empty($industry_filter) || !empty($graduation_year_filter) || $verified_filter !== ''): ?>
                        <span class="bg-primary-light dark:bg-primary-dark/30 text-primary dark:text-primary-light text-sm py-1 px-3 rounded-full flex items-center">
                            <i class="fas fa-filter mr-1"></i> Filtered Results
                        </span>
                    <?php endif; ?>
                </div>
                
        <?php if (count($alumni_list) > 0): ?>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                            <thead>
                                <tr>
                                    <th class="px-6 py-3 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Alumni</th>
                                    <th class="px-6 py-3 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Contact</th>
                                    <th class="px-6 py-3 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Professional Info</th>
                                    <th class="px-6 py-3 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    <?php foreach ($alumni_list as $alumni): ?>
                                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/50">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="flex-shrink-0 h-10 w-10">
                                                    <?php if (!empty($alumni['profile_image'])): ?>
                                                        <img class="h-10 w-10 rounded-full object-cover" src="../uploads/<?= htmlspecialchars($alumni['profile_image']) ?>" alt="<?= htmlspecialchars($alumni['full_name']) ?>">
                                                    <?php else: ?>
                                                        <div class="h-10 w-10 rounded-full bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center text-purple-600 dark:text-purple-400">
                                                            <i class="fas fa-user-tie"></i>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="ml-4">
                                                    <div class="text-sm font-medium text-gray-900 dark:text-white">
                                                        <?= htmlspecialchars($alumni['full_name']) ?>
                                                    </div>
                                                    <div class="text-xs text-gray-500 dark:text-gray-400">
                                                        ID: <?= $alumni['id'] ?>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4">
                                            <div class="text-sm text-gray-900 dark:text-white">
                                                <?= htmlspecialchars($alumni['email']) ?>
                                            </div>
                                            <?php if (!empty($alumni['phone'])): ?>
                                                <div class="text-xs text-gray-500 dark:text-gray-400">
                                                    <?= htmlspecialchars($alumni['phone']) ?>
                                                </div>
                                            <?php endif; ?>
                                        </td>
                                        <td class="px-6 py-4">
                                            <div class="text-sm text-gray-900 dark:text-white">
                                                <?= !empty($alumni['company']) ? htmlspecialchars($alumni['company']) : '<span class="text-gray-400 dark:text-gray-500">No company</span>' ?>
                                            </div>
                                            <div class="mt-1">
                                                <?php if (!empty($alumni['industry'])): ?>
                                                    <span class="inline-block bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 text-xs px-2 py-1 rounded">
                                                        <?= htmlspecialchars($alumni['industry']) ?>
                                                    </span>
                                                <?php endif; ?>
                                                <?php if (!empty($alumni['graduation_year'])): ?>
                                                    <span class="inline-block bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300 text-xs px-2 py-1 rounded ml-1">
                                                        Class of <?= htmlspecialchars($alumni['graduation_year']) ?>
                                                    </span>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <?php if ($alumni['verified']): ?>
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300">
                                                    <i class="fas fa-check-circle mr-1"></i> Verified
                                                </span>
                                            <?php else: ?>
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300">
                                                    <i class="fas fa-clock mr-1"></i> Pending
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                                            <div class="flex space-x-3">
                                                <a href="view_alumni.php?id=<?= $alumni['id'] ?>" class="text-gray-600 dark:text-gray-400 hover:text-primary dark:hover:text-primary-light" title="View Profile">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="edit_alumni.php?id=<?= $alumni['id'] ?>" class="text-primary hover:text-primary-dark dark:hover:text-primary-light" title="Edit Profile">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <?php if (!$alumni['verified']): ?>
                                                    <a href="verify_alumni.php?id=<?= $alumni['id'] ?>" class="text-green-600 dark:text-green-400 hover:text-green-700 dark:hover:text-green-300" title="Verify Alumni">
                                                        <i class="fas fa-check-circle"></i>
                                                    </a>
                                                <?php endif; ?>
                                                <a href="assign_mentees.php?id=<?= $alumni['id'] ?>" class="text-purple-600 dark:text-purple-400 hover:text-purple-700 dark:hover:text-purple-300" title="Assign Mentees">
                                                    <i class="fas fa-users"></i>
                                                </a>
                                            </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
                    </div>
                    
                    <!-- Pagination -->
                    <?php if ($totalPages > 1): ?>
                        <div class="px-6 py-4 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-700">
                            <div class="flex flex-col sm:flex-row justify-between items-center">
                                <div class="text-sm text-gray-500 dark:text-gray-400 mb-4 sm:mb-0">
                                    Showing <?= ($offset + 1) ?> to <?= min($offset + $limit, $totalAlumni) ?> of <?= $totalAlumni ?> alumni
                                </div>
                                <div class="flex space-x-1">
                                    <?php 
                                        // Build query string for pagination links
                                        $queryParams = [];
                                        if (!empty($search)) $queryParams['search'] = $search;
                                        if (!empty($industry_filter)) $queryParams['industry'] = $industry_filter;
                                        if (!empty($graduation_year_filter)) $queryParams['graduation_year'] = $graduation_year_filter;
                                        if ($verified_filter !== '') $queryParams['verified'] = $verified_filter;
                                        
                                        $queryString = http_build_query($queryParams);
                                        $queryPrefix = empty($queryString) ? '' : $queryString . '&';
                                    ?>
                                    
                                    <?php if ($page > 1): ?>
                                        <a href="?<?= $queryPrefix ?>page=1" class="btn btn-sm btn-outline">
                                            <i class="fas fa-angle-double-left"></i>
                                        </a>
                                        <a href="?<?= $queryPrefix ?>page=<?= ($page - 1) ?>" class="btn btn-sm btn-outline">
                                            <i class="fas fa-angle-left"></i>
                                        </a>
                                    <?php endif; ?>
                                    
                                    <span class="btn btn-sm btn-primary">
                                        <?= $page ?> of <?= $totalPages ?>
                                    </span>
                                    
                                    <?php if ($page < $totalPages): ?>
                                        <a href="?<?= $queryPrefix ?>page=<?= ($page + 1) ?>" class="btn btn-sm btn-outline">
                                            <i class="fas fa-angle-right"></i>
                                        </a>
                                        <a href="?<?= $queryPrefix ?>page=<?= $totalPages ?>" class="btn btn-sm btn-outline">
                                            <i class="fas fa-angle-double-right"></i>
                                        </a>
        <?php endif; ?>
    </div>
</div>
                        </div>
                    <?php endif; ?>
                <?php else: ?>
                    <div class="p-6 text-center">
                        <?php if (!empty($search) || !empty($industry_filter) || !empty($graduation_year_filter) || $verified_filter !== ''): ?>
                            <div class="py-12">
                                <div class="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <i class="fas fa-search text-gray-400 text-2xl"></i>
                                </div>
                                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No matching results</h3>
                                <p class="text-gray-500 dark:text-gray-400 mb-4">We couldn't find any alumni matching your filters.</p>
                                <a href="list_alumni.php" class="btn btn-outline">
                                    <i class="fas fa-times mr-2"></i> Clear Filters
                                </a>
                            </div>
                        <?php else: ?>
                            <div class="py-12">
                                <div class="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <i class="fas fa-user-tie text-gray-400 text-2xl"></i>
                                </div>
                                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No alumni yet</h3>
                                <p class="text-gray-500 dark:text-gray-400 mb-4">Start building your alumni network by inviting graduates to join.</p>
                                <a href="invite_alumni.php" class="btn btn-primary">
                                    <i class="fas fa-user-plus mr-2"></i> Invite Alumni
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>
            
            <!-- Quick Tips -->
            <div class="mt-6 bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
                <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4 flex items-center">
                    <i class="fas fa-lightbulb text-yellow-500 mr-2"></i> Alumni Management Tips
                </h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div class="flex items-start">
                        <div class="flex-shrink-0 h-10 w-10 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center text-blue-600 dark:text-blue-400">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div class="ml-4">
                            <h4 class="text-sm font-medium text-gray-900 dark:text-white">Invite Alumni</h4>
                            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                                Send personalized invitations to recent graduates to join your alumni network.
                            </p>
                        </div>
                    </div>
                    
                    <div class="flex items-start">
                        <div class="flex-shrink-0 h-10 w-10 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center text-green-600 dark:text-green-400">
                            <i class="fas fa-handshake"></i>
                        </div>
                        <div class="ml-4">
                            <h4 class="text-sm font-medium text-gray-900 dark:text-white">Mentorship Program</h4>
                            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                                Connect alumni with current students for mentoring relationships.
                            </p>
                        </div>
                    </div>
                    
                    <div class="flex items-start">
                        <div class="flex-shrink-0 h-10 w-10 rounded-full bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center text-purple-600 dark:text-purple-400">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                        <div class="ml-4">
                            <h4 class="text-sm font-medium text-gray-900 dark:text-white">Organize Events</h4>
                            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                                Plan networking events to strengthen your alumni community.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

<?php
// Include the footer
require_once '../includes/footer.php';
?>

<script>
    // Auto-submit form when select filters change
    document.getElementById('industry').addEventListener('change', function() {
        this.form.submit();
    });
    
    document.getElementById('graduation_year').addEventListener('change', function() {
        this.form.submit();
    });
    
    document.getElementById('verified').addEventListener('change', function() {
        this.form.submit();
    });
</script>
