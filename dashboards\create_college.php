<?php
// Include session configuration
require_once '../includes/session_config.php';
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
require_once '../includes/db.php';

if (!isset($_SESSION['superadmin_id'])) {
    header("Location: ../auth/superadmin_login.php");
    exit;
}

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $college_name = trim($_POST['college_name']);
    $slug = trim($_POST['slug']);
    $location = isset($_POST['location']) ? trim($_POST['location']) : '';
    $website = isset($_POST['website']) ? trim($_POST['website']) : '';
    $status = isset($_POST['status']) ? trim($_POST['status']) : 'active';

    if (!empty($college_name) && !empty($slug)) {
        // Check if college with same name or slug already exists
        $check = $conn->prepare("SELECT COUNT(*) FROM colleges WHERE name = ? OR slug = ?");
        $check->execute([$college_name, $slug]);
        $exists = $check->fetchColumn();
        
        if ($exists) {
            $error = "A college with this name or slug already exists.";
        } else {
            // Check if location and website columns exist in the colleges table
            // If they don't exist, add only name and slug
            $columns = [];
            $values = [];
            $placeholders = [];
            
            $columns[] = "name";
            $values[] = $college_name;
            $placeholders[] = "?";
            
            $columns[] = "slug";
            $values[] = $slug;
            $placeholders[] = "?";
            
            // Check if location column exists in the colleges table
            try {
                $stmt = $conn->prepare("SHOW COLUMNS FROM colleges LIKE 'location'");
                $stmt->execute();
                if ($stmt->rowCount() > 0) {
                    $columns[] = "location";
                    $values[] = $location;
                    $placeholders[] = "?";
                }
            } catch (PDOException $e) {
                // Ignore
            }
            
            // Check if website column exists in the colleges table
            try {
                $stmt = $conn->prepare("SHOW COLUMNS FROM colleges LIKE 'website'");
                $stmt->execute();
                if ($stmt->rowCount() > 0) {
                    $columns[] = "website";
                    $values[] = $website;
                    $placeholders[] = "?";
                }
            } catch (PDOException $e) {
                // Ignore
            }
            
            // Check if status column exists in the colleges table
            try {
                $stmt = $conn->prepare("SHOW COLUMNS FROM colleges LIKE 'status'");
                $stmt->execute();
                if ($stmt->rowCount() > 0) {
                    $columns[] = "status";
                    $values[] = $status;
                    $placeholders[] = "?";
                }
            } catch (PDOException $e) {
                // Ignore
            }
            
            $sql = "INSERT INTO colleges (" . implode(", ", $columns) . ") VALUES (" . implode(", ", $placeholders) . ")";
        $stmt = $conn->prepare($sql);
            $stmt->execute($values);

            $success = "College added successfully.";
        $_SESSION['flash_message'] = "College added successfully.";
            
            // Clear form fields after successful submission
            $college_name = '';
            $slug = '';
            $location = '';
            $website = '';
        }
    } else {
        $error = "College name and slug are required.";
    }

    if (empty($error) && !empty($success)) {
    header("Location: superadmin_dashboard.php");
    exit;
    }
}

// Include the header
require_once '../includes/header.php';
?>

<!-- Main Content -->
<div class="container mx-auto px-4 py-8 max-w-4xl">
    <!-- Page Header -->
    <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                <span class="bg-primary/10 dark:bg-primary/20 p-2 rounded-md mr-3">
                    <i class="fas fa-university text-primary"></i>
                </span>
                Create New College
            </h1>
            <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">Add a new college to the Mentoshri platform</p>
            </div>
        <div class="mt-4 md:mt-0">
            <a href="superadmin_dashboard.php" class="btn btn-outline flex items-center">
                    <i class="fas fa-arrow-left mr-2"></i> Back to Dashboard
                </a>
            </div>
        </div>
    
    <?php if (!empty($error)): ?>
        <div class="mb-6 bg-red-50 dark:bg-red-900/30 border-l-4 border-red-500 text-red-700 dark:text-red-400 p-4 flex justify-between items-center" role="alert">
            <div>
                <i class="fas fa-exclamation-circle mr-2"></i>
                <?= $error ?>
            </div>
            <button type="button" class="text-red-700 dark:text-red-400 hover:text-red-900" onclick="this.parentElement.style.display='none';">
                <i class="fas fa-times"></i>
            </button>
                </div>
            <?php endif; ?>
            
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 transition-all duration-300 hover:shadow-lg">
                <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
            <h2 class="font-semibold text-gray-800 dark:text-white flex items-center">
                College Information
            </h2>
                </div>
                
                <div class="p-6">
            <form method="POST" action="" class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="form-group">
                        <label for="college_name" class="form-label required">College Name</label>
                        <input type="text" id="college_name" name="college_name" class="form-input" placeholder="Enter college name" value="<?= htmlspecialchars($college_name ?? '') ?>" required>
                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Official name of the institution</p>
                            </div>
                            
                            <div class="form-group">
                        <label for="slug" class="form-label required">College Slug</label>
                                <div class="relative">
                            <input type="text" id="slug" name="slug" class="form-input pl-10" placeholder="e.g. harvard-university" value="<?= htmlspecialchars($slug ?? '') ?>" required>
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <span class="text-gray-500 dark:text-gray-400">/</span>
                            </div>
                        </div>
                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Unique identifier used in URLs (lowercase, no spaces)</p>
                    </div>
                    
                    <div class="form-group">
                        <label for="location" class="form-label">Location</label>
                        <input type="text" id="location" name="location" class="form-input" placeholder="City, State, Country" value="<?= htmlspecialchars($location ?? '') ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="website" class="form-label">Website URL</label>
                        <input type="url" id="website" name="website" class="form-input" placeholder="https://example.edu" value="<?= htmlspecialchars($website ?? '') ?>">
                    </div>
                    
                    <div class="form-group md:col-span-2">
                        <label for="status" class="form-label">Status</label>
                        <select id="status" name="status" class="form-input">
                            <option value="active" <?= isset($status) && $status === 'active' ? 'selected' : '' ?>>Active</option>
                            <option value="inactive" <?= isset($status) && $status === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                            <option value="pending" <?= isset($status) && $status === 'pending' ? 'selected' : '' ?>>Pending</option>
                        </select>
                    </div>
                </div>
                
                <div class="border-t border-gray-200 dark:border-gray-700 pt-6 flex items-center justify-between">
                    <button type="button" class="btn btn-outline" onclick="window.location.href='superadmin_dashboard.php'">Cancel</button>
                    
                    <div class="flex space-x-2">
                        <button type="reset" class="btn btn-secondary">
                            <i class="fas fa-undo mr-2"></i> Reset
                        </button>
                                <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save mr-2"></i> Create College
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
    
    <div class="mt-8 bg-gray-50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
        <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">What happens next?</h3>
        <ul class="space-y-2 text-sm text-gray-600 dark:text-gray-400">
            <li class="flex items-center">
                <i class="fas fa-check-circle text-green-500 mr-2"></i>
                College will be created with a unique identifier
            </li>
            <li class="flex items-center">
                <i class="fas fa-check-circle text-green-500 mr-2"></i>
                You'll be able to assign college administrators
            </li>
            <li class="flex items-center">
                <i class="fas fa-check-circle text-green-500 mr-2"></i>
                College admins can manage faculty, students, and alumni
            </li>
        </ul>
            </div>
        </div>

    <script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-generate slug from college name
    const collegeNameInput = document.getElementById('college_name');
    const slugInput = document.getElementById('slug');
    
    collegeNameInput.addEventListener('input', function() {
        if (!slugInput.dataset.modified) {
            // Convert to lowercase, replace spaces with hyphens, remove special characters
            const slug = this.value.toLowerCase()
                .replace(/\s+/g, '-')
                .replace(/[^\w\-]+/g, '')
                .replace(/\-\-+/g, '-')
                .replace(/^-+/, '')
                .replace(/-+$/, '');
            
            slugInput.value = slug;
        }
    });
    
    // Mark slug as manually modified when user types in it
    slugInput.addEventListener('input', function() {
        this.dataset.modified = true;
    });
    
    // Reset the modified flag when form is reset
    document.querySelector('form').addEventListener('reset', function() {
        setTimeout(() => {
            slugInput.dataset.modified = false;
        }, 10);
        });
        
    // Theme toggling
    const themeToggle = document.getElementById('themeToggle');
    if (themeToggle) {
        themeToggle.addEventListener('click', function() {
            document.documentElement.classList.toggle('dark');
            localStorage.setItem('theme', document.documentElement.classList.contains('dark') ? 'dark' : 'light');
        });
    }
    
    // Initial theme setting
    if (localStorage.theme === 'dark' || (!('theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
        document.documentElement.classList.add('dark');
    } else {
        document.documentElement.classList.remove('dark');
            }
        });
    </script>
