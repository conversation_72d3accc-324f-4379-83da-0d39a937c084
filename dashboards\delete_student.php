<?php
session_start();
require_once '../includes/db.php';

// Ensure only college admin can access this page
if (!isset($_SESSION['collegeadmin_id'])) {
    header("Location: ../auth/collegeadmin_login.php");
    exit;
}

$college_id = $_SESSION['college_id'];
$admin_name = $_SESSION['collegeadmin_name'] ?? 'Admin';

// Set role explicitly for the sidebar
$role = 'collegeadmin';
$user_id = $_SESSION['collegeadmin_id'];
$user_name = $_SESSION['collegeadmin_name'];
$current_page = basename($_SERVER['PHP_SELF']);

$student_id = $_GET['id'] ?? null;

// Validate student ID
if (!$student_id) {
    $_SESSION['flash_message'] = "Invalid student ID.";
    header("Location: list_students.php");
    exit;
}

// Check if student exists and belongs to this college
$stmt = $conn->prepare("SELECT * FROM students WHERE id = ? AND college_id = ?");
$stmt->execute([$student_id, $college_id]);
$student = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$student) {
    $_SESSION['flash_message'] = "Student not found or access denied.";
    header("Location: list_students.php");
    exit;
}

// Handle deletion if confirmed
if (isset($_POST['confirm_delete']) && $_POST['confirm_delete'] === 'yes') {
    try {
        // Start transaction
        $conn->beginTransaction();
        
        // Delete any mentorship relationships
        $mentorship = $conn->prepare("DELETE FROM mentorships WHERE student_id = ?");
        $mentorship->execute([$student_id]);
        
        // Delete any requests
        $requests = $conn->prepare("DELETE FROM student_requests WHERE student_id = ?");
        $requests->execute([$student_id]);
        
        // Delete student
        $delete = $conn->prepare("DELETE FROM students WHERE id = ? AND college_id = ?");
        $delete->execute([$student_id, $college_id]);
        
        // Commit transaction
        $conn->commit();
        
        $_SESSION['flash_message'] = "Student '" . htmlspecialchars($student['full_name']) . "' has been permanently removed.";
        header("Location: list_students.php");
        exit;
    } catch (Exception $e) {
        // Rollback transaction on error
        $conn->rollBack();
        
        $_SESSION['flash_message'] = "Error removing student: " . $e->getMessage();
        header("Location: view_student.php?id=" . $student_id);
        exit;
    }
}

// Profile photo handling
$photo = !empty($student['profile_photo']) && file_exists("../uploads/{$student['profile_photo']}")
    ? $student['profile_photo']
    : 'default.png';

// Include the header
require_once '../includes/header.php';
?>

    <!-- Main Content -->
    <main class="flex-grow py-8 px-4">
        <div class="container mx-auto">
            <!-- Breadcrumb -->
            <nav class="mb-6 text-sm" aria-label="Breadcrumb">
                <ol class="flex items-center space-x-2">
                    <li>
                        <a href="collegeadmin_dashboard.php" class="text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary-light">
                            <i class="fas fa-home"></i>
                            <span class="sr-only">Dashboard</span>
                        </a>
                    </li>
                    <li class="flex items-center">
                        <i class="fas fa-chevron-right text-gray-400 text-xs mx-1"></i>
                        <a href="list_students.php" class="text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary-light">
                            Student Management
                        </a>
                    </li>
                    <li class="flex items-center">
                        <i class="fas fa-chevron-right text-gray-400 text-xs mx-1"></i>
                        <a href="view_student.php?id=<?= $student_id ?>" class="text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary-light">
                            <?= htmlspecialchars($student['full_name']) ?>
                        </a>
                    </li>
                    <li class="flex items-center">
                        <i class="fas fa-chevron-right text-gray-400 text-xs mx-1"></i>
                        <span class="text-gray-700 dark:text-gray-300">Delete Student</span>
                    </li>
                </ol>
            </nav>

            <div class="max-w-xl mx-auto">
                <div class="mb-6">
                    <h1 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                        <i class="fas fa-trash-alt text-red-600 dark:text-red-400 mr-3"></i>
                        Delete Student Account
                    </h1>
                    <p class="text-gray-600 dark:text-gray-400 mt-1">
                        You are about to permanently delete <?= htmlspecialchars($student['full_name']) ?>'s account
                    </p>
                </div>
                
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 overflow-hidden">
                    <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
                        <h2 class="font-semibold text-gray-800 dark:text-white flex items-center">
                            <i class="fas fa-exclamation-triangle text-red-600 dark:text-red-400 mr-2"></i> Confirm Deletion
                        </h2>
                    </div>
                    
                    <div class="p-6">
                        <!-- Student Info -->
                        <div class="flex items-center mb-6 pb-6 border-b border-gray-200 dark:border-gray-700">
                            <div class="w-16 h-16 rounded-full bg-gray-100 dark:bg-gray-700 overflow-hidden mr-4 flex-shrink-0">
                                <?php if ($photo === 'default.png'): ?>
                                    <div class="w-full h-full flex items-center justify-center text-gray-400 dark:text-gray-500">
                                        <i class="fas fa-user-graduate text-2xl"></i>
                                    </div>
                                <?php else: ?>
                                    <img src="../uploads/<?= htmlspecialchars($photo) ?>" alt="<?= htmlspecialchars($student['full_name']) ?>" class="w-full h-full object-cover">
                                <?php endif; ?>
                            </div>
                            <div>
                                <h3 class="text-lg font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($student['full_name']) ?></h3>
                                <p class="text-gray-600 dark:text-gray-400"><?= htmlspecialchars($student['email']) ?></p>
                                <div class="flex flex-wrap gap-2 mt-1">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300">
                                        <?= htmlspecialchars($student['department']) ?>
                                    </span>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300">
                                        Year <?= $student['year'] ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-red-50 dark:bg-red-900/20 border-l-4 border-red-500 text-red-700 dark:text-red-400 p-4 mb-6">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium">
                                        Warning: This action cannot be undone
                                    </p>
                                    <p class="text-sm mt-1">
                                        You are about to permanently delete this student's account and all associated data.
                                    </p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-6">
                            <h4 class="font-medium text-gray-900 dark:text-white mb-3">This will permanently remove:</h4>
                            <ul class="space-y-2">
                                <li class="flex items-start">
                                    <i class="fas fa-check-circle text-red-500 mt-1 mr-2"></i>
                                    <span class="text-gray-700 dark:text-gray-300">The student's profile and account information</span>
                                </li>
                                <li class="flex items-start">
                                    <i class="fas fa-check-circle text-red-500 mt-1 mr-2"></i>
                                    <span class="text-gray-700 dark:text-gray-300">All mentorship relationships and assignments</span>
                                </li>
                                <li class="flex items-start">
                                    <i class="fas fa-check-circle text-red-500 mt-1 mr-2"></i>
                                    <span class="text-gray-700 dark:text-gray-300">All mentor requests made by this student</span>
                                </li>
                                <li class="flex items-start">
                                    <i class="fas fa-check-circle text-red-500 mt-1 mr-2"></i>
                                    <span class="text-gray-700 dark:text-gray-300">Access to the platform and all associated services</span>
                                </li>
                            </ul>
                        </div>
                        
                        <form method="post" id="deleteForm" class="mt-6">
                            <div class="flex items-center mb-6">
                                <input 
                                    type="checkbox" 
                                    id="confirm_checkbox" 
                                    class="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded"
                                >
                                <label for="confirm_checkbox" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                                    I understand that this action is permanent and cannot be reversed
                                </label>
                            </div>
                            
                            <div class="flex justify-between items-center">
                                <a href="view_student.php?id=<?= $student_id ?>" class="btn btn-outline">
                                    <i class="fas fa-arrow-left mr-2"></i> Cancel
                                </a>
                                <button 
                                    type="submit" 
                                    name="confirm_delete" 
                                    value="yes" 
                                    id="deleteButton"
                                    class="btn bg-red-600 hover:bg-red-700 text-white opacity-50 cursor-not-allowed"
                                    disabled
                                >
                                    <i class="fas fa-trash-alt mr-2"></i> Permanently Delete
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </main>

<?php
// Include the footer
require_once '../includes/footer.php';
?>

<script>
    // Delete confirmation checkbox
    document.getElementById('confirm_checkbox').addEventListener('change', function() {
        const deleteButton = document.getElementById('deleteButton');
        if (this.checked) {
            deleteButton.disabled = false;
            deleteButton.classList.remove('opacity-50', 'cursor-not-allowed');
        } else {
            deleteButton.disabled = true;
            deleteButton.classList.add('opacity-50', 'cursor-not-allowed');
        }
    });
    
    // Additional confirmation on form submit
    document.getElementById('deleteForm').addEventListener('submit', function(e) {
        if (!confirm('Are you absolutely sure you want to delete this student? This action cannot be undone.')) {
            e.preventDefault();
        }
    });
</script> 