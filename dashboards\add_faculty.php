<?php
// Include session configuration
require_once '../includes/session_config.php';
require_once '../includes/db.php';
require_once '../includes/db_schema_check.php';
require_once '../notify/send_email.php';

if (!isset($_SESSION['collegeadmin_id'])) {
    header("Location: ../auth/collegeadmin_login.php");
    exit;
}

$error = '';
$success = '';
$email_sent = false;

// Get college details
$college_id = $_SESSION['college_id'];
$admin_name = $_SESSION['collegeadmin_name'];

// Set role explicitly for the sidebar
$role = 'collegeadmin';
$user_id = $_SESSION['collegeadmin_id'];
$user_name = $_SESSION['collegeadmin_name'];
$current_page = basename($_SERVER['PHP_SELF']);

$stmt = $conn->prepare("SELECT name FROM colleges WHERE id = ?");
$stmt->execute([$college_id]);
$college = $stmt->fetch(PDO::FETCH_ASSOC);

// Check and update faculties table schema
$schemaUpdates = checkAndUpdateFacultiesSchema($conn);
$schemaUpdateMessages = [];
if (!empty($schemaUpdates) && !isset($schemaUpdates['error'])) {
    $schemaUpdateMessages = $schemaUpdates;
}

// Get departments for suggestions
$deptStmt = $conn->prepare("SELECT DISTINCT department FROM faculties WHERE college_id = ? ORDER BY department ASC");
$deptStmt->execute([$college_id]);
$departments = $deptStmt->fetchAll(PDO::FETCH_COLUMN);

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = trim($_POST['name']);
    $email = trim($_POST['email']);
    $password = $_POST['password'];
    $department = trim($_POST['department']);
    $position = trim($_POST['position'] ?? '');
    $bio = trim($_POST['bio'] ?? '');
    $phone = trim($_POST['phone'] ?? '');
    $status = $_POST['status'] ?? 'active';
    $send_credentials = isset($_POST['send_credentials']);

    if (!$name || !$email || !$password || !$department) {
        $error = "Name, email, password, and department are required.";
    } else {
        // Check if email already exists
        $check = $conn->prepare("SELECT COUNT(*) FROM faculties WHERE email = ?");
        $check->execute([$email]);
        $exists = $check->fetchColumn();
        
        if ($exists) {
            $error = "A faculty member with this email already exists.";
        } else {
            try {
        $hashed = password_hash($password, PASSWORD_DEFAULT);
                
                // Build the SQL query with all available fields
                $sql = "INSERT INTO faculties (college_id, name, email, password, department, position, bio, phone, status, created_at) 
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
        $stmt = $conn->prepare($sql);
                $stmt->execute([$_SESSION['college_id'], $name, $email, $hashed, $department, $position, $bio, $phone, $status]);
                $faculty_id = $conn->lastInsertId();
        $success = "Faculty added successfully.";
                
                // Send email with credentials if requested
                if ($send_credentials) {
                    // Get college name
                    $college_name = $college['name'] ?? 'Our College';
                    
                    // Create email body
                    $subject = "Your Faculty Account Credentials for $college_name";
                    $body = "
                        <html>
                        <head>
                            <style>
                                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                                .header { background-color: #0a66c2; color: white; padding: 20px; text-align: center; }
                                .content { padding: 20px; background-color: #f9f9f9; }
                                .credentials { background-color: #fff; padding: 15px; border-left: 4px solid #0a66c2; margin: 20px 0; }
                                .footer { text-align: center; margin-top: 20px; font-size: 12px; color: #666; }
                            </style>
                        </head>
                        <body>
                            <div class='container'>
                                <div class='header'>
                                    <h2>Welcome to $college_name</h2>
                                </div>
                                <div class='content'>
                                    <p>Dear $name,</p>
                                    <p>Your faculty account has been created in the Mentoshri system. Below are your login credentials:</p>
                                    
                                    <div class='credentials'>
                                        <p><strong>Email:</strong> $email</p>
                                        <p><strong>Password:</strong> $password</p>
                                        <p><strong>Department:</strong> $department</p>
                                    </div>
                                    
                                    <p>Please login at: <a href='http://{$_SERVER['HTTP_HOST']}/auth/faculty_login.php'>Faculty Login</a></p>
                                    
                                    <p>We recommend changing your password after the first login.</p>
                                    
                                    <p>Best regards,<br>$admin_name<br>College Administrator</p>
                                </div>
                                <div class='footer'>
                                    <p>This is an automated message. Please do not reply to this email.</p>
                                </div>
                            </div>
                        </body>
                        </html>
                    ";
                    
                    // Send the email
                    $email_sent = sendEmail($email, $full_name ?? $email, $subject, $body);
                    
                    if ($email_sent) {
                        $success .= " Login credentials have been sent to the faculty's email address.";
                    } else {
                        $success .= " However, there was an issue sending the login credentials email.";
                    }
                }
                
                // Clear form data after successful submission
                $name = $email = $password = $department = $position = $bio = $phone = '';
                $status = 'active';
            } catch (PDOException $e) {
                // Fallback to minimal insert if the dynamic approach fails
                $error = "Error adding faculty member: " . $e->getMessage();
                
                try {
                    // Try with only the essential fields
                    $minimalSql = "INSERT INTO faculties (college_id, name, email, password, department, created_at) 
                                  VALUES (?, ?, ?, ?, ?, NOW())";
                    $minimalStmt = $conn->prepare($minimalSql);
                    $minimalStmt->execute([$_SESSION['college_id'], $name, $email, $hashed, $department]);
                    $faculty_id = $conn->lastInsertId();
                    $success = "Faculty added successfully with basic information.";
                    $error = ""; // Clear error if minimal insert succeeds
                    
                    // Send email with credentials if requested
                    if ($send_credentials) {
                        // Get college name
                        $college_name = $college['name'] ?? 'Our College';
                        
                        // Create email body
                        $subject = "Your Faculty Account Credentials for $college_name";
                        $body = "
                            <html>
                            <head>
                                <style>
                                    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                                    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                                    .header { background-color: #0a66c2; color: white; padding: 20px; text-align: center; }
                                    .content { padding: 20px; background-color: #f9f9f9; }
                                    .credentials { background-color: #fff; padding: 15px; border-left: 4px solid #0a66c2; margin: 20px 0; }
                                    .footer { text-align: center; margin-top: 20px; font-size: 12px; color: #666; }
                                </style>
                            </head>
                            <body>
                                <div class='container'>
                                    <div class='header'>
                                        <h2>Welcome to $college_name</h2>
                                    </div>
                                    <div class='content'>
                                        <p>Dear $name,</p>
                                        <p>Your faculty account has been created in the Mentoshri system. Below are your login credentials:</p>
                                        
                                        <div class='credentials'>
                                            <p><strong>Email:</strong> $email</p>
                                            <p><strong>Password:</strong> $password</p>
                                            <p><strong>Department:</strong> $department</p>
                                        </div>
                                        
                                        <p>Please login at: <a href='http://{$_SERVER['HTTP_HOST']}/auth/faculty_login.php'>Faculty Login</a></p>
                                        
                                        <p>We recommend changing your password after the first login.</p>
                                        
                                        <p>Best regards,<br>$admin_name<br>College Administrator</p>
                                    </div>
                                    <div class='footer'>
                                        <p>This is an automated message. Please do not reply to this email.</p>
                                    </div>
                                </div>
                            </body>
                            </html>
                        ";
                        
                        // Send the email
                        $email_sent = sendEmail($email, $full_name ?? $email, $subject, $body);
                        
                        if ($email_sent) {
                            $success .= " Login credentials have been sent to the faculty's email address.";
                        } else {
                            $success .= " However, there was an issue sending the login credentials email.";
                        }
                    }
                    
                    // Clear form data after successful submission
                    $name = $email = $password = $department = $position = $bio = $phone = '';
                    $status = 'active';
                } catch (PDOException $innerEx) {
                    $error = "Could not add faculty member: " . $innerEx->getMessage();
                }
            }
        }
    }
}

// Include the header
require_once '../includes/header.php';
?>

    <!-- Main Content -->
    <main class="flex-grow py-6 px-4">
        <div class="max-w-5xl mx-auto">
            <!-- Page Header -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8">
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="bg-gradient-to-br from-blue-500 to-blue-600 p-3 rounded-xl shadow-lg">
                            <i class="fas fa-user-plus text-white text-xl"></i>
                        </div>
                        <div>
                            <h1 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                                Add New Faculty Member
                                <span class="ml-3 px-3 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 text-sm font-medium rounded-full">
                                    Step 1 of 1
                                </span>
                            </h1>
                            <p class="text-gray-600 dark:text-gray-400 mt-1">
                                Add a faculty member to <span class="font-medium text-blue-600 dark:text-blue-400"><?= htmlspecialchars($college['name'] ?? 'your college') ?></span>
                                <span class="mx-2">•</span>
                                <span class="text-sm"><?= date('l, F j, Y') ?></span>
                            </p>
                        </div>
                    </div>
                    <div class="mt-6 lg:mt-0 flex flex-wrap gap-3">
                        <a href="list_faculty.php" class="btn btn-outline flex items-center text-sm hover:shadow-md transition-all duration-200">
                            <i class="fas fa-arrow-left mr-2"></i> Back to Faculty List
                        </a>
                        <a href="import_faculty.php" class="btn btn-outline flex items-center text-sm hover:shadow-md transition-all duration-200">
                            <i class="fas fa-file-import mr-2"></i> Bulk Import
                        </a>
                    </div>
                </div>

                <!-- Progress Indicator -->
                <div class="mt-6">
                    <div class="flex items-center space-x-4">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                                <i class="fas fa-user text-white text-sm"></i>
                            </div>
                            <span class="ml-2 text-sm font-medium text-blue-600">Personal Info</span>
                        </div>
                        <div class="flex-1 h-1 bg-blue-200 dark:bg-blue-800 rounded-full">
                            <div class="h-1 bg-blue-600 rounded-full" style="width: 100%"></div>
                        </div>
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                                <i class="fas fa-check text-white text-sm"></i>
                            </div>
                            <span class="ml-2 text-sm font-medium text-blue-600">Complete</span>
                        </div>
                    </div>
                </div>
            </div>

<?php if ($error): ?>
                <div class="bg-red-50 dark:bg-red-900/30 border-l-4 border-red-500 text-red-700 dark:text-red-400 p-4 mb-6 flex justify-between items-center" role="alert">
                    <p><?= htmlspecialchars($error) ?></p>
                    <button type="button" class="text-red-700 dark:text-red-400 hover:text-red-900" onclick="this.parentElement.style.display='none';">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="bg-green-50 dark:bg-green-900/30 border-l-4 border-green-500 text-green-700 dark:text-green-400 p-4 mb-6 flex justify-between items-center" role="alert">
                    <div>
                        <p class="font-medium"><?= htmlspecialchars($success) ?></p>
                        <p class="text-sm mt-1">You can now <a href="list_faculty.php" class="underline">view all faculty members</a> or add another one.</p>
                    </div>
                    <button type="button" class="text-green-700 dark:text-green-400 hover:text-green-900" onclick="this.parentElement.style.display='none';">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($schemaUpdateMessages)): ?>
                <div class="bg-blue-50 dark:bg-blue-900/30 border-l-4 border-blue-500 text-blue-700 dark:text-blue-400 p-4 mb-6 flex justify-between items-center" role="alert">
                    <div>
                        <p class="font-medium">Database schema has been updated</p>
                        <ul class="text-sm mt-1 list-disc list-inside">
                            <?php foreach ($schemaUpdateMessages as $message): ?>
                                <li><?= htmlspecialchars($message) ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                    <button type="button" class="text-blue-700 dark:text-blue-400 hover:text-blue-900" onclick="this.parentElement.style.display='none';">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
<?php endif; ?>

            <!-- Faculty Form -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Main Form -->
                <div class="lg:col-span-2">
                    <form method="post" id="facultyForm" class="space-y-8">
                        <!-- Personal Information Card -->
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                            <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
                                <h2 class="text-lg font-semibold text-gray-800 dark:text-white flex items-center">
                                    <i class="fas fa-user mr-3 text-blue-600"></i> Personal Information
                                </h2>
                                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">Basic details about the faculty member</p>
                            </div>

                            <div class="p-6 space-y-6">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div class="form-group">
                                        <label for="name" class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                                            Full Name <span class="text-red-500">*</span>
                                        </label>
                                        <div class="relative">
                                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <i class="fas fa-user text-gray-400"></i>
                                            </div>
                                            <input
                                                type="text"
                                                id="name"
                                                name="name"
                                                class="form-input pl-10 w-full rounded-lg border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                                                value="<?= htmlspecialchars($name ?? '') ?>"
                                                placeholder="Enter faculty member's full name"
                                                required
                                            >
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label for="email" class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                                            Email Address <span class="text-red-500">*</span>
                                        </label>
                                        <div class="relative">
                                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <i class="fas fa-envelope text-gray-400"></i>
                                            </div>
                                            <input
                                                type="email"
                                                id="email"
                                                name="email"
                                                class="form-input pl-10 w-full rounded-lg border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                                                value="<?= htmlspecialchars($email ?? '') ?>"
                                                placeholder="<EMAIL>"
                                                required
                                            >
                                        </div>
                                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-2 flex items-center">
                                            <i class="fas fa-info-circle mr-1"></i>
                                            Will be used as username for login
                                        </p>
                                    </div>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div class="form-group">
                                        <label for="phone" class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                                            Phone Number
                                        </label>
                                        <div class="relative">
                                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <i class="fas fa-phone text-gray-400"></i>
                                            </div>
                                            <input
                                                type="tel"
                                                id="phone"
                                                name="phone"
                                                class="form-input pl-10 w-full rounded-lg border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                                                value="<?= htmlspecialchars($phone ?? '') ?>"
                                                placeholder="e.g. +****************"
                                            >
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label for="status" class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                                            Account Status
                                        </label>
                                        <div class="relative">
                                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <i class="fas fa-toggle-on text-gray-400"></i>
                                            </div>
                                            <select id="status" name="status" class="form-input pl-10 w-full rounded-lg border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200">
                                                <option value="active" <?= (isset($status) && $status === 'active') ? 'selected' : '' ?>>Active</option>
                                                <option value="inactive" <?= (isset($status) && $status === 'inactive') ? 'selected' : '' ?>>Inactive</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Professional Information Card -->
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                            <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
                                <h2 class="text-lg font-semibold text-gray-800 dark:text-white flex items-center">
                                    <i class="fas fa-briefcase mr-3 text-green-600"></i> Professional Information
                                </h2>
                                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">Academic and professional details</p>
                            </div>

                            <div class="p-6 space-y-6">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div class="form-group">
                                        <label for="department" class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                                            Department <span class="text-red-500">*</span>
                                        </label>
                                        <div class="relative">
                                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <i class="fas fa-building text-gray-400"></i>
                                            </div>
                                            <input
                                                type="text"
                                                id="department"
                                                name="department"
                                                class="form-input pl-10 w-full rounded-lg border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                                                value="<?= htmlspecialchars($department ?? '') ?>"
                                                placeholder="e.g. Computer Science"
                                                list="department-list"
                                                required
                                            >
                                        <?php if (!empty($departments)): ?>
                                            <datalist id="department-list">
                                                <?php foreach ($departments as $dept): ?>
                                                    <option value="<?= htmlspecialchars($dept) ?>">
                                                <?php endforeach; ?>
                                            </datalist>
                                        <?php endif; ?>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label for="position" class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                                            Position/Designation
                                        </label>
                                        <div class="relative">
                                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <i class="fas fa-user-tie text-gray-400"></i>
                                            </div>
                                            <input
                                                type="text"
                                                id="position"
                                                name="position"
                                                class="form-input pl-10 w-full rounded-lg border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                                                value="<?= htmlspecialchars($position ?? '') ?>"
                                                placeholder="e.g. Assistant Professor"
                                            >
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="bio" class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                                        Bio / Description
                                    </label>
                                    <div class="relative">
                                        <textarea
                                            id="bio"
                                            name="bio"
                                            class="form-input w-full rounded-lg border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 h-24 resize-none"
                                            placeholder="Brief description or biography of the faculty member"
                                        ><?= htmlspecialchars($bio ?? '') ?></textarea>
                                    </div>
                                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-2 flex items-center">
                                        <i class="fas fa-info-circle mr-1"></i>
                                        Include relevant experience, specializations, or achievements
                                    </p>
                                </div>
                            </div>
                        </div>

                        <!-- Account Information Card -->
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                            <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
                                <h2 class="text-lg font-semibold text-gray-800 dark:text-white flex items-center">
                                    <i class="fas fa-key mr-3 text-orange-600"></i> Account Information
                                </h2>
                                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">Login credentials and access settings</p>
                            </div>

                            <div class="p-6 space-y-6">
                                <div class="form-group">
                                    <label for="password" class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                                        Password <span class="text-red-500">*</span>
                                    </label>
                                    <div class="relative">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <i class="fas fa-lock text-gray-400"></i>
                                        </div>
                                        <input
                                            type="password"
                                            id="password"
                                            name="password"
                                            class="form-input pl-10 pr-32 w-full rounded-lg border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                                            placeholder="Enter secure password"
                                            required
                                            minlength="8"
                                        >
                                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center space-x-2">
                                            <button type="button" id="generatePassword" class="text-blue-600 hover:text-blue-800 text-sm font-medium transition-colors">
                                                Generate
                                            </button>
                                        </div>
                                    </div>
                                    <div class="flex items-center justify-between mt-2">
                                        <div class="text-xs text-gray-500 dark:text-gray-400 flex items-center">
                                            <i class="fas fa-info-circle mr-1"></i>
                                            Minimum 8 characters recommended
                                        </div>
                                        <label class="inline-flex items-center">
                                            <input type="checkbox" id="showPassword" class="form-checkbox h-4 w-4 text-blue-600 rounded focus:ring-blue-500">
                                            <span class="ml-2 text-xs text-gray-500 dark:text-gray-400">Show password</span>
                                        </label>
                                    </div>
                                </div>

                                <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                                    <label class="inline-flex items-start">
                                        <input type="checkbox" id="sendCredentials" name="send_credentials" class="form-checkbox h-5 w-5 text-blue-600 rounded focus:ring-blue-500 mt-0.5">
                                        <div class="ml-3">
                                            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Send login credentials to faculty email</span>
                                            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Faculty will receive an email with their login details</p>
                                        </div>
                                    </label>
                                </div>

                                <!-- Submit Actions -->
                                <div class="border-t border-gray-200 dark:border-gray-700 pt-6 flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
                                    <a href="list_faculty.php" class="btn btn-outline hover:shadow-md transition-all duration-200">
                                        <i class="fas fa-arrow-left mr-2"></i> Back to List
                                    </a>
                                    <div class="flex flex-col sm:flex-row gap-3">
                                        <button type="reset" class="btn btn-outline hover:shadow-md transition-all duration-200">
                                            <i class="fas fa-redo mr-2"></i> Reset Form
                                        </button>
                                        <button type="submit" class="btn btn-primary shadow-lg hover:shadow-xl transition-all duration-200">
                                            <i class="fas fa-user-plus mr-2"></i> Add Faculty Member
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Sidebar -->
                <div class="lg:col-span-1">
                    <div class="space-y-6">
                        <!-- Quick Tips -->
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                            <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
                                <h3 class="text-lg font-semibold text-gray-800 dark:text-white flex items-center">
                                    <i class="fas fa-lightbulb mr-3 text-yellow-600"></i> Quick Tips
                                </h3>
                            </div>
                            <div class="p-6 space-y-4">
                                <div class="flex items-start space-x-3">
                                    <i class="fas fa-check-circle text-green-500 mt-1"></i>
                                    <div>
                                        <p class="text-sm font-medium text-gray-900 dark:text-white">Email Verification</p>
                                        <p class="text-xs text-gray-600 dark:text-gray-400">Use official college email addresses for better security</p>
                                    </div>
                                </div>
                                <div class="flex items-start space-x-3">
                                    <i class="fas fa-check-circle text-green-500 mt-1"></i>
                                    <div>
                                        <p class="text-sm font-medium text-gray-900 dark:text-white">Strong Passwords</p>
                                        <p class="text-xs text-gray-600 dark:text-gray-400">Use the generate button for secure passwords</p>
                                    </div>
                                </div>
                                <div class="flex items-start space-x-3">
                                    <i class="fas fa-check-circle text-green-500 mt-1"></i>
                                    <div>
                                        <p class="text-sm font-medium text-gray-900 dark:text-white">Department Names</p>
                                        <p class="text-xs text-gray-600 dark:text-gray-400">Use consistent department naming across faculty</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                            <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
                                <h3 class="text-lg font-semibold text-gray-800 dark:text-white flex items-center">
                                    <i class="fas fa-bolt mr-3 text-blue-600"></i> Quick Actions
                                </h3>
                            </div>
                            <div class="p-6 space-y-3">
                                <a href="import_faculty.php" class="flex items-center p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                                    <i class="fas fa-file-import text-blue-600 mr-3"></i>
                                    <div>
                                        <p class="text-sm font-medium text-gray-900 dark:text-white">Bulk Import</p>
                                        <p class="text-xs text-gray-600 dark:text-gray-400">Import multiple faculty from CSV</p>
                                    </div>
                                </a>
                                <a href="list_faculty.php" class="flex items-center p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                                    <i class="fas fa-list text-green-600 mr-3"></i>
                                    <div>
                                        <p class="text-sm font-medium text-gray-900 dark:text-white">View All Faculty</p>
                                        <p class="text-xs text-gray-600 dark:text-gray-400">Manage existing faculty members</p>
                                    </div>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Generate strong password
        document.getElementById('generatePassword').addEventListener('click', function() {
            const length = 12;
            const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+";
            let password = "";
            
            for (let i = 0; i < length; i++) {
                const randomIndex = Math.floor(Math.random() * charset.length);
                password += charset[randomIndex];
            }
            
            document.getElementById('password').value = password;
            
            // Optional: Copy to clipboard
            navigator.clipboard.writeText(password).then(() => {
                // Show a toast notification instead of alert
                showToast('Strong password generated and copied to clipboard!', 'success');
            });
        });
        
        // Show/hide password
        document.getElementById('showPassword').addEventListener('change', function() {
            const passwordField = document.getElementById('password');
            passwordField.type = this.checked ? 'text' : 'password';
        });
        
        // Form validation with improved feedback
        document.getElementById('facultyForm').addEventListener('submit', function(e) {
            const name = document.getElementById('name').value.trim();
            const email = document.getElementById('email').value.trim();
            const department = document.getElementById('department').value.trim();
            const password = document.getElementById('password').value;
            let isValid = true;
            
            // Reset previous error states
            document.querySelectorAll('.error-message').forEach(el => el.remove());
            document.querySelectorAll('.border-red-500').forEach(el => el.classList.remove('border-red-500'));
            
            if (!name) {
                showFieldError('name', 'Name is required');
                isValid = false;
            }
            
            if (!email) {
                showFieldError('email', 'Email is required');
                isValid = false;
            } else if (!isValidEmail(email)) {
                showFieldError('email', 'Please enter a valid email address');
                isValid = false;
            }
            
            if (!department) {
                showFieldError('department', 'Department is required');
                isValid = false;
            }
            
            if (!password) {
                showFieldError('password', 'Password is required');
                isValid = false;
            } else if (password.length < 8) {
                showFieldError('password', 'Password should be at least 8 characters long');
                isValid = false;
            }
            
            if (!isValid) {
                e.preventDefault();
                // Scroll to the first error
                const firstError = document.querySelector('.error-message');
                if (firstError) {
                    firstError.parentElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
            }
        });
        
        function showFieldError(fieldId, message) {
            const field = document.getElementById(fieldId);
            field.classList.add('border-red-500');
            
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error-message text-red-500 text-xs mt-1';
            errorDiv.textContent = message;
            
            field.parentNode.appendChild(errorDiv);
        }
        
        function isValidEmail(email) {
            const re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
            return re.test(String(email).toLowerCase());
        }
        
        // Toast notification system
        function showToast(message, type = 'info') {
            // Remove any existing toasts
            const existingToast = document.getElementById('toast');
            if (existingToast) {
                existingToast.remove();
            }
            
            // Create toast element
            const toast = document.createElement('div');
            toast.id = 'toast';
            toast.className = 'fixed bottom-4 right-4 px-4 py-2 rounded-lg shadow-lg z-50 flex items-center';
            
            // Set background color based on type
            if (type === 'success') {
                toast.classList.add('bg-green-500', 'text-white');
                toast.innerHTML = '<i class="fas fa-check-circle mr-2"></i>';
            } else if (type === 'error') {
                toast.classList.add('bg-red-500', 'text-white');
                toast.innerHTML = '<i class="fas fa-exclamation-circle mr-2"></i>';
            } else {
                toast.classList.add('bg-blue-500', 'text-white');
                toast.innerHTML = '<i class="fas fa-info-circle mr-2"></i>';
            }
            
            // Add message
            toast.innerHTML += message;
            
            // Add to document
            document.body.appendChild(toast);
            
            // Remove after 3 seconds
            setTimeout(() => {
                toast.classList.add('opacity-0', 'transition-opacity', 'duration-300');
                setTimeout(() => toast.remove(), 300);
            }, 3000);
        }
    </script>

<?php
// Include the footer
require_once '../includes/footer.php';
?>

