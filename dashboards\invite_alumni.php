<?php
session_start();
require_once '../includes/db.php';
require_once '../includes/db_schema_check.php';
require_once '../notify/send_email.php';

if (!isset($_SESSION['collegeadmin_id'])) {
    header("Location: ../auth/collegeadmin_login.php");
    exit;
}

$college_id = $_SESSION['college_id'];
$admin_name = $_SESSION['collegeadmin_name'] ?? 'Admin';
$error = '';
$success = '';

// Check and add missing columns if they don't exist
try {
    // Check for department column
    $checkColumnStmt = $conn->prepare("SHOW COLUMNS FROM alumni LIKE 'department'");
    $checkColumnStmt->execute();
    
    if ($checkColumnStmt->rowCount() == 0) {
        // Column doesn't exist, add it
        $conn->exec("ALTER TABLE alumni ADD COLUMN department VARCHAR(100) DEFAULT NULL AFTER graduation_year");
    }
    
    // Check for company column
    $checkColumnStmt = $conn->prepare("SHOW COLUMNS FROM alumni LIKE 'company'");
    $checkColumnStmt->execute();
    
    if ($checkColumnStmt->rowCount() == 0) {
        // Column doesn't exist, add it
        $conn->exec("ALTER TABLE alumni ADD COLUMN company VARCHAR(100) DEFAULT NULL AFTER department");
    }
    
    // Check for position column
    $checkColumnStmt = $conn->prepare("SHOW COLUMNS FROM alumni LIKE 'position'");
    $checkColumnStmt->execute();
    
    if ($checkColumnStmt->rowCount() == 0) {
        // Column doesn't exist, add it
        $conn->exec("ALTER TABLE alumni ADD COLUMN position VARCHAR(100) DEFAULT NULL AFTER company");
    }
    
    // Check for industry column
    $checkColumnStmt = $conn->prepare("SHOW COLUMNS FROM alumni LIKE 'industry'");
    $checkColumnStmt->execute();
    
    if ($checkColumnStmt->rowCount() == 0) {
        // Column doesn't exist, add it
        $conn->exec("ALTER TABLE alumni ADD COLUMN industry VARCHAR(100) DEFAULT NULL AFTER position");
    }
} catch (PDOException $e) {
    $error = "Database schema check error: " . $e->getMessage();
}

// Get college details
$collegeStmt = $conn->prepare("SELECT name FROM colleges WHERE id = ?");
$collegeStmt->execute([$college_id]);
$college = $collegeStmt->fetch(PDO::FETCH_ASSOC);

// Get departments for dropdown
$deptStmt = $conn->prepare("SELECT DISTINCT department FROM students WHERE college_id = ? ORDER BY department ASC");
$deptStmt->execute([$college_id]);
$departments = $deptStmt->fetchAll(PDO::FETCH_COLUMN);

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $full_name = trim($_POST['full_name'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $graduation_year = trim($_POST['graduation_year'] ?? '');
    $department = trim($_POST['department'] ?? '');
    $company = trim($_POST['company'] ?? '');
    $position = trim($_POST['position'] ?? '');
    $industry = trim($_POST['industry'] ?? '');
    $message = trim($_POST['message'] ?? '');
    
    // Validate inputs
    if (empty($full_name)) {
        $error = "Full name is required.";
    } elseif (empty($email)) {
        $error = "Email address is required.";
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = "Please enter a valid email address.";
    } elseif (empty($graduation_year)) {
        $error = "Graduation year is required.";
    } elseif (empty($department)) {
        $error = "Department is required.";
    } else {
        // Check if email already exists
        $checkStmt = $conn->prepare("SELECT id FROM alumni WHERE email = ?");
        $checkStmt->execute([$email]);
        $existingAlumni = $checkStmt->fetch(PDO::FETCH_ASSOC);
        
        if ($existingAlumni) {
            $error = "An alumni with this email address already exists.";
        } else {
            // Generate a random password
            $password = bin2hex(random_bytes(4)); // 8 characters - shorter for easier reading
            $hashed_password = password_hash($password, PASSWORD_DEFAULT);
            
            try {
                // Insert new alumni
                $insertStmt = $conn->prepare("INSERT INTO alumni (college_id, full_name, email, password, graduation_year, department, company, position, industry, verified, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 0, NOW())");
                $insertStmt->execute([$college_id, $full_name, $email, $hashed_password, $graduation_year, $department, $company, $position, $industry]);
                
                $alumni_id = $conn->lastInsertId();
                
                // Send invitation email
                $subject = "You've been invited to join " . htmlspecialchars($college['name']) . " Alumni Network";
                
                $emailMessage = "<p>Dear " . htmlspecialchars($full_name) . ",</p>";
                $emailMessage .= "<p>You have been invited to join the alumni network of " . htmlspecialchars($college['name']) . ".</p>";
                
                if (!empty($message)) {
                    $emailMessage .= "<p>Message from the administrator:</p>";
                    $emailMessage .= "<p><em>" . nl2br(htmlspecialchars($message)) . "</em></p>";
                }
                
                $emailMessage .= "<p>To get started, please log in using the credentials below:</p>";
                $emailMessage .= "<div style='background-color: #f0f0f0; padding: 15px; border-left: 4px solid #0a66c2; margin: 20px 0;'>";
                $emailMessage .= "<p style='font-size: 16px;'><strong>Email:</strong> " . htmlspecialchars($email) . "</p>";
                $emailMessage .= "<p style='font-size: 16px;'><strong>Password:</strong> <span style='background-color: #e7f3ff; padding: 3px 6px; border-radius: 3px;'>" . $password . "</span></p>";
                $emailMessage .= "</div>";
                
                $emailMessage .= "<p>Login at: <a href='http://" . $_SERVER['HTTP_HOST'] . "/auth/alumni_login.php' style='color: #0a66c2; text-decoration: underline; font-weight: bold;'>Alumni Login</a></p>";
                
                $emailMessage .= "<p>After logging in, we recommend updating your profile and changing your password.</p>";
                
                $emailMessage .= "<p>Regards,<br>" . htmlspecialchars($admin_name) . "<br>Administration Team<br>" . htmlspecialchars($college['name']) . "</p>";
                
                $email_sent = sendEmail($email, $full_name, $subject, $emailMessage);
                
                if ($email_sent) {
                    $success = "Invitation sent successfully to " . htmlspecialchars($email) . " with login credentials.";
                } else {
                    $success = "Alumni account created, but there was an issue sending the email invitation. Please provide the login credentials manually:<br><strong>Email:</strong> " . htmlspecialchars($email) . "<br><strong>Password:</strong> " . $password;
                }
            } catch (PDOException $e) {
                $error = "Database error: " . $e->getMessage();
            }
        }
    }
}
// Include the header
require_once '../includes/header.php';
?>
    <!-- Main Content -->
    <main class="flex-grow py-6 px-4">
        <div class="max-w-7xl mx-auto">
            <!-- Page Header -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8">
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="bg-gradient-to-br from-purple-500 to-purple-600 p-3 rounded-xl shadow-lg">
                            <i class="fas fa-user-plus text-white text-xl"></i>
                        </div>
                        <div>
                            <h1 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                                Invite Alumni
                            </h1>
                            <nav class="text-sm text-gray-600 dark:text-gray-400 mt-1" aria-label="Breadcrumb">
                                <ol class="flex items-center space-x-2">
                                    <li>
                                        <a href="collegeadmin_dashboard.php" class="hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
                                            Dashboard
                                        </a>
                                    </li>
                                    <li class="flex items-center">
                                        <i class="fas fa-chevron-right text-gray-400 text-xs mx-2"></i>
                                        <a href="list_alumni.php" class="hover:text-blue-600 dark:hover:text-blue-400 transition-colors">Alumni Management</a>
                                    </li>
                                    <li class="flex items-center">
                                        <i class="fas fa-chevron-right text-gray-400 text-xs mx-2"></i>
                                        <span class="text-gray-900 dark:text-white font-medium">Invite Alumni</span>
                                    </li>
                                </ol>
                            </nav>
                            <p class="text-gray-600 dark:text-gray-400 mt-2">
                                Send invitations to alumni to join the network at <span class="font-medium text-purple-600 dark:text-purple-400"><?= htmlspecialchars($college['name'] ?? 'your college') ?></span>
                            </p>
                        </div>
                    </div>
                    <div class="mt-4 lg:mt-0 flex flex-wrap gap-3">
                        <a href="list_alumni.php" class="inline-flex items-center px-4 py-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-medium rounded-lg transition-colors">
                            <i class="fas fa-arrow-left mr-2"></i> Back to Alumni
                        </a>
                        <a href="export_alumni.php" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors">
                            <i class="fas fa-download mr-2"></i> Export Alumni
                        </a>
                    </div>
                </div>
            </div>

            <!-- Alert Messages -->
            <?php if ($error): ?>
                <div class="bg-red-50 dark:bg-red-900/50 border border-red-200 dark:border-red-800 rounded-xl p-4 mb-6">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <i class="fas fa-exclamation-circle text-red-600 dark:text-red-400"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-red-800 dark:text-red-200">Error</h3>
                            <p class="text-sm text-red-700 dark:text-red-300 mt-1"><?= htmlspecialchars($error) ?></p>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <?php if ($success): ?>
                <div class="bg-green-50 dark:bg-green-900/50 border border-green-200 dark:border-green-800 rounded-xl p-4 mb-6">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <i class="fas fa-check-circle text-green-600 dark:text-green-400"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-green-800 dark:text-green-200">Success</h3>
                            <p class="text-sm text-green-700 dark:text-green-300 mt-1"><?= $success ?></p>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Main Content Grid -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Left Column: Invite Form -->
                <div class="lg:col-span-2">
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                            <h2 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                                <i class="fas fa-envelope text-purple-600 mr-2"></i> Send Invitation
                            </h2>
                            <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                                Fill out the form to invite an alumni to join the platform
                            </p>
                        </div>
                        
                        <form method="POST" action="" class="p-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                                <div>
                                    <label for="full_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Full Name <span class="text-red-500">*</span></label>
                                    <input type="text" id="full_name" name="full_name" class="form-input" required>
                                </div>
                                
                                <div>
                                    <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Email Address <span class="text-red-500">*</span></label>
                                    <input type="email" id="email" name="email" class="form-input" required>
                                </div>
                                
                                <div>
                                    <label for="graduation_year" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Graduation Year <span class="text-red-500">*</span></label>
                                    <select id="graduation_year" name="graduation_year" class="form-select" required>
                                        <option value="">Select Year</option>
                                        <?php 
                                        $currentYear = date('Y');
                                        for ($year = $currentYear; $year >= $currentYear - 50; $year--) {
                                            echo "<option value=\"$year\">$year</option>";
                                        }
                                        ?>
                                    </select>
                                </div>
                                
                                <div>
                                    <label for="department" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Department <span class="text-red-500">*</span></label>
                                    <select id="department" name="department" class="form-select" required>
                                        <option value="">Select Department</option>
                                        <?php foreach ($departments as $dept): ?>
                                            <option value="<?= htmlspecialchars($dept) ?>"><?= htmlspecialchars($dept) ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div>
                                    <label for="company" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Current Company</label>
                                    <input type="text" id="company" name="company" class="form-input">
                                </div>
                                
                                <div>
                                    <label for="position" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Current Position</label>
                                    <input type="text" id="position" name="position" class="form-input">
                                </div>
                                
                                <div class="md:col-span-2">
                                    <label for="industry" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Industry</label>
                                    <input type="text" id="industry" name="industry" class="form-input">
                                </div>
                                
                                <div class="md:col-span-2">
                                    <label for="message" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Personal Message (Optional)</label>
                                    <textarea id="message" name="message" rows="4" class="form-textarea" placeholder="Add a personal message to the invitation email..."></textarea>
                                </div>
                            </div>
                            
                            <div class="flex justify-end">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-paper-plane mr-2"></i> Send Invitation
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Tips and Information -->
                <div class="lg:col-span-1">
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6 mb-6">
                        <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4 flex items-center">
                            <i class="fas fa-info-circle text-primary mr-2"></i> About Invitations
                        </h3>
                        
                        <ul class="space-y-3 text-sm text-gray-600 dark:text-gray-400">
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                                <span>Invited alumni will receive an email with login credentials.</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                                <span>They will need to verify their account by logging in.</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                                <span>Alumni can update their profile information after logging in.</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                                <span>Adding a personal message can increase response rates.</span>
                            </li>
                        </ul>
                    </div>
                    
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
                        <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4 flex items-center">
                            <i class="fas fa-users text-primary mr-2"></i> Bulk Invitations
                        </h3>
                        
                        <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
                            Need to invite multiple alumni at once? Use our bulk invitation feature to send invitations to many alumni simultaneously.
                        </p>
                        
                        <a href="bulk_invite_alumni.php" class="btn btn-outline w-full flex items-center justify-center">
                            <i class="fas fa-file-import mr-2"></i> Bulk Invite Alumni
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 py-4 mt-auto">
        <div class="container mx-auto px-4">
            <div class="text-center text-gray-600 dark:text-gray-400 text-sm">
                &copy; <?= date('Y') ?> Mentoshri. All rights reserved.
            </div>
        </div>
    </footer>

    <script>
        // Theme Toggle
        const themeToggle = document.getElementById('themeToggle');
        const htmlElement = document.documentElement;
        
        // Check for saved theme preference or use system preference
        if (localStorage.theme === 'dark' || 
            (!('theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
            htmlElement.classList.add('dark');
        } else {
            htmlElement.classList.remove('dark');
        }
        
        // Toggle theme
        themeToggle.addEventListener('click', () => {
            if (htmlElement.classList.contains('dark')) {
                htmlElement.classList.remove('dark');
                localStorage.theme = 'light';
            } else {
                htmlElement.classList.add('dark');
                localStorage.theme = 'dark';
            }
        });
    </script>

<?php require_once '../includes/footer.php'; ?>