<?php
require_once '../includes/db.php';
require_once '../includes/header.php';
require_once '../includes/contribution_helpers.php';

session_start();
if (!isset($_SESSION['alumni_id'])) {
    header("Location: ../auth/alumni_login.php");
    exit;
}

$alumni_id = $_SESSION['alumni_id'];

// Fetch contributions using helper
$contributions = getPledgesByAlumni($conn, $alumni_id);
?>

<div class="container mt-4">
    <h2>🎁 My Contributions</h2>
    <p class="text-muted">Here you can track your past pledges and their fulfillment status.</p>

    <?php if (count($contributions) === 0): ?>
        <div class="alert alert-info">You haven’t pledged to any campaigns yet.</div>
    <?php endif; ?>

    <?php foreach ($contributions as $contrib): ?>
        <div class="card mb-3 shadow-sm">
            <div class="card-body">
                <h5 class="card-title"><?= htmlspecialchars($contrib['campaign_title']) ?></h5>
                <p class="card-text">
                    <strong>Type:</strong> <?= ucfirst($contrib['pledge_type']) ?><br>
                    <?php if ($contrib['pledge_type'] === 'monetary' && $contrib['amount']): ?>
                        <strong>Amount:</strong> ₹<?= number_format($contrib['amount']) ?><br>
                    <?php endif; ?>
                    <strong>Description:</strong> <?= htmlspecialchars($contrib['description']) ?><br>
                    <strong>Status:</strong> 
                    <span class="badge bg-<?= $contrib['status'] === 'fulfilled' ? 'success' : ($contrib['status'] === 'declined' ? 'danger' : 'warning') ?>">
                        <?= ucfirst($contrib['status']) ?>
                    </span><br>
                    <?php if ($contrib['allow_contact']): ?>
                        <strong>You agreed to be contacted via <?= ucfirst($contrib['contact_preference']) ?></strong><br>
                    <?php endif; ?>
                    <small class="text-muted">Pledged on <?= date("d M Y", strtotime($contrib['created_at'])) ?></small>
                </p>
                <div class="text-end">
                    <a href="mailto:<?= htmlspecialchars($contrib['contact_email']) ?>" class="btn btn-outline-primary btn-sm">Contact Campaign Owner</a>
                </div>
            </div>
        </div>
    <?php endforeach; ?>
</div>

<?php require_once '../includes/footer.php'; ?>
