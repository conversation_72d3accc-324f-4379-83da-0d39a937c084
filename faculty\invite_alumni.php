<?php
use <PERSON><PERSON><PERSON><PERSON><PERSON>\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ailer\PHPMailer\Exception;

require '../vendor/autoload.php'; // Or adjust the path if not using Composer

require_once '../includes/db.php';
require_once '../includes/header.php';

if (!isset($_SESSION['faculty_id'])) {
    header("Location: ../auth/faculty_login.php");
    exit;
}

$success = $error = '';
$generatedToken = '';

function generateToken($length = 32) {
    return bin2hex(random_bytes($length / 2));
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = trim($_POST['email']);
    $phone = trim($_POST['phone']);

    if ($email && $phone) {
        $error = "Please fill only one field — either Email or Phone, not both.";
    } elseif (!$email && !$phone) {
        $error = "Please enter either an email or phone number.";
    } else {
        $token = generateToken();
        $faculty_id = $_SESSION['faculty_id'];
        $college_id = $_SESSION['college_id'];

        $stmt = $conn->prepare("INSERT INTO alumni_invites (college_id, faculty_id, email, phone, token) VALUES (?, ?, ?, ?, ?)");
        $stmt->execute([$college_id, $faculty_id, $email ?: null, $phone ?: null, $token]);

        if ($phone) {
            // Fetch college name
            $collegeNameStmt = $conn->prepare("SELECT name FROM colleges WHERE id = ?");
            $collegeNameStmt->execute([$college_id]);
            $collegeName = $collegeNameStmt->fetchColumn();
            $facultyName = $_SESSION['faculty_name'];
        
            // Redirect to WhatsApp invite with college and faculty details
            echo "<script>
                window.location.href = 'https://wa.me/{$phone}?text=' + encodeURIComponent(
                    '👋 Hi! You’ve been invited to join the Connect My Students mentorship platform by *$facultyName* from *$collegeName*.\\n\\nRegister using this link:\\nhttps://darkviolet-vulture-501696.hostingersite.com//auth/alumni_register.php?token={$token}'
                );
            </script>";
        } else {
            $mail = new PHPMailer(true);

            try {
                // Server settings
                $mail->isSMTP();
                $mail->Host = 'smtp.gmail.com';
                $mail->SMTPAuth = true;
                $mail->Username = '<EMAIL>';
                $mail->Password = 'xoqrvepcjjpuycgo'; // App Password (no spaces)
                $mail->SMTPSecure = 'tls';
                $mail->Port = 587;
            
                $mail->setFrom('<EMAIL>', 'Connect My Students'); // ✅ Update this
                $mail->addAddress($email);
            
                $mail->isHTML(true);
                $mail->Subject = 'You are invited to Connect My Students platform';
                // Fetch faculty name and college name
                $facultyName = $_SESSION['faculty_name'];
                $collegeNameStmt = $conn->prepare("SELECT name FROM colleges WHERE id = ?");
                $collegeNameStmt->execute([$_SESSION['college_id']]);
                $collegeName = $collegeNameStmt->fetchColumn();
                
                $registerLink = "https://darkviolet-vulture-501696.hostingersite.com/auth/alumni_register.php?token=$token";
                
                $mail->Body = "
                    <p>Hi there,</p>
                    <p>You’ve been invited by <strong>$facultyName</strong> from <strong>$collegeName</strong> to join the <strong>Connect My Students</strong> mentorship platform.</p>
                    <p>This platform connects alumni like you with students seeking guidance and support.</p>
                    <p><strong>Click below to join and set up your profile:</strong><br>
                    <a href='$registerLink'>$registerLink</a></p>
                    <p>If you didn’t expect this invite, feel free to ignore it.</p>
                    <br>
                    <p>Regards,<br>Connect My Students Team</p>
                ";
            
                $mail->send();
                $success = "Email invite sent to $email!";
            } catch (Exception $e) {
                $error = "Mailer Error: {$mail->ErrorInfo}";
            }

        }
    }
}
?>

<h2 class="mb-4">Invite New Alumni</h2>

<?php if ($error): ?>
    <div class="alert alert-danger"><?= htmlspecialchars($error) ?></div>
<?php elseif ($success): ?>
    <div class="alert alert-success"><?= htmlspecialchars($success) ?></div>
<?php endif; ?>

<form method="post" class="card p-4 shadow" id="inviteForm">
    <div class="mb-3">
        <label class="form-label">Alumni Email</label>
        <input type="email" name="email" id="emailInput" class="form-control" placeholder="Enter email...">
    </div>

    <div class="text-center mb-2 fw-bold text-secondary">OR</div>

    <div class="mb-3">
        <label class="form-label">Phone Number</label>
        <input type="text" name="phone" id="phoneInput" class="form-control" placeholder="With country code (e.g., 919876543210)">
    </div>

    <button type="submit" class="btn btn-primary w-100" id="inviteBtn" disabled>Send Invite</button>
</form>

<?php require_once '../includes/footer.php'; ?>

<!-- JavaScript for button label & validation -->
<script>
const emailInput = document.getElementById('emailInput');
const phoneInput = document.getElementById('phoneInput');
const inviteBtn = document.getElementById('inviteBtn');

function updateButton() {
    const email = emailInput.value.trim();
    const phone = phoneInput.value.trim();

    if (email && phone) {
        inviteBtn.disabled = true;
        inviteBtn.innerText = 'Select only one!';
        alert("Please fill only one field: Email OR Phone (not both)");
    } else if (phone) {
        inviteBtn.disabled = false;
        inviteBtn.innerText = 'Send WhatsApp Invite';
    } else if (email) {
        inviteBtn.disabled = false;
        inviteBtn.innerText = 'Send Email Invite';
    } else {
        inviteBtn.disabled = true;
        inviteBtn.innerText = 'Send Invite';
    }
}

emailInput.addEventListener('input', updateButton);
phoneInput.addEventListener('input', updateButton);
</script>
