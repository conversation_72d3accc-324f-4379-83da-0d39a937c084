<?php
session_start();
require_once '../includes/db.php';
require_once '../includes/gamify.php'; // Include gamification logic

// Check session
if (isset($_SESSION['faculty_id'])) {
    $role = 'faculty';
    $user_id = $_SESSION['faculty_id'];
    $user_name = $_SESSION['faculty_name'] ?? 'Faculty';
} elseif (isset($_SESSION['alumni_id'])) {
    $role = 'alumni';
    $user_id = $_SESSION['alumni_id'];
    $user_name = $_SESSION['alumni_name'] ?? 'Alumni';
} elseif (isset($_SESSION['collegeadmin_id'])) {
    $role = 'collegeadmin';
    $user_id = $_SESSION['collegeadmin_id'];
    $user_name = $_SESSION['collegeadmin_name'] ?? 'Admin';
} elseif (isset($_SESSION['student_id'])) {
    $role = 'student';
    $user_id = $_SESSION['student_id'];
    $user_name = $_SESSION['student_name'] ?? 'Student';
} else {
    // Redirect to appropriate login page instead of logout
    if (isset($_SERVER['HTTP_REFERER'])) {
        if (strpos($_SERVER['HTTP_REFERER'], 'collegeadmin') !== false) {
            header("Location: ../auth/collegeadmin_login.php");
        } else if (strpos($_SERVER['HTTP_REFERER'], 'faculty') !== false) {
            header("Location: ../auth/faculty_login.php");
        } else if (strpos($_SERVER['HTTP_REFERER'], 'alumni') !== false) {
            header("Location: ../auth/alumni_login.php");
        } else if (strpos($_SERVER['HTTP_REFERER'], 'student') !== false) {
            header("Location: ../auth/student_login.php");
        } else {
            header("Location: ../auth/collegeadmin_login.php");
        }
    } else {
        header("Location: ../auth/collegeadmin_login.php");
    }
    exit;
}
$current_page = basename($_SERVER['PHP_SELF']);
$college_id = $_SESSION['college_id'] ?? 0;

if (!isset($_GET['file'])) {
    // Redirect to resource list with error message
    $_SESSION['flash_error'] = "No file specified for download.";
    header('Location: resource_list.php');
    exit;
}

$file = basename($_GET['file']);

// Verify that the file belongs to the user's college
$stmt = $conn->prepare("SELECT * FROM resources WHERE file_path = ? AND college_id = ?");
$stmt->execute([$file, $college_id]);
$resource = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$resource) {
    // File not found or not authorized
    $_SESSION['flash_error'] = "The requested file was not found or you don't have permission to access it.";
    header('Location: resource_list.php');
    exit;
}

$path = "../uploads/" . $file;

if (file_exists($path)) {
    // Log the download
    $stmt = $conn->prepare("INSERT INTO resource_downloads (resource_id, user_id, user_role, downloaded_at) VALUES (?, ?, ?, NOW())");
    $stmt->execute([$resource['id'], $user_id, $role]);
    
    // Award XP only if student is logged in
    if ($role === 'student') {
        awardXPForResourceDownload($conn, $user_id);
    }

    // Proceed with file download
    header('Content-Description: File Transfer');
    header('Content-Type: application/octet-stream');
    header('Content-Disposition: attachment; filename="' . $file . '"');
    header('Expires: 0');
    header('Cache-Control: must-revalidate');
    header('Pragma: public');
    header('Content-Length: ' . filesize($path));
    readfile($path);
    exit;
} else {
    // Physical file missing
    $_SESSION['flash_error'] = "The file could not be found on the server. Please contact an administrator.";
    header('Location: resource_list.php');
    exit;
}
?>
