<?php
/**
 * Database Schema Check and Update Script
 * 
 * This script checks if required columns exist in the database tables
 * and creates them if they don't exist.
 */

// Function to check if a column exists in a table
function columnExists($conn, $table, $column) {
    try {
        $stmt = $conn->prepare("SHOW COLUMNS FROM $table LIKE ?");
        $stmt->execute([$column]);
        return $stmt->rowCount() > 0;
    } catch (PDOException $e) {
        return false;
    }
}

// Function to check if a table exists
function tableExists($conn, $table) {
    try {
        $stmt = $conn->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$table]);
        return $stmt->rowCount() > 0;
    } catch (PDOException $e) {
        return false;
    }
}

// Function to add a column to a table if it doesn't exist
function addColumnIfNotExists($conn, $table, $column, $definition) {
    if (!columnExists($conn, $table, $column)) {
        try {
            $sql = "ALTER TABLE $table ADD COLUMN $column $definition";
            $conn->exec($sql);
            return true;
        } catch (PDOException $e) {
            return false;
        }
    }
    return false;
}

// Function to check and update the faculties table schema
function checkAndUpdateFacultiesSchema($conn) {
    $updates = [];
    
    // Check if faculties table exists
    if (!tableExists($conn, 'faculties')) {
        return ['error' => 'Faculties table does not exist'];
    }
    
    // Check and add position column if it doesn't exist
    if (addColumnIfNotExists($conn, 'faculties', 'position', 'VARCHAR(100) DEFAULT NULL')) {
        $updates[] = 'Added position column to faculties table';
    }
    
    // Check and add bio column if it doesn't exist
    if (addColumnIfNotExists($conn, 'faculties', 'bio', 'TEXT DEFAULT NULL')) {
        $updates[] = 'Added bio column to faculties table';
    }
    
    // Check and add phone column if it doesn't exist
    if (addColumnIfNotExists($conn, 'faculties', 'phone', 'VARCHAR(20) DEFAULT NULL')) {
        $updates[] = 'Added phone column to faculties table';
    }
    
    // Check and add status column if it doesn't exist
    if (addColumnIfNotExists($conn, 'faculties', 'status', "ENUM('active', 'inactive') DEFAULT 'active'")) {
        $updates[] = 'Added status column to faculties table';
    }
    
    // Check and add profile_image column if it doesn't exist
    if (addColumnIfNotExists($conn, 'faculties', 'profile_image', 'VARCHAR(255) DEFAULT NULL')) {
        $updates[] = 'Added profile_image column to faculties table';
    }
    
    // Check and add updated_at column if it doesn't exist
    if (addColumnIfNotExists($conn, 'faculties', 'updated_at', 'TIMESTAMP NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP')) {
        $updates[] = 'Added updated_at column to faculties table';
    }
    
    return $updates;
}

/**
 * Check and update students table schema to include all necessary columns
 * 
 * @param PDO $conn Database connection
 * @return bool True if updates were made, false otherwise
 */
function checkAndUpdateStudentsSchema($conn) {
    $updates = false;
    
    // Define columns that should exist in the students table
    $requiredColumns = [
        'gender' => "ALTER TABLE students ADD COLUMN gender VARCHAR(20) NULL AFTER date_of_birth",
        'bio' => "ALTER TABLE students ADD COLUMN bio TEXT NULL AFTER gender",
        'skills' => "ALTER TABLE students ADD COLUMN skills TEXT NULL AFTER bio",
        'address' => "ALTER TABLE students ADD COLUMN address TEXT NULL AFTER phone",
        'city' => "ALTER TABLE students ADD COLUMN city VARCHAR(100) NULL AFTER address",
        'state' => "ALTER TABLE students ADD COLUMN state VARCHAR(100) NULL AFTER city",
        'country' => "ALTER TABLE students ADD COLUMN country VARCHAR(100) NULL AFTER state",
        'pincode' => "ALTER TABLE students ADD COLUMN pincode VARCHAR(20) NULL AFTER country",
        'date_of_birth' => "ALTER TABLE students ADD COLUMN date_of_birth DATE NULL AFTER pincode",
        'updated_at' => "ALTER TABLE students ADD COLUMN updated_at TIMESTAMP NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP AFTER created_at"
    ];
    
    // Check each column and add if missing
    foreach ($requiredColumns as $column => $alterSql) {
        try {
            $checkColumnStmt = $conn->prepare("SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS 
                                             WHERE TABLE_NAME = 'students' AND COLUMN_NAME = ?");
            $checkColumnStmt->execute([$column]);
            $columnExists = $checkColumnStmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$columnExists) {
                // Add the missing column
                $conn->exec($alterSql);
                $updates = true;
            }
        } catch (PDOException $e) {
            // Log error but continue with other columns
            error_log("Error checking/adding column $column: " . $e->getMessage());
        }
    }
    
    return $updates;
}

// Function to check and update the alumni table schema
function checkAndUpdateAlumniSchema($conn) {
    $updates = [];
    
    // Check if alumni table exists
    if (!tableExists($conn, 'alumni')) {
        return ['error' => 'Alumni table does not exist'];
    }
    
    // Check and add position column if it doesn't exist
    if (addColumnIfNotExists($conn, 'alumni', 'current_position', 'VARCHAR(100) DEFAULT NULL')) {
        $updates[] = 'Added current_position column to alumni table';
    }
    
    // Check and add company column if it doesn't exist
    if (addColumnIfNotExists($conn, 'alumni', 'company', 'VARCHAR(100) DEFAULT NULL')) {
        $updates[] = 'Added company column to alumni table';
    }
    
    // Check and add phone column if it doesn't exist
    if (addColumnIfNotExists($conn, 'alumni', 'phone', 'VARCHAR(20) DEFAULT NULL')) {
        $updates[] = 'Added phone column to alumni table';
    }
    
    // Check and add status column if it doesn't exist
    if (addColumnIfNotExists($conn, 'alumni', 'status', "ENUM('active', 'inactive', 'pending') DEFAULT 'pending'")) {
        $updates[] = 'Added status column to alumni table';
    }
    
    // Check and add profile_image column if it doesn't exist
    if (addColumnIfNotExists($conn, 'alumni', 'profile_image', 'VARCHAR(255) DEFAULT NULL')) {
        $updates[] = 'Added profile_image column to alumni table';
    }
    
    // Check and add bio column if it doesn't exist
    if (addColumnIfNotExists($conn, 'alumni', 'bio', 'TEXT DEFAULT NULL')) {
        $updates[] = 'Added bio column to alumni table';
    }
    
    // Check and add graduation_year column if it doesn't exist
    if (addColumnIfNotExists($conn, 'alumni', 'graduation_year', 'INT(4) DEFAULT NULL')) {
        $updates[] = 'Added graduation_year column to alumni table';
    }
    
    return $updates;
}

// Add this function to check and create the mentorships table if it doesn't exist
function checkAndCreateMentorshipsTable($conn) {
    $messages = [];
    
    // Check if mentorships table exists
    $tableCheckStmt = $conn->prepare("SHOW TABLES LIKE 'mentorships'");
    $tableCheckStmt->execute();
    $mentorshipTableExists = $tableCheckStmt->rowCount() > 0;
    
    if (!$mentorshipTableExists) {
        try {
            // Create mentorships table
            $createTableSQL = "CREATE TABLE `mentorships` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `student_id` int(11) DEFAULT NULL,
                `faculty_id` int(11) DEFAULT NULL,
                `alumni_id` int(11) DEFAULT NULL,
                `status` enum('active','completed','cancelled') DEFAULT 'active',
                `start_date` date DEFAULT NULL,
                `end_date` date DEFAULT NULL,
                `goals` text DEFAULT NULL,
                `notes` text DEFAULT NULL,
                `created_at` timestamp NULL DEFAULT current_timestamp(),
                `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
                PRIMARY KEY (`id`),
                KEY `student_id` (`student_id`),
                KEY `faculty_id` (`faculty_id`),
                KEY `alumni_id` (`alumni_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";
            
            $conn->exec($createTableSQL);
            
            // Add foreign key constraints if possible
            try {
                $conn->exec("ALTER TABLE `mentorships` 
                    ADD CONSTRAINT `mentorships_ibfk_1` FOREIGN KEY (`student_id`) REFERENCES `students` (`id`) ON DELETE SET NULL,
                    ADD CONSTRAINT `mentorships_ibfk_2` FOREIGN KEY (`faculty_id`) REFERENCES `faculties` (`id`) ON DELETE SET NULL,
                    ADD CONSTRAINT `mentorships_ibfk_3` FOREIGN KEY (`alumni_id`) REFERENCES `alumni` (`id`) ON DELETE SET NULL;");
            } catch (PDOException $e) {
                // If adding constraints fails, continue without them
                $messages[] = "Created mentorships table without foreign key constraints: " . $e->getMessage();
            }
            
            $messages[] = "Created mentorships table successfully";
        } catch (PDOException $e) {
            $messages['error'] = "Failed to create mentorships table: " . $e->getMessage();
        }
    }
    
    return $messages;
}

// Run the schema checks if this file is accessed directly
if (basename($_SERVER['PHP_SELF']) == basename(__FILE__)) {
    require_once 'db.php';
    
    $results = [];
    $results['faculties'] = checkAndUpdateFacultiesSchema($conn);
    $results['students'] = checkAndUpdateStudentsSchema($conn);
    $results['alumni'] = checkAndUpdateAlumniSchema($conn);
    
    // Output results
    header('Content-Type: application/json');
    echo json_encode($results, JSON_PRETTY_PRINT);
}
?> 