<?php
session_start();
require_once '../includes/db.php';

if (!isset($_SESSION['superadmin_id'])) {
    header("Location: ../auth/superadmin_login.php");
    exit;
}

$college_id = $_GET['id'] ?? null;
if (!$college_id) {
    $_SESSION['flash_message'] = "Invalid college ID.";
    header("Location: superadmin_dashboard.php");
    exit;
}

// Fetch college details
$stmt = $conn->prepare("SELECT * FROM colleges WHERE id = ?");
$stmt->execute([$college_id]);
$college = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$college) {
    $_SESSION['flash_message'] = "College not found.";
    header("Location: superadmin_dashboard.php");
    exit;
}

// Fetch college admins
$stmt = $conn->prepare("SELECT * FROM college_admins WHERE college_id = ? ORDER BY created_at DESC");
$stmt->execute([$college_id]);
$admins = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Count students
$stmt = $conn->prepare("SELECT COUNT(*) FROM students WHERE college_id = ?");
$stmt->execute([$college_id]);
$studentCount = $stmt->fetchColumn();

// Count alumni
$stmt = $conn->prepare("SELECT COUNT(*) FROM alumni WHERE college_id = ?");
$stmt->execute([$college_id]);
$alumniCount = $stmt->fetchColumn();

// Count faculty
$stmt = $conn->prepare("SELECT COUNT(*) FROM faculties WHERE college_id = ?");
$stmt->execute([$college_id]);
$facultyCount = $stmt->fetchColumn();

// Get recent students
$stmt = $conn->prepare("SELECT * FROM students WHERE college_id = ? ORDER BY created_at DESC LIMIT 5");
$stmt->execute([$college_id]);
$recentStudents = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get recent alumni
$stmt = $conn->prepare("SELECT * FROM alumni WHERE college_id = ? ORDER BY created_at DESC LIMIT 5");
$stmt->execute([$college_id]);
$recentAlumni = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($college['name']) ?> - Mentoshri</title>
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts - Inter -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        'primary': '#0a66c2',
                        'primary-dark': '#004182',
                        'primary-light': '#e7f3ff',
                    },
                    fontFamily: {
                        'sans': ['Inter', 'sans-serif'],
                    },
                }
            }
        }
    </script>
    <!-- Design System -->
    <link rel="stylesheet" href="../assets/css/design-system.css">
</head>
<body class="font-sans bg-gray-50 dark:bg-gray-900 min-h-screen flex flex-col">
    <!-- Header -->
    <header class="bg-white dark:bg-gray-800 shadow-sm">
        <div class="container mx-auto px-4 py-3 flex items-center justify-between">
            <div class="flex items-center">
                <a href="superadmin_dashboard.php" class="text-primary font-bold text-2xl">Mentoshri</a>
            </div>
            
            <div class="flex items-center space-x-4">
                <button id="themeToggle" class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700">
                    <i class="fas fa-moon dark:hidden"></i>
                    <i class="fas fa-sun hidden dark:block"></i>
                </button>
                <a href="superadmin_dashboard.php" class="text-gray-600 dark:text-gray-300 hover:text-primary dark:hover:text-primary">
                    <i class="fas fa-arrow-left mr-2"></i> Back to Dashboard
                </a>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="flex-grow py-8 px-4">
        <div class="container mx-auto">
            <!-- College Header -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 mb-6 overflow-hidden">
                <div class="h-32 bg-gradient-to-r from-primary to-primary-dark"></div>
                <div class="px-6 py-4 flex flex-col md:flex-row md:items-center md:justify-between">
                    <div class="flex items-center">
                        <div class="w-16 h-16 bg-primary-light dark:bg-primary-dark/30 rounded-lg -mt-12 shadow-md flex items-center justify-center">
                            <i class="fas fa-university text-primary text-3xl"></i>
                        </div>
                        <div class="ml-4">
                            <h1 class="text-2xl font-bold text-gray-900 dark:text-white"><?= htmlspecialchars($college['name']) ?></h1>
                            <p class="text-gray-600 dark:text-gray-400 text-sm">Slug: <?= htmlspecialchars($college['slug']) ?></p>
                        </div>
                    </div>
                    <div class="mt-4 md:mt-0 flex space-x-3">
                        <a href="edit_college.php?id=<?= $college['id'] ?>" class="btn btn-outline flex items-center">
                            <i class="fas fa-edit mr-2"></i> Edit College
                        </a>
                        <a href="assign_admin.php?college_id=<?= $college['id'] ?>" class="btn btn-primary flex items-center">
                            <i class="fas fa-user-plus mr-2"></i> Add Admin
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Stats Overview -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                <!-- Total Admins -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
                    <div class="flex items-center">
                        <div class="bg-blue-100 dark:bg-blue-900/30 p-3 rounded-full">
                            <i class="fas fa-user-shield text-blue-600 dark:text-blue-400 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">College Admins</h3>
                            <p class="text-2xl font-bold text-gray-900 dark:text-white"><?= count($admins) ?></p>
                        </div>
                    </div>
                </div>
                
                <!-- Total Faculty -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
                    <div class="flex items-center">
                        <div class="bg-yellow-100 dark:bg-yellow-900/30 p-3 rounded-full">
                            <i class="fas fa-chalkboard-teacher text-yellow-600 dark:text-yellow-400 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Faculty</h3>
                            <p class="text-2xl font-bold text-gray-900 dark:text-white"><?= $facultyCount ?></p>
                        </div>
                    </div>
                </div>
                
                <!-- Total Students -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
                    <div class="flex items-center">
                        <div class="bg-green-100 dark:bg-green-900/30 p-3 rounded-full">
                            <i class="fas fa-user-graduate text-green-600 dark:text-green-400 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Students</h3>
                            <p class="text-2xl font-bold text-gray-900 dark:text-white"><?= $studentCount ?></p>
                        </div>
                    </div>
                </div>
                
                <!-- Total Alumni -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
                    <div class="flex items-center">
                        <div class="bg-purple-100 dark:bg-purple-900/30 p-3 rounded-full">
                            <i class="fas fa-user-tie text-purple-600 dark:text-purple-400 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Alumni</h3>
                            <p class="text-2xl font-bold text-gray-900 dark:text-white"><?= $alumniCount ?></p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Main Content Grid -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Left Column -->
                <div class="lg:col-span-2">
                    <!-- College Admins -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 mb-6">
                        <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4 flex justify-between items-center">
                            <h2 class="font-semibold text-gray-800 dark:text-white">College Administrators</h2>
                            <a href="assign_admin.php?college_id=<?= $college['id'] ?>" class="text-sm text-primary hover:underline">
                                <i class="fas fa-plus"></i> Add Admin
                            </a>
                        </div>
                        <div class="p-6">
                            <?php if (count($admins) > 0): ?>
                                <div class="space-y-4">
                                    <?php foreach ($admins as $admin): ?>
                                        <div class="flex items-center p-3 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700">
                                            <div class="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center text-blue-600 dark:text-blue-400 mr-4">
                                                <i class="fas fa-user-shield"></i>
                                            </div>
                                            <div class="flex-grow">
                                                <h4 class="font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($admin['name']) ?></h4>
                                                <p class="text-sm text-gray-500 dark:text-gray-400"><?= htmlspecialchars($admin['email']) ?></p>
                                            </div>
                                            <a href="edit_collegeadmin.php?id=<?= $admin['id'] ?>" class="text-primary hover:text-primary-dark">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php else: ?>
                                <div class="text-center py-6">
                                    <div class="w-12 h-12 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                                        <i class="fas fa-user-shield text-gray-400 text-xl"></i>
                                    </div>
                                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No admins assigned</h3>
                                    <p class="text-gray-500 dark:text-gray-400 mb-4">This college doesn't have any administrators yet.</p>
                                    <a href="assign_admin.php?college_id=<?= $college['id'] ?>" class="btn btn-primary">
                                        <i class="fas fa-user-plus mr-2"></i> Assign Admin
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <!-- Recent Students -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 mb-6">
                        <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
                            <h2 class="font-semibold text-gray-800 dark:text-white">Recent Students</h2>
                        </div>
                        <div class="p-6">
                            <?php if (count($recentStudents) > 0): ?>
                                <div class="space-y-4">
                                    <?php foreach ($recentStudents as $student): ?>
                                        <div class="flex items-center p-3 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700">
                                            <div class="w-10 h-10 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center text-green-600 dark:text-green-400 mr-4">
                                                <i class="fas fa-user-graduate"></i>
                                            </div>
                                            <div class="flex-grow">
                                                <h4 class="font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($student['full_name']) ?></h4>
                                                <div class="flex items-center text-sm text-gray-500 dark:text-gray-400">
                                                    <span class="mr-3"><?= htmlspecialchars($student['department']) ?></span>
                                                    <span class="bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 text-xs px-2 py-0.5 rounded">
                                                        Year <?= htmlspecialchars($student['year']) ?>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                                <?php if ($studentCount > 5): ?>
                                    <div class="mt-4 text-center">
                                        <a href="#" class="text-primary hover:underline text-sm">View all <?= $studentCount ?> students</a>
                                    </div>
                                <?php endif; ?>
                            <?php else: ?>
                                <div class="text-center py-6">
                                    <p class="text-gray-500 dark:text-gray-400">No students registered yet.</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <!-- Right Column -->
                <div>
                    <!-- College Details -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 mb-6">
                        <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
                            <h2 class="font-semibold text-gray-800 dark:text-white">College Details</h2>
                        </div>
                        <div class="p-6">
                            <div class="space-y-4">
                                <div>
                                    <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">College ID</h3>
                                    <p class="text-gray-900 dark:text-white"><?= $college['id'] ?></p>
                                </div>
                                <div>
                                    <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Name</h3>
                                    <p class="text-gray-900 dark:text-white"><?= htmlspecialchars($college['name']) ?></p>
                                </div>
                                <div>
                                    <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Slug</h3>
                                    <p class="text-gray-900 dark:text-white"><?= htmlspecialchars($college['slug']) ?></p>
                                </div>
                                <div>
                                    <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Created On</h3>
                                    <p class="text-gray-900 dark:text-white"><?= date('F j, Y', strtotime($college['created_at'])) ?></p>
                                </div>
                                <div>
                                    <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Last Updated</h3>
                                    <p class="text-gray-900 dark:text-white"><?= date('F j, Y', strtotime($college['updated_at'] ?? $college['created_at'])) ?></p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Recent Alumni -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700">
                        <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
                            <h2 class="font-semibold text-gray-800 dark:text-white">Recent Alumni</h2>
                        </div>
                        <div class="p-6">
                            <?php if (count($recentAlumni) > 0): ?>
                                <div class="space-y-4">
                                    <?php foreach ($recentAlumni as $alumni): ?>
                                        <div class="flex items-center p-3 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700">
                                            <div class="w-10 h-10 rounded-full bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center text-purple-600 dark:text-purple-400 mr-4">
                                                <i class="fas fa-user-tie"></i>
                                            </div>
                                            <div class="flex-grow">
                                                <h4 class="font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($alumni['full_name']) ?></h4>
                                                <p class="text-sm text-gray-500 dark:text-gray-400">
                                                    <?= htmlspecialchars($alumni['graduation_year']) ?> Graduate
                                                </p>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                                <?php if ($alumniCount > 5): ?>
                                    <div class="mt-4 text-center">
                                        <a href="#" class="text-primary hover:underline text-sm">View all <?= $alumniCount ?> alumni</a>
                                    </div>
                                <?php endif; ?>
                            <?php else: ?>
                                <div class="text-center py-6">
                                    <p class="text-gray-500 dark:text-gray-400">No alumni registered yet.</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 py-4 mt-6">
        <div class="container mx-auto px-4">
            <div class="text-center text-gray-600 dark:text-gray-400 text-sm">
                &copy; <?= date('Y') ?> Mentoshri. All rights reserved.
            </div>
        </div>
    </footer>

    <script>
        // Theme Toggle
        const themeToggle = document.getElementById('themeToggle');
        const htmlElement = document.documentElement;
        
        // Check for saved theme preference or use system preference
        if (localStorage.theme === 'dark' || 
            (!('theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
            htmlElement.classList.add('dark');
        } else {
            htmlElement.classList.remove('dark');
        }
        
        // Toggle theme
        themeToggle.addEventListener('click', () => {
            if (htmlElement.classList.contains('dark')) {
                htmlElement.classList.remove('dark');
                localStorage.theme = 'light';
            } else {
                htmlElement.classList.add('dark');
                localStorage.theme = 'dark';
            }
        });
    </script>
</body>
</html> 