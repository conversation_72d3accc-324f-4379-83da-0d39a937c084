<?php
// Include session configuration
require_once '../includes/session_config.php';
require_once '../includes/db.php';
require_once '../includes/db_schema_check.php';

if (!isset($_SESSION['collegeadmin_id'])) {
    header("Location: ../auth/collegeadmin_login.php");
    exit;
}

$college_id = $_SESSION['college_id'];
$admin_name = $_SESSION['collegeadmin_name'] ?? 'Admin';

// Set role explicitly for the sidebar
$role = 'collegeadmin';
$user_id = $_SESSION['collegeadmin_id'];
$user_name = $_SESSION['collegeadmin_name'];
$current_page = basename($_SERVER['PHP_SELF']);

// Pagination setup
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 10; // Items per page
$offset = ($page - 1) * $limit;

// Search and filter functionality
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$department_filter = isset($_GET['department']) ? trim($_GET['department']) : '';
$year_filter = isset($_GET['year']) ? trim($_GET['year']) : '';
$verified_filter = isset($_GET['verified']) ? $_GET['verified'] : '';

// Build query conditions
$conditions = ["college_id = ?"];
$params = [$college_id];

if (!empty($search)) {
    $conditions[] = "(full_name LIKE ? OR email LIKE ? OR roll_number LIKE ?)";
    $searchParam = "%$search%";
    $params = array_merge($params, [$searchParam, $searchParam, $searchParam]);
}

if (!empty($department_filter)) {
    $conditions[] = "department = ?";
    $params[] = $department_filter;
}

if (!empty($year_filter)) {
    $conditions[] = "year = ?";
    $params[] = $year_filter;
}

if ($verified_filter !== '') {
    $conditions[] = "verified = ?";
    $params[] = $verified_filter;
}

$whereClause = implode(' AND ', $conditions);

// Count total students for pagination
$countSql = "SELECT COUNT(*) FROM students WHERE $whereClause";
$countStmt = $conn->prepare($countSql);
$countStmt->execute($params);
$totalStudents = $countStmt->fetchColumn();
$totalPages = ceil($totalStudents / $limit);

// Fetch students with pagination and filters
$sql = "SELECT id, college_id, full_name, email, roll_number, department, year, division, profile_image, verified, status, created_at FROM students WHERE $whereClause ORDER BY created_at DESC LIMIT $limit OFFSET $offset";
$stmt = $conn->prepare($sql);
$stmt->execute($params);
$students = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get departments and years for filters
$deptStmt = $conn->prepare("SELECT DISTINCT department FROM students WHERE college_id = ? ORDER BY department ASC");
$deptStmt->execute([$college_id]);
$departments = $deptStmt->fetchAll(PDO::FETCH_COLUMN);

$yearStmt = $conn->prepare("SELECT DISTINCT year FROM students WHERE college_id = ? ORDER BY year ASC");
$yearStmt->execute([$college_id]);
$years = $yearStmt->fetchAll(PDO::FETCH_COLUMN);

// Get college details
$collegeStmt = $conn->prepare("SELECT name FROM colleges WHERE id = ?");
$collegeStmt->execute([$college_id]);
$college = $collegeStmt->fetch(PDO::FETCH_ASSOC);

// Check for flash message
$flash_message = '';
$flash_type = '';
if (isset($_SESSION['flash_message'])) {
    $flash_message = $_SESSION['flash_message'];
    $flash_type = $_SESSION['flash_type'] ?? 'info';
    unset($_SESSION['flash_message']);
    unset($_SESSION['flash_type']);
}

// Include the header
require_once '../includes/header.php';
?>

<!-- Main Content -->
<main class="flex-grow py-8 px-4">
    <div class="container mx-auto">
        <!-- Breadcrumb -->
        <nav class="mb-6 text-sm" aria-label="Breadcrumb">
            <ol class="flex items-center space-x-2">
                <li>
                    <a href="collegeadmin_dashboard.php" class="text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary-light">
                        <i class="fas fa-home"></i>
                        <span class="sr-only">Dashboard</span>
                    </a>
                </li>
                <li class="flex items-center">
                    <i class="fas fa-chevron-right text-gray-400 text-xs mx-1"></i>
                    <span class="text-gray-700 dark:text-gray-300">Student Management</span>
                </li>
            </ol>
        </nav>

        <!-- Flash Message -->
        <?php if (!empty($flash_message)): ?>
            <?php 
                $alertClass = 'bg-blue-50 dark:bg-blue-900/30 border-blue-500 text-blue-700 dark:text-blue-400';
                $iconClass = 'fas fa-info-circle';
                
                if ($flash_type === 'success') {
                    $alertClass = 'bg-green-50 dark:bg-green-900/30 border-green-500 text-green-700 dark:text-green-400';
                    $iconClass = 'fas fa-check-circle';
                } elseif ($flash_type === 'error') {
                    $alertClass = 'bg-red-50 dark:bg-red-900/30 border-red-500 text-red-700 dark:text-red-400';
                    $iconClass = 'fas fa-exclamation-circle';
                } elseif ($flash_type === 'warning') {
                    $alertClass = 'bg-yellow-50 dark:bg-yellow-900/30 border-yellow-500 text-yellow-700 dark:text-yellow-400';
                    $iconClass = 'fas fa-exclamation-triangle';
                }
            ?>
            <div class="<?= $alertClass ?> border-l-4 p-4 mb-6 flex justify-between items-center" role="alert">
                <div class="flex items-center">
                    <i class="<?= $iconClass ?> mr-2"></i>
                    <span><?= $flash_message ?></span>
                </div>
                <button type="button" class="text-current hover:opacity-75" onclick="this.parentElement.style.display='none';">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        <?php endif; ?>

        <!-- Page Header -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                <div class="flex items-center space-x-4">
                    <div class="bg-gradient-to-br from-blue-500 to-blue-600 p-3 rounded-xl shadow-lg">
                        <i class="fas fa-user-graduate text-white text-xl"></i>
                    </div>
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                            Student Management
                        </h1>
                        <p class="text-gray-600 dark:text-gray-400 mt-1">
                            Manage all students at <span class="font-medium text-blue-600 dark:text-blue-400"><?= htmlspecialchars($college['name'] ?? 'your college') ?></span>
                            <span class="mx-2">•</span>
                            <span class="text-sm"><?= date('l, F j, Y') ?></span>
                        </p>
                    </div>
                </div>
                <div class="mt-6 lg:mt-0 flex flex-wrap gap-3">
                    <a href="add_student.php" class="btn btn-primary flex items-center text-sm hover:shadow-md transition-all duration-200">
                        <i class="fas fa-user-plus mr-2"></i> Add New Student
                    </a>
                    <a href="import_students.php" class="btn btn-outline flex items-center text-sm hover:shadow-md transition-all duration-200">
                        <i class="fas fa-file-import mr-2"></i> Import Students
                    </a>
                    <div class="relative group">
                        <button class="btn btn-outline flex items-center text-sm hover:shadow-md transition-all duration-200">
                            <i class="fas fa-file-export mr-2"></i> Export Students
                            <i class="fas fa-chevron-down ml-2 text-xs"></i>
                        </button>
                        <div class="absolute right-0 mt-2 w-56 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 hidden group-hover:block z-10">
                            <div class="py-2">
                                <a href="export_students.php" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                    <i class="fas fa-cog mr-2"></i> Configure Export
                                </a>
                                <button onclick="exportWithCurrentFilters()" class="w-full text-left block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                    <i class="fas fa-download mr-2"></i> Quick Export (Current Filters)
                                </button>
                                <div class="border-t border-gray-200 dark:border-gray-700 my-1"></div>
                                <a href="export_students.php?export=csv&department=all&verified=1&fields[]=full_name&fields[]=email&fields[]=department&fields[]=year&fields[]=verified" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                    <i class="fas fa-users mr-2"></i> Export Verified Students
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <?php
            // Get student statistics
            $statsStmt = $conn->prepare("
                SELECT
                    COUNT(*) as total_students,
                    COUNT(CASE WHEN verified = 1 THEN 1 END) as verified_students,
                    COUNT(CASE WHEN verified = 0 THEN 1 END) as pending_students,
                    COUNT(DISTINCT department) as total_departments,
                    COUNT(DISTINCT year) as total_years
                FROM students
                WHERE college_id = ?
            ");
            $statsStmt->execute([$college_id]);
            $stats = $statsStmt->fetch(PDO::FETCH_ASSOC);
            ?>

            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center mr-4">
                        <i class="fas fa-users text-blue-600 dark:text-blue-400 text-xl"></i>
                    </div>
                    <div>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white"><?= $stats['total_students'] ?></p>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Total Students</p>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center mr-4">
                        <i class="fas fa-user-check text-green-600 dark:text-green-400 text-xl"></i>
                    </div>
                    <div>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white"><?= $stats['verified_students'] ?></p>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Verified Students</p>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center mr-4">
                        <i class="fas fa-user-clock text-orange-600 dark:text-orange-400 text-xl"></i>
                    </div>
                    <div>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white"><?= $stats['pending_students'] ?></p>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Pending Verification</p>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center mr-4">
                        <i class="fas fa-building text-purple-600 dark:text-purple-400 text-xl"></i>
                    </div>
                    <div>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white"><?= $stats['total_departments'] ?></p>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Departments</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Search and Filters -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 mb-8">
            <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
                <h2 class="text-lg font-semibold text-gray-800 dark:text-white flex items-center">
                    <i class="fas fa-filter mr-3 text-primary"></i> Search & Filter Students
                </h2>
            </div>

            <div class="p-6">
                <form method="GET" class="space-y-4" id="searchForm">
                    <!-- Main Search Bar -->
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                            <i class="fas fa-search text-gray-400"></i>
                        </div>
                        <input
                            type="text"
                            name="search"
                            id="searchInput"
                            value="<?= htmlspecialchars($search) ?>"
                            class="form-input pl-12 pr-4 py-3 w-full text-base rounded-lg border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-primary focus:border-primary"
                            placeholder="Search by name, email, or roll number..."
                        >
                    </div>

                    <!-- Filter Options -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <!-- Department Filter -->
                        <div class="relative">
                            <label for="departmentFilter" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Department</label>
                            <div class="absolute inset-y-0 left-0 pl-3 pt-7 flex items-center pointer-events-none">
                                <i class="fas fa-building text-primary"></i>
                            </div>
                            <select name="department" id="departmentFilter" class="form-input pl-10 w-full appearance-none">
                                <option value="">All Departments</option>
                                <?php foreach ($departments as $dept): ?>
                                    <option value="<?= htmlspecialchars($dept) ?>" <?= $department_filter === $dept ? 'selected' : '' ?>><?= htmlspecialchars($dept) ?></option>
                                <?php endforeach; ?>
                            </select>
                            <div class="absolute inset-y-0 right-0 pr-3 pt-7 flex items-center pointer-events-none">
                                <i class="fas fa-chevron-down text-gray-400"></i>
                            </div>
                        </div>

                        <!-- Year Filter -->
                        <div class="relative">
                            <label for="yearFilter" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Academic Year</label>
                            <div class="absolute inset-y-0 left-0 pl-3 pt-7 flex items-center pointer-events-none">
                                <i class="fas fa-calendar-alt text-primary"></i>
                            </div>
                            <select name="year" id="yearFilter" class="form-input pl-10 w-full appearance-none">
                                <option value="">All Years</option>
                                <?php for ($i = 1; $i <= 4; $i++): ?>
                                    <option value="<?= $i ?>" <?= $year_filter == $i ? 'selected' : '' ?>>Year <?= $i ?></option>
                                <?php endfor; ?>
                            </select>
                            <div class="absolute inset-y-0 right-0 pr-3 pt-7 flex items-center pointer-events-none">
                                <i class="fas fa-chevron-down text-gray-400"></i>
                            </div>
                        </div>

                        <!-- Status Filter -->
                        <div class="relative">
                            <label for="statusFilter" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Status</label>
                            <div class="absolute inset-y-0 left-0 pl-3 pt-7 flex items-center pointer-events-none">
                                <i class="fas fa-check-circle text-primary"></i>
                            </div>
                            <select name="verified" id="statusFilter" class="form-input pl-10 w-full appearance-none">
                                <option value="">All Statuses</option>
                                <option value="1" <?= $verified_filter === '1' ? 'selected' : '' ?>>Verified</option>
                                <option value="0" <?= $verified_filter === '0' ? 'selected' : '' ?>>Pending</option>
                            </select>
                            <div class="absolute inset-y-0 right-0 pr-3 pt-7 flex items-center pointer-events-none">
                                <i class="fas fa-chevron-down text-gray-400"></i>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="flex items-end">
                            <div class="flex items-center space-x-2 w-full">
                                <button type="submit" class="btn btn-primary w-full">
                                    <i class="fas fa-filter mr-2"></i> Filter
                                </button>
                                <a href="list_students.php" class="btn btn-outline w-full">
                                    <i class="fas fa-times mr-2"></i> Clear
                                </a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Students List -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 mb-8">
            <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                    <div>
                        <h2 class="text-lg font-semibold text-gray-800 dark:text-white flex items-center">
                            <i class="fas fa-users mr-3 text-blue-600"></i>
                            Students Directory
                            <?php if ($totalStudents > 0): ?>
                                <span class="ml-2 text-sm text-gray-500 dark:text-gray-400">
                                    (<?= $totalStudents ?> students)
                                </span>
                            <?php endif; ?>
                        </h2>
                        <?php if (!empty($search) || !empty($department_filter) || !empty($year_filter) || $verified_filter !== ''): ?>
                            <div class="mt-2 flex flex-wrap gap-2">
                                <?php
                                if (!empty($search)) echo '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300">Search: "' . htmlspecialchars($search) . '"</span>';
                                if (!empty($department_filter)) echo '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300">Department: ' . htmlspecialchars($department_filter) . '</span>';
                                if (!empty($year_filter)) echo '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300">Year: ' . htmlspecialchars($year_filter) . '</span>';
                                if ($verified_filter === '1') echo '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300">Verified</span>';
                                if ($verified_filter === '0') echo '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300">Pending Verification</span>';
                                ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="mt-4 sm:mt-0 flex items-center space-x-3">
                        <div class="flex items-center bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
                            <button id="gridViewBtn" class="px-3 py-1.5 text-sm font-medium rounded-md transition-colors bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm">
                                <i class="fas fa-th-large mr-1"></i> Grid
                            </button>
                            <button id="listViewBtn" class="px-3 py-1.5 text-sm font-medium rounded-md transition-colors text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white">
                                <i class="fas fa-list mr-1"></i> List
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
        <?php if (count($students) > 0): ?>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead>
                            <tr>
                                <th class="px-6 py-3 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">ID</th>
                                <th class="px-6 py-3 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Name</th>
                                <th class="px-6 py-3 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Email</th>
                                <th class="px-6 py-3 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Roll No.</th>
                                <th class="px-6 py-3 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Department</th>
                                <th class="px-6 py-3 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Year</th>
                                <th class="px-6 py-3 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                <?php foreach ($students as $student): ?>
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"><?= $student['id'] ?></td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <?php if (!empty($student['profile_photo'])): ?>
                                        <div class="w-8 h-8 rounded-full overflow-hidden mr-3">
                                            <img src="../uploads/<?= htmlspecialchars($student['profile_photo']) ?>" alt="Profile" class="w-full h-full object-cover">
                                        </div>
                                    <?php else: ?>
                                        <div class="w-8 h-8 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center text-green-600 dark:text-green-400 mr-3">
                                            <i class="fas fa-user-graduate"></i>
                                        </div>
                                    <?php endif; ?>
                                    <div>
                                        <div class="font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($student['full_name']) ?></div>
                                        <?php if (!empty($student['division'])): ?>
                                            <div class="text-xs text-gray-500 dark:text-gray-400">Division: <?= htmlspecialchars($student['division']) ?></div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400"><?= htmlspecialchars($student['email']) ?></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400"><?= htmlspecialchars($student['roll_number'] ?? 'N/A') ?></td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300">
                                    <?= htmlspecialchars($student['department'] ?? 'Unknown') ?>
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                <?= !empty($student['year']) ? 'Year ' . htmlspecialchars($student['year']) : 'N/A' ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <?php if (isset($student['verified']) && $student['verified']): ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300">
                                        <span class="w-1.5 h-1.5 inline-block bg-green-500 rounded-full mr-1.5"></span>
                                        Verified
                                    </span>
                                <?php else: ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300">
                                        <span class="w-1.5 h-1.5 inline-block bg-yellow-500 rounded-full mr-1.5"></span>
                                        Pending
                                    </span>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                <div class="flex space-x-3">
                                    <a href="view_student.php?id=<?= $student['id'] ?>" class="text-gray-600 dark:text-gray-400 hover:text-primary dark:hover:text-primary-light" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="edit_student.php?id=<?= $student['id'] ?>" class="text-primary hover:text-primary-dark" title="Edit Student">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <?php if (!isset($student['verified']) || !$student['verified']): ?>
                                        <a href="verify_student.php?id=<?= $student['id'] ?>" class="text-green-600 dark:text-green-400 hover:text-green-700 dark:hover:text-green-300" title="Verify Student">
                                            <i class="fas fa-check-circle"></i>
                                        </a>
                                    <?php endif; ?>
                                    <a href="delete_student.php?id=<?= $student['id'] ?>" class="text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300" title="Delete Student">
                                        <i class="fas fa-trash-alt"></i>
                                    </a>
                                </div>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
                </div>
                
                <!-- Pagination -->
                <?php if ($totalPages > 1): ?>
                    <div class="px-6 py-4 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-700">
                        <div class="flex justify-between items-center">
                            <div class="text-sm text-gray-500 dark:text-gray-400">
                                Showing <?= ($offset + 1) ?> to <?= min($offset + $limit, $totalStudents) ?> of <?= $totalStudents ?> students
                            </div>
                            <div class="flex space-x-1">
                                <?php 
                                    // Build query string for pagination links
                                    $queryParams = [];
                                    if (!empty($search)) $queryParams['search'] = $search;
                                    if (!empty($department_filter)) $queryParams['department'] = $department_filter;
                                    if (!empty($year_filter)) $queryParams['year'] = $year_filter;
                                    if ($verified_filter !== '') $queryParams['verified'] = $verified_filter;
                                    
                                    $queryString = http_build_query($queryParams);
                                    $queryPrefix = empty($queryString) ? '' : $queryString . '&';
                                ?>
                                
                                <?php if ($page > 1): ?>
                                    <a href="?<?= $queryPrefix ?>page=<?= ($page - 1) ?>" class="btn btn-outline btn-sm">
                                        <i class="fas fa-chevron-left mr-1"></i> Previous
                                    </a>
                                <?php endif; ?>
                                
                                <?php if ($page < $totalPages): ?>
                                    <a href="?<?= $queryPrefix ?>page=<?= ($page + 1) ?>" class="btn btn-outline btn-sm">
                                        Next <i class="fas fa-chevron-right ml-1"></i>
                                    </a>
            <?php endif; ?>
        </div>
    </div>
</div>
                <?php endif; ?>
            <?php else: ?>
                <div class="p-6 text-center">
                    <?php if (!empty($search) || !empty($department_filter) || !empty($year_filter) || $verified_filter !== ''): ?>
                        <div class="py-12">
                            <div class="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-search text-gray-400 text-2xl"></i>
                            </div>
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No matching results</h3>
                            <p class="text-gray-500 dark:text-gray-400 mb-4">We couldn't find any students matching your filters.</p>
                            <a href="list_students.php" class="btn btn-outline">
                                <i class="fas fa-times mr-2"></i> Clear Filters
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="py-12">
                            <div class="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-user-graduate text-gray-400 text-2xl"></i>
                            </div>
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No students yet</h3>
                            <p class="text-gray-500 dark:text-gray-400 mb-4">Get started by adding students to your college.</p>
                            <div class="flex justify-center space-x-3">
                                <a href="add_student.php" class="btn btn-primary">
                                    <i class="fas fa-user-plus mr-2"></i> Add Student
                                </a>
                                <a href="import_students.php" class="btn btn-outline">
                                    <i class="fas fa-file-import mr-2"></i> Bulk Import
                                </a>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- Bulk Actions Card -->
        <?php if (count($students) > 0): ?>
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 mb-6">
            <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
                <h2 class="font-semibold text-gray-800 dark:text-white flex items-center">
                    <i class="fas fa-tasks mr-2 text-primary"></i> Bulk Actions
                </h2>
            </div>
            
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <a href="bulk_verify_students.php" class="btn btn-outline w-full text-left flex items-center">
                        <i class="fas fa-check-circle mr-2 text-green-600"></i> Verify Selected Students
                    </a>
                    <a href="bulk_email_students.php" class="btn btn-outline w-full text-left flex items-center">
                        <i class="fas fa-envelope mr-2 text-blue-600"></i> Email Selected Students
                    </a>
                    <a href="bulk_assign_mentors.php" class="btn btn-outline w-full text-left flex items-center">
                        <i class="fas fa-users-cog mr-2 text-purple-600"></i> Assign Mentors
                    </a>
                    <a href="bulk_export_students.php" class="btn btn-outline w-full text-left flex items-center">
                        <i class="fas fa-file-export mr-2 text-yellow-600"></i> Export Selected Data
                    </a>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</main>

<script>
    // Export functionality with current filters
    function exportWithCurrentFilters() {
        const searchInput = document.getElementById('searchInput');
        const departmentFilter = document.getElementById('departmentFilter');
        const yearFilter = document.getElementById('yearFilter');
        const statusFilter = document.getElementById('statusFilter');

        const params = new URLSearchParams();

        // Add current filters to export URL
        if (searchInput && searchInput.value.trim()) {
            params.set('search', searchInput.value.trim());
        }

        if (departmentFilter && departmentFilter.value && departmentFilter.value !== '') {
            params.set('department', departmentFilter.value);
        }

        if (yearFilter && yearFilter.value && yearFilter.value !== '') {
            params.set('year', yearFilter.value);
        }

        if (statusFilter && statusFilter.value && statusFilter.value !== '') {
            params.set('verified', statusFilter.value);
        }

        // Set default fields for quick export
        params.set('fields[]', 'full_name');
        params.set('fields[]', 'email');
        params.set('fields[]', 'department');
        params.set('fields[]', 'year');
        params.set('fields[]', 'verified');

        // Redirect to export page with current filters
        window.location.href = 'export_students.php?' + params.toString();
    }

    // View toggle functionality
    function toggleView(viewType) {
        const gridView = document.getElementById('studentGrid');
        const listView = document.getElementById('studentList');
        const gridBtn = document.getElementById('gridViewBtn');
        const listBtn = document.getElementById('listViewBtn');

        if (viewType === 'grid') {
            gridView?.classList.remove('hidden');
            listView?.classList.add('hidden');
            gridBtn.classList.add('bg-white', 'dark:bg-gray-600', 'text-gray-900', 'dark:text-white', 'shadow-sm');
            gridBtn.classList.remove('text-gray-500', 'dark:text-gray-400');
            listBtn.classList.remove('bg-white', 'dark:bg-gray-600', 'text-gray-900', 'dark:text-white', 'shadow-sm');
            listBtn.classList.add('text-gray-500', 'dark:text-gray-400');
        } else {
            gridView?.classList.add('hidden');
            listView?.classList.remove('hidden');
            listBtn.classList.add('bg-white', 'dark:bg-gray-600', 'text-gray-900', 'dark:text-white', 'shadow-sm');
            listBtn.classList.remove('text-gray-500', 'dark:text-gray-400');
            gridBtn.classList.remove('bg-white', 'dark:bg-gray-600', 'text-gray-900', 'dark:text-white', 'shadow-sm');
            gridBtn.classList.add('text-gray-500', 'dark:text-gray-400');
        }

        // Save preference
        localStorage.setItem('studentViewType', viewType);
    }

    // Advanced filters toggle
    function toggleAdvancedFilters() {
        // This can be implemented to show/hide additional filter options
        alert('Advanced filters feature coming soon!');
    }

    // Initialize view toggle
    document.addEventListener('DOMContentLoaded', function() {
        const gridBtn = document.getElementById('gridViewBtn');
        const listBtn = document.getElementById('listViewBtn');

        if (gridBtn && listBtn) {
            gridBtn.addEventListener('click', () => toggleView('grid'));
            listBtn.addEventListener('click', () => toggleView('list'));

            // Load saved view preference
            const savedView = localStorage.getItem('studentViewType') || 'grid';
            toggleView(savedView);
        }
    });
</script>

<?php
// Include the footer
require_once '../includes/footer.php';
?>
    // Auto-submit form when select filters change
    document.getElementById('departmentFilter').addEventListener('change', function() {
        this.form.submit();
    });
    
    document.getElementById('yearFilter').addEventListener('change', function() {
        this.form.submit();
    });
    
    document.getElementById('statusFilter').addEventListener('change', function() {
        this.form.submit();
    });
</script>
