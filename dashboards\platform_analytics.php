<?php
// Include session configuration
require_once '../includes/session_config.php';
require_once '../includes/db.php';

if (!isset($_SESSION['superadmin_id'])) {
    header("Location: ../auth/superadmin_login.php");
    exit;
}

// Get time period from query params
$period = isset($_GET['period']) ? $_GET['period'] : 'monthly';
$validPeriods = ['weekly', 'monthly', 'quarterly', 'yearly'];
if (!in_array($period, $validPeriods)) {
    $period = 'monthly';
}

// Define date range based on period
$endDate = date('Y-m-d');
$startDate = '';

switch ($period) {
    case 'weekly':
        $startDate = date('Y-m-d', strtotime('-7 days'));
        $dateGroupFormat = '%Y-%m-%d';
        break;
    case 'monthly':
        $startDate = date('Y-m-d', strtotime('-30 days'));
        $dateGroupFormat = '%Y-%m-%d';
        break;
    case 'quarterly':
        $startDate = date('Y-m-d', strtotime('-90 days'));
        $dateGroupFormat = '%Y-%m-%d';
        break;
    case 'yearly':
        $startDate = date('Y-m-d', strtotime('-365 days'));
        $dateGroupFormat = '%Y-%m';
        break;
}

// Fetch user growth analytics
try {
    // Students growth
    $stmt = $conn->prepare("SELECT DATE_FORMAT(created_at, '$dateGroupFormat') as date, COUNT(*) as count 
                          FROM students 
                          WHERE created_at BETWEEN ? AND ? 
                          GROUP BY DATE_FORMAT(created_at, '$dateGroupFormat')
                          ORDER BY date");
    $stmt->execute([$startDate, $endDate]);
    $studentGrowthData = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Alumni growth
    $stmt = $conn->prepare("SELECT DATE_FORMAT(created_at, '$dateGroupFormat') as date, COUNT(*) as count 
                          FROM alumni 
                          WHERE created_at BETWEEN ? AND ? 
                          GROUP BY DATE_FORMAT(created_at, '$dateGroupFormat')
                          ORDER BY date");
    $stmt->execute([$startDate, $endDate]);
    $alumniGrowthData = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Faculty growth
    $stmt = $conn->prepare("SELECT DATE_FORMAT(created_at, '$dateGroupFormat') as date, COUNT(*) as count 
                          FROM faculties 
                          WHERE created_at BETWEEN ? AND ? 
                          GROUP BY DATE_FORMAT(created_at, '$dateGroupFormat')
                          ORDER BY date");
    $stmt->execute([$startDate, $endDate]);
    $facultyGrowthData = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // College growth
    $stmt = $conn->prepare("SELECT DATE_FORMAT(created_at, '$dateGroupFormat') as date, COUNT(*) as count 
                          FROM colleges 
                          WHERE created_at BETWEEN ? AND ? 
                          GROUP BY DATE_FORMAT(created_at, '$dateGroupFormat')
                          ORDER BY date");
    $stmt->execute([$startDate, $endDate]);
    $collegeGrowthData = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $error = "Error fetching growth data: " . $e->getMessage();
}

// Fetch total counts
try {
    // Total students
    $stmt = $conn->query("SELECT COUNT(*) FROM students");
    $totalStudents = $stmt->fetchColumn();
    
    // Total alumni
    $stmt = $conn->query("SELECT COUNT(*) FROM alumni");
    $totalAlumni = $stmt->fetchColumn();
    
    // Total faculty
    $stmt = $conn->query("SELECT COUNT(*) FROM faculties");
    $totalFaculty = $stmt->fetchColumn();
    
    // Total colleges
    $stmt = $conn->query("SELECT COUNT(*) FROM colleges");
    $totalColleges = $stmt->fetchColumn();
    
    // New students in period
    $stmt = $conn->prepare("SELECT COUNT(*) FROM students WHERE created_at BETWEEN ? AND ?");
    $stmt->execute([$startDate, $endDate]);
    $newStudents = $stmt->fetchColumn();
    
    // New alumni in period
    $stmt = $conn->prepare("SELECT COUNT(*) FROM alumni WHERE created_at BETWEEN ? AND ?");
    $stmt->execute([$startDate, $endDate]);
    $newAlumni = $stmt->fetchColumn();
    
    // New faculty in period
    $stmt = $conn->prepare("SELECT COUNT(*) FROM faculties WHERE created_at BETWEEN ? AND ?");
    $stmt->execute([$startDate, $endDate]);
    $newFaculty = $stmt->fetchColumn();
    
    // New colleges in period
    $stmt = $conn->prepare("SELECT COUNT(*) FROM colleges WHERE created_at BETWEEN ? AND ?");
    $stmt->execute([$startDate, $endDate]);
    $newColleges = $stmt->fetchColumn();
} catch (PDOException $e) {
    $error = "Error fetching total counts: " . $e->getMessage();
}

// Calculate growth percentages
$studentGrowthPercent = $totalStudents > 0 ? round(($newStudents / $totalStudents) * 100, 1) : 0;
$alumniGrowthPercent = $totalAlumni > 0 ? round(($newAlumni / $totalAlumni) * 100, 1) : 0;
$facultyGrowthPercent = $totalFaculty > 0 ? round(($newFaculty / $totalFaculty) * 100, 1) : 0;
$collegeGrowthPercent = $totalColleges > 0 ? round(($newColleges / $totalColleges) * 100, 1) : 0;

// Check for mentorship table
$hasMentorships = false;
try {
    $stmt = $conn->prepare("SHOW TABLES LIKE 'mentorships'");
    $stmt->execute();
    $hasMentorships = $stmt->rowCount() > 0;
    
    if ($hasMentorships) {
        // Total mentorships
        $stmt = $conn->query("SELECT COUNT(*) FROM mentorships");
        $totalMentorships = $stmt->fetchColumn();
        
        // Active mentorships
        $stmt = $conn->query("SELECT COUNT(*) FROM mentorships WHERE status = 'active'");
        $activeMentorships = $stmt->fetchColumn();
        
        // New mentorships in period
        $stmt = $conn->prepare("SELECT COUNT(*) FROM mentorships WHERE created_at BETWEEN ? AND ?");
        $stmt->execute([$startDate, $endDate]);
        $newMentorships = $stmt->fetchColumn();
        
        // Mentorship success rate
        $stmt = $conn->query("SELECT COUNT(*) FROM mentorships WHERE status = 'completed'");
        $completedMentorships = $stmt->fetchColumn();
        $mentorshipSuccessRate = $totalMentorships > 0 ? round(($completedMentorships / $totalMentorships) * 100, 1) : 0;
    }
} catch (PDOException $e) {
    // Table doesn't exist, ignore
}

// Check for events table
$hasEvents = false;
try {
    $stmt = $conn->prepare("SHOW TABLES LIKE 'events'");
    $stmt->execute();
    $hasEvents = $stmt->rowCount() > 0;
    
    if ($hasEvents) {
        // Total events
        $stmt = $conn->query("SELECT COUNT(*) FROM events");
        $totalEvents = $stmt->fetchColumn();
        
        // Upcoming events
        $stmt = $conn->prepare("SELECT COUNT(*) FROM events WHERE event_date >= ?");
        $stmt->execute([date('Y-m-d')]);
        $upcomingEvents = $stmt->fetchColumn();
        
        // New events in period
        $stmt = $conn->prepare("SELECT COUNT(*) FROM events WHERE created_at BETWEEN ? AND ?");
        $stmt->execute([$startDate, $endDate]);
        $newEvents = $stmt->fetchColumn();
    }
} catch (PDOException $e) {
    // Table doesn't exist, ignore
}

// Convert data to JSON for JavaScript charts
$growthChartData = [
    'labels' => [],
    'datasets' => [
        [
            'label' => 'Students',
            'data' => [],
            'borderColor' => 'rgb(59, 130, 246)',
            'backgroundColor' => 'rgba(59, 130, 246, 0.5)'
        ],
        [
            'label' => 'Alumni',
            'data' => [],
            'borderColor' => 'rgb(139, 92, 246)',
            'backgroundColor' => 'rgba(139, 92, 246, 0.5)'
        ],
        [
            'label' => 'Faculty',
            'data' => [],
            'borderColor' => 'rgb(16, 185, 129)',
            'backgroundColor' => 'rgba(16, 185, 129, 0.5)'
        ],
        [
            'label' => 'Colleges',
            'data' => [],
            'borderColor' => 'rgb(245, 158, 11)',
            'backgroundColor' => 'rgba(245, 158, 11, 0.5)'
        ]
    ]
];

// Prepare chart data
$dates = [];
// Process Student data
foreach ($studentGrowthData as $data) {
    $dates[$data['date']] = true;
}
// Process Alumni data
foreach ($alumniGrowthData as $data) {
    $dates[$data['date']] = true;
}
// Process Faculty data
foreach ($facultyGrowthData as $data) {
    $dates[$data['date']] = true;
}
// Process College data
foreach ($collegeGrowthData as $data) {
    $dates[$data['date']] = true;
}

// Sort dates and create the labels
$dates = array_keys($dates);
sort($dates);
$growthChartData['labels'] = $dates;

// Fill in the data for each dataset
foreach ($dates as $date) {
    // Students
    $found = false;
    foreach ($studentGrowthData as $data) {
        if ($data['date'] === $date) {
            $growthChartData['datasets'][0]['data'][] = $data['count'];
            $found = true;
            break;
        }
    }
    if (!$found) {
        $growthChartData['datasets'][0]['data'][] = 0;
    }
    
    // Alumni
    $found = false;
    foreach ($alumniGrowthData as $data) {
        if ($data['date'] === $date) {
            $growthChartData['datasets'][1]['data'][] = $data['count'];
            $found = true;
            break;
        }
    }
    if (!$found) {
        $growthChartData['datasets'][1]['data'][] = 0;
    }
    
    // Faculty
    $found = false;
    foreach ($facultyGrowthData as $data) {
        if ($data['date'] === $date) {
            $growthChartData['datasets'][2]['data'][] = $data['count'];
            $found = true;
            break;
        }
    }
    if (!$found) {
        $growthChartData['datasets'][2]['data'][] = 0;
    }
    
    // Colleges
    $found = false;
    foreach ($collegeGrowthData as $data) {
        if ($data['date'] === $date) {
            $growthChartData['datasets'][3]['data'][] = $data['count'];
            $found = true;
            break;
        }
    }
    if (!$found) {
        $growthChartData['datasets'][3]['data'][] = 0;
    }
}

// Include the header
require_once '../includes/header.php';
?>

<!-- Main Content -->
<div class="container mx-auto px-4 py-8 max-w-7xl">
    <!-- Page Header -->
    <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                <span class="bg-blue-500/10 dark:bg-blue-500/20 p-2 rounded-md mr-3">
                    <i class="fas fa-chart-line text-blue-600 dark:text-blue-400"></i>
                </span>
                Platform Analytics
            </h1>
            <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">Track and analyze platform usage and growth metrics</p>
        </div>
        <div class="mt-4 md:mt-0">
            <div class="inline-flex rounded-md shadow-sm" role="group">
                <a href="?period=weekly" class="px-4 py-2 text-sm font-medium rounded-l-lg border <?= $period === 'weekly' ? 'bg-primary text-white border-primary' : 'bg-white dark:bg-gray-700 text-gray-700 dark:text-white border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600' ?>">
                    Weekly
                </a>
                <a href="?period=monthly" class="px-4 py-2 text-sm font-medium border-t border-b <?= $period === 'monthly' ? 'bg-primary text-white border-primary' : 'bg-white dark:bg-gray-700 text-gray-700 dark:text-white border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600' ?>">
                    Monthly
                </a>
                <a href="?period=quarterly" class="px-4 py-2 text-sm font-medium border-t border-b <?= $period === 'quarterly' ? 'bg-primary text-white border-primary' : 'bg-white dark:bg-gray-700 text-gray-700 dark:text-white border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600' ?>">
                    Quarterly
                </a>
                <a href="?period=yearly" class="px-4 py-2 text-sm font-medium rounded-r-lg border <?= $period === 'yearly' ? 'bg-primary text-white border-primary' : 'bg-white dark:bg-gray-700 text-gray-700 dark:text-white border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600' ?>">
                    Yearly
                </a>
            </div>
        </div>
    </div>
    
    <!-- Overview Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Students Card -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6 transition-all duration-300 hover:shadow-md">
            <div class="flex justify-between items-start">
                <div>
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Students</p>
                    <div class="flex items-baseline mt-1">
                        <h3 class="text-2xl font-bold text-gray-900 dark:text-white"><?= number_format($totalStudents) ?></h3>
                        <span class="ml-2 text-sm font-medium <?= $studentGrowthPercent > 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' ?>">
                            <i class="fas fa-<?= $studentGrowthPercent >= 0 ? 'arrow-up' : 'arrow-down' ?> mr-0.5"></i>
                            <?= abs($studentGrowthPercent) ?>%
                        </span>
                    </div>
                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                        <span class="font-medium"><?= number_format($newStudents) ?> new</span> in this period
                    </p>
                </div>
                <div class="bg-blue-100 dark:bg-blue-900/30 p-3 rounded-full">
                    <i class="fas fa-user-graduate text-blue-600 dark:text-blue-400"></i>
                </div>
            </div>
            <div class="w-full bg-gray-200 dark:bg-gray-700 h-1.5 mt-4 rounded-full overflow-hidden">
                <div class="bg-blue-500 h-1.5 rounded-full" style="width: <?= min(100, $studentGrowthPercent + 5) ?>%"></div>
            </div>
        </div>
        
        <!-- Alumni Card -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6 transition-all duration-300 hover:shadow-md">
            <div class="flex justify-between items-start">
                <div>
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Alumni</p>
                    <div class="flex items-baseline mt-1">
                        <h3 class="text-2xl font-bold text-gray-900 dark:text-white"><?= number_format($totalAlumni) ?></h3>
                        <span class="ml-2 text-sm font-medium <?= $alumniGrowthPercent > 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' ?>">
                            <i class="fas fa-<?= $alumniGrowthPercent >= 0 ? 'arrow-up' : 'arrow-down' ?> mr-0.5"></i>
                            <?= abs($alumniGrowthPercent) ?>%
                        </span>
                    </div>
                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                        <span class="font-medium"><?= number_format($newAlumni) ?> new</span> in this period
                    </p>
                </div>
                <div class="bg-purple-100 dark:bg-purple-900/30 p-3 rounded-full">
                    <i class="fas fa-user-tie text-purple-600 dark:text-purple-400"></i>
                </div>
            </div>
            <div class="w-full bg-gray-200 dark:bg-gray-700 h-1.5 mt-4 rounded-full overflow-hidden">
                <div class="bg-purple-500 h-1.5 rounded-full" style="width: <?= min(100, $alumniGrowthPercent + 5) ?>%"></div>
            </div>
        </div>
        
        <!-- Faculty Card -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6 transition-all duration-300 hover:shadow-md">
            <div class="flex justify-between items-start">
                <div>
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Faculty</p>
                    <div class="flex items-baseline mt-1">
                        <h3 class="text-2xl font-bold text-gray-900 dark:text-white"><?= number_format($totalFaculty) ?></h3>
                        <span class="ml-2 text-sm font-medium <?= $facultyGrowthPercent > 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' ?>">
                            <i class="fas fa-<?= $facultyGrowthPercent >= 0 ? 'arrow-up' : 'arrow-down' ?> mr-0.5"></i>
                            <?= abs($facultyGrowthPercent) ?>%
                        </span>
                    </div>
                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                        <span class="font-medium"><?= number_format($newFaculty) ?> new</span> in this period
                    </p>
                </div>
                <div class="bg-green-100 dark:bg-green-900/30 p-3 rounded-full">
                    <i class="fas fa-chalkboard-teacher text-green-600 dark:text-green-400"></i>
                </div>
            </div>
            <div class="w-full bg-gray-200 dark:bg-gray-700 h-1.5 mt-4 rounded-full overflow-hidden">
                <div class="bg-green-500 h-1.5 rounded-full" style="width: <?= min(100, $facultyGrowthPercent + 5) ?>%"></div>
            </div>
        </div>
        
        <!-- Colleges Card -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6 transition-all duration-300 hover:shadow-md">
            <div class="flex justify-between items-start">
                <div>
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Colleges</p>
                    <div class="flex items-baseline mt-1">
                        <h3 class="text-2xl font-bold text-gray-900 dark:text-white"><?= number_format($totalColleges) ?></h3>
                        <span class="ml-2 text-sm font-medium <?= $collegeGrowthPercent > 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' ?>">
                            <i class="fas fa-<?= $collegeGrowthPercent >= 0 ? 'arrow-up' : 'arrow-down' ?> mr-0.5"></i>
                            <?= abs($collegeGrowthPercent) ?>%
                        </span>
                    </div>
                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                        <span class="font-medium"><?= number_format($newColleges) ?> new</span> in this period
                    </p>
                </div>
                <div class="bg-amber-100 dark:bg-amber-900/30 p-3 rounded-full">
                    <i class="fas fa-university text-amber-600 dark:text-amber-400"></i>
                </div>
            </div>
            <div class="w-full bg-gray-200 dark:bg-gray-700 h-1.5 mt-4 rounded-full overflow-hidden">
                <div class="bg-amber-500 h-1.5 rounded-full" style="width: <?= min(100, $collegeGrowthPercent + 5) ?>%"></div>
            </div>
        </div>
    </div>
    
    <!-- User Growth Chart -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6 mb-8">
        <h2 class="text-lg font-semibold text-gray-800 dark:text-white mb-4">User Growth</h2>
        <div class="h-80">
            <canvas id="growthChart"></canvas>
        </div>
    </div>
    
    <!-- Secondary Metrics -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <?php if ($hasMentorships): ?>
        <!-- Mentorship Stats -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
            <h2 class="text-lg font-semibold text-gray-800 dark:text-white mb-4 flex items-center">
                <i class="fas fa-handshake text-primary mr-2"></i>
                Mentorship Statistics
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Mentorships</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white mt-1"><?= number_format($totalMentorships) ?></p>
                </div>
                <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Active Mentorships</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white mt-1"><?= number_format($activeMentorships) ?></p>
                </div>
                <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Success Rate</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white mt-1"><?= $mentorshipSuccessRate ?>%</p>
                </div>
            </div>
            <div class="mt-4">
                <div class="flex justify-between mb-1">
                    <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Completion Rate</span>
                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300"><?= $mentorshipSuccessRate ?>%</span>
                </div>
                <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
                    <div class="bg-primary h-2.5 rounded-full" style="width: <?= $mentorshipSuccessRate ?>%"></div>
                </div>
            </div>
        </div>
        <?php endif; ?>
        
        <?php if ($hasEvents): ?>
        <!-- Event Stats -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
            <h2 class="text-lg font-semibold text-gray-800 dark:text-white mb-4 flex items-center">
                <i class="fas fa-calendar-alt text-primary mr-2"></i>
                Event Statistics
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Events</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white mt-1"><?= number_format($totalEvents) ?></p>
                </div>
                <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Upcoming Events</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white mt-1"><?= number_format($upcomingEvents) ?></p>
                </div>
                <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">New Events</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white mt-1"><?= number_format($newEvents) ?></p>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- Include Chart.js for analytics -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize growth chart
    const growthCtx = document.getElementById('growthChart').getContext('2d');
    const growthData = <?= json_encode($growthChartData) ?>;
    
    const growthChart = new Chart(growthCtx, {
        type: 'line',
        data: growthData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        precision: 0
                    }
                }
            },
            plugins: {
                legend: {
                    position: 'top',
                },
                tooltip: {
                    callbacks: {
                        title: function(context) {
                            return context[0].label;
                        }
                    }
                }
            },
            interaction: {
                intersect: false,
                mode: 'index',
            }
        }
    });
    
    // Handle dark mode for charts
    function updateChartTheme() {
        const isDarkMode = document.documentElement.classList.contains('dark');
        const textColor = isDarkMode ? '#d1d5db' : '#374151';
        const gridColor = isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)';
        
        growthChart.options.scales.x.ticks.color = textColor;
        growthChart.options.scales.x.grid.color = gridColor;
        growthChart.options.scales.y.ticks.color = textColor;
        growthChart.options.scales.y.grid.color = gridColor;
        growthChart.options.plugins.legend.labels.color = textColor;
        growthChart.update();
    }
    
    // Initial theme setup
    updateChartTheme();
    
    // Watch for theme changes
    const observer = new MutationObserver(mutations => {
        mutations.forEach(mutation => {
            if (mutation.attributeName === 'class') {
                updateChartTheme();
            }
        });
    });
    observer.observe(document.documentElement, { attributes: true });
});
</script>