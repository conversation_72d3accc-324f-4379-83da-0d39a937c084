<?php
// Start the session if not already started
if (session_status() === PHP_SESSION_NONE) {
    // Set session timeout to 15 days (must be set before session_start)
    // 15 days in seconds: 15 * 24 * 60 * 60 = 1296000 seconds
    $session_lifetime = 1296000; // 15 days
    
    ini_set('session.gc_maxlifetime', $session_lifetime);
    
    // Set the session cookie parameters
    session_set_cookie_params(
        $session_lifetime,
        '/',
        '',
        false, // Set to true if using HTTPS
        true // httponly
    );
    
    session_start();
}

// Auto-redirect based on user role if logged in
function auto_redirect_to_dashboard() {
    // Define dashboard URLs for each role
    $dashboards = [
        'superadmin_id' => '/dashboards/superadmin_dashboard.php',
        'collegeadmin_id' => '/dashboards/collegeadmin_dashboard.php',
        'faculty_id' => '/dashboards/faculty_dashboard.php',
        'student_id' => '/student/student_dashboard.php',
        'alumni_id' => '/alumni/alumni_dashboard.php'
    ];
    
    // Check if user is logged in with any role
    foreach ($dashboards as $session_key => $dashboard_url) {
        if (isset($_SESSION[$session_key])) {
            // User is logged in, redirect to their dashboard
            header("Location: " . $dashboard_url);
            exit;
        }
    }
    
    // If no session found, user stays on current page
    return false;
}