<?php
// Include session configuration
require_once '../includes/session_config.php';
require_once '../includes/db.php';
require_once '../includes/db_schema_check.php';

if (!isset($_SESSION['collegeadmin_id'])) {
    header("Location: ../auth/collegeadmin_login.php");
    exit;
}

$college_id = $_SESSION['college_id'];
$admin_name = $_SESSION['collegeadmin_name'] ?? 'Admin';

// Set role explicitly for the sidebar
$role = 'collegeadmin';
$user_id = $_SESSION['collegeadmin_id'];
$user_name = $_SESSION['collegeadmin_name'];
$current_page = basename($_SERVER['PHP_SELF']);

$error = '';
$success = '';
$students = [];

// Get college details
$collegeStmt = $conn->prepare("SELECT name FROM colleges WHERE id = ?");
$collegeStmt->execute([$college_id]);
$college = $collegeStmt->fetch(PDO::FETCH_ASSOC);

// Get departments for filter and update
$deptStmt = $conn->prepare("SELECT DISTINCT department FROM students WHERE college_id = ? ORDER BY department ASC");
$deptStmt->execute([$college_id]);
$departments = $deptStmt->fetchAll(PDO::FETCH_COLUMN);

// Get years for filter and update
$yearStmt = $conn->prepare("SELECT DISTINCT year FROM students WHERE college_id = ? ORDER BY year ASC");
$yearStmt->execute([$college_id]);
$years = $yearStmt->fetchAll(PDO::FETCH_COLUMN);

// Count total students
$countStmt = $conn->prepare("SELECT COUNT(*) FROM students WHERE college_id = ?");
$countStmt->execute([$college_id]);
$total_students = $countStmt->fetchColumn();

// Handle form submission for bulk update
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['student_ids']) && is_array($_POST['student_ids']) && !empty($_POST['student_ids'])) {
        $student_ids = $_POST['student_ids'];
        $update_fields = [];
        $update_values = [];
        
        // Check which fields to update
        if (!empty($_POST['update_department']) && !empty($_POST['new_department'])) {
            $update_fields[] = "department = ?";
            $update_values[] = $_POST['new_department'];
        }
        
        if (!empty($_POST['update_year']) && !empty($_POST['new_year'])) {
            $update_fields[] = "year = ?";
            $update_values[] = $_POST['new_year'];
        }
        
        if (!empty($_POST['update_division']) && isset($_POST['new_division'])) {
            $update_fields[] = "division = ?";
            $update_values[] = $_POST['new_division'];
        }
        
        if (isset($_POST['update_verified'])) {
            $update_fields[] = "verified = ?";
            $update_values[] = $_POST['new_verified'] === '1' ? 1 : 0;
        }
        
        if (count($update_fields) === 0) {
            $error = "Please select at least one field to update.";
        } else {
            try {
                // Start transaction
                $conn->beginTransaction();
                
                // Build placeholders for SQL query
                $placeholders = implode(',', array_fill(0, count($student_ids), '?'));
                $set_clause = implode(', ', $update_fields);
                
                // Update students
                $sql = "UPDATE students SET $set_clause WHERE id IN ($placeholders) AND college_id = ?";
                $params = array_merge($update_values, $student_ids, [$college_id]);
                $stmt = $conn->prepare($sql);
                $stmt->execute($params);
                
                $updated_count = $stmt->rowCount();
                
                // Commit transaction
                $conn->commit();
                
                $success = "$updated_count student" . ($updated_count !== 1 ? "s" : "") . " updated successfully.";
                
            } catch (PDOException $e) {
                // Rollback transaction on error
                $conn->rollBack();
                $error = "Database error: " . $e->getMessage();
            }
        }
    } else {
        $error = "No students selected for update.";
    }
}

// Apply filters
$conditions = ["college_id = ?"];
$params = [$college_id];

if (!empty($_GET['department'])) {
    $conditions[] = "department = ?";
    $params[] = $_GET['department'];
}

if (!empty($_GET['year'])) {
    $conditions[] = "year = ?";
    $params[] = $_GET['year'];
}

if (!empty($_GET['verified'])) {
    if ($_GET['verified'] === '1') {
        $conditions[] = "verified = 1";
    } elseif ($_GET['verified'] === '0') {
        $conditions[] = "verified = 0";
    }
}

if (!empty($_GET['search'])) {
    $conditions[] = "(full_name LIKE ? OR email LIKE ? OR roll_number LIKE ?)";
    $searchParam = "%" . $_GET['search'] . "%";
    $params = array_merge($params, [$searchParam, $searchParam, $searchParam]);
}

$whereClause = implode(' AND ', $conditions);

// Get students with filters
$sql = "SELECT * FROM students WHERE $whereClause ORDER BY full_name ASC LIMIT 100";
$stmt = $conn->prepare($sql);
$stmt->execute($params);
$students = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Count filtered results
$countFilteredStmt = $conn->prepare("SELECT COUNT(*) FROM students WHERE $whereClause");
$countFilteredStmt->execute($params);
$filtered_count = $countFilteredStmt->fetchColumn();

// Include the header
require_once '../includes/header.php';
?>

    <!-- Main Content -->
    <main class="flex-grow py-6 px-4">
        <div class="max-w-7xl mx-auto">
            <!-- Page Header -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8">
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="bg-gradient-to-br from-purple-500 to-purple-600 p-3 rounded-xl shadow-lg">
                            <i class="fas fa-edit text-white text-xl"></i>
                        </div>
                        <div>
                            <h1 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                                Bulk Update Students
                            </h1>
                            <nav class="text-sm text-gray-600 dark:text-gray-400 mt-1" aria-label="Breadcrumb">
                                <ol class="flex items-center space-x-2">
                                    <li>
                                        <a href="collegeadmin_dashboard.php" class="hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
                                            Dashboard
                                        </a>
                                    </li>
                                    <li class="flex items-center">
                                        <i class="fas fa-chevron-right text-gray-400 text-xs mx-2"></i>
                                        <a href="list_students.php" class="hover:text-blue-600 dark:hover:text-blue-400 transition-colors">Student Management</a>
                                    </li>
                                    <li class="flex items-center">
                                        <i class="fas fa-chevron-right text-gray-400 text-xs mx-2"></i>
                                        <span class="text-gray-900 dark:text-white font-medium">Bulk Update</span>
                                    </li>
                                </ol>
                            </nav>
                        </div>
                    </div>
                    <div class="mt-4 lg:mt-0 flex flex-wrap gap-3">
                        <a href="list_students.php" class="inline-flex items-center px-4 py-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-medium rounded-lg transition-colors">
                            <i class="fas fa-arrow-left mr-2"></i> Back to Students
                        </a>
                        <a href="bulk_verify_students.php" class="inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white font-medium rounded-lg transition-colors">
                            <i class="fas fa-check-circle mr-2"></i> Bulk Verify
                        </a>
                    </div>
                </div>
            </div>

            <!-- Alert Messages -->
            <?php if ($error): ?>
                <div class="bg-red-50 dark:bg-red-900/50 border border-red-200 dark:border-red-800 rounded-xl p-4 mb-6">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <i class="fas fa-exclamation-circle text-red-600 dark:text-red-400"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-red-800 dark:text-red-200">Error</h3>
                            <p class="text-sm text-red-700 dark:text-red-300 mt-1"><?= htmlspecialchars($error) ?></p>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <?php if ($success): ?>
                <div class="bg-green-50 dark:bg-green-900/50 border border-green-200 dark:border-green-800 rounded-xl p-4 mb-6">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <i class="fas fa-check-circle text-green-600 dark:text-green-400"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-green-800 dark:text-green-200">Success</h3>
                            <p class="text-sm text-green-700 dark:text-green-300 mt-1"><?= htmlspecialchars($success) ?></p>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Statistics Cards -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                    <div class="flex items-center">
                        <div class="bg-blue-100 dark:bg-blue-900/30 p-3 rounded-lg">
                            <i class="fas fa-users text-blue-600 dark:text-blue-400 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm text-gray-600 dark:text-gray-400">Total Students</p>
                            <p class="text-2xl font-bold text-gray-900 dark:text-white"><?= number_format($total_students) ?></p>
                        </div>
                    </div>
                </div>
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                    <div class="flex items-center">
                        <div class="bg-orange-100 dark:bg-orange-900/30 p-3 rounded-lg">
                            <i class="fas fa-filter text-orange-600 dark:text-orange-400 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm text-gray-600 dark:text-gray-400">Filtered Results</p>
                            <p class="text-2xl font-bold text-gray-900 dark:text-white"><?= number_format($filtered_count) ?></p>
                        </div>
                    </div>
                </div>
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                    <div class="flex items-center">
                        <div class="bg-purple-100 dark:bg-purple-900/30 p-3 rounded-lg">
                            <i class="fas fa-edit text-purple-600 dark:text-purple-400 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm text-gray-600 dark:text-gray-400">Selected for Update</p>
                            <p class="text-2xl font-bold text-purple-600 dark:text-purple-400" id="selectedCount">0</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content Grid -->
            <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
                <!-- Left Column: Filters & Students List -->
                <div class="lg:col-span-3 space-y-6">
                    <!-- Filters -->
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                            <i class="fas fa-filter text-blue-600 mr-2"></i>
                            Filter Students
                        </h3>
                        <form method="get" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Search</label>
                                <input type="text" name="search" value="<?= htmlspecialchars($_GET['search'] ?? '') ?>"
                                       placeholder="Name, email, or roll number..."
                                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Department</label>
                                <select name="department" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                                    <option value="">All Departments</option>
                                    <?php foreach ($departments as $dept): ?>
                                        <option value="<?= htmlspecialchars($dept) ?>" <?= ($_GET['department'] ?? '') === $dept ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($dept) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Year</label>
                                <select name="year" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                                    <option value="">All Years</option>
                                    <?php foreach ($years as $year): ?>
                                        <option value="<?= htmlspecialchars($year) ?>" <?= ($_GET['year'] ?? '') === $year ? 'selected' : '' ?>>
                                            Year <?= htmlspecialchars($year) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="flex items-end">
                                <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors">
                                    <i class="fas fa-search mr-2"></i> Filter
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Update Fields -->
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                            <i class="fas fa-edit text-purple-600 mr-2"></i>
                            Update Fields
                        </h3>
                        <form id="updateForm" method="post">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- Department Update -->
                                <div class="space-y-3">
                                    <div class="flex items-center">
                                        <input type="checkbox" id="update_department" name="update_department" class="h-4 w-4 text-purple-600 border-gray-300 rounded focus:ring-purple-500">
                                        <label for="update_department" class="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300">Update Department</label>
                                    </div>
                                    <select name="new_department" id="new_department" disabled class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:text-white disabled:opacity-50">
                                        <option value="">Select Department</option>
                                        <?php foreach ($departments as $dept): ?>
                                            <option value="<?= htmlspecialchars($dept) ?>"><?= htmlspecialchars($dept) ?></option>
                                        <?php endforeach; ?>
                                        <option value="Computer Science">Computer Science</option>
                                        <option value="Information Technology">Information Technology</option>
                                        <option value="Electronics">Electronics</option>
                                        <option value="Mechanical">Mechanical</option>
                                        <option value="Civil">Civil</option>
                                    </select>
                                </div>

                                <!-- Year Update -->
                                <div class="space-y-3">
                                    <div class="flex items-center">
                                        <input type="checkbox" id="update_year" name="update_year" class="h-4 w-4 text-purple-600 border-gray-300 rounded focus:ring-purple-500">
                                        <label for="update_year" class="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300">Update Year</label>
                                    </div>
                                    <select name="new_year" id="new_year" disabled class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:text-white disabled:opacity-50">
                                        <option value="">Select Year</option>
                                        <option value="1">Year 1</option>
                                        <option value="2">Year 2</option>
                                        <option value="3">Year 3</option>
                                        <option value="4">Year 4</option>
                                    </select>
                                </div>

                                <!-- Division Update -->
                                <div class="space-y-3">
                                    <div class="flex items-center">
                                        <input type="checkbox" id="update_division" name="update_division" class="h-4 w-4 text-purple-600 border-gray-300 rounded focus:ring-purple-500">
                                        <label for="update_division" class="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300">Update Division</label>
                                    </div>
                                    <input type="text" name="new_division" id="new_division" disabled placeholder="e.g., A, B, C"
                                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:text-white disabled:opacity-50">
                                </div>

                                <!-- Verification Status Update -->
                                <div class="space-y-3">
                                    <div class="flex items-center">
                                        <input type="checkbox" id="update_verified" name="update_verified" class="h-4 w-4 text-purple-600 border-gray-300 rounded focus:ring-purple-500">
                                        <label for="update_verified" class="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300">Update Verification Status</label>
                                    </div>
                                    <select name="new_verified" id="new_verified" disabled class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:text-white disabled:opacity-50">
                                        <option value="1">Verified</option>
                                        <option value="0">Not Verified</option>
                                    </select>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- Students List -->
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                            <div class="flex items-center justify-between">
                                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                                    Students List (<?= number_format($filtered_count) ?> found)
                                </h3>
                                <div class="flex items-center space-x-3">
                                    <button type="button" onclick="selectAll()" class="text-sm text-blue-600 hover:text-blue-700 font-medium">
                                        Select All
                                    </button>
                                    <button type="button" onclick="selectNone()" class="text-sm text-gray-600 hover:text-gray-700 font-medium">
                                        Select None
                                    </button>
                                </div>
                            </div>
                        </div>

                        <?php if (count($students) > 0): ?>
                            <div class="p-6">
                                <div class="space-y-4">
                                    <?php foreach ($students as $student): ?>
                                        <div class="flex items-center p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                                            <input type="checkbox" name="student_ids[]" value="<?= $student['id'] ?>" form="updateForm"
                                                   class="student-checkbox h-4 w-4 text-purple-600 border-gray-300 rounded focus:ring-purple-500"
                                                   onchange="updateSelectedCount()">
                                            <div class="ml-4 flex-grow">
                                                <div class="flex items-center justify-between">
                                                    <div>
                                                        <h4 class="font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($student['full_name']) ?></h4>
                                                        <p class="text-sm text-gray-600 dark:text-gray-400"><?= htmlspecialchars($student['email']) ?></p>
                                                    </div>
                                                    <div class="text-right">
                                                        <div class="flex items-center space-x-2">
                                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                                                <?= htmlspecialchars($student['department']) ?>
                                                            </span>
                                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                                                Year <?= $student['year'] ?>
                                                            </span>
                                                            <?php if ($student['verified']): ?>
                                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                                                    <i class="fas fa-check mr-1"></i> Verified
                                                                </span>
                                                            <?php else: ?>
                                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                                                                    <i class="fas fa-clock mr-1"></i> Pending
                                                                </span>
                                                            <?php endif; ?>
                                                        </div>
                                                        <?php if ($student['roll_number']): ?>
                                                            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Roll: <?= htmlspecialchars($student['roll_number']) ?></p>
                                                        <?php endif; ?>
                                                        <?php if ($student['division']): ?>
                                                            <p class="text-xs text-gray-500 dark:text-gray-400">Division: <?= htmlspecialchars($student['division']) ?></p>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>

                                <!-- Action Buttons -->
                                <div class="mt-6 flex justify-end space-x-3">
                                    <button type="submit" form="updateForm" id="updateButton"
                                            class="px-6 py-2 bg-purple-600 hover:bg-purple-700 text-white font-medium rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                                            disabled>
                                        <i class="fas fa-save mr-2"></i> Update Selected Students
                                    </button>
                                </div>
                            </div>
                        <?php else: ?>
                            <div class="p-12 text-center">
                                <div class="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <i class="fas fa-users text-gray-400 text-2xl"></i>
                                </div>
                                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Students Found</h3>
                                <p class="text-gray-600 dark:text-gray-400">No students match your current filters.</p>
                                <a href="?<?= http_build_query(array_filter(['department' => '', 'year' => '', 'verified' => '', 'search' => ''])) ?>"
                                   class="inline-flex items-center mt-4 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors">
                                    <i class="fas fa-refresh mr-2"></i> Clear Filters
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Right Sidebar -->
                <div class="space-y-6">
                    <!-- Update Instructions -->
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                        <div class="p-4 border-b border-gray-200 dark:border-gray-700">
                            <h3 class="font-semibold text-gray-900 dark:text-white flex items-center">
                                <i class="fas fa-info-circle text-blue-600 mr-2"></i>
                                How to Update
                            </h3>
                        </div>
                        <div class="p-4 space-y-3 text-sm">
                            <div class="flex items-start">
                                <i class="fas fa-check text-green-600 mr-3 mt-0.5 w-4"></i>
                                <span class="text-gray-700 dark:text-gray-300">Select fields you want to update</span>
                            </div>
                            <div class="flex items-start">
                                <i class="fas fa-users text-blue-600 mr-3 mt-0.5 w-4"></i>
                                <span class="text-gray-700 dark:text-gray-300">Choose students from the list</span>
                            </div>
                            <div class="flex items-start">
                                <i class="fas fa-save text-purple-600 mr-3 mt-0.5 w-4"></i>
                                <span class="text-gray-700 dark:text-gray-300">Click update to apply changes</span>
                            </div>
                            <div class="flex items-start">
                                <i class="fas fa-shield-alt text-orange-600 mr-3 mt-0.5 w-4"></i>
                                <span class="text-gray-700 dark:text-gray-300">Changes are applied immediately</span>
                            </div>
                        </div>
                    </div>

                    <!-- Update Options -->
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                        <div class="p-4 border-b border-gray-200 dark:border-gray-700">
                            <h3 class="font-semibold text-gray-900 dark:text-white flex items-center">
                                <i class="fas fa-cogs text-purple-600 mr-2"></i>
                                Available Updates
                            </h3>
                        </div>
                        <div class="p-4 space-y-3 text-sm">
                            <div class="flex items-center">
                                <i class="fas fa-graduation-cap text-blue-600 mr-3 w-4"></i>
                                <span class="text-gray-700 dark:text-gray-300">Department</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-calendar-alt text-green-600 mr-3 w-4"></i>
                                <span class="text-gray-700 dark:text-gray-300">Academic Year</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-users text-orange-600 mr-3 w-4"></i>
                                <span class="text-gray-700 dark:text-gray-300">Division/Section</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-check-circle text-purple-600 mr-3 w-4"></i>
                                <span class="text-gray-700 dark:text-gray-300">Verification Status</span>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                        <div class="p-4 border-b border-gray-200 dark:border-gray-700">
                            <h3 class="font-semibold text-gray-900 dark:text-white flex items-center">
                                <i class="fas fa-bolt text-purple-600 mr-2"></i>
                                Quick Actions
                            </h3>
                        </div>
                        <div class="p-4 space-y-2">
                            <a href="bulk_verify_students.php" class="w-full inline-flex items-center justify-center px-3 py-2 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-lg transition-colors">
                                <i class="fas fa-check-circle mr-2"></i> Bulk Verify
                            </a>
                            <a href="bulk_email_students.php" class="w-full inline-flex items-center justify-center px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors">
                                <i class="fas fa-envelope mr-2"></i> Send Email
                            </a>
                            <a href="export_students.php" class="w-full inline-flex items-center justify-center px-3 py-2 bg-indigo-600 hover:bg-indigo-700 text-white text-sm font-medium rounded-lg transition-colors">
                                <i class="fas fa-download mr-2"></i> Export Data
                            </a>
                            <a href="list_students.php" class="w-full inline-flex items-center justify-center px-3 py-2 bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium rounded-lg transition-colors">
                                <i class="fas fa-list mr-2"></i> All Students
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

<script>
function updateSelectedCount() {
    const checkboxes = document.querySelectorAll('.student-checkbox:checked');
    const count = checkboxes.length;
    document.getElementById('selectedCount').textContent = count;

    const updateButton = document.getElementById('updateButton');
    if (updateButton) {
        updateButton.disabled = count === 0;
    }
}

function selectAll() {
    const checkboxes = document.querySelectorAll('.student-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
    });
    updateSelectedCount();
}

function selectNone() {
    const checkboxes = document.querySelectorAll('.student-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
    updateSelectedCount();
}

// Enable/disable update fields based on checkboxes
document.addEventListener('DOMContentLoaded', function() {
    const updateCheckboxes = [
        { checkbox: 'update_department', field: 'new_department' },
        { checkbox: 'update_year', field: 'new_year' },
        { checkbox: 'update_division', field: 'new_division' },
        { checkbox: 'update_verified', field: 'new_verified' }
    ];

    updateCheckboxes.forEach(item => {
        const checkbox = document.getElementById(item.checkbox);
        const field = document.getElementById(item.field);

        if (checkbox && field) {
            checkbox.addEventListener('change', function() {
                field.disabled = !this.checked;
                if (!this.checked) {
                    field.value = '';
                }
            });
        }
    });

    // Form submission validation
    document.getElementById('updateForm').addEventListener('submit', function(e) {
        const checkedStudents = document.querySelectorAll('.student-checkbox:checked');
        const checkedFields = document.querySelectorAll('input[name^="update_"]:checked');

        if (checkedStudents.length === 0) {
            e.preventDefault();
            alert('Please select at least one student to update.');
            return false;
        }

        if (checkedFields.length === 0) {
            e.preventDefault();
            alert('Please select at least one field to update.');
            return false;
        }

        // Validate that selected fields have values
        let hasValidValues = false;
        checkedFields.forEach(checkbox => {
            const fieldName = checkbox.name.replace('update_', 'new_');
            const field = document.querySelector(`[name="${fieldName}"]`);
            if (field && field.value.trim() !== '') {
                hasValidValues = true;
            }
        });

        if (!hasValidValues) {
            e.preventDefault();
            alert('Please provide values for the selected update fields.');
            return false;
        }

        if (!confirm(`Are you sure you want to update ${checkedStudents.length} student(s)?`)) {
            e.preventDefault();
            return false;
        }

        return true;
    });

    // Initialize counts
    updateSelectedCount();
});
</script>

<?php require_once '../includes/footer.php'; ?>
