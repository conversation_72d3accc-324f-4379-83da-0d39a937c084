<?php
require_once '../includes/db.php';

// Check session
if (isset($_SESSION['faculty_id'])) {
    $role = 'faculty';
    $user_id = $_SESSION['faculty_id'];
    $user_name = $_SESSION['faculty_name'] ?? 'Faculty';
} elseif (isset($_SESSION['alumni_id'])) {
    $role = 'alumni';
    $user_id = $_SESSION['alumni_id'];
    $user_name = $_SESSION['alumni_name'] ?? 'Alumni';
} elseif (isset($_SESSION['collegeadmin_id'])) {
    $role = 'collegeadmin';
    $user_id = $_SESSION['collegeadmin_id'];
    $user_name = $_SESSION['collegeadmin_name'] ?? 'Admin';
} elseif (isset($_SESSION['student_id'])) {
    $role = 'student';
    $user_id = $_SESSION['student_id'];
    $user_name = $_SESSION['student_name'] ?? 'Student';
} else {
    // Redirect to appropriate login page instead of logout
    if (isset($_SERVER['HTTP_REFERER'])) {
        if (strpos($_SERVER['HTTP_REFERER'], 'collegeadmin') !== false) {
            header("Location: ../auth/collegeadmin_login.php");
        } else if (strpos($_SERVER['HTTP_REFERER'], 'faculty') !== false) {
            header("Location: ../auth/faculty_login.php");
        } else if (strpos($_SERVER['HTTP_REFERER'], 'alumni') !== false) {
            header("Location: ../auth/alumni_login.php");
        } else if (strpos($_SERVER['HTTP_REFERER'], 'student') !== false) {
            header("Location: ../auth/student_login.php");
        } else {
            header("Location: ../auth/collegeadmin_login.php");
        }
    } else {
        header("Location: ../auth/collegeadmin_login.php");
    }
    exit;
}
$current_page = basename($_SERVER['PHP_SELF']);

$college_id = $_SESSION['college_id'] ?? 0;

// Modify the query to join with students and alumni tables using the correct column name
$stmt = $conn->prepare("
    SELECT r.*, 
           CASE 
               WHEN r.role = 'student' THEN s.full_name 
               WHEN r.role = 'alumni' THEN a.full_name 
               WHEN r.role = 'faculty' THEN f.name
               WHEN r.role = 'collegeadmin' THEN ca.name
           END AS uploader_name
    FROM resources r
    LEFT JOIN students s ON r.uploaded_by = s.id AND r.role = 'student'
    LEFT JOIN alumni a ON r.uploaded_by = a.id AND r.role = 'alumni'
    LEFT JOIN faculties f ON r.uploaded_by = f.id AND r.role = 'faculty'
    LEFT JOIN college_admins ca ON r.uploaded_by = ca.id AND r.role = 'collegeadmin'
    WHERE r.college_id = ? 
    ORDER BY r.uploaded_at DESC
");
$stmt->execute([$college_id]);
$resources = $stmt->fetchAll(PDO::FETCH_ASSOC);

require_once '../includes/header.php';
?>

<!-- Main Content -->
<main class="flex-grow py-8 px-4">
    <div class="container mx-auto">
        <!-- Breadcrumb -->
        <nav class="mb-6 text-sm" aria-label="Breadcrumb">
            <ol class="flex items-center space-x-2">
                <li>
                    <a href="<?php
                        if ($role === 'collegeadmin') echo '../dashboards/collegeadmin_dashboard.php';
                        elseif ($role === 'faculty') echo '../dashboards/faculty_dashboard.php';
                        elseif ($role === 'alumni') echo '../alumni/alumni_dashboard.php';
                        elseif ($role === 'student') echo '../student/student_dashboard.php';
                        else echo '../index.php';
                    ?>" class="text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary-light">
                        <i class="fas fa-home"></i>
                        <span class="sr-only">Dashboard</span>
                    </a>
                </li>
                <li class="flex items-center">
                    <i class="fas fa-chevron-right text-gray-400 text-xs mx-1"></i>
                    <span class="text-gray-700 dark:text-gray-300">Resources</span>
                </li>
            </ol>
        </nav>

        <div class="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                    <i class="fas fa-book text-primary mr-3"></i>
                    Study Resources
                </h1>
                <p class="text-gray-600 dark:text-gray-400 mt-1">
                    Browse and download educational materials shared by faculty and peers
                </p>
            </div>
            <?php if ($role === 'faculty' || $role === 'alumni' || $role === 'collegeadmin'): ?>
            <div class="mt-4 md:mt-0">
                <a href="upload.php" class="btn btn-primary flex items-center">
                    <i class="fas fa-upload mr-2"></i> Upload Resource
                </a>
            </div>
            <?php endif; ?>
        </div>

        <?php if (count($resources) > 0): ?>
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 overflow-hidden">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead class="bg-gray-50 dark:bg-gray-700">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Title</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Uploaded By</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Uploaded On</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Action</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                            <?php foreach ($resources as $r): ?>
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <?php
                                            $extension = pathinfo($r['file_path'], PATHINFO_EXTENSION);
                                            $icon = match(strtolower($extension)) {
                                                'pdf' => 'fa-file-pdf',
                                                'doc', 'docx' => 'fa-file-word',
                                                'xls', 'xlsx' => 'fa-file-excel',
                                                'ppt', 'pptx' => 'fa-file-powerpoint',
                                                'zip', 'rar' => 'fa-file-archive',
                                                'jpg', 'jpeg', 'png', 'gif' => 'fa-file-image',
                                                default => 'fa-file-alt'
                                            };
                                            $color = match(strtolower($extension)) {
                                                'pdf' => 'text-red-500',
                                                'doc', 'docx' => 'text-blue-500',
                                                'xls', 'xlsx' => 'text-green-500',
                                                'ppt', 'pptx' => 'text-orange-500',
                                                'zip', 'rar' => 'text-purple-500',
                                                'jpg', 'jpeg', 'png', 'gif' => 'text-pink-500',
                                                default => 'text-gray-500'
                                            };
                                            ?>
                                            <i class="fas <?= $icon ?> <?= $color ?> mr-3 text-lg"></i>
                                            <div class="text-sm font-medium text-gray-900 dark:text-white">
                                                <?= htmlspecialchars($r['title']) ?>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500 dark:text-gray-400">
                                            <?= htmlspecialchars($r['uploader_name'] ?? 'Unknown') ?>
                                            <span class="text-xs text-gray-400 dark:text-gray-500 ml-1">(<?= ucfirst($r['role']) ?>)</span>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500 dark:text-gray-400">
                                            <?= date('F j, Y', strtotime($r['uploaded_at'])) ?>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                                        <a href="download.php?file=<?= urlencode($r['file_path']) ?>" 
                                           class="btn btn-sm btn-primary flex items-center w-fit">
                                            <i class="fas fa-download mr-1"></i> Download
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        <?php else: ?>
            <div class="bg-gray-50 dark:bg-gray-700/30 border border-gray-200 dark:border-gray-700 rounded-lg p-6 text-center">
                <div class="flex flex-col items-center">
                    <div class="h-16 w-16 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center text-gray-400 dark:text-gray-500 mb-4">
                        <i class="fas fa-book text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-1">No Resources Available</h3>
                    <p class="text-gray-500 dark:text-gray-400">
                        There are no resources available at this time.
                        <?php if ($role === 'faculty' || $role === 'alumni' || $role === 'collegeadmin'): ?>
                            Be the first to share educational materials with the community.
                        <?php endif; ?>
                    </p>
                    <?php if ($role === 'faculty' || $role === 'alumni' || $role === 'collegeadmin'): ?>
                        <a href="upload.php" class="btn btn-primary mt-4">
                            <i class="fas fa-upload mr-2"></i> Upload Resource
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>
    </div>
</main>

<?php require_once '../includes/footer.php'; ?>