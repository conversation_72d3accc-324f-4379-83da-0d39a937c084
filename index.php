<?php
// Include session configuration
require_once __DIR__ . '/includes/session_config.php';

// Auto-redirect to dashboard if user is already logged in
auto_redirect_to_dashboard();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mentoshri - Alumni Mentorship Network</title>
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts - Inter -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        'primary': '#0a66c2',
                        'primary-dark': '#004182',
                        'primary-light': '#e7f3ff',
                        'accent': '#f59e0b',
                    },
                    fontFamily: {
                        'sans': ['Inter', 'sans-serif'],
                    },
                }
            }
        }
    </script>
    <!-- Design System -->
    <link rel="stylesheet" href="assets/css/design-system.css">
    <style>
        /* Additional landing page styles */
        .hero-gradient {
            background: linear-gradient(135deg, #fef3e2 0%, #e0f2fe 100%);
        }

        .feature-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
        }

        .testimonial-card {
            position: relative;
        }

        .testimonial-card::before {
            content: '"';
            position: absolute;
            top: 10px;
            left: 15px;
            font-size: 4rem;
            color: rgba(59, 130, 246, 0.1);
            font-family: Georgia, serif;
            line-height: 1;
        }

        /* Smooth scrolling */
        html {
            scroll-behavior: smooth;
        }
</style>
</head>
<body class="font-sans bg-gray-50 dark:bg-gray-900 min-h-screen flex flex-col">
    <!-- Header -->
    <header class="bg-white shadow-sm">
        <div class="container mx-auto px-6 py-4 flex items-center justify-between">
            <!-- Logo -->
            <div class="flex items-center">
                <div class="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center mr-3">
                    <span class="text-white font-bold text-xl">M</span>
                </div>
            </div>

            <!-- Navigation -->
            <div class="flex items-center space-x-6">
                <a href="#about" class="text-gray-600 hover:text-gray-900 font-medium px-4 py-2 rounded-lg border border-gray-300 hover:border-gray-400 transition-colors">About</a>
                <a href="#contact" class="text-gray-600 hover:text-gray-900 font-medium px-4 py-2 rounded-lg border border-gray-300 hover:border-gray-400 transition-colors">Contact</a>
                <button id="loginButton" class="bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-2 rounded-lg transition-colors">Sign up</button>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="bg-white py-16 md:py-24">
        <div class="container mx-auto px-6">
            <div class="flex flex-col items-center text-center">
                <!-- Hero Image -->
                <div class="mb-12 max-w-2xl">
                    <div class="bg-gradient-to-br from-orange-100 to-blue-100 rounded-3xl p-8 shadow-lg">
                        <img src="https://images.unsplash.com/photo-1523240795612-9a054b0db644?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
                             alt="Students and graduates celebrating"
                             class="w-full h-auto rounded-2xl shadow-md">
                    </div>
                </div>

                <!-- Hero Content -->
                <div class="max-w-4xl">
                    <h1 class="text-4xl md:text-6xl font-bold text-gray-900 mb-6 leading-tight">
                        Connect With Alumni Who've Been There
                    </h1>
                    <p class="text-xl text-gray-600 mb-12 max-w-3xl mx-auto leading-relaxed">
                        Bridging students with successful alumni for mentorship, guidance, and career opportunities.
                    </p>

                    <!-- CTA Buttons -->
                    <div class="flex flex-col sm:flex-row gap-4 justify-center">
                        <button id="getStartedBtn" class="bg-blue-600 hover:bg-blue-700 text-white font-semibold px-8 py-4 rounded-lg transition-colors text-lg">
                            Get Started
                        </button>
                        <button class="bg-white hover:bg-gray-50 text-gray-700 font-semibold px-8 py-4 rounded-lg border border-gray-300 hover:border-gray-400 transition-colors text-lg">
                            Learn more
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="about" class="py-16 bg-gray-50">
        <div class="container mx-auto px-6">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold mb-4 text-gray-900">How Mentoshri Works</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">Our platform connects students with alumni mentors who can provide guidance, share experiences, and help navigate career paths.</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <!-- Feature 1 -->
                <div class="feature-card bg-white p-8 rounded-xl shadow-sm border border-gray-100 text-center hover:shadow-lg">
                    <div class="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-user-graduate text-blue-600 text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-4 text-gray-900">Connect with Alumni</h3>
                    <p class="text-gray-600">Find and connect with alumni from your college who are working in your field of interest.</p>
                </div>

                <!-- Feature 2 -->
                <div class="feature-card bg-white p-8 rounded-xl shadow-sm border border-gray-100 text-center hover:shadow-lg">
                    <div class="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-comments text-blue-600 text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-4 text-gray-900">Personalized Mentorship</h3>
                    <p class="text-gray-600">Get one-on-one guidance through chat, video calls, or in-person meetings.</p>
                </div>

                <!-- Feature 3 -->
                <div class="feature-card bg-white p-8 rounded-xl shadow-sm border border-gray-100 text-center hover:shadow-lg">
                    <div class="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-briefcase text-blue-600 text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-4 text-gray-900">Career Opportunities</h3>
                    <p class="text-gray-600">Access job postings, internships, and referrals from your college's alumni network.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-6">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold mb-4 text-gray-900">Success Stories</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">Hear from students and alumni who have benefited from our platform.</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <!-- Testimonial 1 -->
                <div class="testimonial-card bg-gray-50 p-8 rounded-xl border border-gray-100">
                    <p class="text-gray-600 mb-6 pl-6">Finding a mentor through Mentoshri completely changed my career trajectory. My mentor helped me navigate job interviews and prepare for my role at Google.</p>
                    <div class="flex items-center">
                        <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=60&q=80" alt="Student" class="w-12 h-12 rounded-full mr-4 object-cover">
                        <div>
                            <h4 class="font-semibold text-gray-900">Priya Sharma</h4>
                            <p class="text-gray-500 text-sm">Computer Science, 2022</p>
                        </div>
                    </div>
                </div>

                <!-- Testimonial 2 -->
                <div class="testimonial-card bg-gray-50 p-8 rounded-xl border border-gray-100">
                    <p class="text-gray-600 mb-6 pl-6">As an alumnus, mentoring students has been incredibly rewarding. It's a way to give back and stay connected with my alma mater.</p>
                    <div class="flex items-center">
                        <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=60&q=80" alt="Alumni" class="w-12 h-12 rounded-full mr-4 object-cover">
                        <div>
                            <h4 class="font-semibold text-gray-900">Rahul Patel</h4>
                            <p class="text-gray-500 text-sm">Senior Product Manager, Amazon</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="bg-blue-600 text-white py-16">
        <div class="container mx-auto px-6 text-center">
            <h2 class="text-3xl font-bold mb-4">Ready to Get Started?</h2>
            <p class="text-xl mb-8 max-w-2xl mx-auto opacity-90">Join our platform today and connect with mentors who can help shape your future.</p>
            <button id="joinNowBtn" class="bg-white text-blue-600 hover:bg-gray-100 font-semibold px-8 py-3 rounded-lg transition-colors">Join Now</button>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="container mx-auto px-6">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <div class="flex items-center mb-4">
                        <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center mr-2">
                            <span class="text-white font-bold text-sm">M</span>
                        </div>
                        <span class="font-bold text-xl">Mentoshri</span>
                    </div>
                    <p class="text-gray-400 mb-4">Connecting students with alumni mentors for career guidance and professional development.</p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-blue-400"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="text-gray-400 hover:text-blue-400"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-gray-400 hover:text-blue-400"><i class="fab fa-linkedin-in"></i></a>
                        <a href="#" class="text-gray-400 hover:text-blue-400"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>

                <div>
                    <h4 class="font-semibold mb-4">Platform</h4>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-400 hover:text-white">How It Works</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white">Features</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white">Testimonials</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white">Success Stories</a></li>
                    </ul>
                </div>

                <div>
                    <h4 class="font-semibold mb-4">Support</h4>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-400 hover:text-white">Help Center</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white">FAQs</a></li>
                        <li id="contact"><a href="#contact" class="text-gray-400 hover:text-white">Contact Us</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white">Terms of Service</a></li>
                    </ul>
                </div>

                <div>
                    <h4 class="font-semibold mb-4">Company</h4>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-400 hover:text-white">About Us</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white">Our Team</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white">Careers</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white">Privacy Policy</a></li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-gray-800 mt-12 pt-8 text-center">
                <p class="text-gray-400">&copy; <?= date('Y') ?> Mentoshri. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Login Modal -->
    <div id="loginModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4 hidden">
        <div class="bg-white rounded-xl max-w-md w-full p-6 shadow-2xl" role="dialog">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-xl font-semibold text-gray-800">Choose Your Role</h3>
                <button id="closeLoginModal" class="text-gray-500 hover:text-gray-700 p-1">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            <div class="space-y-3">
                <a href="auth/superadmin_login.php" class="flex items-center justify-center w-full p-4 bg-gray-50 hover:bg-gray-100 text-gray-800 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors">
                    <i class="fas fa-user-shield mr-3 text-blue-600"></i> Super Admin
                </a>
                <a href="auth/collegeadmin_login.php" class="flex items-center justify-center w-full p-4 bg-gray-50 hover:bg-gray-100 text-gray-800 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors">
                    <i class="fas fa-university mr-3 text-blue-600"></i> College Admin
                </a>
                <a href="auth/faculty_login.php" class="flex items-center justify-center w-full p-4 bg-gray-50 hover:bg-gray-100 text-gray-800 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors">
                    <i class="fas fa-chalkboard-teacher mr-3 text-blue-600"></i> Faculty
                </a>
                <a href="auth/alumni_login.php" class="flex items-center justify-center w-full p-4 bg-gray-50 hover:bg-gray-100 text-gray-800 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors">
                    <i class="fas fa-user-tie mr-3 text-blue-600"></i> Alumni
                </a>
                <a href="auth/student_login.php" class="flex items-center justify-center w-full p-4 bg-gray-50 hover:bg-gray-100 text-gray-800 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors">
                    <i class="fas fa-user-graduate mr-3 text-blue-600"></i> Student
                </a>
            </div>
        </div>
    </div>

    <script>
        // Modal functionality
        const loginModal = document.getElementById('loginModal');
        const loginButton = document.getElementById('loginButton');
        const closeLoginModal = document.getElementById('closeLoginModal');
        const getStartedBtn = document.getElementById('getStartedBtn');
        const joinNowBtn = document.getElementById('joinNowBtn');

        function openLoginModal() {
            loginModal.classList.remove('hidden');
            document.body.style.overflow = 'hidden'; // Prevent background scrolling
        }

        function closeModal() {
            loginModal.classList.add('hidden');
            document.body.style.overflow = 'auto'; // Restore scrolling
        }

        // Event listeners
        loginButton.addEventListener('click', openLoginModal);
        getStartedBtn.addEventListener('click', openLoginModal);
        joinNowBtn.addEventListener('click', openLoginModal);
        closeLoginModal.addEventListener('click', closeModal);

        // Close modal when clicking outside
        loginModal.addEventListener('click', (e) => {
            if (e.target === loginModal) {
                closeModal();
            }
        });

        // Close on escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && !loginModal.classList.contains('hidden')) {
                closeModal();
            }
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add animation on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe feature cards and testimonials
        document.querySelectorAll('.feature-card, .testimonial-card').forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(card);
        });
    </script>
</body>
</html>
