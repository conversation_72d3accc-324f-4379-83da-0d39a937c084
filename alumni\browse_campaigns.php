<?php
require_once '../includes/db.php';
require_once '../includes/header.php';
require_once '../includes/campaign_helpers.php';
session_start();

if (!isset($_SESSION['alumni_id'])) {
    header("Location: ../auth/alumni_login.php");
    exit;
}

$alumni_id = $_SESSION['alumni_id'];
$college_id = $_SESSION['college_id'];

// Fetch all active campaigns using helper
$campaigns = getActiveCampaigns($conn);
?>

<div class="container mt-4">
    <h2>🎁 Explore Campaigns</h2>
    <p class="text-muted">
        Discover active initiatives where you can give back — by donating, volunteering, or offering support in kind.
    </p>

    <?php if (count($campaigns) === 0): ?>
        <div class="alert alert-info">
            No active campaigns at the moment. Please check back soon.
        </div>
    <?php endif; ?>

    <div class="row">
        <?php foreach ($campaigns as $campaign): ?>
            <div class="col-md-6">
                <div class="card mb-4 shadow-sm h-100">
                    <?php if ($campaign['thumbnail_path'] && file_exists('../' . $campaign['thumbnail_path'])): ?>
                        <img src="../<?= htmlspecialchars($campaign['thumbnail_path']) ?>" class="card-img-top" style="height: 200px; object-fit: cover;">
                    <?php endif; ?>
                    <div class="card-body">
                        <h5 class="card-title"><?= htmlspecialchars($campaign['title']) ?></h5>
                        <p class="card-text small text-muted">
                            <?= htmlspecialchars($campaign['college_name']) ?> | Category: <?= ucfirst($campaign['category']) ?>
                        </p>
                        <p class="card-text">
                            <?= nl2br(htmlspecialchars(substr($campaign['description'], 0, 120))) ?>...
                        </p>
                        <p class="card-text">
                            <strong>Needs:</strong> <?= str_replace(',', ', ', $campaign['contribution_types']) ?>
                        </p>
                        <?php if (!empty($campaign['goal_amount'])): ?>
                            <p class="card-text">
                                <strong>Target:</strong> ₹<?= number_format($campaign['goal_amount']) ?>
                            </p>
                        <?php endif; ?>
                        <a href="contribute.php?campaign_id=<?= $campaign['id'] ?>" class="btn btn-primary btn-sm">Contribute</a>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
</div>

<?php require_once '../includes/footer.php'; ?>
