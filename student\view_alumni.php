<?php
require_once '../includes/db.php';
require_once '../includes/header.php';

if (!isset($_SESSION['student_id'])) {
    header("Location: ../auth/student_login.php");
    exit;
}

$student_id = $_SESSION['student_id'];
$college_id = $_SESSION['college_id'];

// Fetch all verified alumni from same college
$alumniStmt = $conn->prepare("SELECT id, full_name, current_position, company, profile_photo FROM alumni WHERE college_id = ? AND verified = 1");
$alumniStmt->execute([$college_id]);
$alumniList = $alumniStmt->fetchAll(PDO::FETCH_ASSOC);

// Fetch all student requests
$reqStmt = $conn->prepare("SELECT alumni_id, status FROM student_requests WHERE student_id = ?");
$reqStmt->execute([$student_id]);
$requests = [];
foreach ($reqStmt->fetchAll(PDO::FETCH_ASSOC) as $r) {
    $requests[$r['alumni_id']] = $r['status'];
}
?>

<h2 class="mb-4">Verified Alumni</h2>

<div class="row">
<?php foreach ($alumniList as $alumni): ?>
    <div class="col-md-6">
        <div class="card mb-3 shadow-sm">
            <div class="card-body d-flex align-items-center">
                <img src="../uploads/<?= file_exists("../uploads/{$alumni['profile_photo']}") ? $alumni['profile_photo'] : 'profile.gif' ?>" class="rounded-circle me-3" width="60" height="60">
                <div class="flex-grow-1">
                    <h5 class="mb-0"><?= htmlspecialchars($alumni['full_name']) ?></h5>
                    <small class="text-muted"><?= htmlspecialchars($alumni['current_position']) ?> @ <?= htmlspecialchars($alumni['company']) ?></small>
                </div>
                <div class="text-end">
                    <?php if (!isset($requests[$alumni['id']])): ?>
                        <a href="request_mentor.php?alumni_id=<?= $alumni['id'] ?>" class="btn btn-sm btn-primary">Request Mentorship</a>
                    <?php elseif ($requests[$alumni['id']] === 'accepted'): ?>
                        <a href="../chat/chat.php?alumni_id=<?= $alumni['id'] ?>" class="btn btn-sm btn-success">Chat</a>
                    <?php else: ?>
                        <button class="btn btn-sm btn-secondary" disabled>Request Sent</button>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
<?php endforeach; ?>
</div>

<?php require_once '../includes/footer.php'; ?>
