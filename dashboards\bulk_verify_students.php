<?php
session_start();
require_once '../includes/db.php';
require_once '../includes/db_schema_check.php';
require_once '../notify/send_email.php';

if (!isset($_SESSION['collegeadmin_id'])) {
    header("Location: ../auth/collegeadmin_login.php");
    exit;
}

$college_id = $_SESSION['college_id'];
$admin_name = $_SESSION['collegeadmin_name'] ?? 'Admin';

// Set role explicitly for the sidebar
$role = 'collegeadmin';
$user_id = $_SESSION['collegeadmin_id'];
$user_name = $_SESSION['collegeadmin_name'];
$current_page = basename($_SERVER['PHP_SELF']);

$error = '';
$success = '';
$students = [];
$total_unverified = 0;

// Get college details
$collegeStmt = $conn->prepare("SELECT name FROM colleges WHERE id = ?");
$collegeStmt->execute([$college_id]);
$college = $collegeStmt->fetch(PDO::FETCH_ASSOC);

// Get departments for filter
$deptStmt = $conn->prepare("SELECT DISTINCT department FROM students WHERE college_id = ? AND verified = 0 ORDER BY department ASC");
$deptStmt->execute([$college_id]);
$departments = $deptStmt->fetchAll(PDO::FETCH_COLUMN);

// Get years for filter
$yearStmt = $conn->prepare("SELECT DISTINCT year FROM students WHERE college_id = ? AND verified = 0 ORDER BY year ASC");
$yearStmt->execute([$college_id]);
$years = $yearStmt->fetchAll(PDO::FETCH_COLUMN);

// Count total unverified students
$countStmt = $conn->prepare("SELECT COUNT(*) FROM students WHERE college_id = ? AND verified = 0");
$countStmt->execute([$college_id]);
$total_unverified = $countStmt->fetchColumn();

// Handle form submission for bulk verification
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['student_ids']) && is_array($_POST['student_ids']) && !empty($_POST['student_ids'])) {
        $student_ids = $_POST['student_ids'];
        $send_notifications = isset($_POST['send_notifications']) ? true : false;
        
        try {
            // Start transaction
            $conn->beginTransaction();
            
            // Build placeholders for SQL query
            $placeholders = implode(',', array_fill(0, count($student_ids), '?'));
            
            // Update verification status
            $stmt = $conn->prepare("UPDATE students SET verified = 1 WHERE id IN ($placeholders) AND college_id = ?");
            $params = array_merge($student_ids, [$college_id]);
            $stmt->execute($params);
            
            $verified_count = $stmt->rowCount();
            
            // Send email notifications if requested
            if ($send_notifications && $verified_count > 0) {
                // Get student details for email
                $emailStmt = $conn->prepare("SELECT id, full_name, email FROM students WHERE id IN ($placeholders)");
                $emailStmt->execute($student_ids);
                $students_to_notify = $emailStmt->fetchAll(PDO::FETCH_ASSOC);
                
                $notification_count = 0;
                
                foreach ($students_to_notify as $student) {
                    $subject = "Your Student Account Has Been Verified - " . htmlspecialchars($college['name']);
                    $message = "<p>Dear " . htmlspecialchars($student['full_name']) . ",</p>";
                    $message .= "<p>Your student account at " . htmlspecialchars($college['name']) . " has been verified.</p>";
                    $message .= "<p>You can now log in and access all features of the platform.</p>";
                    $message .= "<p>Login at: <a href='https://" . $_SERVER['HTTP_HOST'] . "/auth/student_login.php'>Student Login</a></p>";
                    $message .= "<p>Regards,<br>" . htmlspecialchars($admin_name) . "<br>Administration Team<br>" . htmlspecialchars($college['name']) . "</p>";
                    
                    $email_sent = sendEmail($student['email'], $student['full_name'], $subject, $message);
                    if ($email_sent) {
                        $notification_count++;
                    }
                }
                
                $success = "$verified_count student" . ($verified_count !== 1 ? "s" : "") . " verified successfully.";
                if ($notification_count > 0) {
                    $success .= " Email notifications sent to $notification_count student" . ($notification_count !== 1 ? "s" : "") . ".";
                }
            } else {
                $success = "$verified_count student" . ($verified_count !== 1 ? "s" : "") . " verified successfully.";
            }
            
            // Commit transaction
            $conn->commit();
            
        } catch (PDOException $e) {
            // Rollback transaction on error
            $conn->rollBack();
            $error = "Database error: " . $e->getMessage();
        }
    } else {
        $error = "No students selected for verification.";
    }
}

// Apply filters
$conditions = ["college_id = ? AND verified = 0"];
$params = [$college_id];

if (!empty($_GET['department'])) {
    $conditions[] = "department = ?";
    $params[] = $_GET['department'];
}

if (!empty($_GET['year'])) {
    $conditions[] = "year = ?";
    $params[] = $_GET['year'];
}

$whereClause = implode(' AND ', $conditions);

// Get unverified students with filters
$sql = "SELECT * FROM students WHERE $whereClause ORDER BY created_at DESC LIMIT 100";
$stmt = $conn->prepare($sql);
$stmt->execute($params);
$students = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Count filtered results
$countFilteredStmt = $conn->prepare("SELECT COUNT(*) FROM students WHERE $whereClause");
$countFilteredStmt->execute($params);
$filtered_count = $countFilteredStmt->fetchColumn();

// Include the header
require_once '../includes/header.php';
?>

<!-- Main Content -->
<main class="flex-grow py-8 px-4">
        <div class="container mx-auto">
            <!-- Breadcrumb -->
            <nav class="mb-6 text-sm" aria-label="Breadcrumb">
                <ol class="flex items-center space-x-2">
                    <li>
                        <a href="collegeadmin_dashboard.php" class="text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary-light">
                            <i class="fas fa-home"></i>
                            <span class="sr-only">Dashboard</span>
                        </a>
                    </li>
                    <li class="flex items-center">
                        <i class="fas fa-chevron-right text-gray-400 text-xs mx-1"></i>
                        <a href="list_students.php" class="text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary-light">
                            Student Management
                        </a>
                    </li>
                    <li class="flex items-center">
                        <i class="fas fa-chevron-right text-gray-400 text-xs mx-1"></i>
                        <span class="text-gray-700 dark:text-gray-300">Bulk Verify Students</span>
                    </li>
                </ol>
            </nav>

            <div class="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                        <i class="fas fa-user-check text-primary mr-3"></i>
                        Bulk Verify Students
                    </h1>
                    <p class="text-gray-600 dark:text-gray-400 mt-1">
                        Verify multiple student accounts at once for <?= htmlspecialchars($college['name'] ?? 'your college') ?>
                    </p>
                </div>
                <div class="mt-4 md:mt-0">
                    <a href="list_students.php" class="btn btn-outline flex items-center">
                        <i class="fas fa-arrow-left mr-2"></i> Back to Students
                    </a>
                </div>
            </div>
            
            <?php if ($error): ?>
                <div class="bg-red-50 dark:bg-red-900/30 border-l-4 border-red-500 text-red-700 dark:text-red-400 p-4 mb-6 flex justify-between items-center" role="alert">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-circle mr-2"></i>
                        <span><?= htmlspecialchars($error) ?></span>
                    </div>
                    <button type="button" class="text-red-700 dark:text-red-400 hover:opacity-75" onclick="this.parentElement.style.display='none';">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="bg-green-50 dark:bg-green-900/30 border-l-4 border-green-500 text-green-700 dark:text-green-400 p-4 mb-6 flex justify-between items-center" role="alert">
                    <div class="flex items-center">
                        <i class="fas fa-check-circle mr-2"></i>
                        <span><?= htmlspecialchars($success) ?></span>
                    </div>
                    <button type="button" class="text-green-700 dark:text-green-400 hover:opacity-75" onclick="this.parentElement.style.display='none';">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            <?php endif; ?>
            
            <!-- Summary Card -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 p-6 mb-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="flex items-center">
                        <div class="bg-yellow-100 dark:bg-yellow-900/30 p-3 rounded-full mr-4">
                            <i class="fas fa-user-clock text-yellow-600 dark:text-yellow-400 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Unverified Students</h3>
                            <p class="text-2xl font-bold text-gray-900 dark:text-white"><?= $total_unverified ?></p>
                        </div>
                    </div>
                    
                    <div class="flex items-center">
                        <div class="bg-blue-100 dark:bg-blue-900/30 p-3 rounded-full mr-4">
                            <i class="fas fa-filter text-blue-600 dark:text-blue-400 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Filtered Results</h3>
                            <p class="text-2xl font-bold text-gray-900 dark:text-white"><?= $filtered_count ?></p>
                        </div>
                    </div>
                    
                    <div class="flex items-center">
                        <div class="bg-green-100 dark:bg-green-900/30 p-3 rounded-full mr-4">
                            <i class="fas fa-check-double text-green-600 dark:text-green-400 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Selected</h3>
                            <p class="text-2xl font-bold text-gray-900 dark:text-white" id="selectedCount">0</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Filters -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 mb-6">
                <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
                    <h2 class="font-semibold text-gray-800 dark:text-white flex items-center">
                        <i class="fas fa-filter mr-2 text-primary"></i> Filter Students
                    </h2>
                </div>
                
                <div class="p-6">
                    <form method="GET" id="filterForm" class="space-y-4">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div>
                                <label for="department" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Department</label>
                                <select name="department" id="department" class="form-select w-full">
                                    <option value="">All Departments</option>
                                    <?php foreach ($departments as $dept): ?>
                                        <option value="<?= htmlspecialchars($dept) ?>" <?= isset($_GET['department']) && $_GET['department'] === $dept ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($dept) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div>
                                <label for="year" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Year</label>
                                <select name="year" id="year" class="form-select w-full">
                                    <option value="">All Years</option>
                                    <?php foreach ($years as $year): ?>
                                        <option value="<?= htmlspecialchars($year) ?>" <?= isset($_GET['year']) && $_GET['year'] == $year ? 'selected' : '' ?>>
                                            Year <?= htmlspecialchars($year) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div class="flex items-end">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-filter mr-2"></i> Apply Filters
                                </button>
                                <?php if (!empty($_GET['department']) || !empty($_GET['year'])): ?>
                                    <a href="bulk_verify_students.php" class="btn btn-outline ml-2">
                                        <i class="fas fa-times mr-2"></i> Clear
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            
            <?php if (count($students) > 0): ?>
                <!-- Student List -->
                <form method="post" id="verifyForm">
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 overflow-hidden mb-6">
                        <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4 flex justify-between items-center">
                            <h2 class="font-semibold text-gray-800 dark:text-white flex items-center">
                                <i class="fas fa-user-clock mr-2 text-primary"></i> 
                                Unverified Students <span class="ml-2 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300 text-xs font-medium px-2.5 py-0.5 rounded-full"><?= $filtered_count ?></span>
                            </h2>
                            <div class="text-sm text-gray-600 dark:text-gray-400">
                                <?= date('F j, Y') ?>
                            </div>
                        </div>
                        
                        <div class="overflow-x-auto">
                            <table class="w-full text-left">
                                <thead class="bg-gray-50 dark:bg-gray-700 text-xs uppercase">
                                    <tr>
                                        <th class="px-6 py-3 text-gray-500 dark:text-gray-400 font-medium">
                                            <div class="flex items-center">
                                                <input 
                                                    type="checkbox" 
                                                    id="selectAll" 
                                                    class="w-4 h-4 text-primary bg-gray-100 border-gray-300 rounded focus:ring-primary-light dark:bg-gray-700 dark:border-gray-600"
                                                >
                                                <label for="selectAll" class="sr-only">Select All</label>
                                            </div>
                                        </th>
                                        <th class="px-6 py-3 text-gray-500 dark:text-gray-400 font-medium">Student</th>
                                        <th class="px-6 py-3 text-gray-500 dark:text-gray-400 font-medium">Roll Number</th>
                                        <th class="px-6 py-3 text-gray-500 dark:text-gray-400 font-medium">Department</th>
                                        <th class="px-6 py-3 text-gray-500 dark:text-gray-400 font-medium">Year</th>
                                        <th class="px-6 py-3 text-gray-500 dark:text-gray-400 font-medium">Registration Date</th>
                                        <th class="px-6 py-3 text-gray-500 dark:text-gray-400 font-medium">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($students as $student): ?>
                                        <tr class="border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700/30">
                                            <td class="px-6 py-4">
                                                <div class="flex items-center">
                                                    <input 
                                                        type="checkbox" 
                                                        name="student_ids[]" 
                                                        value="<?= $student['id'] ?>" 
                                                        class="student-checkbox w-4 h-4 text-primary bg-gray-100 border-gray-300 rounded focus:ring-primary-light dark:bg-gray-700 dark:border-gray-600"
                                                    >
                                                </div>
                                            </td>
                                            <td class="px-6 py-4">
                                                <div class="flex items-center">
                                                    <div class="w-10 h-10 rounded-full bg-gray-100 dark:bg-gray-700 overflow-hidden mr-3 flex-shrink-0">
                                                        <?php if (!empty($student['profile_photo']) && file_exists("../uploads/{$student['profile_photo']}")): ?>
                                                            <img src="../uploads/<?= htmlspecialchars($student['profile_photo']) ?>" alt="Student" class="w-full h-full object-cover">
                                                        <?php else: ?>
                                                            <div class="w-full h-full flex items-center justify-center text-gray-400 dark:text-gray-500">
                                                                <i class="fas fa-user-graduate"></i>
                                                            </div>
                                                        <?php endif; ?>
                                                    </div>
                                                    <div>
                                                        <div class="font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($student['full_name']) ?></div>
                                                        <div class="text-sm text-gray-500 dark:text-gray-400"><?= htmlspecialchars($student['email']) ?></div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 text-gray-700 dark:text-gray-300">
                                                <?= htmlspecialchars($student['roll_number']) ?>
                                            </td>
                                            <td class="px-6 py-4">
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300">
                                                    <?= htmlspecialchars($student['department']) ?>
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 text-gray-700 dark:text-gray-300">
                                                Year <?= $student['year'] ?>
                                            </td>
                                            <td class="px-6 py-4 text-gray-700 dark:text-gray-300">
                                                <?= date('M d, Y', strtotime($student['created_at'])) ?>
                                            </td>
                                            <td class="px-6 py-4">
                                                <div class="flex space-x-2">
                                                    <a href="view_student.php?id=<?= $student['id'] ?>" class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300" title="View Student">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="verify_student.php?id=<?= $student['id'] ?>" class="text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300" title="Verify Student">
                                                        <i class="fas fa-user-check"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="p-6 bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
                            <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                                <div class="flex items-center mb-4 md:mb-0">
                                    <div class="flex items-center">
                                        <input 
                                            type="checkbox" 
                                            id="send_notifications" 
                                            name="send_notifications" 
                                            checked
                                            class="w-4 h-4 text-primary focus:ring-primary-light border-gray-300 rounded"
                                        >
                                        <label for="send_notifications" class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                                            Send email notifications to students
                                        </label>
                                    </div>
                                </div>
                                
                                <button type="submit" id="verifySelectedBtn" class="btn btn-primary" disabled>
                                    <i class="fas fa-check-circle mr-2"></i> Verify Selected Students
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
                
                <!-- Tips Card -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 p-6">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
                        <i class="fas fa-lightbulb text-yellow-500 mr-2"></i> Verification Tips
                    </h3>
                    <ul class="space-y-3 text-gray-600 dark:text-gray-400">
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                            <span>Use filters to narrow down the students you want to verify.</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                            <span>Check student details before verifying to ensure they belong to your institution.</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                            <span>Sending email notifications helps students know their accounts are ready to use.</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                            <span>Verified students can access all platform features including mentorship requests.</span>
                        </li>
                    </ul>
                </div>
            <?php else: ?>
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 p-10 text-center">
                    <div class="w-20 h-20 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-check-double text-gray-400 dark:text-gray-500 text-3xl"></i>
                    </div>
                    <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-2">All caught up!</h2>
                    <p class="text-gray-600 dark:text-gray-400 mb-6">
                        There are no unverified students that match your filters.
                    </p>
                    <div class="flex justify-center space-x-4">
                        <a href="list_students.php" class="btn btn-outline">
                            <i class="fas fa-users mr-2"></i> View All Students
                        </a>
                        <a href="add_student.php" class="btn btn-primary">
                            <i class="fas fa-user-plus mr-2"></i> Add Student
                        </a>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </main>

<?php
// Include the footer
require_once '../includes/footer.php';
?>

<script>
    // Select all checkbox functionality
    const selectAllCheckbox = document.getElementById('selectAll');
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('.student-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateSelectedCount();
            updateVerifyButtonState();
        });
        
        // Individual checkbox change handler
        const studentCheckboxes = document.querySelectorAll('.student-checkbox');
        studentCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                // Update "Select All" checkbox state
                const allCheckboxes = document.querySelectorAll('.student-checkbox');
                const checkedCheckboxes = document.querySelectorAll('.student-checkbox:checked');
                selectAllCheckbox.checked = allCheckboxes.length === checkedCheckboxes.length;
                
                updateSelectedCount();
                updateVerifyButtonState();
            });
        });
    }
    
    // Update selected count display
    function updateSelectedCount() {
        const selectedCountElement = document.getElementById('selectedCount');
        const checkedCheckboxes = document.querySelectorAll('.student-checkbox:checked');
        if (selectedCountElement) {
            selectedCountElement.textContent = checkedCheckboxes.length;
        }
    }
    
    // Enable/disable verify button based on selection
    function updateVerifyButtonState() {
        const verifyButton = document.getElementById('verifySelectedBtn');
        const checkedCheckboxes = document.querySelectorAll('.student-checkbox:checked');
        
        if (verifyButton) {
            if (checkedCheckboxes.length > 0) {
                verifyButton.disabled = false;
                verifyButton.classList.remove('opacity-50', 'cursor-not-allowed');
            } else {
                verifyButton.disabled = true;
                verifyButton.classList.add('opacity-50', 'cursor-not-allowed');
            }
        }
    }
    
    // Initialize counts and button states
    updateSelectedCount();
    updateVerifyButtonState();
    
    // Form submission confirmation
    const verifyForm = document.getElementById('verifyForm');
    if (verifyForm) {
        verifyForm.addEventListener('submit', function(e) {
            const checkedCheckboxes = document.querySelectorAll('.student-checkbox:checked');
            if (checkedCheckboxes.length === 0) {
                e.preventDefault();
                alert('Please select at least one student to verify.');
                return false;
            }
            
            if (checkedCheckboxes.length > 20) {
                if (!confirm(`You are about to verify ${checkedCheckboxes.length} students. This might take a moment. Continue?`)) {
                    e.preventDefault();
                    return false;
                }
            }
            
            return true;
        });
    }
</script> 