<?php
session_start();
require_once '../includes/db.php';
require_once '../includes/header.php';

if (!isset($_SESSION['superadmin_id'])) {
    header("Location: ../auth/superadmin_login.php");
    exit;
}

// Perform system checks
$systemChecks = [];

// Database connection check
try {
    $dbStatus = [
        'name' => 'Database Connection',
        'status' => 'Online',
        'status_class' => 'badge-success',
        'details' => 'Connected to MySQL database',
        'icon' => 'database',
        'health' => 100
    ];
    
    // Test query
    $stmt = $conn->query("SELECT 1");
    $stmt->fetch();
} catch (PDOException $e) {
    $dbStatus = [
        'name' => 'Database Connection',
        'status' => 'Offline',
        'status_class' => 'badge-danger',
        'details' => 'Error: ' . $e->getMessage(),
        'icon' => 'database',
        'health' => 0
    ];
}
$systemChecks[] = $dbStatus;

// PHP version check
$requiredPhpVersion = '7.4.0';
$currentPhpVersion = PHP_VERSION;
$phpVersionStatus = version_compare($currentPhpVersion, $requiredPhpVersion, '>=');
$phpVersionHealth = $phpVersionStatus ? 100 : 60;

$systemChecks[] = [
    'name' => 'PHP Version',
    'status' => $phpVersionStatus ? 'Compatible' : 'Outdated',
    'status_class' => $phpVersionStatus ? 'badge-success' : 'badge-warning',
    'details' => "Current: $currentPhpVersion (Required: $requiredPhpVersion or higher)",
    'icon' => 'code',
    'health' => $phpVersionHealth
];

// File permissions check
$uploadDir = '../uploads';
$uploadDirWritable = is_writable($uploadDir);
$uploadHealth = $uploadDirWritable ? 100 : 0;

$systemChecks[] = [
    'name' => 'Upload Directory',
    'status' => $uploadDirWritable ? 'Writable' : 'Not Writable',
    'status_class' => $uploadDirWritable ? 'badge-success' : 'badge-danger',
    'details' => $uploadDirWritable ? 'Directory has correct permissions' : 'Directory permissions need to be set to allow uploads',
    'icon' => 'folder',
    'health' => $uploadHealth
];

// Check disk space
$totalSpace = disk_total_space('/');
$freeSpace = disk_free_space('/');
$usedSpace = $totalSpace - $freeSpace;
$percentUsed = round(($usedSpace / $totalSpace) * 100, 2);
$spaceStatus = $percentUsed < 90 ? 'Sufficient' : 'Low';
$spaceHealth = 100 - $percentUsed; // Invert percentage to represent health

$systemChecks[] = [
    'name' => 'Disk Space',
    'status' => $spaceStatus,
    'status_class' => $percentUsed < 90 ? 'badge-success' : ($percentUsed < 95 ? 'badge-warning' : 'badge-danger'),
    'details' => formatBytes($freeSpace) . ' free of ' . formatBytes($totalSpace) . ' (' . $percentUsed . '% used)',
    'icon' => 'hdd',
    'health' => $spaceHealth
];

// Check required PHP extensions
$requiredExtensions = ['pdo', 'pdo_mysql', 'gd', 'mbstring', 'json', 'curl'];
$missingExtensions = [];

foreach ($requiredExtensions as $extension) {
    if (!extension_loaded($extension)) {
        $missingExtensions[] = $extension;
    }
}

$extensionsHealth = empty($missingExtensions) ? 100 : (100 - ((count($missingExtensions) / count($requiredExtensions)) * 100));

$systemChecks[] = [
    'name' => 'PHP Extensions',
    'status' => empty($missingExtensions) ? 'All Installed' : 'Missing',
    'status_class' => empty($missingExtensions) ? 'badge-success' : 'badge-danger',
    'details' => empty($missingExtensions) ? 'All required extensions are installed' : 'Missing: ' . implode(', ', $missingExtensions),
    'icon' => 'puzzle-piece',
    'health' => $extensionsHealth
];

// Check email configuration
$emailConfigured = function_exists('mail');
$emailHealth = $emailConfigured ? 100 : 60;

$systemChecks[] = [
    'name' => 'Email Service',
    'status' => $emailConfigured ? 'Configured' : 'Not Configured',
    'status_class' => $emailConfigured ? 'badge-success' : 'badge-warning',
    'details' => $emailConfigured ? 'PHP mail function is available' : 'PHP mail function is not available',
    'icon' => 'envelope',
    'health' => $emailHealth
];

// Get server info
$serverInfo = [
    'Server Software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
    'Server OS' => PHP_OS,
    'Server IP' => $_SERVER['SERVER_ADDR'] ?? 'Unknown',
    'Server Name' => $_SERVER['SERVER_NAME'] ?? 'Unknown',
    'PHP Version' => PHP_VERSION,
    'MySQL Version' => getMySQLVersion($conn),
    'Max Upload Size' => ini_get('upload_max_filesize'),
    'Max Post Size' => ini_get('post_max_size'),
    'Memory Limit' => ini_get('memory_limit'),
    'Execution Time Limit' => ini_get('max_execution_time') . ' seconds'
];

// Check for system updates (simulated)
$systemUpdates = [
    [
        'component' => 'Core System',
        'current_version' => '1.5.2',
        'latest_version' => '1.5.2',
        'update_available' => false,
        'importance' => 'none',
        'date' => '2023-10-15'
    ],
    [
        'component' => 'Admin Dashboard',
        'current_version' => '2.1.0',
        'latest_version' => '2.1.3',
        'update_available' => true,
        'importance' => 'low',
        'date' => '2023-11-20'
    ],
    [
        'component' => 'Security Module',
        'current_version' => '3.0.1',
        'latest_version' => '3.2.0',
        'update_available' => true,
        'importance' => 'high',
        'date' => '2023-12-05'
    ]
];

// Recent backups (simulated)
$recentBackups = [
    [
        'name' => 'Full System Backup',
        'size' => '258.4 MB',
        'date' => '2023-12-01 08:30:15',
        'type' => 'automatic',
        'status' => 'completed'
    ],
    [
        'name' => 'Database Backup',
        'size' => '42.7 MB',
        'date' => '2023-11-25 14:22:10',
        'type' => 'manual',
        'status' => 'completed'
    ],
    [
        'name' => 'User Files Backup',
        'size' => '189.2 MB',
        'date' => '2023-11-18 22:15:03',
        'type' => 'automatic',
        'status' => 'completed'
    ]
];

// Helper function to format bytes
function formatBytes($bytes, $precision = 2) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    
    $bytes /= (1 << (10 * $pow));
    
    return round($bytes, $precision) . ' ' . $units[$pow];
}

// Helper function to get MySQL version
function getMySQLVersion($conn) {
    try {
        $stmt = $conn->query("SELECT VERSION() as version");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['version'] ?? 'Unknown';
    } catch (PDOException $e) {
        return 'Unknown';
    }
}

// Calculate overall system health
$overallHealth = 0;
foreach ($systemChecks as $check) {
    $overallHealth += $check['health'];
}
$overallHealth = round($overallHealth / count($systemChecks));

// Handle system check action
$actionMessage = '';
$actionType = '';
if (isset($_POST['action'])) {
    if ($_POST['action'] === 'run_check') {
        // Simulate running a comprehensive check
        sleep(1); // Simulate processing time
        $actionMessage = "System check completed successfully at " . date('Y-m-d H:i:s');
        $actionType = 'success';
    } else if ($_POST['action'] === 'create_backup') {
        // Simulate creating a backup
        sleep(2); // Simulate processing time
        $actionMessage = "Database backup created successfully at " . date('Y-m-d H:i:s');
        $actionType = 'success';
    } else if ($_POST['action'] === 'update_system') {
        // Simulate system update
        sleep(2); // Simulate processing time
        $actionMessage = "System update initiated. This may take several minutes to complete.";
        $actionType = 'info';
    }
}
?>

<!-- Main Content -->
<div class="container mx-auto px-4 py-8 max-w-7xl">
    <!-- Page Header -->
    <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                <span class="bg-indigo-500/10 dark:bg-indigo-500/20 p-2 rounded-md mr-3">
                    <i class="fas fa-server text-indigo-600 dark:text-indigo-400"></i>
                </span>
                System Health & Monitoring
            </h1>
            <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">View system status, perform checks, and manage backups</p>
        </div>
        <div class="mt-4 md:mt-0 flex flex-wrap gap-3">
            <form method="post">
                <input type="hidden" name="action" value="run_check">
                <button type="submit" class="btn btn-primary flex items-center py-2">
                    <i class="fas fa-sync-alt mr-2"></i> Run System Check
                </button>
            </form>
            <form method="post">
                <input type="hidden" name="action" value="create_backup">
                <button type="submit" class="btn btn-outline flex items-center py-2">
                    <i class="fas fa-download mr-2"></i> Create Backup
                </button>
            </form>
        </div>
    </div>
    
    <!-- Action Message -->
    <?php if (!empty($actionMessage)): ?>
        <div class="mb-6 bg-<?= $actionType ?>-50 dark:bg-<?= $actionType ?>-900/30 border-l-4 border-<?= $actionType ?>-500 text-<?= $actionType ?>-700 dark:text-<?= $actionType ?>-400 p-4 flex justify-between items-center" role="alert">
            <div>
                <i class="fas fa-info-circle mr-2"></i>
                <?= $actionMessage ?>
            </div>
            <button type="button" class="text-<?= $actionType ?>-700 dark:text-<?= $actionType ?>-400 hover:text-<?= $actionType ?>-900" onclick="this.parentElement.style.display='none';">
                <i class="fas fa-times"></i>
            </button>
        </div>
    <?php endif; ?>
    
    <!-- System Health Overview -->
    <div class="mb-8">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 transition-all duration-300 hover:shadow-lg p-6">
            <div class="flex flex-col md:flex-row items-start md:items-center justify-between mb-6">
                <div>
                    <h2 class="text-xl font-semibold text-gray-800 dark:text-white">System Health Overview</h2>
                    <p class="text-gray-600 dark:text-gray-400 text-sm">Last checked: <?= date('M j, Y H:i:s') ?></p>
                </div>
                <div class="mt-4 md:mt-0">
                    <span class="px-3 py-1 rounded-full text-sm font-medium <?= $overallHealth >= 80 ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400' : ($overallHealth >= 60 ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400' : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400') ?>">
                        Overall Health: <?= $overallHealth ?>%
                    </span>
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <?php foreach ($systemChecks as $check): ?>
                    <div class="bg-gray-50 dark:bg-gray-750 rounded-lg p-5 border border-gray-200 dark:border-gray-700 transition-all duration-200 hover:shadow-md">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center">
                                <div class="w-10 h-10 rounded-full flex items-center justify-center <?= $check['health'] >= 80 ? 'bg-green-100 text-green-600 dark:bg-green-900/30 dark:text-green-400' : ($check['health'] >= 60 ? 'bg-yellow-100 text-yellow-600 dark:bg-yellow-900/30 dark:text-yellow-400' : 'bg-red-100 text-red-600 dark:bg-red-900/30 dark:text-red-400') ?>">
                                    <i class="fas fa-<?= $check['icon'] ?>"></i>
                                </div>
                                <div class="ml-3">
                                    <h3 class="font-medium text-gray-900 dark:text-white"><?= $check['name'] ?></h3>
                                    <p class="text-xs text-gray-500 dark:text-gray-400"><?= $check['details'] ?></p>
                                </div>
                            </div>
                            <span class="badge <?= $check['status_class'] ?>"><?= $check['status'] ?></span>
                        </div>
                        <div class="bg-gray-200 dark:bg-gray-700 h-2 rounded-full overflow-hidden">
                            <div class="<?= $check['health'] >= 80 ? 'bg-green-500' : ($check['health'] >= 60 ? 'bg-yellow-500' : 'bg-red-500') ?> h-full rounded-full" style="width: <?= $check['health'] ?>%"></div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
    
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <!-- Server Information -->
        <div class="lg:col-span-2">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 transition-all duration-300 hover:shadow-lg overflow-hidden">
                <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4 flex justify-between items-center">
                    <h2 class="font-semibold text-gray-800 dark:text-white flex items-center">
                        <i class="fas fa-info-circle mr-2 text-primary"></i> Server Information
                    </h2>
                    <button id="copyServerInfo" class="text-primary hover:text-primary-dark transition-colors">
                        <i class="far fa-copy"></i>
                    </button>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-4">
                        <?php foreach ($serverInfo as $key => $value): ?>
                            <div>
                                <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400"><?= $key ?></h4>
                                <p class="text-base text-gray-900 dark:text-gray-200 font-medium"><?= $value ?></p>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- System Updates -->
        <div>
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 transition-all duration-300 hover:shadow-lg h-full">
                <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4 flex justify-between items-center">
                    <h2 class="font-semibold text-gray-800 dark:text-white flex items-center">
                        <i class="fas fa-arrow-up mr-2 text-primary"></i> System Updates
                    </h2>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-light text-primary dark:bg-primary-dark/30 dark:text-primary-light">
                        <?php 
                            $updateCount = 0;
                            foreach ($systemUpdates as $update) {
                                if ($update['update_available']) $updateCount++;
                            }
                            echo $updateCount . ' available';
                        ?>
                    </span>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <?php foreach ($systemUpdates as $update): ?>
                            <div class="flex items-center justify-between p-3 border <?= $update['update_available'] ? ($update['importance'] === 'high' ? 'border-red-200 bg-red-50 dark:border-red-900 dark:bg-red-900/20' : 'border-yellow-200 bg-yellow-50 dark:border-yellow-900 dark:bg-yellow-900/20') : 'border-gray-200 bg-gray-50 dark:border-gray-700 dark:bg-gray-750' ?> rounded-lg">
                                <div>
                                    <h3 class="font-medium text-gray-900 dark:text-gray-200"><?= $update['component'] ?></h3>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">
                                        <?php if ($update['update_available']): ?>
                                            Version <?= $update['current_version'] ?> → <?= $update['latest_version'] ?>
                                        <?php else: ?>
                                            Version <?= $update['current_version'] ?> (latest)
                                        <?php endif; ?>
                                    </p>
                                    <p class="text-xs text-gray-400 dark:text-gray-500 mt-1"><?= $update['date'] ?></p>
                                </div>
                                <?php if ($update['update_available']): ?>
                                    <form method="post">
                                        <input type="hidden" name="action" value="update_system">
                                        <input type="hidden" name="component" value="<?= $update['component'] ?>">
                                        <button type="submit" class="btn btn-sm <?= $update['importance'] === 'high' ? 'btn-danger' : 'btn-primary' ?>">Update</button>
                                    </form>
                                <?php else: ?>
                                    <span class="badge badge-gray">Up to date</span>
                                <?php endif; ?>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Recent Backups -->
    <div class="mb-8">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 transition-all duration-300 hover:shadow-lg">
            <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4 flex justify-between items-center">
                <h2 class="font-semibold text-gray-800 dark:text-white flex items-center">
                    <i class="fas fa-history mr-2 text-primary"></i> Recent Backups
                </h2>
                <a href="#" class="text-sm text-primary hover:underline flex items-center">
                    View All <i class="fas fa-arrow-right ml-1 text-xs"></i>
                </a>
            </div>
            <div class="p-6">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead>
                            <tr>
                                <th class="px-6 py-3 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider rounded-tl-md">Backup Name</th>
                                <th class="px-6 py-3 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Size</th>
                                <th class="px-6 py-3 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Date</th>
                                <th class="px-6 py-3 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Type</th>
                                <th class="px-6 py-3 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider rounded-tr-md">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                            <?php foreach ($recentBackups as $backup): ?>
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-750 transition-colors">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0 w-8 h-8 flex items-center justify-center bg-primary/10 text-primary dark:bg-primary/20 rounded-md">
                                                <i class="fas fa-archive"></i>
                                            </div>
                                            <div class="ml-3">
                                                <p class="text-sm font-medium text-gray-900 dark:text-white"><?= $backup['name'] ?></p>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400"><?= $backup['size'] ?></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400"><?= $backup['date'] ?></td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?= $backup['type'] === 'automatic' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300' : 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300' ?>">
                                            <?= ucfirst($backup['type']) ?>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm space-x-2">
                                        <a href="#" class="text-primary hover:text-primary-dark transition-colors" title="Download">
                                            <i class="fas fa-download"></i>
                                        </a>
                                        <a href="#" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 transition-colors" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="#" class="text-red-500 hover:text-red-700 transition-colors" title="Delete">
                                            <i class="fas fa-trash-alt"></i>
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Copy server information
    const copyServerInfo = document.getElementById('copyServerInfo');
    if (copyServerInfo) {
        copyServerInfo.addEventListener('click', function() {
            // Create a text representation of server info
            let serverInfoText = '';
            <?php foreach ($serverInfo as $key => $value): ?>
                serverInfoText += '<?= $key ?>: <?= $value ?>\n';
            <?php endforeach; ?>
            
            // Copy to clipboard
            navigator.clipboard.writeText(serverInfoText).then(() => {
                // Change button icon temporarily
                const icon = this.querySelector('i');
                icon.classList.remove('fa-copy');
                icon.classList.add('fa-check');
                setTimeout(() => {
                    icon.classList.remove('fa-check');
                    icon.classList.add('fa-copy');
                }, 2000);
            });
        });
    }
});
</script> 