<?php
// Include session configuration
require_once '../includes/session_config.php';
require_once '../includes/db.php';

if (!isset($_SESSION['superadmin_id'])) {
    header("Location: ../auth/superadmin_login.php");
    exit;
}

// Pagination setup
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 10; // Items per page
$offset = ($page - 1) * $limit;

// Search and filter functionality
$search = isset($_GET['search']) ? trim($_GET['search']) : '';

// Build query conditions
$conditions = ["1=1"]; // Always true condition to start with
$params = [];

if (!empty($search)) {
    $conditions[] = "(name LIKE ? OR slug LIKE ?)";
    $searchParam = "%$search%";
    $params = array_merge($params, [$searchParam, $searchParam]);
}

// Status filter if exists in schema
try {
    $checkStatusColumn = $conn->prepare("SHOW COLUMNS FROM colleges LIKE 'status'");
    $checkStatusColumn->execute();
    $hasStatusColumn = $checkStatusColumn->rowCount() > 0;
    
    if ($hasStatusColumn && isset($_GET['status']) && $_GET['status'] !== '') {
        $conditions[] = "status = ?";
        $params[] = $_GET['status'];
    }
} catch (PDOException $e) {
    // Column doesn't exist, ignore filter
}

$whereClause = implode(' AND ', $conditions);

// Count total colleges for pagination
$countSql = "SELECT COUNT(*) FROM colleges WHERE $whereClause";
$countStmt = $conn->prepare($countSql);
$countStmt->execute($params);
$totalColleges = $countStmt->fetchColumn();
$totalPages = ceil($totalColleges / $limit);

// Fetch colleges with pagination and filters
$sql = "SELECT * FROM colleges WHERE $whereClause ORDER BY created_at DESC LIMIT $limit OFFSET $offset";
$stmt = $conn->prepare($sql);
$stmt->execute($params);
$colleges = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get college stats
$statsQuery = "SELECT 
    c.id,
    c.name,
    (SELECT COUNT(*) FROM college_admins WHERE college_id = c.id) as admin_count,
    (SELECT COUNT(*) FROM students WHERE college_id = c.id) as student_count,
    (SELECT COUNT(*) FROM alumni WHERE college_id = c.id) as alumni_count,
    (SELECT COUNT(*) FROM faculties WHERE college_id = c.id) as faculty_count
    FROM colleges c
    WHERE " . str_replace('1=1', 'c.id IN (SELECT id FROM colleges WHERE ' . $whereClause . ')', $whereClause) . "
    ORDER BY c.created_at DESC 
    LIMIT $limit OFFSET $offset";
    
try {
    $statsStmt = $conn->prepare($statsQuery);
    $statsStmt->execute($params);
    $collegeStats = $statsStmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Convert to associative array with college ID as key
    $collegeStatsMap = [];
    foreach ($collegeStats as $stat) {
        $collegeStatsMap[$stat['id']] = $stat;
    }
} catch (PDOException $e) {
    // If the query fails, we'll just continue without stats
    $collegeStatsMap = [];
}

// Success/error messages
$message = '';
$messageType = '';

if (isset($_SESSION['flash_message'])) {
    $message = $_SESSION['flash_message'];
    $messageType = isset($_SESSION['flash_message_type']) ? $_SESSION['flash_message_type'] : 'success';
    unset($_SESSION['flash_message']);
    unset($_SESSION['flash_message_type']);
}

// Include the header
require_once '../includes/header.php';
?>

<!-- Main Content -->
<div class="container mx-auto px-4 py-8 max-w-7xl">
    <!-- Page Header -->
    <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                <span class="bg-primary/10 dark:bg-primary/20 p-2 rounded-md mr-3">
                    <i class="fas fa-university text-primary"></i>
                </span>
                College Management
            </h1>
            <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">View, edit and manage colleges on the platform</p>
        </div>
        <div class="mt-4 md:mt-0">
            <a href="create_college.php" class="btn btn-primary flex items-center">
                <i class="fas fa-plus-circle mr-2"></i> Add College
            </a>
        </div>
    </div>
    
    <!-- Action Message -->
    <?php if (!empty($message)): ?>
        <div class="mb-6 bg-<?= $messageType ?>-50 dark:bg-<?= $messageType ?>-900/30 border-l-4 border-<?= $messageType ?>-500 text-<?= $messageType ?>-700 dark:text-<?= $messageType ?>-400 p-4 flex justify-between items-center" role="alert">
            <div>
                <i class="fas fa-<?= $messageType === 'success' ? 'check-circle' : 'exclamation-circle' ?> mr-2"></i>
                <?= $message ?>
            </div>
            <button type="button" class="text-<?= $messageType ?>-700 dark:text-<?= $messageType ?>-400 hover:text-<?= $messageType ?>-900" onclick="this.parentElement.style.display='none';">
                <i class="fas fa-times"></i>
            </button>
        </div>
    <?php endif; ?>
    
    <!-- Filters & Search -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6 mb-8">
        <div class="flex flex-col md:flex-row md:items-end md:justify-between gap-4">
            <div class="flex-grow">
                <label for="search" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Search Colleges</label>
                <div class="relative">
                    <input type="text" id="search" class="form-input pl-10 w-full md:max-w-xs" placeholder="Search by name or slug..." value="<?= htmlspecialchars($search) ?>">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400 text-sm"></i>
                    </div>
                </div>
            </div>
            
            <?php if (isset($hasStatusColumn) && $hasStatusColumn): ?>
            <div>
                <label for="status-filter" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Status</label>
                <select id="status-filter" class="form-input">
                    <option value="">All Statuses</option>
                    <option value="active" <?= isset($_GET['status']) && $_GET['status'] === 'active' ? 'selected' : '' ?>>Active</option>
                    <option value="inactive" <?= isset($_GET['status']) && $_GET['status'] === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                    <option value="pending" <?= isset($_GET['status']) && $_GET['status'] === 'pending' ? 'selected' : '' ?>>Pending</option>
                </select>
            </div>
            <?php endif; ?>
            
            <div>
                <button id="filter-btn" class="btn btn-primary flex items-center">
                    <i class="fas fa-filter mr-2"></i> Apply Filters
                </button>
            </div>
        </div>
    </div>
    
    <!-- Colleges Table -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 mb-6">
        <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4 flex justify-between items-center">
            <h2 class="font-semibold text-gray-800 dark:text-white flex items-center">
                <i class="fas fa-list mr-2 text-primary"></i> Colleges List
                <span class="ml-2 px-2.5 py-0.5 bg-gray-100 dark:bg-gray-700 rounded-full text-xs font-medium text-gray-800 dark:text-gray-300">
                    <?= $totalColleges ?> total
                </span>
            </h2>
        </div>
        <div class="p-6 overflow-x-auto">
            <?php if (count($colleges) > 0): ?>
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead>
                        <tr>
                            <th class="px-6 py-3 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider rounded-tl-md">College</th>
                            <th class="px-6 py-3 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Slug</th>
                            <th class="px-6 py-3 bg-gray-50 dark:bg-gray-700 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Admins</th>
                            <th class="px-6 py-3 bg-gray-50 dark:bg-gray-700 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Students</th>
                            <th class="px-6 py-3 bg-gray-50 dark:bg-gray-700 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Alumni</th>
                            <th class="px-6 py-3 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Created</th>
                            <?php if (isset($hasStatusColumn) && $hasStatusColumn): ?>
                            <th class="px-6 py-3 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
                            <?php endif; ?>
                            <th class="px-6 py-3 bg-gray-50 dark:bg-gray-700 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider rounded-tr-md">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                        <?php foreach ($colleges as $college): ?>
                            <tr class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-primary to-primary-dark/80 flex items-center justify-center text-white mr-4 shadow-sm">
                                            <i class="fas fa-university"></i>
                                        </div>
                                        <div>
                                            <div class="font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($college['name']) ?></div>
                                            <?php if (isset($college['location']) && !empty($college['location'])): ?>
                                                <div class="text-sm text-gray-500 dark:text-gray-400"><?= htmlspecialchars($college['location']) ?></div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                                        <?= htmlspecialchars($college['slug']) ?>
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-500 dark:text-gray-400">
                                    <?php 
                                    $adminCount = isset($collegeStatsMap[$college['id']]['admin_count']) ? 
                                        $collegeStatsMap[$college['id']]['admin_count'] : 0;
                                    echo $adminCount;
                                    ?>
                                    <a href="list_admins.php?college_id=<?= $college['id'] ?>" class="ml-1 text-primary hover:underline">
                                        <i class="fas fa-eye text-xs"></i>
                                    </a>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-500 dark:text-gray-400">
                                    <?php 
                                    $studentCount = isset($collegeStatsMap[$college['id']]['student_count']) ? 
                                        $collegeStatsMap[$college['id']]['student_count'] : 0;
                                    echo $studentCount;
                                    ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-500 dark:text-gray-400">
                                    <?php 
                                    $alumniCount = isset($collegeStatsMap[$college['id']]['alumni_count']) ? 
                                        $collegeStatsMap[$college['id']]['alumni_count'] : 0;
                                    echo $alumniCount;
                                    ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                    <i class="far fa-calendar-alt mr-1"></i> 
                                    <?= date('M j, Y', strtotime($college['created_at'])) ?>
                                </td>
                                <?php if (isset($hasStatusColumn) && $hasStatusColumn): ?>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <?php 
                                    $status = $college['status'] ?? 'active';
                                    $statusClass = $status === 'active' ? 
                                        'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400' : 
                                        ($status === 'inactive' ? 
                                        'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400' : 
                                        'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400');
                                    ?>
                                    <span class="px-2.5 py-0.5 rounded-full text-xs font-medium <?= $statusClass ?>">
                                        <?= ucfirst(htmlspecialchars($status)) ?>
                                    </span>
                                </td>
                                <?php endif; ?>
                                <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
                                    <div class="flex items-center justify-center space-x-2">
                                        <a href="view_college.php?id=<?= $college['id'] ?>" class="p-1.5 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 rounded-md text-gray-600 dark:text-gray-400 transition-colors" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="edit_college.php?id=<?= $college['id'] ?>" class="p-1.5 bg-primary/10 hover:bg-primary/20 dark:bg-primary/20 dark:hover:bg-primary/30 rounded-md text-primary transition-colors" title="Edit College">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="assign_admin.php?college_id=<?= $college['id'] ?>" class="p-1.5 bg-blue-100 hover:bg-blue-200 dark:bg-blue-900/30 dark:hover:bg-blue-800/50 rounded-md text-blue-600 dark:text-blue-400 transition-colors" title="Assign Admin">
                                            <i class="fas fa-user-plus"></i>
                                        </a>
                                        <a href="delete_college.php?id=<?= $college['id'] ?>" class="p-1.5 bg-red-100 hover:bg-red-200 dark:bg-red-900/30 dark:hover:bg-red-800/50 rounded-md text-red-600 dark:text-red-400 transition-colors" title="Delete College" onclick="return confirm('Are you sure you want to delete this college? This action cannot be undone.');">
                                            <i class="fas fa-trash-alt"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
                
                <!-- Pagination -->
                <?php if ($totalPages > 1): ?>
                <div class="px-6 py-4 flex items-center justify-between border-t border-gray-200 dark:border-gray-700">
                    <div class="text-sm text-gray-500 dark:text-gray-400">
                        Showing <?= $offset + 1 ?> to <?= min($offset + $limit, $totalColleges) ?> of <?= $totalColleges ?> colleges
                    </div>
                    <div class="flex space-x-2">
                        <?php if ($page > 1): ?>
                            <a href="?page=<?= $page - 1 ?>&search=<?= urlencode($search) ?><?= isset($_GET['status']) ? '&status=' . urlencode($_GET['status']) : '' ?>" class="px-3 py-1 rounded border border-gray-300 dark:border-gray-600 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700">
                                Previous
                            </a>
                        <?php else: ?>
                            <span class="px-3 py-1 rounded border border-gray-200 dark:border-gray-700 text-sm font-medium text-gray-400 dark:text-gray-600 cursor-not-allowed">
                                Previous
                            </span>
                        <?php endif; ?>
                        
                        <?php if ($page < $totalPages): ?>
                            <a href="?page=<?= $page + 1 ?>&search=<?= urlencode($search) ?><?= isset($_GET['status']) ? '&status=' . urlencode($_GET['status']) : '' ?>" class="px-3 py-1 rounded border border-gray-300 dark:border-gray-600 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700">
                                Next
                            </a>
                        <?php else: ?>
                            <span class="px-3 py-1 rounded border border-gray-200 dark:border-gray-700 text-sm font-medium text-gray-400 dark:text-gray-600 cursor-not-allowed">
                                Next
                            </span>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>
                
            <?php else: ?>
                <div class="text-center py-12">
                    <div class="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-university text-gray-400 text-2xl"></i>
                    </div>
                    <h3 class="mt-2 text-lg font-medium text-gray-900 dark:text-white">No colleges found</h3>
                    <p class="mt-1 text-gray-500 dark:text-gray-400">
                        <?php if (!empty($search)): ?>
                            No colleges match your search criteria. Try different keywords or clear your search.
                        <?php else: ?>
                            Get started by adding your first college to the platform.
                        <?php endif; ?>
                    </p>
                    <div class="mt-6">
                        <a href="create_college.php" class="btn btn-primary">
                            <i class="fas fa-plus-circle mr-2"></i> Add College
                        </a>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Search and filter functionality
    const searchInput = document.getElementById('search');
    const statusFilter = document.getElementById('status-filter');
    const filterBtn = document.getElementById('filter-btn');
    
    filterBtn.addEventListener('click', function() {
        let url = window.location.pathname + '?';
        
        if (searchInput && searchInput.value) {
            url += 'search=' + encodeURIComponent(searchInput.value) + '&';
        }
        
        if (statusFilter && statusFilter.value) {
            url += 'status=' + encodeURIComponent(statusFilter.value) + '&';
        }
        
        // Remove trailing & if exists
        if (url.endsWith('&')) {
            url = url.slice(0, -1);
        }
        
        window.location.href = url;
    });
    
    // Allow enter key in search box
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                filterBtn.click();
            }
        });
    }
});
</script> 