<?php
function getCampaignSubmissionEmail($facultyName, $campaignTitle, $reviewLink) {
    return "
    <div style='font-family: Arial, sans-serif; background-color: #f5f8fa; padding: 20px;'>
        <h2 style='color: #1a73e8;'>📢 New Campaign Submitted</h2>
        <p><strong>Faculty:</strong> $facultyName</p>
        <p><strong>Campaign:</strong> $campaignTitle</p>
        <p>Please review and approve it:</p>
        <a href='$reviewLink' style='padding: 10px 16px; background-color: #1a73e8; color: #fff; text-decoration: none; border-radius: 4px;'>Review Campaign</a>
    </div>";
}

function getAlumniPledgeEmail($alumniName, $campaignTitle, $pledgeType) {
    return "
    <div style='font-family: Arial, sans-serif; background-color: #f0fdf4; padding: 20px;'>
        <h2 style='color: #16a34a;'>🎁 New Alumni Contribution</h2>
        <p><strong>$alumniName</strong> has pledged to support your campaign <strong>$campaignTitle</strong>.</p>
        <p>Contribution Type: <strong>" . ucfirst($pledgeType) . "</strong></p>
        <p>Please follow up with them directly.</p>
    </div>";
}
