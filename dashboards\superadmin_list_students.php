<?php
// Include session configuration
require_once '../includes/session_config.php';
require_once '../includes/db.php';
require_once '../includes/header.php';

// Ensure the user is logged in as a super admin
if (!isset($_SESSION['superadmin_id'])) {
    header("Location: ../auth/superadmin_login.php");
    exit;
}

// Pagination setup
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 15; // Items per page
$offset = ($page - 1) * $limit;

// Search and filter functionality
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$college_filter = isset($_GET['college_id']) ? (int)$_GET['college_id'] : 0;
$department_filter = isset($_GET['department']) ? trim($_GET['department']) : '';
$year_filter = isset($_GET['year']) ? trim($_GET['year']) : '';
$verified_filter = isset($_GET['verified']) ? $_GET['verified'] : '';

// Build query conditions
$conditions = ["1=1"]; // Always true condition to start with
$params = [];

if (!empty($search)) {
    $conditions[] = "(full_name LIKE ? OR email LIKE ? OR roll_number LIKE ?)";
    $searchParam = "%$search%";
    $params = array_merge($params, [$searchParam, $searchParam, $searchParam]);
}

if ($college_filter > 0) {
    $conditions[] = "college_id = ?";
    $params[] = $college_filter;
}

if (!empty($department_filter)) {
    $conditions[] = "department = ?";
    $params[] = $department_filter;
}

if (!empty($year_filter)) {
    $conditions[] = "year = ?";
    $params[] = $year_filter;
}

if ($verified_filter !== '') {
    $conditions[] = "verified = ?";
    $params[] = $verified_filter;
}

$whereClause = implode(' AND ', $conditions);

// Count total students for pagination
$countSql = "SELECT COUNT(*) FROM students WHERE $whereClause";
$countStmt = $conn->prepare($countSql);
$countStmt->execute($params);
$totalStudents = $countStmt->fetchColumn();
$totalPages = ceil($totalStudents / $limit);

// Fetch students with pagination and filters
$sql = "SELECT s.id, s.college_id, s.full_name, s.email, s.roll_number, s.department,
               s.year, s.division, s.profile_image, s.verified, s.status, s.created_at,
               c.name as college_name
        FROM students s
        LEFT JOIN colleges c ON s.college_id = c.id
        WHERE $whereClause
        ORDER BY s.created_at DESC
        LIMIT $limit OFFSET $offset";
$stmt = $conn->prepare($sql);
$stmt->execute($params);
$students = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get all colleges for filter
$collegeStmt = $conn->prepare("SELECT id, name FROM colleges ORDER BY name ASC");
$collegeStmt->execute();
$colleges = $collegeStmt->fetchAll(PDO::FETCH_ASSOC);

// Get departments and years for filters
$deptStmt = $conn->prepare("SELECT DISTINCT department FROM students WHERE department IS NOT NULL AND department != '' ORDER BY department ASC");
$deptStmt->execute();
$departments = $deptStmt->fetchAll(PDO::FETCH_COLUMN);

$yearStmt = $conn->prepare("SELECT DISTINCT year FROM students WHERE year IS NOT NULL AND year != '' ORDER BY year ASC");
$yearStmt->execute();
$years = $yearStmt->fetchAll(PDO::FETCH_COLUMN);

// Get statistics
$totalVerifiedStmt = $conn->prepare("SELECT COUNT(*) FROM students WHERE verified = 1");
$totalVerifiedStmt->execute();
$totalVerified = $totalVerifiedStmt->fetchColumn();

$totalUnverifiedStmt = $conn->prepare("SELECT COUNT(*) FROM students WHERE verified = 0");
$totalUnverifiedStmt->execute();
$totalUnverified = $totalUnverifiedStmt->fetchColumn();

?>

<!-- Main Content -->
<div class="container mx-auto px-4 py-8 max-w-7xl">
    <!-- Page Header -->
    <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                <span class="bg-blue-500/10 dark:bg-blue-500/20 p-2 rounded-md mr-3">
                    <i class="fas fa-user-graduate text-blue-600 dark:text-blue-400"></i>
                </span>
                Students Directory
            </h1>
            <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">View and manage all students across all colleges</p>
        </div>
        <div class="mt-4 md:mt-0 flex flex-wrap gap-2">
            <a href="superadmin_dashboard.php" class="btn btn-outline flex items-center">
                <i class="fas fa-arrow-left mr-2"></i> Back to Dashboard
            </a>
            <a href="export_students.php" class="btn btn-outline flex items-center">
                <i class="fas fa-file-export mr-2"></i> Export Data
            </a>
        </div>
    </div>
    
    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <!-- Total Students -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 mr-4">
                    <i class="fas fa-users"></i>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Students</p>
                    <p class="text-2xl font-semibold text-gray-900 dark:text-white"><?= number_format($totalStudents) ?></p>
                </div>
            </div>
        </div>
        
        <!-- Verified Students -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 mr-4">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Verified Students</p>
                    <p class="text-2xl font-semibold text-gray-900 dark:text-white"><?= number_format($totalVerified) ?></p>
                </div>
            </div>
        </div>
        
        <!-- Pending Verification -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-amber-100 dark:bg-amber-900/30 text-amber-600 dark:text-amber-400 mr-4">
                    <i class="fas fa-clock"></i>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Pending Verification</p>
                    <p class="text-2xl font-semibold text-gray-900 dark:text-white"><?= number_format($totalUnverified) ?></p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Filters & Search -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6 mb-8">
        <div class="flex flex-col md:flex-row md:items-end md:justify-between gap-4">
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 flex-grow">
                <!-- Search Input -->
                <div>
                    <label for="search" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Search</label>
                    <div class="relative">
                        <input type="text" id="search" class="form-input pl-10 w-full" placeholder="Name, email, roll no..." value="<?= htmlspecialchars($search) ?>">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-search text-gray-400 text-sm"></i>
                        </div>
                    </div>
                </div>
                
                <!-- College Filter -->
                <div>
                    <label for="college-filter" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">College</label>
                    <select id="college-filter" class="form-input">
                        <option value="">All Colleges</option>
                        <?php foreach ($colleges as $college): ?>
                            <option value="<?= $college['id'] ?>" <?= $college_filter == $college['id'] ? 'selected' : '' ?>>
                                <?= htmlspecialchars($college['name']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <!-- Department Filter -->
                <div>
                    <label for="department-filter" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Department</label>
                    <select id="department-filter" class="form-input">
                        <option value="">All Departments</option>
                        <?php foreach ($departments as $department): ?>
                            <option value="<?= $department ?>" <?= $department_filter === $department ? 'selected' : '' ?>>
                                <?= htmlspecialchars($department) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <!-- Year Filter -->
                <div>
                    <label for="year-filter" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Year</label>
                    <select id="year-filter" class="form-input">
                        <option value="">All Years</option>
                        <?php foreach ($years as $year): ?>
                            <option value="<?= $year ?>" <?= $year_filter === $year ? 'selected' : '' ?>>
                                <?= htmlspecialchars($year) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
            </div>
            
            <div class="flex space-x-2">
                <button id="reset-filters" class="btn btn-outline">
                    <i class="fas fa-times mr-2"></i> Clear
                </button>
                <button id="apply-filters" class="btn btn-primary">
                    <i class="fas fa-filter mr-2"></i> Apply Filters
                </button>
            </div>
        </div>
    </div>
    
    <!-- Students Table -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 overflow-hidden mb-6">
        <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4 flex justify-between items-center">
            <h2 class="font-semibold text-gray-800 dark:text-white flex items-center">
                <i class="fas fa-list mr-2 text-primary"></i> Students List
                <span class="ml-2 px-2.5 py-0.5 bg-gray-100 dark:bg-gray-700 rounded-full text-xs font-medium text-gray-800 dark:text-gray-300">
                    <?= $totalStudents ?> total
                </span>
            </h2>
            <div class="flex items-center">
                <span class="text-sm text-gray-600 dark:text-gray-400 mr-2">Status:</span>
                <select id="verified-filter" class="form-input py-1 pl-2 pr-8 text-sm">
                    <option value="">All Students</option>
                    <option value="1" <?= $verified_filter === '1' ? 'selected' : '' ?>>Verified</option>
                    <option value="0" <?= $verified_filter === '0' ? 'selected' : '' ?>>Pending</option>
                </select>
            </div>
        </div>
        
        <div class="overflow-x-auto">
            <?php if (count($students) > 0): ?>
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead>
                        <tr>
                            <th class="px-6 py-3 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Name</th>
                            <th class="px-6 py-3 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">College</th>
                            <th class="px-6 py-3 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Department</th>
                            <th class="px-6 py-3 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Year</th>
                            <th class="px-6 py-3 bg-gray-50 dark:bg-gray-700 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 bg-gray-50 dark:bg-gray-700 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                        <?php foreach ($students as $student): ?>
                            <tr class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="h-10 w-10 flex-shrink-0">
                                            <div class="h-10 w-10 rounded-full bg-gray-200 dark:bg-gray-600 flex items-center justify-center">
                                                <span class="font-medium text-gray-600 dark:text-gray-300">
                                                    <?= strtoupper(substr($student['full_name'] ?? 'S', 0, 1)) ?>
                                                </span>
                                            </div>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($student['full_name']) ?></div>
                                            <div class="text-sm text-gray-500 dark:text-gray-400"><?= htmlspecialchars($student['email']) ?></div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900 dark:text-white"><?= htmlspecialchars($student['college_name']) ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900 dark:text-white"><?= htmlspecialchars($student['department'] ?? 'Not specified') ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900 dark:text-white"><?= htmlspecialchars($student['year'] ?? 'Not specified') ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-center">
                                    <?php if ($student['verified'] == 1): ?>
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400">
                                            Verified
                                        </span>
                                    <?php else: ?>
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400">
                                            Pending
                                        </span>
                                    <?php endif; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
                                    <a href="superadmin_view_student.php?id=<?= $student['id'] ?>" class="text-primary hover:text-primary-dark dark:hover:text-primary-light mr-3">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="superadmin_edit_student.php?id=<?= $student['id'] ?>" class="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-300 mr-3">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
                
                <!-- Pagination -->
                <?php if ($totalPages > 1): ?>
                <div class="px-6 py-4 flex items-center justify-between border-t border-gray-200 dark:border-gray-700">
                    <div class="text-sm text-gray-500 dark:text-gray-400">
                        Showing <?= $offset + 1 ?> to <?= min($offset + $limit, $totalStudents) ?> of <?= $totalStudents ?> students
                    </div>
                    <div class="flex space-x-2">
                        <?php if ($page > 1): ?>
                            <a href="?page=<?= $page - 1 ?>&search=<?= urlencode($search) ?>&college_id=<?= $college_filter ?>&department=<?= urlencode($department_filter) ?>&year=<?= urlencode($year_filter) ?>&verified=<?= $verified_filter ?>" class="px-3 py-1 rounded border border-gray-300 dark:border-gray-600 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700">
                                Previous
                            </a>
                        <?php else: ?>
                            <span class="px-3 py-1 rounded border border-gray-200 dark:border-gray-700 text-sm font-medium text-gray-400 dark:text-gray-600 cursor-not-allowed">
                                Previous
                            </span>
                        <?php endif; ?>
                        
                        <?php if ($page < $totalPages): ?>
                            <a href="?page=<?= $page + 1 ?>&search=<?= urlencode($search) ?>&college_id=<?= $college_filter ?>&department=<?= urlencode($department_filter) ?>&year=<?= urlencode($year_filter) ?>&verified=<?= $verified_filter ?>" class="px-3 py-1 rounded border border-gray-300 dark:border-gray-600 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700">
                                Next
                            </a>
                        <?php else: ?>
                            <span class="px-3 py-1 rounded border border-gray-200 dark:border-gray-700 text-sm font-medium text-gray-400 dark:text-gray-600 cursor-not-allowed">
                                Next
                            </span>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>
            <?php else: ?>
                <div class="text-center py-12">
                    <div class="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-user-graduate text-gray-400 text-2xl"></i>
                    </div>
                    <h3 class="mt-2 text-lg font-medium text-gray-900 dark:text-white">No students found</h3>
                    <p class="mt-1 text-gray-500 dark:text-gray-400">
                        <?php if (!empty($search) || $college_filter > 0 || !empty($department_filter) || !empty($year_filter) || $verified_filter !== ''): ?>
                            Try adjusting your filters or search criteria.
                        <?php else: ?>
                            There are no students registered in the system yet.
                        <?php endif; ?>
                    </p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('search');
    const collegeFilter = document.getElementById('college-filter');
    const departmentFilter = document.getElementById('department-filter');
    const yearFilter = document.getElementById('year-filter');
    const verifiedFilter = document.getElementById('verified-filter');
    const applyFiltersBtn = document.getElementById('apply-filters');
    const resetFiltersBtn = document.getElementById('reset-filters');
    
    // Apply filters
    applyFiltersBtn.addEventListener('click', function() {
        applyFilters();
    });
    
    // Reset filters
    resetFiltersBtn.addEventListener('click', function() {
        searchInput.value = '';
        collegeFilter.value = '';
        departmentFilter.value = '';
        yearFilter.value = '';
        verifiedFilter.value = '';
        applyFilters();
    });
    
    // Listen for Enter key in search field
    searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            applyFilters();
        }
    });
    
    // Apply filters when verified filter changes
    verifiedFilter.addEventListener('change', function() {
        applyFilters();
    });
    
    function applyFilters() {
        let url = window.location.pathname + '?';
        
        if (searchInput.value) {
            url += 'search=' + encodeURIComponent(searchInput.value) + '&';
        }
        
        if (collegeFilter.value) {
            url += 'college_id=' + encodeURIComponent(collegeFilter.value) + '&';
        }
        
        if (departmentFilter.value) {
            url += 'department=' + encodeURIComponent(departmentFilter.value) + '&';
        }
        
        if (yearFilter.value) {
            url += 'year=' + encodeURIComponent(yearFilter.value) + '&';
        }
        
        if (verifiedFilter.value) {
            url += 'verified=' + encodeURIComponent(verifiedFilter.value) + '&';
        }
        
        // Remove trailing &
        if (url.endsWith('&')) {
            url = url.slice(0, -1);
        }
        
        window.location.href = url;
    }
});
</script> 