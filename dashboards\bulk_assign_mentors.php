<?php
session_start();
require_once '../includes/db.php';
require_once '../includes/db_schema_check.php';
require_once '../notify/send_email.php';

if (!isset($_SESSION['collegeadmin_id'])) {
    header("Location: ../auth/collegeadmin_login.php");
    exit;
}

$college_id = $_SESSION['college_id'];
$admin_name = $_SESSION['collegeadmin_name'] ?? 'Admin';
$error = '';
$success = '';
$students = [];
$alumni = [];
$total_students = 0;
$skills = [];

// Get college details
$collegeStmt = $conn->prepare("SELECT name FROM colleges WHERE id = ?");
$collegeStmt->execute([$college_id]);
$college = $collegeStmt->fetch(PDO::FETCH_ASSOC);

// Get departments for filter
$deptStmt = $conn->prepare("SELECT DISTINCT department FROM students WHERE college_id = ? ORDER BY department ASC");
$deptStmt->execute([$college_id]);
$departments = $deptStmt->fetchAll(PDO::FETCH_COLUMN);

// Get years for filter
$yearStmt = $conn->prepare("SELECT DISTINCT year FROM students WHERE college_id = ? ORDER BY year ASC");
$yearStmt->execute([$college_id]);
$years = $yearStmt->fetchAll(PDO::FETCH_COLUMN);

// Check if alumni_skills table exists, create if not
try {
    $conn->query("SELECT 1 FROM alumni_skills LIMIT 1");
} catch (PDOException $e) {
    // Table doesn't exist, create it
    $conn->exec("CREATE TABLE IF NOT EXISTS alumni_skills (
        id INT AUTO_INCREMENT PRIMARY KEY,
        alumni_id INT NOT NULL,
        skill VARCHAR(100) NOT NULL,
        proficiency ENUM('beginner', 'intermediate', 'advanced', 'expert') DEFAULT 'intermediate',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        UNIQUE KEY unique_alumni_skill (alumni_id, skill),
        FOREIGN KEY (alumni_id) REFERENCES alumni(id) ON DELETE CASCADE
    )");
    
    // Add some sample skills for testing
    try {
        // Get some alumni IDs
        $alumniIdsStmt = $conn->prepare("SELECT id FROM alumni WHERE college_id = ? LIMIT 10");
        $alumniIdsStmt->execute([$college_id]);
        $alumniIds = $alumniIdsStmt->fetchAll(PDO::FETCH_COLUMN);
        
        if (!empty($alumniIds)) {
            $sampleSkills = ['Programming', 'Design', 'Marketing', 'Management', 'Finance', 'Data Analysis', 'Communication', 'Leadership'];
            $skillInsert = $conn->prepare("INSERT INTO alumni_skills (alumni_id, skill, proficiency) VALUES (?, ?, ?)");
            
            foreach ($alumniIds as $alumniId) {
                // Add 2-3 random skills per alumni
                $numSkills = rand(2, 3);
                $selectedSkills = array_rand(array_flip($sampleSkills), $numSkills);
                if (!is_array($selectedSkills)) {
                    $selectedSkills = [$selectedSkills];
                }
                
                foreach ($selectedSkills as $skill) {
                    $proficiency = ['beginner', 'intermediate', 'advanced', 'expert'][rand(0, 3)];
                    try {
                        $skillInsert->execute([$alumniId, $skill, $proficiency]);
                    } catch (PDOException $e) {
                        // Ignore duplicate entries
                    }
                }
            }
        }
    } catch (Exception $e) {
        // Ignore errors when adding sample data
    }
}

// Get skills for filter - with error handling
try {
    $skillStmt = $conn->prepare("SELECT DISTINCT skill FROM alumni_skills 
                                JOIN alumni ON alumni_skills.alumni_id = alumni.id 
                                WHERE alumni.college_id = ? 
                                ORDER BY skill ASC");
    $skillStmt->execute([$college_id]);
    $skills = $skillStmt->fetchAll(PDO::FETCH_COLUMN);
} catch (PDOException $e) {
    // If there's an error, just set skills as empty array
    $skills = [];
}

// Count total students
$countStmt = $conn->prepare("SELECT COUNT(*) FROM students WHERE college_id = ? AND verified = 1");
$countStmt->execute([$college_id]);
$total_students = $countStmt->fetchColumn();

// Check if mentorships table exists, create if not
try {
    $conn->query("SELECT 1 FROM mentorships LIMIT 1");
} catch (PDOException $e) {
    // Table doesn't exist, create it
    $conn->exec("CREATE TABLE IF NOT EXISTS mentorships (
        id INT AUTO_INCREMENT PRIMARY KEY,
        student_id INT NOT NULL,
        alumni_id INT NOT NULL,
        status ENUM('pending', 'active', 'completed', 'declined') NOT NULL DEFAULT 'pending',
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME NULL,
        notes TEXT NULL,
        UNIQUE KEY unique_mentorship (student_id, alumni_id),
        INDEX idx_student_id (student_id),
        INDEX idx_alumni_id (alumni_id)
    )");
    
    // We'll try to add foreign keys if possible, but it's not critical
    try {
        $conn->exec("ALTER TABLE mentorships
            ADD CONSTRAINT fk_mentorships_student
            FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE");
        
        $conn->exec("ALTER TABLE mentorships
            ADD CONSTRAINT fk_mentorships_alumni
            FOREIGN KEY (alumni_id) REFERENCES alumni(id) ON DELETE CASCADE");
    } catch (PDOException $alterEx) {
        // Silently ignore foreign key errors - the table will still work
    }
}

// Handle form submission for assigning mentors
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['student_ids']) && is_array($_POST['student_ids']) && !empty($_POST['student_ids']) && 
        isset($_POST['alumni_id']) && !empty($_POST['alumni_id'])) {
        
        $student_ids = $_POST['student_ids'];
        $alumni_id = (int)$_POST['alumni_id'];
        $send_notifications = isset($_POST['send_notifications']) ? true : false;
        $notes = trim($_POST['notes'] ?? '');
        
        try {
            // Start transaction
            $conn->beginTransaction();
            
            // Verify alumni belongs to the college
            $alumniStmt = $conn->prepare("SELECT id, full_name, email FROM alumni WHERE id = ? AND college_id = ? AND verified = 1");
            $alumniStmt->execute([$alumni_id, $college_id]);
            $alumni_data = $alumniStmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$alumni_data) {
                throw new Exception("Selected alumni not found or not verified.");
            }
            
            // Get student details for verification and notification
            $placeholders = implode(',', array_fill(0, count($student_ids), '?'));
            $studentStmt = $conn->prepare("SELECT id, full_name, email FROM students WHERE id IN ($placeholders) AND college_id = ? AND verified = 1");
            $params = array_merge($student_ids, [$college_id]);
            $studentStmt->execute($params);
            $students_data = $studentStmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (count($students_data) === 0) {
                throw new Exception("No valid students selected for mentor assignment.");
            }
            
            // Insert mentorships
            $insertStmt = $conn->prepare("INSERT INTO mentorships (student_id, alumni_id, status, created_at, notes) 
                                         VALUES (?, ?, 'pending', NOW(), ?) 
                                         ON DUPLICATE KEY UPDATE updated_at = NOW(), notes = VALUES(notes)");
            
            $assigned_count = 0;
            $notification_count = 0;
            
            foreach ($students_data as $student) {
                try {
                    $insertStmt->execute([$student['id'], $alumni_id, $notes]);
                    if ($insertStmt->rowCount() > 0) {
                        $assigned_count++;
                        
                        // Send notifications if requested
                        if ($send_notifications) {
                            // Notify student
                            $student_subject = "New Mentor Assigned - " . htmlspecialchars($college['name']);
                            $student_message = "<p>Dear " . htmlspecialchars($student['full_name']) . ",</p>";
                            $student_message .= "<p>We are pleased to inform you that a mentor has been assigned to you at " . htmlspecialchars($college['name']) . ".</p>";
                            $student_message .= "<p><strong>Mentor:</strong> " . htmlspecialchars($alumni_data['full_name']) . "</p>";
                            if (!empty($notes)) {
                                $student_message .= "<p><strong>Notes:</strong> " . nl2br(htmlspecialchars($notes)) . "</p>";
                            }
                            $student_message .= "<p>You can view your mentor's profile and start a conversation through your student dashboard.</p>";
                            $student_message .= "<p>Regards,<br>" . htmlspecialchars($admin_name) . "<br>Administration Team<br>" . htmlspecialchars($college['name']) . "</p>";
                            
                            $student_email_sent = sendEmail($student['email'], $student['full_name'], $student_subject, $student_message);
                            
                            // Notify alumni
                            $alumni_subject = "New Mentee Assigned - " . htmlspecialchars($college['name']);
                            $alumni_message = "<p>Dear " . htmlspecialchars($alumni_data['full_name']) . ",</p>";
                            $alumni_message .= "<p>A new student has been assigned to you as a mentee at " . htmlspecialchars($college['name']) . ".</p>";
                            $alumni_message .= "<p><strong>Student:</strong> " . htmlspecialchars($student['full_name']) . "</p>";
                            if (!empty($notes)) {
                                $alumni_message .= "<p><strong>Notes:</strong> " . nl2br(htmlspecialchars($notes)) . "</p>";
                            }
                            $alumni_message .= "<p>You can view your mentee's profile and start a conversation through your alumni dashboard.</p>";
                            $alumni_message .= "<p>Regards,<br>" . htmlspecialchars($admin_name) . "<br>Administration Team<br>" . htmlspecialchars($college['name']) . "</p>";
                            
                            $alumni_email_sent = sendEmail($alumni_data['email'], $alumni_data['full_name'], $alumni_subject, $alumni_message);
                            
                            if ($student_email_sent || $alumni_email_sent) {
                                $notification_count++;
                            }
                        }
                    }
                } catch (PDOException $e) {
                    // Skip duplicate entries but continue with others
                    if ($e->getCode() != 23000) { // 23000 is the SQLSTATE code for duplicate entry
                        throw $e;
                    }
                }
            }
            
            // Commit transaction
            $conn->commit();
            
            if ($assigned_count > 0) {
                $success = "$assigned_count student" . ($assigned_count !== 1 ? "s" : "") . " assigned to mentor successfully.";
                if ($notification_count > 0) {
                    $success .= " Email notifications sent.";
                }
            } else {
                $error = "No new mentor assignments were made. The selected students may already be assigned to this mentor.";
            }
            
        } catch (Exception $e) {
            // Rollback transaction on error
            $conn->rollBack();
            $error = $e->getMessage();
        }
    } else {
        $error = "Please select both students and a mentor to make assignments.";
    }
}

// Apply filters for students
$student_conditions = ["college_id = ? AND verified = 1"];
$student_params = [$college_id];

if (!empty($_GET['department'])) {
    $student_conditions[] = "department = ?";
    $student_params[] = $_GET['department'];
}

if (!empty($_GET['year'])) {
    $student_conditions[] = "year = ?";
    $student_params[] = $_GET['year'];
}

if (isset($_GET['has_mentor']) && $_GET['has_mentor'] !== '') {
    if ($_GET['has_mentor'] === '1') {
        $student_conditions[] = "id IN (SELECT student_id FROM mentorships)";
    } else {
        $student_conditions[] = "id NOT IN (SELECT student_id FROM mentorships)";
    }
}

$student_whereClause = implode(' AND ', $student_conditions);

// Get students with filters
$student_sql = "SELECT s.*, 
                (SELECT COUNT(*) FROM mentorships WHERE student_id = s.id) AS mentor_count 
                FROM students s 
                WHERE $student_whereClause 
                ORDER BY full_name ASC LIMIT 100";
$student_stmt = $conn->prepare($student_sql);
$student_stmt->execute($student_params);
$students = $student_stmt->fetchAll(PDO::FETCH_ASSOC);

// Count filtered students
$countFilteredStudentStmt = $conn->prepare("SELECT COUNT(*) FROM students WHERE $student_whereClause");
$countFilteredStudentStmt->execute($student_params);
$filtered_student_count = $countFilteredStudentStmt->fetchColumn();

// Apply filters for alumni
$alumni_conditions = ["college_id = ? AND verified = 1"];
$alumni_params = [$college_id];

if (!empty($_GET['alumni_skill'])) {
    $alumni_conditions[] = "id IN (SELECT alumni_id FROM alumni_skills WHERE skill = ?)";
    $alumni_params[] = $_GET['alumni_skill'];
}

$alumni_whereClause = implode(' AND ', $alumni_conditions);

// Get alumni with filters
$alumni_sql = "SELECT a.*, 
              (SELECT COUNT(*) FROM mentorships WHERE alumni_id = a.id) AS mentee_count,
              (SELECT GROUP_CONCAT(skill SEPARATOR ', ') FROM alumni_skills WHERE alumni_id = a.id) AS skills
              FROM alumni a 
              WHERE $alumni_whereClause 
              ORDER BY full_name ASC";
$alumni_stmt = $conn->prepare($alumni_sql);
$alumni_stmt->execute($alumni_params);
$alumni = $alumni_stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bulk Assign Mentors - Mentoshri</title>
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts - Inter -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        'primary': '#0a66c2',
                        'primary-dark': '#004182',
                        'primary-light': '#e7f3ff',
                    },
                    fontFamily: {
                        'sans': ['Inter', 'sans-serif'],
                    },
                }
            }
        }
    </script>
    <!-- Design System -->
    <link rel="stylesheet" href="../assets/css/design-system.css">
    <style>
        .mentor-card.selected {
            border-color: #0a66c2;
            background-color: #e7f3ff;
        }
        
        .dark .mentor-card.selected {
            border-color: #7eb6ff;
            background-color: rgba(10, 102, 194, 0.2);
        }
        
        .mentor-card .check-icon {
            display: none;
        }
        
        .mentor-card.selected .check-icon {
            display: flex;
        }
    </style>
</head>
<body class="font-sans bg-gray-50 dark:bg-gray-900 min-h-screen flex flex-col">
    <!-- Header -->
    <header class="bg-white dark:bg-gray-800 shadow-sm">
        <div class="container mx-auto px-4 py-3 flex items-center justify-between">
            <div class="flex items-center">
                <a href="collegeadmin_dashboard.php" class="text-primary font-bold text-2xl">Mentoshri</a>
            </div>
            
            <div class="flex items-center space-x-4">
                <button id="themeToggle" class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700">
                    <i class="fas fa-moon dark:hidden"></i>
                    <i class="fas fa-sun hidden dark:block"></i>
                </button>
                <div class="relative">
                    <button id="notificationsBtn" class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 relative">
                        <i class="fas fa-bell"></i>
                        <span class="absolute top-0 right-0 h-4 w-4 bg-red-500 rounded-full text-xs text-white flex items-center justify-center">2</span>
                    </button>
                </div>
                <div class="relative group">
                    <button class="flex items-center space-x-1">
                        <div class="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center text-blue-600 dark:text-blue-400">
                            <i class="fas fa-user-shield"></i>
                        </div>
                        <span class="text-gray-700 dark:text-gray-300"><?= htmlspecialchars($admin_name) ?></span>
                        <i class="fas fa-chevron-down text-gray-400 text-xs"></i>
                    </button>
                    <div class="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg border border-gray-200 dark:border-gray-700 hidden group-hover:block z-10">
                        <div class="py-1">
                            <a href="collegeadmin_profile.php" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                <i class="fas fa-user mr-2"></i> Profile
                            </a>
                            <a href="collegeadmin_settings.php" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                <i class="fas fa-cog mr-2"></i> Settings
                            </a>
                            <div class="border-t border-gray-200 dark:border-gray-700"></div>
                            <a href="../auth/logout.php" class="block px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-700">
                                <i class="fas fa-sign-out-alt mr-2"></i> Logout
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="flex-grow py-8 px-4">
        <div class="container mx-auto">
            <!-- Breadcrumb -->
            <nav class="mb-6 text-sm" aria-label="Breadcrumb">
                <ol class="flex items-center space-x-2">
                    <li>
                        <a href="collegeadmin_dashboard.php" class="text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary-light">
                            <i class="fas fa-home"></i>
                            <span class="sr-only">Dashboard</span>
                        </a>
                    </li>
                    <li class="flex items-center">
                        <i class="fas fa-chevron-right text-gray-400 text-xs mx-1"></i>
                        <a href="list_students.php" class="text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary-light">
                            Student Management
                        </a>
                    </li>
                    <li class="flex items-center">
                        <i class="fas fa-chevron-right text-gray-400 text-xs mx-1"></i>
                        <span class="text-gray-700 dark:text-gray-300">Bulk Assign Mentors</span>
                    </li>
                </ol>
            </nav>

            <div class="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                        <i class="fas fa-users-cog text-primary mr-3"></i>
                        Bulk Assign Mentors
                    </h1>
                    <p class="text-gray-600 dark:text-gray-400 mt-1">
                        Assign mentors to multiple students at <?= htmlspecialchars($college['name'] ?? 'your college') ?>
                    </p>
                </div>
                <div class="mt-4 md:mt-0">
                    <a href="list_students.php" class="btn btn-outline flex items-center">
                        <i class="fas fa-arrow-left mr-2"></i> Back to Students
                    </a>
                </div>
            </div>
            
            <?php if ($error): ?>
                <div class="bg-red-50 dark:bg-red-900/30 border-l-4 border-red-500 text-red-700 dark:text-red-400 p-4 mb-6 flex justify-between items-center" role="alert">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-circle mr-2"></i>
                        <span><?= htmlspecialchars($error) ?></span>
                    </div>
                    <button type="button" class="text-red-700 dark:text-red-400 hover:opacity-75" onclick="this.parentElement.style.display='none';">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="bg-green-50 dark:bg-green-900/30 border-l-4 border-green-500 text-green-700 dark:text-green-400 p-4 mb-6 flex justify-between items-center" role="alert">
                    <div class="flex items-center">
                        <i class="fas fa-check-circle mr-2"></i>
                        <span><?= htmlspecialchars($success) ?></span>
                    </div>
                    <button type="button" class="text-green-700 dark:text-green-400 hover:opacity-75" onclick="this.parentElement.style.display='none';">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            <?php endif; ?>

            <!-- Stats Overview -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 border border-gray-200 dark:border-gray-700">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 mr-4">
                            <i class="fas fa-user-graduate"></i>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Students</p>
                            <p class="text-xl font-semibold text-gray-800 dark:text-white"><?= $total_students ?></p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 border border-gray-200 dark:border-gray-700">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 mr-4">
                            <i class="fas fa-user-tie"></i>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Available Mentors</p>
                            <p class="text-xl font-semibold text-gray-800 dark:text-white"><?= count($alumni) ?></p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 border border-gray-200 dark:border-gray-700">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400 mr-4">
                            <i class="fas fa-handshake"></i>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Active Mentorships</p>
                            <?php
                            // Count active mentorships
                            try {
                                $mentorshipStmt = $conn->prepare("SELECT COUNT(*) FROM mentorships m 
                                                                JOIN students s ON m.student_id = s.id 
                                                                WHERE s.college_id = ? AND m.status = 'active'");
                                $mentorshipStmt->execute([$college_id]);
                                $active_mentorships = $mentorshipStmt->fetchColumn();
                            } catch (PDOException $e) {
                                $active_mentorships = 0;
                            }
                            ?>
                            <p class="text-xl font-semibold text-gray-800 dark:text-white"><?= $active_mentorships ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <form method="GET" action="" class="mb-8">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 border border-gray-200 dark:border-gray-700">
                    <h2 class="text-lg font-semibold text-gray-800 dark:text-white mb-4">Filter Students</h2>
                    
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div>
                            <label for="department" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Department</label>
                            <select id="department" name="department" class="form-select">
                                <option value="">All Departments</option>
                                <?php foreach ($departments as $dept): ?>
                                    <option value="<?= htmlspecialchars($dept) ?>" <?= isset($_GET['department']) && $_GET['department'] === $dept ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($dept) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div>
                            <label for="year" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Year</label>
                            <select id="year" name="year" class="form-select">
                                <option value="">All Years</option>
                                <?php foreach ($years as $year): ?>
                                    <option value="<?= htmlspecialchars($year) ?>" <?= isset($_GET['year']) && $_GET['year'] === $year ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($year) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div>
                            <label for="has_mentor" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Mentor Status</label>
                            <select id="has_mentor" name="has_mentor" class="form-select">
                                <option value="">All Students</option>
                                <option value="0" <?= isset($_GET['has_mentor']) && $_GET['has_mentor'] === '0' ? 'selected' : '' ?>>Without Mentor</option>
                                <option value="1" <?= isset($_GET['has_mentor']) && $_GET['has_mentor'] === '1' ? 'selected' : '' ?>>With Mentor</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="mt-6 flex items-center">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-filter mr-2"></i> Apply Filters
                        </button>
                        <a href="bulk_assign_mentors.php" class="btn btn-outline ml-3">
                            <i class="fas fa-times mr-2"></i> Clear Filters
                        </a>
                        <div class="ml-auto text-sm text-gray-600 dark:text-gray-400">
                            Showing <?= count($students) ?> of <?= $filtered_student_count ?> students
                        </div>
                    </div>
                </div>
            </form>
            
            <form method="POST" action="" id="mentorAssignmentForm">
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <!-- Student Selection -->
                    <div class="lg:col-span-2">
                        <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 overflow-hidden">
                            <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                                <h2 class="text-lg font-semibold text-gray-800 dark:text-white flex items-center">
                                    <i class="fas fa-user-graduate text-primary mr-2"></i> Select Students
                                </h2>
                                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                                    Select students to assign to a mentor. You can select multiple students.
                                </p>
                            </div>
                            
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                                    <thead class="bg-gray-50 dark:bg-gray-700">
                                        <tr>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                                <div class="flex items-center">
                                                    <input type="checkbox" id="selectAllStudents" class="form-checkbox">
                                                    <label for="selectAllStudents" class="ml-2">Select All</label>
                                                </div>
                                            </th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                                Student
                                            </th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                                Department
                                            </th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                                Year
                                            </th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                                Mentors
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                        <?php if (count($students) > 0): ?>
                                            <?php foreach ($students as $student): ?>
                                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/50">
                                                    <td class="px-6 py-4 whitespace-nowrap">
                                                        <input type="checkbox" name="student_ids[]" value="<?= $student['id'] ?>" class="form-checkbox student-checkbox">
                                                    </td>
                                                    <td class="px-6 py-4 whitespace-nowrap">
                                                        <div class="flex items-center">
                                                            <div class="flex-shrink-0 h-10 w-10">
                                                                <?php if (!empty($student['profile_image'])): ?>
                                                                    <img class="h-10 w-10 rounded-full object-cover" src="../uploads/<?= htmlspecialchars($student['profile_image']) ?>" alt="">
                                                                <?php else: ?>
                                                                    <div class="h-10 w-10 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                                                                        <i class="fas fa-user text-gray-400 dark:text-gray-500"></i>
                                                                    </div>
                                                                <?php endif; ?>
                                                            </div>
                                                            <div class="ml-4">
                                                                <div class="text-sm font-medium text-gray-900 dark:text-white">
                                                                    <?= htmlspecialchars($student['full_name']) ?>
                                                                </div>
                                                                <div class="text-sm text-gray-500 dark:text-gray-400">
                                                                    <?= htmlspecialchars($student['email']) ?>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td class="px-6 py-4 whitespace-nowrap">
                                                        <div class="text-sm text-gray-900 dark:text-white"><?= htmlspecialchars($student['department'] ?? 'N/A') ?></div>
                                                    </td>
                                                    <td class="px-6 py-4 whitespace-nowrap">
                                                        <div class="text-sm text-gray-900 dark:text-white"><?= htmlspecialchars($student['year'] ?? 'N/A') ?></div>
                                                    </td>
                                                    <td class="px-6 py-4 whitespace-nowrap">
                                                        <?php if ($student['mentor_count'] > 0): ?>
                                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400">
                                                                <?= $student['mentor_count'] ?> assigned
                                                            </span>
                                                        <?php else: ?>
                                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-400">
                                                                No mentor
                                                            </span>
                                                        <?php endif; ?>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        <?php else: ?>
                                            <tr>
                                                <td colspan="5" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                                                    <div class="flex flex-col items-center py-6">
                                                        <i class="fas fa-search text-gray-400 text-3xl mb-3"></i>
                                                        <p class="text-base">No students found matching your criteria</p>
                                                        <p class="text-sm mt-1">Try adjusting your filters or add new students</p>
                                                        <a href="add_student.php" class="mt-3 btn btn-sm btn-outline">
                                                            <i class="fas fa-plus mr-1"></i> Add Student
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                            
                            <div class="p-4 bg-gray-50 dark:bg-gray-700/50 border-t border-gray-200 dark:border-gray-700">
                                <div class="flex items-center justify-between">
                                    <div class="text-sm text-gray-700 dark:text-gray-300">
                                        <span id="selectedCount">0</span> students selected
                                    </div>
                                    <div>
                                        <button type="button" id="clearSelection" class="btn btn-sm btn-outline">
                                            Clear Selection
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Alumni Selection -->
                    <div class="lg:col-span-1">
                        <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 overflow-hidden">
                            <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                                <h2 class="text-lg font-semibold text-gray-800 dark:text-white flex items-center">
                                    <i class="fas fa-user-tie text-primary mr-2"></i> Select Mentor
                                </h2>
                                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                                    Choose an alumni mentor from the list.
                                </p>
                            </div>
                            
                            <div class="overflow-y-auto max-h-96">
                                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                                    <thead class="bg-gray-50 dark:bg-gray-700">
                                        <tr>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                                Mentor
                                            </th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                                Skills
                                            </th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                                Mentees
                                            </th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                                Action
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                        <?php if (count($alumni) > 0): ?>
                                            <?php foreach ($alumni as $mentor): ?>
                                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/50">
                                                    <td class="px-6 py-4 whitespace-nowrap">
                                                        <div class="flex items-center">
                                                            <div class="flex-shrink-0 h-10 w-10">
                                                                <?php if (!empty($mentor['profile_image'])): ?>
                                                                    <img class="h-10 w-10 rounded-full object-cover" src="../uploads/<?= htmlspecialchars($mentor['profile_image']) ?>" alt="">
                                                                <?php else: ?>
                                                                    <div class="h-10 w-10 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                                                                        <i class="fas fa-user text-gray-400 dark:text-gray-500"></i>
                                                                    </div>
                                                                <?php endif; ?>
                                                            </div>
                                                            <div class="ml-4">
                                                                <div class="text-sm font-medium text-gray-900 dark:text-white">
                                                                    <?= htmlspecialchars($mentor['full_name']) ?>
                                                                </div>
                                                                <div class="text-sm text-gray-500 dark:text-gray-400">
                                                                    <?= htmlspecialchars($mentor['email']) ?>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                                        <?= htmlspecialchars($mentor['skills'] ?? 'N/A') ?>
                                                    </td>
                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                                        <?= $mentor['mentee_count'] ?>
                                                    </td>
                                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                        <button type="button" class="btn btn-sm btn-primary" onclick="selectMentor('<?= $mentor['id'] ?>')">
                                                            Assign
                                                        </button>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        <?php else: ?>
                                            <tr>
                                                <td colspan="4" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                                                    <div class="flex flex-col items-center py-6">
                                                        <i class="fas fa-user-tie text-gray-400 text-3xl mb-3"></i>
                                                        <p class="text-base">No available mentors found</p>
                                                        <p class="text-sm mt-1">Add more alumni to the system</p>
                                                        <a href="add_alumni.php" class="mt-3 btn btn-sm btn-outline">
                                                            <i class="fas fa-plus mr-1"></i> Add Alumni
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="lg:col-span-3">
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 border border-gray-200 dark:border-gray-700">
                        <h2 class="text-lg font-semibold text-gray-800 dark:text-white mb-4 flex items-center">
                            <i class="fas fa-info-circle text-primary mr-2"></i> Assignment Details
                        </h2>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="alumni_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Select Mentor</label>
                                <select id="alumni_id" name="alumni_id" class="form-select" required>
                                    <option value="">Select a mentor</option>
                                    <?php foreach ($alumni as $mentor): ?>
                                        <option value="<?= $mentor['id'] ?>">
                                            <?= htmlspecialchars($mentor['full_name']) ?> (<?= htmlspecialchars($mentor['skills'] ?? 'N/A') ?>)
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div>
                                <label for="send_notifications" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                    <input type="checkbox" id="send_notifications" name="send_notifications" value="1" checked>
                                    Send Notifications to Students
                                </label>
                            </div>
                            <div>
                                <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Notes (Optional)</label>
                                <textarea id="notes" name="notes" rows="3" class="form-textarea" placeholder="Add any notes for the mentor..."></textarea>
                            </div>
                        </div>
                        
                        <div class="mt-6 flex justify-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane mr-2"></i> Assign Mentors
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </main>

    <footer class="bg-white dark:bg-gray-800 shadow-sm mt-auto">
        <div class="container mx-auto px-4 py-3 text-center text-sm text-gray-600 dark:text-gray-400">
            &copy; <?= date('Y') ?> Mentoshri. All rights reserved.
        </div>
    </footer>

    <script>
        // Function to select a specific mentor
        function selectMentor(alumniId) {
            document.getElementById('alumni_id').value = alumniId;
            // Scroll to the assignment details section
            document.querySelector('.lg\\:col-span-3').scrollIntoView({ behavior: 'smooth' });
        }
        
        document.addEventListener('DOMContentLoaded', function() {
            const selectAllStudentsCheckbox = document.getElementById('selectAllStudents');
            const studentCheckboxes = document.querySelectorAll('.student-checkbox');
            const clearSelectionButton = document.getElementById('clearSelection');
            const selectedCountSpan = document.getElementById('selectedCount');
            const mentorAssignmentForm = document.getElementById('mentorAssignmentForm');
            const alumniIdSelect = document.getElementById('alumni_id');
            const studentIdsInput = document.querySelector('input[name="student_ids"]');

            // Function to update selected count
            function updateSelectedCount() {
                const selected = Array.from(studentCheckboxes).filter(checkbox => checkbox.checked).length;
                selectedCountSpan.textContent = selected;
                selectAllStudentsCheckbox.checked = selected === studentCheckboxes.length && studentCheckboxes.length > 0;
                clearSelectionButton.disabled = selected === 0;
            }

            // Initial update
            updateSelectedCount();

            // Select All/Deselect All
            selectAllStudentsCheckbox.addEventListener('change', function() {
                studentCheckboxes.forEach(checkbox => {
                    checkbox.checked = this.checked;
                });
                updateSelectedCount();
            });

            // Clear Selection
            clearSelectionButton.addEventListener('click', function() {
                studentCheckboxes.forEach(checkbox => {
                    checkbox.checked = false;
                });
                updateSelectedCount();
            });

            // Handle form submission
            mentorAssignmentForm.addEventListener('submit', function(event) {
                const selectedAlumniId = alumniIdSelect.value;
                const selectedStudentIds = Array.from(studentCheckboxes).filter(checkbox => checkbox.checked).map(checkbox => checkbox.value);

                if (selectedAlumniId === '') {
                    event.preventDefault();
                    alert('Please select a mentor.');
                    return;
                }

                if (selectedStudentIds.length === 0) {
                    event.preventDefault();
                    alert('Please select at least one student to assign.');
                    return;
                }

                if (!confirm('Are you sure you want to assign these students to the selected mentor?')) {
                    event.preventDefault();
                }
            });
        });
    </script>
</body>
</html> 