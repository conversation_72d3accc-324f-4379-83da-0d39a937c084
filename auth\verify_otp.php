<?php
// Include session configuration
require_once '../includes/session_config.php';
require_once '../includes/db.php';
require_once '../includes/testing_config.php';

$error = '';

if (!isset($_SESSION['pending_2fa'])) {
    header("Location: login_choice.php");
    exit;
}

// Auto-bypass for testing - check if skip parameter is set
if (isTestingMode() && isset($_GET['skip']) && $_GET['skip'] === 'testing') {
    logTestingAction('OTP_SKIP', "User bypassed OTP via URL parameter");
    $pending = $_SESSION['pending_2fa'];
    $role = $pending['role'];
    $user_id = $pending['id'];

    $dashboard = [
        'collegeadmin' => '../dashboards/collegeadmin_dashboard.php',
        'faculty' => '../dashboards/faculty_dashboard.php',
        'alumni' => '../alumni/alumni_dashboard.php'
    ][$role];

    unset($_SESSION['pending_2fa']);

    if ($role === 'collegeadmin') {
        $_SESSION['collegeadmin_id'] = $user_id;
        $_SESSION['collegeadmin_name'] = $pending['name'];
        $_SESSION['college_id'] = $pending['college_id'];
    } elseif ($role === 'faculty') {
        $_SESSION['faculty_id'] = $user_id;
        $_SESSION['faculty_name'] = $pending['name'];
        $_SESSION['college_id'] = $pending['college_id'];
    } elseif ($role === 'alumni') {
        $_SESSION['alumni_id'] = $user_id;
        $_SESSION['alumni_name'] = $pending['name'];
        $_SESSION['college_id'] = $pending['college_id'];
    }

    header("Location: $dashboard");
    exit;
}

$pending = $_SESSION['pending_2fa'];
$role = $pending['role'];
$email = $pending['email'];
$user_id = $pending['id'];

$table = [
    'collegeadmin' => 'college_admins',
    'faculty' => 'faculties',
    'alumni' => 'alumni'
][$role];

$dashboard = [
    'collegeadmin' => '../dashboards/collegeadmin_dashboard.php',
    'faculty' => '../dashboards/faculty_dashboard.php',
    'alumni' => '../alumni/alumni_dashboard.php'
][$role];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $otp_input = trim($_POST['otp']);

    // TESTING MODE: Skip OTP validation and always return true
    if (isTestingMode() && isOtpBypassEnabled()) {
        logTestingAction('OTP_BYPASS', "User: $user_id, Role: $role");
        // Skip OTP validation for testing - always succeed
        unset($_SESSION['pending_2fa']);

        if ($role === 'collegeadmin') {
            $_SESSION['collegeadmin_id'] = $user_id;
            $_SESSION['collegeadmin_name'] = $pending['name'];
            $_SESSION['college_id'] = $pending['college_id'];
        } elseif ($role === 'faculty') {
            $_SESSION['faculty_id'] = $user_id;
            $_SESSION['faculty_name'] = $pending['name'];
            $_SESSION['college_id'] = $pending['college_id'];
        } elseif ($role === 'alumni') {
            $_SESSION['alumni_id'] = $user_id;
            $_SESSION['alumni_name'] = $pending['name'];
            $_SESSION['college_id'] = $pending['college_id'];
        }

        // Optional: Clear OTP
        $clear = $conn->prepare("UPDATE $table SET otp_code = NULL, otp_expires_at = NULL WHERE id = ?");
        $clear->execute([$user_id]);

        header("Location: $dashboard");
        exit;
    } else {
        // Normal OTP validation (for production)
        $stmt = $conn->prepare("SELECT otp_code, otp_expires_at FROM $table WHERE id = ?");
        $stmt->execute([$user_id]);
        $row = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($row && $row['otp_code'] === $otp_input && strtotime($row['otp_expires_at']) > time()) {
            unset($_SESSION['pending_2fa']);

            if ($role === 'collegeadmin') {
                $_SESSION['collegeadmin_id'] = $user_id;
                $_SESSION['collegeadmin_name'] = $pending['name'];
                $_SESSION['college_id'] = $pending['college_id'];
            } elseif ($role === 'faculty') {
                $_SESSION['faculty_id'] = $user_id;
                $_SESSION['faculty_name'] = $pending['name'];
                $_SESSION['college_id'] = $pending['college_id'];
            } elseif ($role === 'alumni') {
                $_SESSION['alumni_id'] = $user_id;
                $_SESSION['alumni_name'] = $pending['name'];
                $_SESSION['college_id'] = $pending['college_id'];
            }

            // Optional: Clear OTP
            $clear = $conn->prepare("UPDATE $table SET otp_code = NULL, otp_expires_at = NULL WHERE id = ?");
            $clear->execute([$user_id]);

            header("Location: $dashboard");
            exit;
        } else {
            $error = "Invalid or expired OTP.";
        }
    }
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Verify OTP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body class="bg-light">
<div class="container mt-5">
    <div class="card shadow col-md-6 offset-md-3">
        <div class="card-header bg-dark text-white">Verify OTP</div>
        <div class="card-body">
            <!-- Testing Mode Notice -->
            <?php if (isTestingMode()): ?>
                <?php showTestingNotice('OTP validation is disabled for testing. Enter any 6-digit code to proceed.'); ?>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-danger"><?= htmlspecialchars($error) ?></div>
            <?php endif; ?>
            <form method="post">
                <div class="mb-3">
                    <?php if (isTestingMode()): ?>
                        <label>Enter any 6-digit OTP (Testing Mode)</label>
                        <input type="text" name="otp" maxlength="6" class="form-control" placeholder="<?= getTestingOtpCode() ?>" value="<?= getTestingOtpCode() ?>" required>
                        <small class="form-text text-muted">Any 6-digit code will work in testing mode</small>
                    <?php else: ?>
                        <label>Enter the OTP sent to your email</label>
                        <input type="text" name="otp" maxlength="6" class="form-control" required>
                    <?php endif; ?>
                </div>
                <button type="submit" class="btn btn-dark w-100">Verify OTP</button>
            </form>

            <?php if (isTestingMode()): ?>
            <!-- Testing Skip Button -->
            <div class="mt-3 text-center">
                <a href="verify_otp.php?skip=testing" class="btn btn-outline-warning btn-sm">
                    🚀 Skip OTP (Testing)
                </a>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>
</body>
</html>
