<?php
session_start();
require_once '../includes/db.php';
require_once '../includes/db_schema_check.php';

if (!isset($_SESSION['collegeadmin_id'])) {
    header("Location: ../auth/collegeadmin_login.php");
    exit;
}

$college_id = $_SESSION['college_id'];
$admin_name = $_SESSION['collegeadmin_name'] ?? 'Admin';
$error = '';
$success = '';

// Check and update students schema to ensure all required columns exist
try {
    $schemaUpdated = checkAndUpdateStudentsSchema($conn);
    if ($schemaUpdated) {
        $success = "Missing columns have been added to the students table. ";
    }
} catch (PDOException $e) {
    $error = "Error updating database schema: " . $e->getMessage();
}

// Get college details
$collegeStmt = $conn->prepare("SELECT name FROM colleges WHERE id = ?");
$collegeStmt->execute([$college_id]);
$college = $collegeStmt->fetch(PDO::FETCH_ASSOC);

// Get departments for filter
$deptStmt = $conn->prepare("SELECT DISTINCT department FROM students WHERE college_id = ? ORDER BY department ASC");
$deptStmt->execute([$college_id]);
$departments = $deptStmt->fetchAll(PDO::FETCH_COLUMN);

// Get years for filter
$yearStmt = $conn->prepare("SELECT DISTINCT year FROM students WHERE college_id = ? ORDER BY year ASC");
$yearStmt->execute([$college_id]);
$years = $yearStmt->fetchAll(PDO::FETCH_COLUMN);

// Get divisions for filter
$divStmt = $conn->prepare("SELECT DISTINCT division FROM students WHERE college_id = ? AND division IS NOT NULL ORDER BY division ASC");
$divStmt->execute([$college_id]);
$divisions = $divStmt->fetchAll(PDO::FETCH_COLUMN);

// Count total students
$countStmt = $conn->prepare("SELECT COUNT(*) FROM students WHERE college_id = ?");
$countStmt->execute([$college_id]);
$total_students = $countStmt->fetchColumn();

// Available fields for export
$available_fields = [
    'id' => 'ID',
    'full_name' => 'Full Name',
    'email' => 'Email',
    'phone' => 'Phone',
    'roll_number' => 'Roll Number',
    'department' => 'Department',
    'year' => 'Year',
    'division' => 'Division',
    'address' => 'Address',
    'city' => 'City',
    'state' => 'State',
    'country' => 'Country',
    'pincode' => 'Pincode',
    'date_of_birth' => 'Date of Birth',
    'gender' => 'Gender',
    'bio' => 'Bio',
    'skills' => 'Skills',
    'created_at' => 'Registration Date',
    'verified' => 'Verification Status'
];

// Default selected fields
$default_fields = ['full_name', 'email', 'phone', 'roll_number', 'department', 'year', 'division'];

// Handle export request
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['export'])) {
    // Validate selected fields
    if (!isset($_POST['fields']) || !is_array($_POST['fields']) || count($_POST['fields']) == 0) {
        $error = "Please select at least one field to export.";
    } else {
        $selected_fields = array_intersect($_POST['fields'], array_keys($available_fields));
        
        if (count($selected_fields) == 0) {
            $error = "Please select valid fields to export.";
        } else {
            // Build query conditions based on filters
            $conditions = ["college_id = ?"];
            $params = [$college_id];
            
            if (!empty($_POST['department'])) {
                $conditions[] = "department = ?";
                $params[] = $_POST['department'];
            }
            
            if (!empty($_POST['year'])) {
                $conditions[] = "year = ?";
                $params[] = $_POST['year'];
            }
            
            if (!empty($_POST['division'])) {
                $conditions[] = "division = ?";
                $params[] = $_POST['division'];
            }
            
            if (isset($_POST['verification_status']) && $_POST['verification_status'] !== '') {
                $conditions[] = "verified = ?";
                $params[] = (int)$_POST['verification_status'];
            }
            
            $whereClause = implode(' AND ', $conditions);
            
            // Prepare and execute query
            $sql = "SELECT " . implode(', ', $selected_fields) . " FROM students WHERE $whereClause";
            $stmt = $conn->prepare($sql);
            $stmt->execute($params);
            $students = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (count($students) == 0) {
                $error = "No students found matching your criteria.";
            } else {
                // Generate CSV file
                $filename = "students_export_" . date('Y-m-d_H-i-s') . ".csv";
                
                // Set headers for download
                header('Content-Type: text/csv');
                header('Content-Disposition: attachment; filename="' . $filename . '"');
                
                // Open output stream
                $output = fopen('php://output', 'w');
                
                // Add UTF-8 BOM for Excel compatibility
                fputs($output, "\xEF\xBB\xBF");
                
                // Add header row
                $header = [];
                foreach ($selected_fields as $field) {
                    $header[] = $available_fields[$field];
                }
                fputcsv($output, $header);
                
                // Add data rows
                foreach ($students as $student) {
                    // Format verification status
                    if (isset($student['verified'])) {
                        $student['verified'] = $student['verified'] ? 'Verified' : 'Not Verified';
                    }
                    
                    fputcsv($output, $student);
                }
                
                // Close output stream
                fclose($output);
                exit;
            }
        }
    }
}

// Get count of students matching filters (for preview)
$filtered_count = $total_students;
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['preview'])) {
    $conditions = ["college_id = ?"];
    $params = [$college_id];
    
    if (!empty($_POST['department'])) {
        $conditions[] = "department = ?";
        $params[] = $_POST['department'];
    }
    
    if (!empty($_POST['year'])) {
        $conditions[] = "year = ?";
        $params[] = $_POST['year'];
    }
    
    if (!empty($_POST['division'])) {
        $conditions[] = "division = ?";
        $params[] = $_POST['division'];
    }
    
    if (isset($_POST['verification_status']) && $_POST['verification_status'] !== '') {
        $conditions[] = "verified = ?";
        $params[] = (int)$_POST['verification_status'];
    }
    
    $whereClause = implode(' AND ', $conditions);
    
    $countStmt = $conn->prepare("SELECT COUNT(*) FROM students WHERE $whereClause");
    $countStmt->execute($params);
    $filtered_count = $countStmt->fetchColumn();
    
    $success = "Preview updated. $filtered_count students match your criteria.";
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bulk Export Students - Mentoshri</title>
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts - Inter -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        'primary': '#0a66c2',
                        'primary-dark': '#004182',
                        'primary-light': '#e7f3ff',
                    },
                    fontFamily: {
                        'sans': ['Inter', 'sans-serif'],
                    },
                }
            }
        }
    </script>
    <!-- Design System -->
    <link rel="stylesheet" href="../assets/css/design-system.css">
</head>
<body class="font-sans bg-gray-50 dark:bg-gray-900 min-h-screen flex flex-col">
    <!-- Header -->
    <header class="bg-white dark:bg-gray-800 shadow-sm">
        <div class="container mx-auto px-4 py-3 flex items-center justify-between">
            <div class="flex items-center">
                <a href="collegeadmin_dashboard.php" class="text-primary font-bold text-2xl">Mentoshri</a>
            </div>
            
            <div class="flex items-center space-x-4">
                <button id="themeToggle" class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700">
                    <i class="fas fa-moon dark:hidden"></i>
                    <i class="fas fa-sun hidden dark:block"></i>
                </button>
                <div class="relative">
                    <button id="notificationsBtn" class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 relative">
                        <i class="fas fa-bell"></i>
                        <span class="absolute top-0 right-0 h-4 w-4 bg-red-500 rounded-full text-xs text-white flex items-center justify-center">2</span>
                    </button>
                </div>
                <div class="relative group">
                    <button class="flex items-center space-x-1">
                        <div class="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center text-blue-600 dark:text-blue-400">
                            <i class="fas fa-user-shield"></i>
                        </div>
                        <span class="text-gray-700 dark:text-gray-300"><?= htmlspecialchars($admin_name) ?></span>
                        <i class="fas fa-chevron-down text-gray-400 text-xs"></i>
                    </button>
                    <div class="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg border border-gray-200 dark:border-gray-700 hidden group-hover:block z-10">
                        <div class="py-1">
                            <a href="collegeadmin_profile.php" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                <i class="fas fa-user mr-2"></i> Profile
                            </a>
                            <a href="collegeadmin_settings.php" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                <i class="fas fa-cog mr-2"></i> Settings
                            </a>
                            <div class="border-t border-gray-200 dark:border-gray-700"></div>
                            <a href="../auth/logout.php" class="block px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-700">
                                <i class="fas fa-sign-out-alt mr-2"></i> Logout
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="flex-grow py-8 px-4">
        <div class="container mx-auto">
            <!-- Breadcrumb -->
            <nav class="mb-6 text-sm" aria-label="Breadcrumb">
                <ol class="flex items-center space-x-2">
                    <li>
                        <a href="collegeadmin_dashboard.php" class="text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary-light">
                            <i class="fas fa-home"></i>
                            <span class="sr-only">Dashboard</span>
                        </a>
                    </li>
                    <li class="flex items-center">
                        <i class="fas fa-chevron-right text-gray-400 text-xs mx-1"></i>
                        <a href="list_students.php" class="text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary-light">
                            Student Management
                        </a>
                    </li>
                    <li class="flex items-center">
                        <i class="fas fa-chevron-right text-gray-400 text-xs mx-1"></i>
                        <span class="text-gray-700 dark:text-gray-300">Bulk Export Students</span>
                    </li>
                </ol>
            </nav>

            <div class="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                        <i class="fas fa-file-export text-primary mr-3"></i>
                        Bulk Export Students
                    </h1>
                    <p class="text-gray-600 dark:text-gray-400 mt-1">
                        Export student data from <?= htmlspecialchars($college['name'] ?? 'your college') ?> to CSV format
                    </p>
                </div>
                <div class="mt-4 md:mt-0">
                    <a href="list_students.php" class="btn btn-outline flex items-center">
                        <i class="fas fa-arrow-left mr-2"></i> Back to Students
                    </a>
                </div>
            </div>
            
            <?php if ($error): ?>
                <div class="bg-red-50 dark:bg-red-900/30 border-l-4 border-red-500 text-red-700 dark:text-red-400 p-4 mb-6 flex justify-between items-center" role="alert">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-circle mr-2"></i>
                        <span><?= htmlspecialchars($error) ?></span>
                    </div>
                    <button type="button" class="text-red-700 dark:text-red-400 hover:opacity-75" onclick="this.parentElement.style.display='none';">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="bg-green-50 dark:bg-green-900/30 border-l-4 border-green-500 text-green-700 dark:text-green-400 p-4 mb-6 flex justify-between items-center" role="alert">
                    <div class="flex items-center">
                        <i class="fas fa-check-circle mr-2"></i>
                        <span><?= htmlspecialchars($success) ?></span>
                    </div>
                    <button type="button" class="text-green-700 dark:text-green-400 hover:opacity-75" onclick="this.parentElement.style.display='none';">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            <?php endif; ?>

            <!-- Stats Overview -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 border border-gray-200 dark:border-gray-700">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 mr-4">
                            <i class="fas fa-user-graduate"></i>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Students</p>
                            <p class="text-xl font-semibold text-gray-800 dark:text-white"><?= $total_students ?></p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 border border-gray-200 dark:border-gray-700">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 mr-4">
                            <i class="fas fa-filter"></i>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Filtered Students</p>
                            <p class="text-xl font-semibold text-gray-800 dark:text-white"><?= $filtered_count ?></p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 border border-gray-200 dark:border-gray-700">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400 mr-4">
                            <i class="fas fa-file-csv"></i>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Export Format</p>
                            <p class="text-xl font-semibold text-gray-800 dark:text-white">CSV</p>
                        </div>
                    </div>
                </div>
            </div>

            <form method="POST" action="" id="exportForm">
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <!-- Filters -->
                    <div class="lg:col-span-1">
                        <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 overflow-hidden">
                            <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                                <h2 class="text-lg font-semibold text-gray-800 dark:text-white flex items-center">
                                    <i class="fas fa-filter text-primary mr-2"></i> Filter Students
                                </h2>
                                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                                    Apply filters to select specific students for export
                                </p>
                            </div>
                            
                            <div class="p-6 space-y-4">
                                <div>
                                    <label for="department" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Department</label>
                                    <select id="department" name="department" class="form-select">
                                        <option value="">All Departments</option>
                                        <?php foreach ($departments as $dept): ?>
                                            <option value="<?= htmlspecialchars($dept) ?>" <?= isset($_POST['department']) && $_POST['department'] === $dept ? 'selected' : '' ?>>
                                                <?= htmlspecialchars($dept) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div>
                                    <label for="year" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Year</label>
                                    <select id="year" name="year" class="form-select">
                                        <option value="">All Years</option>
                                        <?php foreach ($years as $year): ?>
                                            <option value="<?= htmlspecialchars($year) ?>" <?= isset($_POST['year']) && $_POST['year'] === $year ? 'selected' : '' ?>>
                                                <?= htmlspecialchars($year) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div>
                                    <label for="division" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Division</label>
                                    <select id="division" name="division" class="form-select">
                                        <option value="">All Divisions</option>
                                        <?php foreach ($divisions as $div): ?>
                                            <option value="<?= htmlspecialchars($div) ?>" <?= isset($_POST['division']) && $_POST['division'] === $div ? 'selected' : '' ?>>
                                                <?= htmlspecialchars($div) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div>
                                    <label for="verification_status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Verification Status</label>
                                    <select id="verification_status" name="verification_status" class="form-select">
                                        <option value="">All Students</option>
                                        <option value="1" <?= isset($_POST['verification_status']) && $_POST['verification_status'] === '1' ? 'selected' : '' ?>>Verified</option>
                                        <option value="0" <?= isset($_POST['verification_status']) && $_POST['verification_status'] === '0' ? 'selected' : '' ?>>Not Verified</option>
                                    </select>
                                </div>
                                
                                <div class="pt-4">
                                    <button type="submit" name="preview" class="btn btn-primary w-full">
                                        <i class="fas fa-search mr-2"></i> Preview Results
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Field Selection -->
                    <div class="lg:col-span-2">
                        <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 overflow-hidden">
                            <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                                <h2 class="text-lg font-semibold text-gray-800 dark:text-white flex items-center">
                                    <i class="fas fa-columns text-primary mr-2"></i> Select Fields to Export
                                </h2>
                                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                                    Choose which student data fields to include in your export
                                </p>
                            </div>
                            
                            <div class="p-6">
                                <div class="mb-4 flex items-center justify-between">
                                    <div>
                                        <button type="button" id="selectAllFields" class="btn btn-sm btn-outline">Select All</button>
                                        <button type="button" id="deselectAllFields" class="btn btn-sm btn-outline ml-2">Deselect All</button>
                                    </div>
                                    <div>
                                        <button type="button" id="selectDefaultFields" class="btn btn-sm btn-outline">Default Fields</button>
                                    </div>
                                </div>
                                
                                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                    <?php foreach ($available_fields as $field_key => $field_label): ?>
                                        <div class="flex items-center">
                                            <input type="checkbox" id="field_<?= $field_key ?>" name="fields[]" value="<?= $field_key ?>" class="form-checkbox field-checkbox"
                                                <?= (isset($_POST['fields']) && in_array($field_key, $_POST['fields'])) || 
                                                    (!isset($_POST['fields']) && in_array($field_key, $default_fields)) ? 'checked' : '' ?>>
                                            <label for="field_<?= $field_key ?>" class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                                                <?= htmlspecialchars($field_label) ?>
                                            </label>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                            
                            <div class="p-6 bg-gray-50 dark:bg-gray-700/50 border-t border-gray-200 dark:border-gray-700">
                                <div class="flex items-center justify-between">
                                    <div class="text-sm text-gray-600 dark:text-gray-400">
                                        <span id="selectedFieldsCount">0</span> fields selected
                                    </div>
                                    <div>
                                        <button type="submit" name="export" class="btn btn-primary">
                                            <i class="fas fa-file-export mr-2"></i> Export to CSV
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Export Tips -->
                        <div class="mt-6 bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
                            <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4 flex items-center">
                                <i class="fas fa-lightbulb text-yellow-500 mr-2"></i> Export Tips
                            </h3>
                            
                            <ul class="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                                <li class="flex items-start">
                                    <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                                    <span>The exported CSV file can be opened in Microsoft Excel, Google Sheets, or any spreadsheet software.</span>
                                </li>
                                <li class="flex items-start">
                                    <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                                    <span>UTF-8 encoding is used to ensure proper display of special characters.</span>
                                </li>
                                <li class="flex items-start">
                                    <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                                    <span>Use filters to narrow down the data and export only what you need.</span>
                                </li>
                                <li class="flex items-start">
                                    <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                                    <span>The export includes a header row with field names for easy identification.</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </main>

    <footer class="bg-white dark:bg-gray-800 shadow-sm mt-auto">
        <div class="container mx-auto px-4 py-3 text-center text-sm text-gray-600 dark:text-gray-400">
            &copy; <?= date('Y') ?> Mentoshri. All rights reserved.
        </div>
    </footer>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const fieldCheckboxes = document.querySelectorAll('.field-checkbox');
            const selectAllFieldsBtn = document.getElementById('selectAllFields');
            const deselectAllFieldsBtn = document.getElementById('deselectAllFields');
            const selectDefaultFieldsBtn = document.getElementById('selectDefaultFields');
            const selectedFieldsCountSpan = document.getElementById('selectedFieldsCount');
            const exportForm = document.getElementById('exportForm');
            
            // Default fields
            const defaultFields = <?= json_encode($default_fields) ?>;
            
            // Function to update selected fields count
            function updateSelectedFieldsCount() {
                const selected = Array.from(fieldCheckboxes).filter(checkbox => checkbox.checked).length;
                selectedFieldsCountSpan.textContent = selected;
            }
            
            // Initial update
            updateSelectedFieldsCount();
            
            // Select all fields
            selectAllFieldsBtn.addEventListener('click', function() {
                fieldCheckboxes.forEach(checkbox => {
                    checkbox.checked = true;
                });
                updateSelectedFieldsCount();
            });
            
            // Deselect all fields
            deselectAllFieldsBtn.addEventListener('click', function() {
                fieldCheckboxes.forEach(checkbox => {
                    checkbox.checked = false;
                });
                updateSelectedFieldsCount();
            });
            
            // Select default fields
            selectDefaultFieldsBtn.addEventListener('click', function() {
                fieldCheckboxes.forEach(checkbox => {
                    checkbox.checked = defaultFields.includes(checkbox.value);
                });
                updateSelectedFieldsCount();
            });
            
            // Update count when any checkbox changes
            fieldCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', updateSelectedFieldsCount);
            });
            
            // Form validation before export
            exportForm.addEventListener('submit', function(event) {
                if (event.submitter && event.submitter.name === 'export') {
                    const selectedFields = Array.from(fieldCheckboxes).filter(checkbox => checkbox.checked);
                    
                    if (selectedFields.length === 0) {
                        event.preventDefault();
                        alert('Please select at least one field to export.');
                    }
                }
            });
        });
    </script>
</body>
</html> 