<?php
session_start();
require_once '../includes/db.php';
require_once '../includes/db_schema_check.php';
require_once '../notify/send_email.php';

// Ensure only college admin can access this page
if (!isset($_SESSION['collegeadmin_id'])) {
    header("Location: ../auth/collegeadmin_login.php");
    exit;
}

$college_id = $_SESSION['college_id'];
$admin_name = $_SESSION['collegeadmin_name'] ?? 'Admin';

// Set role explicitly for the sidebar
$role = 'collegeadmin';
$user_id = $_SESSION['collegeadmin_id'];
$user_name = $_SESSION['collegeadmin_name'];
$current_page = basename($_SERVER['PHP_SELF']);

// Get college details
$collegeStmt = $conn->prepare("SELECT name FROM colleges WHERE id = ?");
$collegeStmt->execute([$college_id]);
$college = $collegeStmt->fetch(PDO::FETCH_ASSOC);

// Process form submission
$success = '';
$error = '';
$preview_data = [];
$column_mapping = [];
$has_header = true;
$uploaded_file = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Handle file upload
    if (isset($_POST['upload']) && isset($_FILES['import_file'])) {
        $file = $_FILES['import_file'];
        $has_header = isset($_POST['has_header']) ? true : false;
        $send_credentials = isset($_POST['send_credentials']) ? true : false;
        
        // Check for errors
        if ($file['error'] !== UPLOAD_ERR_OK) {
            $error = "File upload failed. Error code: " . $file['error'];
        } else {
            // Check file size (5MB limit)
            if ($file['size'] > 5 * 1024 * 1024) {
                $error = "File size exceeds the 5MB limit.";
            } else {
                $file_name = $file['name'];
                $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));
                
                // Check file extension
                if (!in_array($file_ext, ['csv', 'xlsx', 'xls'])) {
                    $error = "Only CSV and Excel files are allowed.";
                } else {
                    // Create uploads directory if it doesn't exist
                    $upload_dir = '../uploads/temp';
                    if (!file_exists($upload_dir)) {
                        mkdir($upload_dir, 0755, true);
                    }
                    
                    // Generate unique filename
                    $uploaded_file = $upload_dir . '/' . uniqid() . '_' . $file_name;
                    
                    // Move uploaded file
                    if (move_uploaded_file($file['tmp_name'], $uploaded_file)) {
                        // Process CSV file
                        if ($file_ext === 'csv') {
                            // Read CSV file
                            $handle = fopen($uploaded_file, 'r');
                            if ($handle !== false) {
                                $row_count = 0;
                                $headers = [];
                                
                                // Read first 10 rows for preview
                                while (($data = fgetcsv($handle)) !== false && $row_count < 11) {
                                    if ($row_count === 0 && $has_header) {
                                        $headers = $data;
                                    } else {
                                        $preview_data[] = $data;
                                    }
                                    $row_count++;
                                }
                                
                                fclose($handle);
                                
                                // If no header row, generate column names
                                if (!$has_header) {
                                    $headers = [];
                                    for ($i = 0; $i < count($preview_data[0]); $i++) {
                                        $headers[] = "Column " . ($i + 1);
                                    }
                                }
                                
                                // Create column mapping
                                $required_fields = ['full_name', 'email', 'department', 'year'];
                                $optional_fields = ['roll_number', 'division', 'phone', 'password'];
                                $all_fields = array_merge($required_fields, $optional_fields);
                                
                                foreach ($headers as $index => $header) {
                                    $header = strtolower(trim(str_replace(' ', '_', $header)));
                                    
                                    // Try to match header with field
                                    $matched = false;
                                    foreach ($all_fields as $field) {
                                        if (strpos($header, $field) !== false) {
                                            $column_mapping[$index] = $field;
                                            $matched = true;
                                            break;
                                        }
                                    }
                                    
                                    if (!$matched) {
                                        $column_mapping[$index] = '';
                                    }
                                }
                                
                                $success = "File uploaded successfully. Please review the data and complete the import.";
                            } else {
                                $error = "Failed to open the uploaded file.";
                            }
                        } else {
                            // For Excel files - would need PhpSpreadsheet library
                            $error = "Excel file processing requires the PhpSpreadsheet library. Please use CSV format for now.";
                            // Delete the uploaded file
                            if (file_exists($uploaded_file)) {
                                unlink($uploaded_file);
                                $uploaded_file = '';
                            }
                        }
                    } else {
                        $error = "Failed to move uploaded file.";
                    }
                }
            }
        }
    }
    
    // Handle import confirmation
    if (isset($_POST['confirm_import']) && !empty($_POST['uploaded_file'])) {
        $uploaded_file = $_POST['uploaded_file'];
        $has_header = isset($_POST['has_header']) && $_POST['has_header'] === '1';
        $send_credentials = isset($_POST['send_credentials']) && $_POST['send_credentials'] === '1';
        $column_mapping = $_POST['column_mapping'] ?? [];
        $import_summary = [];

        if (file_exists($uploaded_file)) {
            $conn->beginTransaction();
            try {
                $file_ext = strtolower(pathinfo($uploaded_file, PATHINFO_EXTENSION));
                $import_data = [];

                if ($file_ext === 'csv') {
                    if (($handle = fopen($uploaded_file, "r")) !== FALSE) {
                        $row_count = 0;
                        while (($data = fgetcsv($handle, 1000, ",")) !== FALSE) {
                            $row_count++;
                            if ($has_header && $row_count === 1) {
                                continue;
                            }
                            $import_data[] = $data;
                        }
                        fclose($handle);
                    }
                }

                $imported_count = 0;
                $skipped_count = 0;
                $errors = [];
                $total_rows = count($import_data);

                if ($total_rows === 0) {
                    throw new Exception("No data rows found in the uploaded file.");
                }

                foreach ($import_data as $row_index => $row_data) {
                    $student_data = [];
                    foreach ($column_mapping as $col_index => $field_name) {
                        if (!empty($field_name) && isset($row_data[$col_index])) {
                            $student_data[$field_name] = trim($row_data[$col_index]);
                        }
                    }

                    // Validate required fields
                    if (empty($student_data['full_name']) || empty($student_data['email']) || empty($student_data['department']) || empty($student_data['year'])) {
                        $skipped_count++;
                        $errors[] = "Row " . ($row_index + 2) . ": Missing required fields (Full Name, Email, Department, or Year).";
                        continue;
                    }

                    // Check for duplicate email
                    $check_email = $conn->prepare("SELECT COUNT(*) FROM students WHERE email = ?");
                    $check_email->execute([$student_data['email']]);
                    if ($check_email->fetchColumn() > 0) {
                        $skipped_count++;
                        $errors[] = "Row " . ($row_index + 2) . ": Email '{$student_data['email']}' already exists.";
                        continue;
                    }

                    // Check for duplicate roll number in the same college
                    if (!empty($student_data['roll_number'])) {
                        $check_roll = $conn->prepare("SELECT COUNT(*) FROM students WHERE roll_number = ? AND college_id = ?");
                        $check_roll->execute([$student_data['roll_number'], $college_id]);
                        if ($check_roll->fetchColumn() > 0) {
                            $skipped_count++;
                            $errors[] = "Row " . ($row_index + 2) . ": Roll number '{$student_data['roll_number']}' already exists in this college.";
                            continue;
                        }
                    }

                    // Generate password if not provided
                    $plain_password = !empty($student_data['password']) ? $student_data['password'] : bin2hex(random_bytes(4));
                    $hashed_password = password_hash($plain_password, PASSWORD_DEFAULT);

                    $sql = "INSERT INTO students (college_id, full_name, email, roll_number, department, year, division, password, verified, created_at) 
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
                    $stmt = $conn->prepare($sql);
                    
                    $result = $stmt->execute([
                        $college_id,
                        $student_data['full_name'],
                        $student_data['email'],
                        $student_data['roll_number'] ?? null,
                        $student_data['department'],
                        $student_data['year'],
                        $student_data['division'] ?? null,
                        $hashed_password,
                        0 // Students are not verified by default on import
                    ]);

                    if ($result) {
                        $imported_count++;
                        if ($send_credentials) {
                            $subject = "Your Student Account Details - " . htmlspecialchars($college['name']);
                            $message = "<p>Dear " . htmlspecialchars($student_data['full_name']) . ",</p>";
                            $message .= "<p>Your student account has been created at " . htmlspecialchars($college['name']) . ".</p>";
                            $message .= "<p><strong>Email:</strong> " . htmlspecialchars($student_data['email']) . "<br>";
                            $message .= "<strong>Password:</strong> " . htmlspecialchars($plain_password) . "</p>";
                            $message .= "<p>You can login at: <a href='https://" . $_SERVER['HTTP_HOST'] . "/auth/student_login.php'>Student Login</a></p>";
                            $message .= "<p>Please keep your credentials secure.</p>";
                            $message .= "<p>Regards,<br>Administration Team<br>" . htmlspecialchars($college['name']) . "</p>";
                            
                            sendEmail($student_data['email'], $student_data['full_name'], $subject, $message);
                        }
                    } else {
                        $skipped_count++;
                        $errors[] = "Row " . ($row_index + 2) . ": Database error on insert.";
                    }
                }

                $conn->commit();

                $success = "Import completed! Successfully imported: $imported_count. Skipped: $skipped_count.";
                $import_summary = ['imported' => $imported_count, 'skipped' => $skipped_count, 'errors' => $errors];

            } catch (Exception $e) {
                $conn->rollBack();
                $error = "A critical error occurred during import, and all changes have been rolled back. Error: " . $e->getMessage();
            } finally {
                if (file_exists($uploaded_file)) {
                    unlink($uploaded_file);
                }
                $preview_data = [];
                $uploaded_file = '';
            }
        } else {
            $error = "Uploaded file not found. Please try uploading again.";
        }
    }
}

// Include the header
require_once '../includes/header.php';
?>

    <!-- Main Content -->
    <main class="flex-grow py-8 px-4 sm:px-6 lg:px-8">
        <div class="max-w-7xl mx-auto">
            <!-- Breadcrumb -->
            <nav class="mb-8 text-sm" aria-label="Breadcrumb">
                <ol class="flex items-center space-x-2">
                    <li>
                        <a href="collegeadmin_dashboard.php" class="text-gray-500 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
                            <i class="fas fa-home"></i>
                            <span class="sr-only">Dashboard</span>
                        </a>
                    </li>
                    <li class="flex items-center">
                        <i class="fas fa-chevron-right text-gray-400 text-xs mx-2"></i>
                        <a href="list_students.php" class="text-gray-500 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
                            Student Management
                        </a>
                    </li>
                    <li class="flex items-center">
                        <i class="fas fa-chevron-right text-gray-400 text-xs mx-2"></i>
                        <span class="text-gray-700 dark:text-gray-300 font-medium">Import Students</span>
                    </li>
                </ol>
            </nav>

            <!-- Enhanced Page Header -->
            <div class="mb-8">
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900 dark:text-white flex items-center">
                            <div class="w-12 h-12 bg-gradient-to-br from-blue-600 to-blue-800 rounded-xl flex items-center justify-center mr-4 shadow-lg">
                                <i class="fas fa-file-import text-white text-xl"></i>
                            </div>
                            Import Students
                        </h1>
                        <p class="text-gray-600 dark:text-gray-400 mt-2 text-lg">
                            Bulk import students from CSV or Excel files with advanced validation and mapping
                        </p>
                    </div>
                    <div class="mt-6 lg:mt-0 flex flex-wrap gap-3">
                        <a href="list_students.php" class="inline-flex items-center px-4 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 font-medium rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-200">
                            <i class="fas fa-arrow-left mr-2"></i> Back to Students
                        </a>
                        <a href="add_student.php" class="inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200">
                            <i class="fas fa-user-plus mr-2"></i> Add Single Student
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Alert Messages -->
            <?php if (!empty($error)): ?>
                <div class="bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-800 rounded-xl p-4 mb-8 flex items-start" role="alert">
                    <div class="flex-shrink-0">
                        <div class="w-10 h-10 bg-red-100 dark:bg-red-800 rounded-lg flex items-center justify-center">
                            <i class="fas fa-exclamation-triangle text-red-600 dark:text-red-300"></i>
                        </div>
                    </div>
                    <div class="ml-4 flex-grow">
                        <h3 class="text-sm font-medium text-red-800 dark:text-red-200">Import Error</h3>
                        <p class="text-sm text-red-700 dark:text-red-300 mt-1"><?= $error ?></p>
                    </div>
                    <button type="button" class="flex-shrink-0 ml-4 text-red-400 hover:text-red-600 dark:hover:text-red-200 transition-colors" onclick="this.parentElement.style.display='none';">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            <?php endif; ?>

            <?php if (!empty($success)): ?>
                <div class="bg-green-50 dark:bg-green-900 border border-green-200 dark:border-green-800 rounded-xl p-4 mb-8 flex items-start" role="alert">
                    <div class="flex-shrink-0">
                        <div class="w-10 h-10 bg-green-100 dark:bg-green-800 rounded-lg flex items-center justify-center">
                            <i class="fas fa-check-circle text-green-600 dark:text-green-300"></i>
                        </div>
                    </div>
                    <div class="ml-4 flex-grow">
                        <h3 class="text-sm font-medium text-green-800 dark:text-green-200">Success</h3>
                        <p class="text-sm text-green-700 dark:text-green-300 mt-1"><?= $success ?></p>
                    </div>
                    <button type="button" class="flex-shrink-0 ml-4 text-green-400 hover:text-green-600 dark:hover:text-green-200 transition-colors" onclick="this.parentElement.style.display='none';">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            <?php endif; ?>
            
            <!-- Enhanced Import Instructions -->
            <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 mb-8 overflow-hidden">
                <div class="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-900 dark:to-gray-800 px-8 py-6 border-b border-gray-200 dark:border-gray-700">
                    <h2 class="text-xl font-bold text-gray-900 dark:text-white flex items-center">
                        <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-info-circle text-blue-600 dark:text-blue-300"></i>
                        </div>
                        Import Instructions & Guidelines
                    </h2>
                    <p class="text-gray-600 dark:text-gray-400 mt-2">Follow these guidelines for successful bulk student import</p>
                </div>

                <div class="p-8">
                    <!-- File Format Requirements -->
                    <div class="mb-8">
                        <div class="flex items-center mb-4">
                            <div class="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-file-alt text-green-600 dark:text-green-300 text-sm"></i>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">File Format Requirements</h3>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="bg-gray-50 dark:bg-gray-700 rounded-xl p-6 border border-gray-200 dark:border-gray-600">
                                <h4 class="font-medium text-gray-900 dark:text-white mb-3 flex items-center">
                                    <i class="fas fa-check-circle text-green-500 mr-2"></i>
                                    Supported Formats
                                </h4>
                                <ul class="space-y-2 text-gray-700 dark:text-gray-300">
                                    <li class="flex items-center"><i class="fas fa-file-csv text-blue-500 mr-2"></i> CSV (comma-separated values)</li>
                                    <li class="flex items-center"><i class="fas fa-file-excel text-green-500 mr-2"></i> Excel (.xlsx, .xls)</li>
                                </ul>
                            </div>
                            <div class="bg-gray-50 dark:bg-gray-700 rounded-xl p-6 border border-gray-200 dark:border-gray-600">
                                <h4 class="font-medium text-gray-900 dark:text-white mb-3 flex items-center">
                                    <i class="fas fa-exclamation-triangle text-yellow-500 mr-2"></i>
                                    File Limitations
                                </h4>
                                <ul class="space-y-2 text-gray-700 dark:text-gray-300">
                                    <li class="flex items-center"><i class="fas fa-weight-hanging text-orange-500 mr-2"></i> Maximum file size: 5MB</li>
                                    <li class="flex items-center"><i class="fas fa-users text-purple-500 mr-2"></i> Maximum 500 students per import</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Column Requirements -->
                    <div class="mb-8">
                        <div class="flex items-center mb-4">
                            <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-columns text-purple-600 dark:text-purple-300 text-sm"></i>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Column Requirements</h3>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="bg-red-50 dark:bg-red-900 rounded-xl p-6 border border-red-200 dark:border-red-800">
                                <h4 class="font-medium text-red-900 dark:text-red-200 mb-3 flex items-center">
                                    <i class="fas fa-asterisk text-red-500 mr-2 text-xs"></i>
                                    Required Columns
                                </h4>
                                <ul class="space-y-2 text-red-800 dark:text-red-300">
                                    <li class="flex items-center"><i class="fas fa-user text-red-500 mr-2"></i> Full Name</li>
                                    <li class="flex items-center"><i class="fas fa-envelope text-red-500 mr-2"></i> Email</li>
                                    <li class="flex items-center"><i class="fas fa-graduation-cap text-red-500 mr-2"></i> Department</li>
                                    <li class="flex items-center"><i class="fas fa-calendar-alt text-red-500 mr-2"></i> Year</li>
                                </ul>
                            </div>
                            <div class="bg-blue-50 dark:bg-blue-900 rounded-xl p-6 border border-blue-200 dark:border-blue-800">
                                <h4 class="font-medium text-blue-900 dark:text-blue-200 mb-3 flex items-center">
                                    <i class="fas fa-plus-circle text-blue-500 mr-2"></i>
                                    Optional Columns
                                </h4>
                                <ul class="space-y-2 text-blue-800 dark:text-blue-300">
                                    <li class="flex items-center"><i class="fas fa-id-card text-blue-500 mr-2"></i> Roll Number</li>
                                    <li class="flex items-center"><i class="fas fa-users text-blue-500 mr-2"></i> Division</li>
                                    <li class="flex items-center"><i class="fas fa-phone text-blue-500 mr-2"></i> Phone</li>
                                    <li class="flex items-center"><i class="fas fa-key text-blue-500 mr-2"></i> Password</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Sample Format -->
                    <div class="mb-8">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-indigo-100 dark:bg-indigo-900 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-table text-indigo-600 dark:text-indigo-300 text-sm"></i>
                                </div>
                                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Sample Format</h3>
                            </div>
                            <div class="flex space-x-3">
                                <a href="../templates/sample_students_import.csv" download class="inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-lg transition-colors">
                                    <i class="fas fa-download mr-2"></i> CSV Template
                                </a>
                                <a href="../templates/sample_students_import.xlsx" download class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors">
                                    <i class="fas fa-download mr-2"></i> Excel Template
                                </a>
                            </div>
                        </div>
                        <div class="bg-gray-50 dark:bg-gray-700 rounded-xl p-6 border border-gray-200 dark:border-gray-600 overflow-hidden">
                            <div class="overflow-x-auto">
                                <table class="min-w-full bg-white dark:bg-gray-800 rounded-lg overflow-hidden text-sm">
                                    <thead>
                                        <tr class="bg-gray-100 dark:bg-gray-900">
                                            <th class="py-3 px-4 text-left font-medium text-gray-900 dark:text-white">Full Name</th>
                                            <th class="py-3 px-4 text-left font-medium text-gray-900 dark:text-white">Email</th>
                                            <th class="py-3 px-4 text-left font-medium text-gray-900 dark:text-white">Department</th>
                                            <th class="py-3 px-4 text-left font-medium text-gray-900 dark:text-white">Year</th>
                                            <th class="py-3 px-4 text-left font-medium text-gray-900 dark:text-white">Roll Number</th>
                                            <th class="py-3 px-4 text-left font-medium text-gray-900 dark:text-white">Division</th>
                                            <th class="py-3 px-4 text-left font-medium text-gray-900 dark:text-white">Phone</th>
                                            <th class="py-3 px-4 text-left font-medium text-gray-900 dark:text-white">Password</th>
                                        </tr>
                                    </thead>
                                    <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                                            <td class="py-3 px-4 text-gray-900 dark:text-white">John Doe</td>
                                            <td class="py-3 px-4 text-gray-700 dark:text-gray-300"><EMAIL></td>
                                            <td class="py-3 px-4 text-gray-700 dark:text-gray-300">Computer Science</td>
                                            <td class="py-3 px-4 text-gray-700 dark:text-gray-300">2</td>
                                            <td class="py-3 px-4 text-gray-700 dark:text-gray-300">CS2023001</td>
                                            <td class="py-3 px-4 text-gray-700 dark:text-gray-300">A</td>
                                            <td class="py-3 px-4 text-gray-700 dark:text-gray-300">+1234567890</td>
                                            <td class="py-3 px-4 text-gray-700 dark:text-gray-300">password123</td>
                                        </tr>
                                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                                            <td class="py-3 px-4 text-gray-900 dark:text-white">Jane Smith</td>
                                            <td class="py-3 px-4 text-gray-700 dark:text-gray-300"><EMAIL></td>
                                            <td class="py-3 px-4 text-gray-700 dark:text-gray-300">Electrical Engineering</td>
                                            <td class="py-3 px-4 text-gray-700 dark:text-gray-300">3</td>
                                            <td class="py-3 px-4 text-gray-700 dark:text-gray-300">EE2022045</td>
                                            <td class="py-3 px-4 text-gray-700 dark:text-gray-300">B</td>
                                            <td class="py-3 px-4 text-gray-700 dark:text-gray-300">+9876543210</td>
                                            <td class="py-3 px-4 text-gray-700 dark:text-gray-300">securepass456</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Important Notes -->
                    <div class="bg-yellow-50 dark:bg-yellow-900 rounded-xl p-6 border border-yellow-200 dark:border-yellow-800">
                        <div class="flex items-center mb-4">
                            <div class="w-8 h-8 bg-yellow-100 dark:bg-yellow-800 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-lightbulb text-yellow-600 dark:text-yellow-300 text-sm"></i>
                            </div>
                            <h3 class="text-lg font-semibold text-yellow-900 dark:text-yellow-200">Important Notes</h3>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <ul class="space-y-3 text-yellow-800 dark:text-yellow-300">
                                <li class="flex items-start">
                                    <i class="fas fa-key text-yellow-600 mr-3 mt-1 text-sm"></i>
                                    <span>If passwords are not provided, random passwords will be generated</span>
                                </li>
                                <li class="flex items-start">
                                    <i class="fas fa-envelope text-yellow-600 mr-3 mt-1 text-sm"></i>
                                    <span>Email addresses must be unique across the system</span>
                                </li>
                            </ul>
                            <ul class="space-y-3 text-yellow-800 dark:text-yellow-300">
                                <li class="flex items-start">
                                    <i class="fas fa-user-check text-yellow-600 mr-3 mt-1 text-sm"></i>
                                    <span>All students will be initially set as "Not Verified"</span>
                                </li>
                                <li class="flex items-start">
                                    <i class="fas fa-users text-yellow-600 mr-3 mt-1 text-sm"></i>
                                    <span>Maximum 500 students can be imported at once</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Enhanced Upload Form -->
            <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 mb-8 overflow-hidden">
                <div class="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-gray-900 dark:to-gray-800 px-8 py-6 border-b border-gray-200 dark:border-gray-700">
                    <h2 class="text-xl font-bold text-gray-900 dark:text-white flex items-center">
                        <div class="w-10 h-10 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-cloud-upload-alt text-green-600 dark:text-green-300"></i>
                        </div>
                        Upload Student Data File
                    </h2>
                    <p class="text-gray-600 dark:text-gray-400 mt-2">Select your CSV or Excel file to begin the import process</p>
                </div>

                <div class="p-8">
                    <form method="POST" enctype="multipart/form-data" class="space-y-8">
                        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                            <!-- File Upload Area -->
                            <div class="lg:col-span-2">
                                <label for="import_file" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-4">Select CSV or Excel File</label>
                                <div class="relative">
                                    <label class="w-full flex flex-col items-center px-8 py-12 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-gray-700 dark:to-gray-600 text-blue-600 dark:text-blue-400 rounded-2xl shadow-lg tracking-wide border-2 border-dashed border-blue-300 dark:border-blue-600 cursor-pointer hover:bg-blue-100 dark:hover:bg-gray-600 transition-all duration-300 group">
                                        <div class="w-16 h-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mb-4 group-hover:scale-110 transition-transform">
                                            <i class="fas fa-cloud-upload-alt text-2xl text-blue-600 dark:text-blue-300"></i>
                                        </div>
                                        <span class="text-lg font-medium text-gray-700 dark:text-gray-300 mb-2">Drop your file here or click to browse</span>
                                        <span class="text-sm text-gray-500 dark:text-gray-400">Supports CSV, Excel (.xlsx, .xls) files up to 5MB</span>
                                        <input id="import_file" name="import_file" type="file" class="hidden" accept=".csv, .xlsx, .xls" />
                                    </label>
                                </div>
                                <div id="file_info" class="mt-4 hidden">
                                    <div class="bg-green-50 dark:bg-green-900 border border-green-200 dark:border-green-800 rounded-lg p-4">
                                        <div class="flex items-center">
                                            <i class="fas fa-file-alt text-green-600 dark:text-green-300 mr-3"></i>
                                            <div>
                                                <p id="file_name" class="text-sm font-medium text-green-800 dark:text-green-200">No file selected</p>
                                                <p id="file_size" class="text-xs text-green-600 dark:text-green-400"></p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Upload Options -->
                            <div class="lg:col-span-1">
                                <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-4">Import Options</h3>
                                <div class="space-y-6">
                                    <div class="bg-gray-50 dark:bg-gray-700 rounded-xl p-6 border border-gray-200 dark:border-gray-600">
                                        <div class="space-y-4">
                                            <div class="flex items-start">
                                                <input type="checkbox" id="has_header" name="has_header" class="h-5 w-5 text-blue-600 border-gray-300 dark:border-gray-600 rounded focus:ring-blue-500 mt-0.5" checked>
                                                <div class="ml-3">
                                                    <label for="has_header" class="text-sm font-medium text-gray-700 dark:text-gray-300">
                                                        File has header row
                                                    </label>
                                                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">First row contains column names</p>
                                                </div>
                                            </div>
                                            <div class="flex items-start">
                                                <input type="checkbox" id="send_credentials" name="send_credentials" class="h-5 w-5 text-blue-600 border-gray-300 dark:border-gray-600 rounded focus:ring-blue-500 mt-0.5">
                                                <div class="ml-3">
                                                    <label for="send_credentials" class="text-sm font-medium text-gray-700 dark:text-gray-300">
                                                        Send login credentials
                                                    </label>
                                                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Email credentials to students</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="flex justify-end pt-6 border-t border-gray-200 dark:border-gray-700">
                            <button type="submit" name="upload" class="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200">
                                <i class="fas fa-upload mr-2"></i> Upload and Preview Data
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <?php if (!empty($preview_data)): ?>
            <!-- Data Preview and Mapping -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 mb-6">
                <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
                    <h2 class="font-semibold text-gray-800 dark:text-white flex items-center">
                        <i class="fas fa-table mr-2 text-primary"></i> Data Preview and Column Mapping
                    </h2>
                </div>
                
                <div class="p-6">
                    <form method="POST" class="space-y-6">
                        <input type="hidden" name="uploaded_file" value="<?= htmlspecialchars($uploaded_file) ?>">
                        <input type="hidden" name="has_header" value="<?= $has_header ? '1' : '0' ?>">
                        <input type="hidden" name="send_credentials" value="<?= isset($_POST['send_credentials']) ? '1' : '0' ?>">
                        
                        <div class="mb-6">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-3">Column Mapping</h3>
                            <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
                                Please verify that the columns are correctly mapped to the appropriate fields. Required fields are marked with an asterisk (*).
                            </p>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                                <?php foreach ($column_mapping as $index => $field): ?>
                                    <div class="form-group">
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                            <?= htmlspecialchars($has_header ? $headers[$index] : "Column " . ($index + 1)) ?>
                                        </label>
                                        <select name="column_mapping[<?= $index ?>]" class="form-select w-full">
                                            <option value="">-- Ignore this column --</option>
                                            <option value="full_name" <?= $field === 'full_name' ? 'selected' : '' ?>>Full Name *</option>
                                            <option value="email" <?= $field === 'email' ? 'selected' : '' ?>>Email *</option>
                                            <option value="department" <?= $field === 'department' ? 'selected' : '' ?>>Department *</option>
                                            <option value="year" <?= $field === 'year' ? 'selected' : '' ?>>Year *</option>
                                            <option value="roll_number" <?= $field === 'roll_number' ? 'selected' : '' ?>>Roll Number</option>
                                            <option value="division" <?= $field === 'division' ? 'selected' : '' ?>>Division</option>
                                            <option value="phone" <?= $field === 'phone' ? 'selected' : '' ?>>Phone</option>
                                            <option value="password" <?= $field === 'password' ? 'selected' : '' ?>>Password</option>
                                        </select>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        
                        <div class="mb-6">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-3">Data Preview</h3>
                            <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
                                Showing up to 10 rows from your file. Please review the data before importing.
                            </p>
                            
                            <div class="overflow-x-auto">
                                <table class="min-w-full bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 text-sm">
                                    <thead>
                                        <tr>
                                            <?php foreach ($headers as $header): ?>
                                                <th class="py-2 px-3 bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-600 text-left"><?= htmlspecialchars($header) ?></th>
                                            <?php endforeach; ?>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($preview_data as $row): ?>
                                            <tr>
                                                <?php foreach ($row as $cell): ?>
                                                    <td class="py-2 px-3 border-b border-gray-200 dark:border-gray-600"><?= htmlspecialchars($cell) ?></td>
                                                <?php endforeach; ?>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        
                        <div class="flex justify-between items-center">
                            <div class="text-sm text-gray-600 dark:text-gray-400">
                                <p><i class="fas fa-info-circle mr-1"></i> Total rows to import: <span class="font-medium"><?= count($preview_data) ?></span></p>
                            </div>
                            <div class="flex space-x-3">
                                <a href="import_students.php" class="btn btn-outline">
                                    <i class="fas fa-times mr-2"></i> Cancel
                                </a>
                                <button type="submit" name="confirm_import" class="btn btn-primary">
                                    <i class="fas fa-file-import mr-2"></i> Import Students
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <?php endif; ?>
            
            <!-- Create Sample Templates Directory and Files -->
            <?php
            $templates_dir = '../templates';
            if (!file_exists($templates_dir)) {
                mkdir($templates_dir, 0755, true);
            }
            
            // Create CSV Template
            $csv_template = $templates_dir . '/sample_students_import.csv';
            if (!file_exists($csv_template)) {
                $csv_content = "Full Name,Email,Department,Year,Roll Number,Division,Phone,Password\n";
                $csv_content .= "John Doe,<EMAIL>,Computer Science,2,CS2023001,A,+1234567890,password123\n";
                $csv_content .= "Jane Smith,<EMAIL>,Electrical Engineering,3,EE2022045,B,+9876543210,securepass456";
                file_put_contents($csv_template, $csv_content);
            }
            
            // Note: Excel template would require a library like PhpSpreadsheet to create
            // For now, we'll just provide the CSV template
            ?>
        </div>
    </main>

<?php
// Include the footer
require_once '../includes/footer.php';
?>

<script>
    // Enhanced file upload functionality
    const fileInput = document.getElementById('import_file');
    const fileNameDisplay = document.getElementById('file_name');
    const fileSizeDisplay = document.getElementById('file_size');
    const fileInfoDiv = document.getElementById('file_info');

    fileInput.addEventListener('change', function() {
        if (this.files.length > 0) {
            const file = this.files[0];
            const fileName = file.name;
            const fileSize = (file.size / 1024 / 1024).toFixed(2); // Convert to MB
            const fileType = fileName.split('.').pop().toUpperCase();

            fileNameDisplay.textContent = fileName;
            fileSizeDisplay.textContent = `${fileSize} MB • ${fileType} file`;
            fileInfoDiv.classList.remove('hidden');

            // Validate file size
            if (file.size > 5 * 1024 * 1024) {
                showAlert('File size exceeds 5MB limit. Please choose a smaller file.', 'error');
                this.value = '';
                fileInfoDiv.classList.add('hidden');
            }
        } else {
            fileNameDisplay.textContent = 'No file selected';
            fileSizeDisplay.textContent = '';
            fileInfoDiv.classList.add('hidden');
        }
    });

    // Enhanced drag and drop functionality
    const dropArea = fileInput.parentElement;

    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        dropArea.addEventListener(eventName, preventDefaults, false);
        document.body.addEventListener(eventName, preventDefaults, false);
    });

    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    ['dragenter', 'dragover'].forEach(eventName => {
        dropArea.addEventListener(eventName, highlight, false);
    });

    ['dragleave', 'drop'].forEach(eventName => {
        dropArea.addEventListener(eventName, unhighlight, false);
    });

    function highlight() {
        dropArea.classList.add('border-blue-500', 'bg-blue-100', 'dark:bg-blue-900');
        dropArea.classList.remove('border-blue-300');
    }

    function unhighlight() {
        dropArea.classList.remove('border-blue-500', 'bg-blue-100', 'dark:bg-blue-900');
        dropArea.classList.add('border-blue-300');
    }

    dropArea.addEventListener('drop', handleDrop, false);

    function handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;

        if (files.length > 0) {
            const file = files[0];
            const allowedTypes = ['csv', 'xlsx', 'xls'];
            const fileType = file.name.split('.').pop().toLowerCase();

            if (allowedTypes.includes(fileType)) {
                fileInput.files = files;
                // Trigger change event
                const event = new Event('change');
                fileInput.dispatchEvent(event);
            } else {
                showAlert('Please upload only CSV or Excel files.', 'error');
            }
        }
    }

    // Alert function
    function showAlert(message, type) {
        const alertDiv = document.createElement('div');
        alertDiv.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg ${
            type === 'error'
                ? 'bg-red-100 border border-red-400 text-red-700'
                : 'bg-green-100 border border-green-400 text-green-700'
        }`;
        alertDiv.innerHTML = `
            <div class="flex items-center">
                <i class="fas ${type === 'error' ? 'fa-exclamation-circle' : 'fa-check-circle'} mr-2"></i>
                <span>${message}</span>
                <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        document.body.appendChild(alertDiv);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentElement) {
                alertDiv.remove();
            }
        }, 5000);
    }
</script>