<?php
// Include session configuration
require_once '../includes/session_config.php';
require_once '../includes/db.php';
require_once '../includes/db_schema_check.php';

if (!isset($_SESSION['collegeadmin_id'])) {
    header("Location: ../auth/collegeadmin_login.php");
    exit;
}

$college_id = $_SESSION['college_id'];
$admin_name = $_SESSION['collegeadmin_name'];

// Get college details
$collegeStmt = $conn->prepare("SELECT name FROM colleges WHERE id = ?");
$collegeStmt->execute([$college_id]);
$college = $collegeStmt->fetch(PDO::FETCH_ASSOC);

// Get faculty statistics
$statsStmt = $conn->prepare("
    SELECT 
        COUNT(*) as total_faculty,
        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_faculty,
        COUNT(CASE WHEN status = 'inactive' THEN 1 END) as inactive_faculty,
        COUNT(DISTINCT department) as total_departments
    FROM faculties 
    WHERE college_id = ?
");
$statsStmt->execute([$college_id]);
$stats = $statsStmt->fetch(PDO::FETCH_ASSOC);

// Get departments for filter
$deptStmt = $conn->prepare("SELECT DISTINCT department FROM faculties WHERE college_id = ? ORDER BY department ASC");
$deptStmt->execute([$college_id]);
$departments = $deptStmt->fetchAll(PDO::FETCH_COLUMN);

// Handle export request
if (isset($_GET['export']) && $_GET['export'] === 'csv') {
    // Build query based on filters
    $whereConditions = ["college_id = ?"];
    $params = [$college_id];
    
    // Apply filters
    if (!empty($_GET['department']) && $_GET['department'] !== 'all') {
        $whereConditions[] = "department = ?";
        $params[] = $_GET['department'];
    }
    
    if (!empty($_GET['status']) && $_GET['status'] !== 'all') {
        $whereConditions[] = "status = ?";
        $params[] = $_GET['status'];
    }
    
    if (!empty($_GET['search'])) {
        $whereConditions[] = "(name LIKE ? OR email LIKE ? OR department LIKE ?)";
        $searchTerm = '%' . $_GET['search'] . '%';
        $params[] = $searchTerm;
        $params[] = $searchTerm;
        $params[] = $searchTerm;
    }
    
    $whereClause = implode(' AND ', $whereConditions);
    
    // Select fields to export
    $fields = $_GET['fields'] ?? ['name', 'email', 'department', 'position', 'phone', 'status'];
    $selectedFields = array_intersect($fields, ['name', 'email', 'department', 'position', 'phone', 'bio', 'status', 'created_at']);
    
    if (empty($selectedFields)) {
        $selectedFields = ['name', 'email', 'department'];
    }
    
    $sql = "SELECT " . implode(', ', $selectedFields) . " FROM faculties WHERE $whereClause ORDER BY name ASC";
    $stmt = $conn->prepare($sql);
    $stmt->execute($params);
    $faculty_data = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Generate CSV
    $filename = 'faculty_export_' . date('Y-m-d_H-i-s') . '.csv';
    
    header('Content-Type: text/csv');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
    header('Expires: 0');
    
    $output = fopen('php://output', 'w');
    
    // Write header row
    $headers = [];
    foreach ($selectedFields as $field) {
        $headers[] = ucwords(str_replace('_', ' ', $field));
    }
    fputcsv($output, $headers);
    
    // Write data rows
    foreach ($faculty_data as $row) {
        $csvRow = [];
        foreach ($selectedFields as $field) {
            $value = $row[$field] ?? '';
            
            // Format specific fields
            if ($field === 'created_at' && !empty($value)) {
                $value = date('Y-m-d H:i:s', strtotime($value));
            } elseif ($field === 'status') {
                $value = ucfirst($value);
            }
            
            $csvRow[] = $value;
        }
        fputcsv($output, $csvRow);
    }
    
    fclose($output);
    exit;
}

// Set role explicitly for the sidebar
$role = 'collegeadmin';
$user_id = $_SESSION['collegeadmin_id'];
$user_name = $_SESSION['collegeadmin_name'];
$current_page = basename($_SERVER['PHP_SELF']);

// Set dashboard URL for header
$dashboard_url = 'collegeadmin_dashboard.php';

// Include the header
require_once '../includes/header.php';
?>

    <!-- Main Content -->
    <main class="flex-grow py-6 px-4">
        <div class="max-w-7xl mx-auto">
            <!-- Page Header -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8">
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="bg-gradient-to-br from-purple-500 to-purple-600 p-3 rounded-xl shadow-lg">
                            <i class="fas fa-file-export text-white text-xl"></i>
                        </div>
                        <div>
                            <h1 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                                Export Faculty Data
                            </h1>
                            <p class="text-gray-600 dark:text-gray-400 mt-1">
                                Export faculty data from <span class="font-medium text-purple-600 dark:text-purple-400"><?= htmlspecialchars($college['name'] ?? 'your college') ?></span>
                                <span class="mx-2">•</span>
                                <span class="text-sm"><?= date('l, F j, Y') ?></span>
                            </p>
                        </div>
                    </div>
                    <div class="mt-6 lg:mt-0 flex flex-wrap gap-3">
                        <a href="list_faculty.php" class="btn btn-outline flex items-center text-sm hover:shadow-md transition-all duration-200">
                            <i class="fas fa-arrow-left mr-2"></i> Back to Faculty List
                        </a>
                        <a href="import_faculty.php" class="btn btn-outline flex items-center text-sm hover:shadow-md transition-all duration-200">
                            <i class="fas fa-file-import mr-2"></i> Import Faculty
                        </a>
                    </div>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-users text-blue-600 dark:text-blue-400 text-xl"></i>
                        </div>
                        <div>
                            <p class="text-2xl font-bold text-gray-900 dark:text-white"><?= $stats['total_faculty'] ?></p>
                            <p class="text-sm text-gray-600 dark:text-gray-400">Total Faculty</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-user-check text-green-600 dark:text-green-400 text-xl"></i>
                        </div>
                        <div>
                            <p class="text-2xl font-bold text-gray-900 dark:text-white"><?= $stats['active_faculty'] ?></p>
                            <p class="text-sm text-gray-600 dark:text-gray-400">Active Faculty</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-user-times text-orange-600 dark:text-orange-400 text-xl"></i>
                        </div>
                        <div>
                            <p class="text-2xl font-bold text-gray-900 dark:text-white"><?= $stats['inactive_faculty'] ?></p>
                            <p class="text-sm text-gray-600 dark:text-gray-400">Inactive Faculty</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-building text-purple-600 dark:text-purple-400 text-xl"></i>
                        </div>
                        <div>
                            <p class="text-2xl font-bold text-gray-900 dark:text-white"><?= $stats['total_departments'] ?></p>
                            <p class="text-sm text-gray-600 dark:text-gray-400">Departments</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters Applied Notice -->
            <?php if (!empty($_GET['search']) || (!empty($_GET['department']) && $_GET['department'] !== 'all') || (!empty($_GET['status']) && $_GET['status'] !== 'all')): ?>
                <div class="bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-800 rounded-xl p-4 mb-6">
                    <div class="flex items-center">
                        <i class="fas fa-info-circle text-blue-500 mr-3"></i>
                        <div>
                            <h4 class="font-medium text-blue-800 dark:text-blue-300">Filters Applied from Faculty List</h4>
                            <p class="text-sm text-blue-700 dark:text-blue-400 mt-1">
                                The export form has been pre-filled with your current search and filter settings.
                                <?php if (!empty($_GET['search'])): ?>
                                    <span class="font-medium">Search: "<?= htmlspecialchars($_GET['search']) ?>"</span>
                                <?php endif; ?>
                                <?php if (!empty($_GET['department']) && $_GET['department'] !== 'all'): ?>
                                    <span class="font-medium">Department: <?= htmlspecialchars($_GET['department']) ?></span>
                                <?php endif; ?>
                                <?php if (!empty($_GET['status']) && $_GET['status'] !== 'all'): ?>
                                    <span class="font-medium">Status: <?= ucfirst($_GET['status']) ?></span>
                                <?php endif; ?>
                            </p>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Main Content Grid -->
            <div class="grid grid-cols-1 xl:grid-cols-4 gap-8">
                <!-- Left Column - Export Form (3/4 width) -->
                <div class="xl:col-span-3">
                    <!-- Export Configuration -->
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 mb-8">
                        <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
                            <h2 class="text-lg font-semibold text-gray-800 dark:text-white flex items-center">
                                <i class="fas fa-cog mr-3 text-blue-600"></i> Export Configuration
                            </h2>
                            <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">Configure your export settings and filters</p>
                        </div>
                        
                        <form id="exportForm" method="GET" class="p-6 space-y-6">
                            <input type="hidden" name="export" value="csv">
                            
                            <!-- Filters Section -->
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <div class="form-group">
                                    <label for="department" class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                                        Department Filter
                                    </label>
                                    <select id="department" name="department" class="form-select w-full rounded-lg border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                        <option value="all">All Departments</option>
                                        <?php foreach ($departments as $dept): ?>
                                            <option value="<?= htmlspecialchars($dept) ?>" <?= (isset($_GET['department']) && $_GET['department'] === $dept) ? 'selected' : '' ?>><?= htmlspecialchars($dept) ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="status" class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                                        Status Filter
                                    </label>
                                    <select id="status" name="status" class="form-select w-full rounded-lg border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                        <option value="all" <?= (!isset($_GET['status']) || $_GET['status'] === 'all') ? 'selected' : '' ?>>All Status</option>
                                        <option value="active" <?= (isset($_GET['status']) && $_GET['status'] === 'active') ? 'selected' : '' ?>>Active Only</option>
                                        <option value="inactive" <?= (isset($_GET['status']) && $_GET['status'] === 'inactive') ? 'selected' : '' ?>>Inactive Only</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="search" class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                                        Search Filter
                                    </label>
                                    <input type="text" id="search" name="search" class="form-input w-full rounded-lg border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="Search by name, email, or department" value="<?= htmlspecialchars($_GET['search'] ?? '') ?>">
                                </div>
                            </div>

                            <!-- Field Selection -->
                            <div class="form-group">
                                <label class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                                    Select Fields to Export
                                </label>
                                <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                                    <label class="flex items-center">
                                        <input type="checkbox" name="fields[]" value="name" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" checked>
                                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Full Name</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="fields[]" value="email" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" checked>
                                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Email</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="fields[]" value="department" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" checked>
                                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Department</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="fields[]" value="position" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" checked>
                                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Position</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="fields[]" value="phone" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Phone</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="fields[]" value="bio" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Bio</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="fields[]" value="status" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" checked>
                                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Status</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="fields[]" value="created_at" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Created Date</span>
                                    </label>
                                </div>
                            </div>

                            <!-- Export Button -->
                            <div class="flex justify-end pt-4 border-t border-gray-200 dark:border-gray-700">
                                <button type="submit" class="btn btn-primary shadow-lg hover:shadow-xl transition-all duration-200">
                                    <i class="fas fa-download mr-2"></i> Export to CSV
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Export Options -->
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 mb-8">
                        <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
                            <h2 class="text-lg font-semibold text-gray-800 dark:text-white flex items-center">
                                <i class="fas fa-info-circle mr-3 text-purple-600"></i> Export Information
                            </h2>
                            <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">Learn about export formats and features</p>
                        </div>
                        <div class="p-6">
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <div class="bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg p-4">
                                    <h4 class="font-medium text-purple-800 dark:text-purple-300 mb-2 flex items-center">
                                        <i class="fas fa-file-csv mr-2"></i> CSV Format
                                    </h4>
                                    <p class="text-sm text-purple-700 dark:text-purple-400 mb-3">
                                        Export data in comma-separated values format, compatible with Excel and other spreadsheet applications.
                                    </p>
                                    <ul class="text-xs text-purple-600 dark:text-purple-400 space-y-1">
                                        <li>• Compatible with Excel, Google Sheets</li>
                                        <li>• Lightweight and fast download</li>
                                        <li>• Easy to import into other systems</li>
                                    </ul>
                                </div>

                                <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                                    <h4 class="font-medium text-blue-800 dark:text-blue-300 mb-2 flex items-center">
                                        <i class="fas fa-filter mr-2"></i> Filter Options
                                    </h4>
                                    <ul class="text-sm text-blue-700 dark:text-blue-400 space-y-1">
                                        <li>• Filter by department</li>
                                        <li>• Filter by status (active/inactive)</li>
                                        <li>• Search by name, email, or department</li>
                                        <li>• Select specific fields to export</li>
                                    </ul>
                                </div>

                                <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
                                    <h4 class="font-medium text-green-800 dark:text-green-300 mb-2 flex items-center">
                                        <i class="fas fa-shield-alt mr-2"></i> Data Security
                                    </h4>
                                    <ul class="text-sm text-green-700 dark:text-green-400 space-y-1">
                                        <li>• Passwords are never exported</li>
                                        <li>• Only authorized college data</li>
                                        <li>• Secure download process</li>
                                        <li>• No data stored on server</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Right Column - Sidebar (1/4 width) -->
                <div class="xl:col-span-1">
                    <div class="space-y-6">
                        <!-- Quick Actions -->
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                            <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
                                <h3 class="text-lg font-semibold text-gray-800 dark:text-white flex items-center">
                                    <i class="fas fa-bolt mr-3 text-blue-600"></i> Quick Actions
                                </h3>
                            </div>
                            <div class="p-6 space-y-3">
                                <a href="list_faculty.php" class="flex items-center p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                                    <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-list text-blue-600 dark:text-blue-400"></i>
                                    </div>
                                    <div>
                                        <p class="font-medium text-gray-900 dark:text-white">All Faculty</p>
                                        <p class="text-sm text-gray-500 dark:text-gray-400">View faculty list</p>
                                    </div>
                                </a>

                                <a href="add_faculty.php" class="flex items-center p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                                    <div class="w-10 h-10 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-user-plus text-green-600 dark:text-green-400"></i>
                                    </div>
                                    <div>
                                        <p class="font-medium text-gray-900 dark:text-white">Add Faculty</p>
                                        <p class="text-sm text-gray-500 dark:text-gray-400">Add new faculty member</p>
                                    </div>
                                </a>

                                <a href="import_faculty.php" class="flex items-center p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                                    <div class="w-10 h-10 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-file-import text-orange-600 dark:text-orange-400"></i>
                                    </div>
                                    <div>
                                        <p class="font-medium text-gray-900 dark:text-white">Import Faculty</p>
                                        <p class="text-sm text-gray-500 dark:text-gray-400">Bulk import faculty</p>
                                    </div>
                                </a>
                            </div>
                        </div>

                        <!-- Export History -->
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                            <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
                                <h3 class="text-lg font-semibold text-gray-800 dark:text-white flex items-center">
                                    <i class="fas fa-history mr-3 text-gray-600"></i> Quick Export
                                </h3>
                            </div>
                            <div class="p-6">
                                <div class="space-y-3">
                                    <a href="export_faculty.php?export=csv&department=all&status=active&fields[]=name&fields[]=email&fields[]=department&fields[]=position&fields[]=status"
                                       class="block p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
                                        <div class="flex items-center">
                                            <i class="fas fa-download text-green-600 mr-3"></i>
                                            <div>
                                                <p class="font-medium text-gray-900 dark:text-white text-sm">Active Faculty</p>
                                                <p class="text-xs text-gray-500 dark:text-gray-400">Basic info for active faculty</p>
                                            </div>
                                        </div>
                                    </a>

                                    <a href="export_faculty.php?export=csv&department=all&status=all&fields[]=name&fields[]=email&fields[]=department&fields[]=position&fields[]=phone&fields[]=status&fields[]=created_at"
                                       class="block p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
                                        <div class="flex items-center">
                                            <i class="fas fa-download text-blue-600 mr-3"></i>
                                            <div>
                                                <p class="font-medium text-gray-900 dark:text-white text-sm">Complete Export</p>
                                                <p class="text-xs text-gray-500 dark:text-gray-400">All faculty with full details</p>
                                            </div>
                                        </div>
                                    </a>

                                    <a href="export_faculty.php?export=csv&department=all&status=all&fields[]=name&fields[]=email&fields[]=department"
                                       class="block p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
                                        <div class="flex items-center">
                                            <i class="fas fa-download text-purple-600 mr-3"></i>
                                            <div>
                                                <p class="font-medium text-gray-900 dark:text-white text-sm">Basic Export</p>
                                                <p class="text-xs text-gray-500 dark:text-gray-400">Name, email, department only</p>
                                            </div>
                                        </div>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Form validation
        document.getElementById('exportForm').addEventListener('submit', function(e) {
            const checkboxes = document.querySelectorAll('input[name="fields[]"]:checked');

            if (checkboxes.length === 0) {
                e.preventDefault();
                alert('Please select at least one field to export.');
                return false;
            }
        });

        // Select/Deselect all fields
        function toggleAllFields(checked) {
            const checkboxes = document.querySelectorAll('input[name="fields[]"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = checked;
            });
        }

        // Add select all/none buttons
        document.addEventListener('DOMContentLoaded', function() {
            const fieldsContainer = document.querySelector('.grid.grid-cols-2.md\\:grid-cols-4.gap-3');
            if (fieldsContainer) {
                const buttonContainer = document.createElement('div');
                buttonContainer.className = 'col-span-full flex space-x-2 mb-2';
                buttonContainer.innerHTML = `
                    <button type="button" onclick="toggleAllFields(true)" class="text-xs text-blue-600 hover:text-blue-800">Select All</button>
                    <span class="text-gray-400">|</span>
                    <button type="button" onclick="toggleAllFields(false)" class="text-xs text-blue-600 hover:text-blue-800">Select None</button>
                `;
                fieldsContainer.insertBefore(buttonContainer, fieldsContainer.firstChild);
            }
        });

        // Theme toggle functionality
        const themeToggleBtn = document.getElementById('themeToggle');

        function getThemePreference() {
            return localStorage.getItem('color-theme') || 'light';
        }

        function setThemePreference(theme) {
            localStorage.setItem('color-theme', theme);
            if (theme === 'dark') {
                document.documentElement.classList.add('dark');
            } else {
                document.documentElement.classList.remove('dark');
            }
        }

        // Set initial theme
        setThemePreference(getThemePreference());

        // Toggle theme when button is clicked
        if (themeToggleBtn) {
            themeToggleBtn.addEventListener('click', function() {
                const currentTheme = getThemePreference();
                const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
                setThemePreference(newTheme);
            });
        }

        // Notifications button
        document.getElementById('notificationsBtn')?.addEventListener('click', function() {
            alert('Notifications feature coming soon!');
        });
    </script>

<?php
// Include the footer
require_once '../includes/footer.php';
?>
