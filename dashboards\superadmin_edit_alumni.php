<?php
session_start();
require_once '../includes/db.php';
require_once '../includes/db_schema_check.php';
require_once '../notify/send_email.php';

// Ensure only super admin can access this page
if (!isset($_SESSION['superadmin_id'])) {
    header("Location: ../auth/superadmin_login.php");
    exit;
}

$superadmin_id = $_SESSION['superadmin_id'];
$superadmin_name = $_SESSION['superadmin_name'] ?? 'Super Admin';
$id = $_GET['id'] ?? null;
$email_sent = false;

if (!$id) {
    $_SESSION['flash_message'] = "Invalid alumni ID.";
    $_SESSION['flash_message_type'] = "error";
    header("Location: superadmin_list_alumni.php");
    exit;
}

// Get colleges for dropdown
$collegesStmt = $conn->prepare("SELECT id, name FROM colleges ORDER BY name ASC");
$collegesStmt->execute();
$colleges = $collegesStmt->fetchAll(PDO::FETCH_ASSOC);

// Get industries for suggestions
$industryStmt = $conn->prepare("SELECT DISTINCT industry FROM alumni WHERE industry IS NOT NULL AND industry != '' ORDER BY industry ASC");
$industryStmt->execute();
$industries = $industryStmt->fetchAll(PDO::FETCH_COLUMN);

// Get alumni details - no college_id restriction for super admin
$stmt = $conn->prepare("SELECT a.*, c.name as college_name FROM alumni a LEFT JOIN colleges c ON a.college_id = c.id WHERE a.id = ?");
$stmt->execute([$id]);
$alumni = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$alumni) {
    $_SESSION['flash_message'] = "Alumni not found.";
    $_SESSION['flash_message_type'] = "error";
    header("Location: superadmin_list_alumni.php");
    exit;
}

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $full_name = trim($_POST['full_name']);
    $email = trim($_POST['email']);
    $phone = trim($_POST['phone'] ?? '');
    $location = trim($_POST['location'] ?? '');
    $graduation_year = trim($_POST['graduation_year'] ?? '');
    $degree = trim($_POST['degree'] ?? '');
    $major = trim($_POST['major'] ?? '');
    $company = trim($_POST['company'] ?? '');
    $job_title = trim($_POST['job_title'] ?? '');
    $industry = trim($_POST['industry'] ?? '');
    $experience_years = intval($_POST['experience_years'] ?? 0);
    $linkedin_url = trim($_POST['linkedin_url'] ?? '');
    $website = trim($_POST['website'] ?? '');
    $bio = trim($_POST['bio'] ?? '');
    $skills = trim($_POST['skills'] ?? '');
    $college_id = intval($_POST['college_id']);
    $password = $_POST['password'];
    $verified = isset($_POST['verified']) ? 1 : 0;
    $send_credentials = isset($_POST['send_credentials']) ? true : false;

    if (!$full_name || !$email || !$college_id) {
        $error = "Name, email, and college are required.";
    } else {
        // Check if email already exists for another alumni
        $check = $conn->prepare("SELECT COUNT(*) FROM alumni WHERE email = ? AND id != ?");
        $check->execute([$email, $id]);
        $exists = $check->fetchColumn();
        
        if ($exists) {
            $error = "An alumni with this email already exists.";
        } else {
            try {
                $password_changed = false;
                
                if ($password) {
                    $hashed = password_hash($password, PASSWORD_DEFAULT);
                    $update = $conn->prepare("UPDATE alumni SET 
                        full_name = ?, 
                        email = ?, 
                        phone = ?, 
                        location = ?, 
                        graduation_year = ?, 
                        degree = ?, 
                        major = ?, 
                        company = ?, 
                        job_title = ?, 
                        industry = ?, 
                        experience_years = ?, 
                        linkedin_url = ?, 
                        website = ?, 
                        bio = ?, 
                        skills = ?, 
                        college_id = ?, 
                        password = ?, 
                        verified = ?, 
                        updated_at = NOW() 
                        WHERE id = ?");
                    $update->execute([
                        $full_name, 
                        $email, 
                        $phone, 
                        $location, 
                        $graduation_year, 
                        $degree, 
                        $major, 
                        $company, 
                        $job_title, 
                        $industry, 
                        $experience_years, 
                        $linkedin_url, 
                        $website, 
                        $bio, 
                        $skills, 
                        $college_id, 
                        $hashed, 
                        $verified, 
                        $id
                    ]);
                    $password_changed = true;
                } else {
                    $update = $conn->prepare("UPDATE alumni SET 
                        full_name = ?, 
                        email = ?, 
                        phone = ?, 
                        location = ?, 
                        graduation_year = ?, 
                        degree = ?, 
                        major = ?, 
                        company = ?, 
                        job_title = ?, 
                        industry = ?, 
                        experience_years = ?, 
                        linkedin_url = ?, 
                        website = ?, 
                        bio = ?, 
                        skills = ?, 
                        college_id = ?, 
                        verified = ?, 
                        updated_at = NOW() 
                        WHERE id = ?");
                    $update->execute([
                        $full_name, 
                        $email, 
                        $phone, 
                        $location, 
                        $graduation_year, 
                        $degree, 
                        $major, 
                        $company, 
                        $job_title, 
                        $industry, 
                        $experience_years, 
                        $linkedin_url, 
                        $website, 
                        $bio, 
                        $skills, 
                        $college_id, 
                        $verified, 
                        $id
                    ]);
                }
                
                // Get college name for the email
                $collegeStmt = $conn->prepare("SELECT name FROM colleges WHERE id = ?");
                $collegeStmt->execute([$college_id]);
                $college = $collegeStmt->fetch(PDO::FETCH_ASSOC);
                
                // Send credentials via email if requested
                if ($send_credentials && $password_changed) {
                    $subject = "Your Updated Account Details - " . htmlspecialchars($college['name']);
                    $message = "<p>Dear " . htmlspecialchars($full_name) . ",</p>";
                    $message .= "<p>Your account details have been updated by the system administrator.</p>";
                    $message .= "<p><strong>Email:</strong> " . htmlspecialchars($email) . "<br>";
                    $message .= "<strong>Password:</strong> " . htmlspecialchars($password) . "</p>";
                    $message .= "<p>You can login at: <a href='https://" . $_SERVER['HTTP_HOST'] . "/auth/alumni_login.php'>Alumni Login</a></p>";
                    $message .= "<p>Please keep your credentials secure.</p>";
                    $message .= "<p>Regards,<br>Administration Team<br>" . htmlspecialchars($college['name']) . "</p>";
                    
                    $email_sent = send_email($email, $subject, $message);
                }
                
                // Upload profile photo if provided
                if (isset($_FILES['profile_photo']) && $_FILES['profile_photo']['error'] == 0) {
                    $allowed = ['jpg', 'jpeg', 'png', 'gif'];
                    $filename = $_FILES['profile_photo']['name'];
                    $ext = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
                    
                    if (in_array($ext, $allowed)) {
                        $new_filename = 'alumni_' . $id . '_' . time() . '.' . $ext;
                        $upload_path = '../uploads/' . $new_filename;
                        
                        if (move_uploaded_file($_FILES['profile_photo']['tmp_name'], $upload_path)) {
                            // Update the profile photo in the database
                            $photoUpdate = $conn->prepare("UPDATE alumni SET profile_photo = ? WHERE id = ?");
                            $photoUpdate->execute([$new_filename, $id]);
                        }
                    }
                }
                
                $success = "Alumni information updated successfully.";
                if ($email_sent) {
                    $success .= " Credentials have been sent to the alumni's email.";
                }
                
                // Refresh alumni data
                $stmt = $conn->prepare("SELECT a.*, c.name as college_name FROM alumni a LEFT JOIN colleges c ON a.college_id = c.id WHERE a.id = ?");
                $stmt->execute([$id]);
                $alumni = $stmt->fetch(PDO::FETCH_ASSOC);
                
            } catch (PDOException $e) {
                $error = "Database error: " . $e->getMessage();
            }
        }
    }
}

// Include the header
require_once '../includes/header.php';
?>

<!-- Main Content -->
<div class="container mx-auto px-4 py-8 max-w-6xl">
    <!-- Page Header -->
    <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                <span class="bg-purple-500/10 dark:bg-purple-500/20 p-2 rounded-md mr-3">
                    <i class="fas fa-user-edit text-purple-600 dark:text-purple-400"></i>
                </span>
                Edit Alumni
            </h1>
            <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">Update alumni information and credentials</p>
        </div>
        <div class="mt-4 md:mt-0 flex flex-wrap gap-2">
            <a href="superadmin_view_alumni.php?id=<?= $alumni['id'] ?>" class="btn btn-outline flex items-center">
                <i class="fas fa-arrow-left mr-2"></i> Back to Profile
            </a>
            <a href="superadmin_list_alumni.php" class="btn btn-outline flex items-center">
                <i class="fas fa-list mr-2"></i> All Alumni
            </a>
        </div>
    </div>
    
    <?php if ($error): ?>
    <div class="mb-6 bg-red-50 dark:bg-red-900/30 border-l-4 border-red-500 text-red-700 dark:text-red-400 p-4 flex justify-between items-center" role="alert">
        <div>
            <i class="fas fa-exclamation-circle mr-2"></i>
            <?= $error ?>
        </div>
        <button type="button" class="text-red-700 dark:text-red-400 hover:text-red-900" onclick="this.parentElement.style.display='none';">
            <i class="fas fa-times"></i>
        </button>
    </div>
    <?php endif; ?>
    
    <?php if ($success): ?>
    <div class="mb-6 bg-green-50 dark:bg-green-900/30 border-l-4 border-green-500 text-green-700 dark:text-green-400 p-4 flex justify-between items-center" role="alert">
        <div>
            <i class="fas fa-check-circle mr-2"></i>
            <?= $success ?>
        </div>
        <button type="button" class="text-green-700 dark:text-green-400 hover:text-green-900" onclick="this.parentElement.style.display='none';">
            <i class="fas fa-times"></i>
        </button>
    </div>
    <?php endif; ?>
    
    <!-- Alumni Edit Form -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 overflow-hidden">
        <div class="p-6">
            <form action="" method="post" enctype="multipart/form-data" class="space-y-6">
                <!-- Personal Information -->
                <div class="space-y-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2">Personal Information</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="form-group">
                            <label for="full_name" class="form-label required">Full Name</label>
                            <input type="text" id="full_name" name="full_name" class="form-input" value="<?= htmlspecialchars($alumni['full_name']) ?>" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="email" class="form-label required">Email Address</label>
                            <input type="email" id="email" name="email" class="form-input" value="<?= htmlspecialchars($alumni['email']) ?>" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="phone" class="form-label">Phone Number</label>
                            <input type="text" id="phone" name="phone" class="form-input" value="<?= htmlspecialchars($alumni['phone'] ?? '') ?>">
                        </div>
                        
                        <div class="form-group">
                            <label for="location" class="form-label">Location</label>
                            <input type="text" id="location" name="location" class="form-input" value="<?= htmlspecialchars($alumni['location'] ?? '') ?>">
                        </div>
                        
                        <div class="form-group">
                            <label for="college_id" class="form-label required">College</label>
                            <select id="college_id" name="college_id" class="form-select" required>
                                <option value="">Select College</option>
                                <?php foreach ($colleges as $college): ?>
                                    <option value="<?= $college['id'] ?>" <?= $alumni['college_id'] == $college['id'] ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($college['name']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="profile_photo" class="form-label">Profile Photo</label>
                            <div class="flex items-center space-x-4">
                                <?php if (!empty($alumni['profile_photo']) && file_exists("../uploads/{$alumni['profile_photo']}")): ?>
                                    <img src="../uploads/<?= $alumni['profile_photo'] ?>" alt="Current profile photo" class="w-16 h-16 rounded-full object-cover">
                                <?php else: ?>
                                    <div class="w-16 h-16 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center text-gray-400">
                                        <i class="fas fa-user text-xl"></i>
                                    </div>
                                <?php endif; ?>
                                <div class="flex-1">
                                    <input type="file" id="profile_photo" name="profile_photo" class="form-input" accept="image/*">
                                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Upload a new photo (JPG, PNG, GIF)</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Education Information -->
                <div class="space-y-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2">Education Information</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="form-group">
                            <label for="graduation_year" class="form-label">Graduation Year</label>
                            <input type="text" id="graduation_year" name="graduation_year" class="form-input" value="<?= htmlspecialchars($alumni['graduation_year'] ?? '') ?>">
                        </div>
                        
                        <div class="form-group">
                            <label for="degree" class="form-label">Degree</label>
                            <input type="text" id="degree" name="degree" class="form-input" value="<?= htmlspecialchars($alumni['degree'] ?? '') ?>">
                        </div>
                        
                        <div class="form-group">
                            <label for="major" class="form-label">Major</label>
                            <input type="text" id="major" name="major" class="form-input" value="<?= htmlspecialchars($alumni['major'] ?? '') ?>">
                        </div>
                    </div>
                </div>
                
                <!-- Professional Information -->
                <div class="space-y-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2">Professional Information</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="form-group">
                            <label for="company" class="form-label">Company</label>
                            <input type="text" id="company" name="company" class="form-input" value="<?= htmlspecialchars($alumni['company'] ?? '') ?>">
                        </div>
                        
                        <div class="form-group">
                            <label for="job_title" class="form-label">Job Title</label>
                            <input type="text" id="job_title" name="job_title" class="form-input" value="<?= htmlspecialchars($alumni['job_title'] ?? '') ?>">
                        </div>
                        
                        <div class="form-group">
                            <label for="industry" class="form-label">Industry</label>
                            <input type="text" id="industry" name="industry" class="form-input" value="<?= htmlspecialchars($alumni['industry'] ?? '') ?>" list="industry-list">
                            <datalist id="industry-list">
                                <?php foreach ($industries as $ind): ?>
                                    <option value="<?= htmlspecialchars($ind) ?>">
                                <?php endforeach; ?>
                            </datalist>
                        </div>
                        
                        <div class="form-group">
                            <label for="experience_years" class="form-label">Years of Experience</label>
                            <input type="number" id="experience_years" name="experience_years" class="form-input" value="<?= intval($alumni['experience_years'] ?? 0) ?>" min="0">
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="form-group">
                            <label for="linkedin_url" class="form-label">LinkedIn URL</label>
                            <input type="url" id="linkedin_url" name="linkedin_url" class="form-input" value="<?= htmlspecialchars($alumni['linkedin_url'] ?? '') ?>">
                        </div>
                        
                        <div class="form-group">
                            <label for="website" class="form-label">Personal Website</label>
                            <input type="url" id="website" name="website" class="form-input" value="<?= htmlspecialchars($alumni['website'] ?? '') ?>">
                        </div>
                    </div>
                </div>
                
                <!-- Additional Information -->
                <div class="space-y-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2">Additional Information</h3>
                    
                    <div class="form-group">
                        <label for="bio" class="form-label">Bio</label>
                        <textarea id="bio" name="bio" class="form-textarea" rows="4"><?= htmlspecialchars($alumni['bio'] ?? '') ?></textarea>
                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Brief description about the alumni's background and interests</p>
                    </div>
                    
                    <div class="form-group">
                        <label for="skills" class="form-label">Skills</label>
                        <input type="text" id="skills" name="skills" class="form-input" value="<?= htmlspecialchars($alumni['skills'] ?? '') ?>">
                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Comma-separated list of skills (e.g., Project Management, Data Analysis, Marketing)</p>
                    </div>
                </div>
                
                <!-- Account Settings -->
                <div class="space-y-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2">Account Settings</h3>
                    
                    <div class="form-group">
                        <label for="password" class="form-label">Password</label>
                        <div class="relative">
                            <input type="password" id="password" name="password" class="form-input pr-10" placeholder="Leave blank to keep current password">
                            <button type="button" class="absolute right-2 top-1/2 -translate-y-1/2 text-gray-500 dark:text-gray-400" onclick="togglePassword('password')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Leave blank to keep current password</p>
                    </div>
                    
                    <div class="flex items-center space-x-2">
                        <input type="checkbox" id="send_credentials" name="send_credentials" class="form-checkbox">
                        <label for="send_credentials" class="text-sm text-gray-700 dark:text-gray-300">Send credentials to alumni's email</label>
                    </div>
                    
                    <div class="flex items-center space-x-2">
                        <input type="checkbox" id="verified" name="verified" class="form-checkbox" <?= $alumni['verified'] ? 'checked' : '' ?>>
                        <label for="verified" class="text-sm text-gray-700 dark:text-gray-300">Verified Alumni</label>
                    </div>
                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Verified alumni have full access to all features</p>
                </div>
                
                <div class="flex justify-end space-x-2 pt-4 border-t border-gray-200 dark:border-gray-700">
                    <a href="superadmin_view_alumni.php?id=<?= $alumni['id'] ?>" class="btn btn-outline">Cancel</a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save mr-2"></i> Save Changes
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function togglePassword(id) {
    const input = document.getElementById(id);
    const icon = input.nextElementSibling.querySelector('i');
    
    if (input.type === 'password') {
        input.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        input.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}
</script> 