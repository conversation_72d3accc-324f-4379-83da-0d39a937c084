<?php
// Include session configuration
require_once '../includes/session_config.php';
require_once '../includes/db.php';
require_once '../includes/header.php';

if (!isset($_SESSION['superadmin_id'])) {
    header("Location: ../auth/superadmin_login.php");
    exit;
}

$error = '';
$success = '';

// Fetch colleges for the dropdown
$stmt = $conn->prepare("SELECT id, name FROM colleges ORDER BY name ASC");
$stmt->execute();
$colleges = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get college_id from query parameter if provided
$selected_college_id = isset($_GET['college_id']) ? $_GET['college_id'] : '';

// Fetch existing admins
$adminsQuery = "SELECT ca.*, c.name as college_name 
                FROM college_admins ca 
                JOIN colleges c ON ca.college_id = c.id 
                ORDER BY ca.created_at DESC LIMIT 5";
$stmt = $conn->prepare($adminsQuery);
$stmt->execute();
$recentAdmins = $stmt->fetchAll(PDO::FETCH_ASSOC);

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $college_id = $_POST['college_id'];
    $name = trim($_POST['name']);
    $email = trim($_POST['email']);
    $password = $_POST['password'];
    $role = isset($_POST['role']) ? trim($_POST['role']) : 'admin';
    $send_email = isset($_POST['send_email']) ? true : false;

    if ($college_id && $name && $email && $password) {
        // Check if email already exists
        $check = $conn->prepare("SELECT COUNT(*) FROM college_admins WHERE email = ?");
        $check->execute([$email]);
        $exists = $check->fetchColumn();
        
        if ($exists) {
            $error = "An admin with this email already exists.";
        } else {
        $hashed_password = password_hash($password, PASSWORD_DEFAULT);

            // Check if role column exists
            try {
                $stmt = $conn->prepare("SHOW COLUMNS FROM college_admins LIKE 'role'");
                $stmt->execute();
                $hasRoleColumn = $stmt->rowCount() > 0;
            } catch (PDOException $e) {
                $hasRoleColumn = false;
            }
            
            // Insert admin based on schema
            if ($hasRoleColumn) {
                $sql = "INSERT INTO college_admins (college_id, name, email, password, role) VALUES (?, ?, ?, ?, ?)";
                $stmt = $conn->prepare($sql);
                $stmt->execute([$college_id, $name, $email, $hashed_password, $role]);
            } else {
        $sql = "INSERT INTO college_admins (college_id, name, email, password) VALUES (?, ?, ?, ?)";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$college_id, $name, $email, $hashed_password]);
            }

            $success = "College admin assigned successfully.";
        $_SESSION['flash_message'] = "College admin assigned successfully.";
            
            // Send email notification if requested
            if ($send_email) {
                // This is a placeholder - actual email sending would be implemented here
                // require_once '../notify/send_email.php';
                // sendNewAdminEmail($email, $name, $password);
            }
            
            // Clear form fields after successful submission
            $name = '';
            $email = '';
            $password = '';
            // Don't clear college_id to allow multiple admins for same college
        }
    } else {
        $error = "All fields are required to assign an admin.";
    }

    if (empty($error) && !empty($success)) {
    header("Location: superadmin_dashboard.php");
    exit;
    }
}
?>

<!-- Main Content -->
<div class="container mx-auto px-4 py-8 max-w-4xl">
    <!-- Page Header -->
    <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                <span class="bg-blue-500/10 dark:bg-blue-500/20 p-2 rounded-md mr-3">
                    <i class="fas fa-user-shield text-blue-600 dark:text-blue-400"></i>
                </span>
                Assign College Administrator
            </h1>
            <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">Create login credentials for a college administrator</p>
            </div>
        <div class="mt-4 md:mt-0">
            <a href="superadmin_dashboard.php" class="btn btn-outline flex items-center">
                    <i class="fas fa-arrow-left mr-2"></i> Back to Dashboard
                </a>
            </div>
        </div>
    
    <?php if (!empty($error)): ?>
        <div class="mb-6 bg-red-50 dark:bg-red-900/30 border-l-4 border-red-500 text-red-700 dark:text-red-400 p-4 flex justify-between items-center" role="alert">
            <div>
                <i class="fas fa-exclamation-circle mr-2"></i>
                <?= $error ?>
            </div>
            <button type="button" class="text-red-700 dark:text-red-400 hover:text-red-900" onclick="this.parentElement.style.display='none';">
                <i class="fas fa-times"></i>
            </button>
                </div>
            <?php endif; ?>
            
    <?php if (!empty($success)): ?>
        <div class="mb-6 bg-green-50 dark:bg-green-900/30 border-l-4 border-green-500 text-green-700 dark:text-green-400 p-4 flex justify-between items-center" role="alert">
            <div>
                <i class="fas fa-check-circle mr-2"></i>
                <?= $success ?>
            </div>
            <button type="button" class="text-green-700 dark:text-green-400 hover:text-green-900" onclick="this.parentElement.style.display='none';">
                <i class="fas fa-times"></i>
            </button>
                </div>
            <?php endif; ?>
            
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Admin Form -->
        <div class="lg:col-span-2">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 transition-all duration-300 hover:shadow-lg">
                <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
                    <h2 class="font-semibold text-gray-800 dark:text-white flex items-center">
                        Admin Information
                    </h2>
                </div>
                
                <div class="p-6">
                    <form method="POST" action="" class="space-y-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="form-group md:col-span-2">
                                <label for="college_id" class="form-label required">Select College</label>
                                <select id="college_id" name="college_id" class="form-input" required>
                                    <option value="">-- Select a college --</option>
                                    <?php foreach ($colleges as $college): ?>
                                        <option value="<?= $college['id'] ?>" <?= ($selected_college_id == $college['id']) ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($college['name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <?php if (count($colleges) === 0): ?>
                                    <p class="text-sm text-red-500 mt-1">No colleges available. <a href="create_college.php" class="font-medium text-primary hover:underline">Create a college</a> first.</p>
                                <?php endif; ?>
                            </div>
                            
                            <div class="form-group">
                                <label for="name" class="form-label required">Admin Name</label>
                                <input type="text" id="name" name="name" class="form-input" placeholder="Full name" value="<?= htmlspecialchars($name ?? '') ?>" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="email" class="form-label required">Email Address</label>
                                <input type="email" id="email" name="email" class="form-input" placeholder="<EMAIL>" value="<?= htmlspecialchars($email ?? '') ?>" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="password" class="form-label required">Password</label>
                                <div class="relative">
                                    <input type="password" id="password" name="password" class="form-input pr-10" placeholder="Create a strong password" value="<?= htmlspecialchars($password ?? '') ?>" required>
                                    <button type="button" id="togglePassword" class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-600 dark:text-gray-400">
                                        <i class="fas fa-eye"></i>
                                        </button>
                                </div>
                                <div class="flex items-center mt-1">
                                    <button type="button" id="generatePassword" class="text-xs text-primary hover:underline">Generate secure password</button>
                                    <span class="mx-2 text-xs text-gray-400">|</span>
                                    <span id="passwordStrength" class="text-xs"></span>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label for="role" class="form-label">Role Type</label>
                                <select id="role" name="role" class="form-input">
                                    <option value="admin">Standard Admin</option>
                                    <option value="super_admin">Super Admin (All Privileges)</option>
                                    <option value="limited">Limited Admin</option>
                                </select>
                            </div>
                            
                            <div class="form-group md:col-span-2">
                                <div class="flex items-start">
                                    <div class="flex items-center h-5">
                                        <input id="send_email" name="send_email" type="checkbox" class="h-4 w-4 text-primary border-gray-300 rounded focus:ring-primary" checked>
                                    </div>
                                    <div class="ml-3 text-sm">
                                        <label for="send_email" class="font-medium text-gray-700 dark:text-gray-300">Send login credentials via email</label>
                                        <p class="text-gray-500 dark:text-gray-400">The admin will receive an email with their login details and a link to reset their password</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                            
                        <div class="border-t border-gray-200 dark:border-gray-700 pt-6 flex items-center justify-between">
                            <button type="button" class="btn btn-outline" onclick="window.location.href='superadmin_dashboard.php'">Cancel</button>
                            
                            <div class="flex space-x-2">
                                <button type="reset" class="btn btn-secondary">
                                    <i class="fas fa-undo mr-2"></i> Reset
                                </button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-user-plus mr-2"></i> Assign Admin
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Recent Admins Sidebar -->
        <div>
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 transition-all duration-300 hover:shadow-lg sticky top-24">
                <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4 flex justify-between items-center">
                    <h2 class="font-semibold text-gray-800 dark:text-white">Recent Admins</h2>
                    <a href="list_admins.php" class="text-sm text-primary hover:underline flex items-center">
                        View All <i class="fas fa-arrow-right ml-1 text-xs"></i>
                    </a>
                </div>
                <div class="p-4">
                    <?php if (count($recentAdmins) > 0): ?>
                        <div class="space-y-3">
                            <?php foreach ($recentAdmins as $admin): ?>
                                <div class="p-3 border border-gray-200 dark:border-gray-700 rounded-md hover:bg-gray-50 dark:hover:bg-gray-750 transition-colors">
                                    <div class="flex items-start space-x-3">
                                        <div class="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center text-blue-600 dark:text-blue-400 flex-shrink-0">
                                            <i class="fas fa-user-shield"></i>
                                        </div>
                                        <div class="flex-grow">
                                            <p class="font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($admin['name']) ?></p>
                                            <p class="text-xs text-gray-500 dark:text-gray-400"><?= htmlspecialchars($admin['email']) ?></p>
                                            <p class="text-xs font-medium text-primary mt-1"><?= htmlspecialchars($admin['college_name']) ?></p>
                                        </div>
                                        <a href="edit_collegeadmin.php?id=<?= $admin['id'] ?>" class="text-gray-500 hover:text-primary" title="Edit Admin">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-6">
                            <div class="w-12 h-12 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center text-gray-400 dark:text-gray-500 mx-auto mb-3">
                                <i class="fas fa-user-slash text-lg"></i>
                            </div>
                            <p class="text-gray-500 dark:text-gray-400">No admins assigned yet</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

    <script>
document.addEventListener('DOMContentLoaded', function() {
    // Password toggle visibility
    const togglePassword = document.getElementById('togglePassword');
    const passwordField = document.getElementById('password');
    
    togglePassword.addEventListener('click', function() {
        const type = passwordField.getAttribute('type') === 'password' ? 'text' : 'password';
        passwordField.setAttribute('type', type);
        
        // Toggle eye icon
        this.querySelector('i').classList.toggle('fa-eye');
        this.querySelector('i').classList.toggle('fa-eye-slash');
        });
        
    // Generate secure password
    const generatePassword = document.getElementById('generatePassword');
    
    generatePassword.addEventListener('click', function() {
            const length = 12;
        const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_-+=";
            let password = "";
            
            for (let i = 0; i < length; i++) {
            password += charset.charAt(Math.floor(Math.random() * charset.length));
            }
            
        passwordField.value = password;
        passwordField.setAttribute('type', 'text');
        togglePassword.querySelector('i').classList.remove('fa-eye');
        togglePassword.querySelector('i').classList.add('fa-eye-slash');
        
        // Trigger password strength check
        checkPasswordStrength(password);
        });
        
    // Check password strength
    const passwordStrength = document.getElementById('passwordStrength');
    
    passwordField.addEventListener('input', function() {
        checkPasswordStrength(this.value);
    });
    
    function checkPasswordStrength(password) {
        // Reset strength indicator
        passwordStrength.className = 'text-xs';
        
        // No password
        if (!password) {
            passwordStrength.textContent = '';
            return;
        }
        
        // Calculate strength
        let strength = 0;
        
        // Length check
        if (password.length >= 8) strength += 1;
        if (password.length >= 12) strength += 1;
        
        // Character type check
        if (password.match(/[a-z]+/)) strength += 1;
        if (password.match(/[A-Z]+/)) strength += 1;
        if (password.match(/[0-9]+/)) strength += 1;
        if (password.match(/[^A-Za-z0-9]+/)) strength += 1;
        
        // Display result
        if (strength < 3) {
            passwordStrength.textContent = 'Weak password';
            passwordStrength.classList.add('text-red-500');
        } else if (strength < 5) {
            passwordStrength.textContent = 'Moderate password';
            passwordStrength.classList.add('text-yellow-500');
        } else {
            passwordStrength.textContent = 'Strong password';
            passwordStrength.classList.add('text-green-500');
        }
    }
    
    // Check initial password strength
    if (passwordField.value) {
        checkPasswordStrength(passwordField.value);
            }
        });
    </script>
