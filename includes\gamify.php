<?php
function updateStudentXP(PDO $conn, int $student_id, int $delta = 10): void {
    $stmt = $conn->prepare("SELECT * FROM user_xp WHERE user_id = ? AND role = 'student'");
    $stmt->execute([$student_id]);
    $record = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($record) {
        $new_xp = $record['xp'] + $delta;
        $new_level = calculateLevel($new_xp);

        $update = $conn->prepare("UPDATE user_xp SET xp = ?, level = ?, last_updated = NOW() WHERE user_id = ? AND role = 'student'");
        $update->execute([$new_xp, $new_level, $student_id]);
    } else {
        $new_xp = $delta;
        $new_level = calculateLevel($new_xp);

        $insert = $conn->prepare("INSERT INTO user_xp (user_id, role, xp, level) VALUES (?, 'student', ?, ?)");
        $insert->execute([$student_id, $new_xp, $new_level]);
    }

    checkAndAwardBadges($conn, $student_id, $new_xp); // 🎖️ Award badges if applicable
}

function checkAndAwardBadges(PDO $conn, int $student_id, int $xp): void {
    $badges = [
        ['id' => 1, 'threshold' => 50],   // Starter
        ['id' => 2, 'threshold' => 100],  // Explorer
        ['id' => 3, 'threshold' => 250],  // Achiever
        ['id' => 4, 'threshold' => 500],  // Master
    ];

    foreach ($badges as $badge) {
        if ($xp >= $badge['threshold']) {
            $stmt = $conn->prepare("SELECT COUNT(*) FROM user_badges WHERE user_id = ? AND badge_id = ?");
            $stmt->execute([$student_id, $badge['id']]);
            if ($stmt->fetchColumn() == 0) {
                $assign = $conn->prepare("INSERT INTO user_badges (user_id, badge_id) VALUES (?, ?)");
                $assign->execute([$student_id, $badge['id']]);
            }
        }
    }
}

function calculateLevel(int $xp): int {
    // Level increases every 100 XP
    return floor($xp / 100) + 1;
}

function getStudentXP(PDO $conn, int $student_id): array {
    $stmt = $conn->prepare("SELECT xp, level FROM user_xp WHERE user_id = ? AND role = 'student'");
    $stmt->execute([$student_id]);
    $row = $stmt->fetch(PDO::FETCH_ASSOC);

    return $row ?: ['xp' => 0, 'level' => 1];
}

function getStudentBadges(PDO $conn, int $student_id): array {
    $stmt = $conn->prepare("SELECT b.name, b.icon, b.description FROM user_badges ub JOIN badges b ON ub.badge_id = b.id WHERE ub.user_id = ?");
    $stmt->execute([$student_id]);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function awardXPForMentorshipRequest(PDO $conn, int $student_id): void {
    updateStudentXP($conn, $student_id, 5); // +5 XP
}

function awardXPForAlumniRating(PDO $conn, int $student_id): void {
    updateStudentXP($conn, $student_id, 10); // +10 XP
}

function awardXPForEventParticipation(PDO $conn, int $student_id): void {
    updateStudentXP($conn, $student_id, 30); // +30 XP
}

function awardXPForResourceDownload(PDO $conn, int $student_id): void {
    updateStudentXP($conn, $student_id, 2); // +2 XP
}

function awardXPForProfileCompletion(PDO $conn, int $student_id): void {
    // Check if already rewarded
    $check = $conn->prepare("SELECT COUNT(*) FROM user_xp WHERE user_id = ? AND role = 'student' AND xp >= 20");
    $check->execute([$student_id]);
    $already_rewarded = $check->fetchColumn();

    if (!$already_rewarded) {
        // Check if required profile fields are filled
        $stmt = $conn->prepare("SELECT bio, goals, skills, profile_photo FROM students WHERE id = ?");
        $stmt->execute([$student_id]);
        $student = $stmt->fetch(PDO::FETCH_ASSOC);

        if (
            !empty($student['bio']) &&
            !empty($student['goals']) &&
            !empty($student['skills']) &&
            !empty($student['profile_photo']) &&
            file_exists("../uploads/" . $student['profile_photo'])
        ) {
            updateStudentXP($conn, $student_id, 20); // 🎉 Award 20 XP
        }
    }
}

?>