<?php
require_once '../includes/db.php';
require_once '../includes/header.php';

session_start();

$sender_id = null;
$receiver_id = null;
$sender_role = null;

// Validate session role
if (isset($_SESSION['student_id'])) {
    $sender_id = $_SESSION['student_id'];
    $sender_role = 'student';
    $receiver_id = $_GET['alumni_id'] ?? null;
} elseif (isset($_SESSION['alumni_id'])) {
    $sender_id = $_SESSION['alumni_id'];
    $sender_role = 'alumni';
    $receiver_id = $_GET['student_id'] ?? null;
} else {
    header("Location: ../auth/logout.php");
    exit;
}

if (!$receiver_id) {
    echo "<div class='alert alert-danger'>Invalid chat participant.</div>";
    exit;
}
?>

<div class="container mt-5">
    <h4>💬 Mentorship Chat</h4>
    <div id="chatBox" class="border p-3 mb-3" style="height: 400px; overflow-y: scroll; background: #f9f9f9;">
        <!-- Messages will be loaded here -->
    </div>

    <form id="messageForm" class="d-flex">
        <input type="hidden" id="receiverId" value="<?= htmlspecialchars($receiver_id) ?>">
        <input type="hidden" id="senderRole" value="<?= htmlspecialchars($sender_role) ?>">
        <input type="text" id="messageInput" class="form-control me-2" placeholder="Type your message..." required>
        <button class="btn btn-primary">Send</button>
        
    </form>
</div>

<!-- Profile Modal -->
<div class="modal fade" id="profileModal" tabindex="-1" aria-labelledby="profileModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="profileModalLabel">User  Profile</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="profileContent">
                <!-- Profile content will be loaded here -->
            </div>
        </div>
    </div>
</div>


<script>
function loadMessages() {
    const xhr = new XMLHttpRequest();
    xhr.open("POST", "fetch_messages.php", true);
    xhr.setRequestHeader("Content-type", "application/x-www-form-urlencoded");

    const senderId = <?= json_encode($sender_id) ?>;
    const senderRole = document.getElementById("senderRole").value;
    const receiverId = document.getElementById("receiverId").value;

    xhr.onload = function() {
        document.getElementById("chatBox").innerHTML = this.responseText;
        document.getElementById("chatBox").scrollTop = chatBox.scrollHeight;
    };

    xhr.send(`sender_id=${senderId}&receiver_id=${receiverId}&sender_role=${senderRole}`);
}

document.getElementById("messageForm").addEventListener("submit", function(e) {
    e.preventDefault();

    const message = document.getElementById("messageInput").value;
    const senderId = <?= json_encode($sender_id) ?>;
    const senderRole = document.getElementById("senderRole").value;
    const receiverId = document.getElementById("receiverId").value;

    const xhr = new XMLHttpRequest();
    xhr.open("POST", "send_message.php", true);
    xhr.setRequestHeader("Content-type", "application/x-www-form-urlencoded");

    xhr.onload = function() {
        document.getElementById("messageInput").value = "";
        loadMessages();
    };

    xhr.send(`sender_id=${senderId}&receiver_id=${receiverId}&message=${encodeURIComponent(message)}&sender_role=${senderRole}`);
});

setInterval(loadMessages, 3000); // Refresh chat every 3 seconds
loadMessages();

function openProfile(userId, userRole) {
    const xhr = new XMLHttpRequest();
    xhr.open("POST", "/chat/fetch_profile.php", true);
    xhr.setRequestHeader("Content-type", "application/x-www-form-urlencoded");

    xhr.onload = function() {
        document.getElementById("profileContent").innerHTML = this.responseText;
        const profileModal = new bootstrap.Modal(document.getElementById('profileModal'));
        profileModal.show();
    };

    xhr.send(`id=${userId}&role=${userRole}`);
}
</script>

<?php require_once '../includes/footer.php'; ?>