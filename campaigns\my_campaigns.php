<?php
require_once '../includes/db.php';
require_once '../includes/header.php';
session_start();

if (!isset($_SESSION['faculty_id'])) {
    header("Location: ../auth/faculty_login.php");
    exit;
}

$faculty_id = $_SESSION['faculty_id'];
$college_id = $_SESSION['college_id'];

// Fetch campaigns created by this faculty
$stmt = $conn->prepare("SELECT * FROM deals WHERE creator_role = 'faculty' AND created_by_user_id = ? AND college_id = ? ORDER BY created_at DESC");
$stmt->execute([$faculty_id, $college_id]);
$campaigns = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<div class="container mt-4">
    <h2>📋 My Campaigns</h2>
    <p class="text-muted">Here are the campaigns you’ve created and their current status.</p>

    <?php if (count($campaigns) === 0): ?>
        <div class="alert alert-info">You haven’t created any campaigns yet.</div>
    <?php endif; ?>

    <?php foreach ($campaigns as $campaign): ?>
        <div class="card mb-4 shadow-sm">
            <div class="row g-0">
                <div class="col-md-4">
                    <?php if ($campaign['thumbnail_path'] && file_exists('../' . $campaign['thumbnail_path'])): ?>
                        <img src="../<?= htmlspecialchars($campaign['thumbnail_path']) ?>" class="img-fluid rounded-start" style="max-height: 200px; object-fit: cover;">
                    <?php else: ?>
                        <div class="bg-secondary text-white d-flex align-items-center justify-content-center" style="height: 200px;">
                            No Image
                        </div>
                    <?php endif; ?>
                </div>
                <div class="col-md-8">
                    <div class="card-body">
                        <h5 class="card-title"><?= htmlspecialchars($campaign['title']) ?></h5>
                        <p class="card-text"><strong>Status:</strong> 
                            <span class="badge bg-<?= 
                                $campaign['status'] === 'approved' ? 'success' :
                                ($campaign['status'] === 'rejected' ? 'danger' :
                                ($campaign['status'] === 'closed' ? 'secondary' : 'warning')) ?>">
                                <?= ucfirst($campaign['status']) ?>
                            </span>
                        </p>
                        <p class="card-text"><strong>Category:</strong> <?= ucfirst($campaign['category']) ?> |
                            <strong>Contribution Types:</strong> <?= str_replace(',', ', ', $campaign['contribution_types']) ?></p>
                        <p class="card-text"><?= nl2br(htmlspecialchars($campaign['description'])) ?></p>
                        <p class="card-text"><small class="text-muted">Created: <?= date("d M Y", strtotime($campaign['created_at'])) ?></small></p>

                        <?php if ($campaign['status'] === 'rejected' && !empty($campaign['approval_remark'])): ?>
                            <p class="text-danger small"><strong>Rejection Note:</strong> <?= htmlspecialchars($campaign['approval_remark']) ?></p>
                        <?php endif; ?>

                        <?php if ($campaign['status'] === 'approved'): ?>
                            <a href="view_contributions.php?campaign_id=<?= $campaign['id'] ?>" class="btn btn-outline-info btn-sm mt-2">View Contributions</a>
                        <?php endif; ?>
                        <?php if ($campaign['status'] === 'pending'): ?>
                            <a href="edit_campaign.php?campaign_id=<?= $campaign['id'] ?>" class="btn btn-outline-secondary btn-sm">Edit Campaign</a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    <?php endforeach; ?>
</div>

<?php require_once '../includes/footer.php'; ?>
