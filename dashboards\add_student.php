<?php
require_once '../includes/session_config.php';
require_once '../includes/db.php';
require_once '../includes/db_schema_check.php';
require_once '../notify/send_email.php';

if (!isset($_SESSION['collegeadmin_id'])) {
    header("Location: ../auth/collegeadmin_login.php");
    exit;
}

$college_id = $_SESSION['college_id'];
$admin_name = $_SESSION['collegeadmin_name'] ?? 'Admin';

// Set role explicitly for the sidebar
$role = 'collegeadmin';
$user_id = $_SESSION['collegeadmin_id'];
$user_name = $_SESSION['collegeadmin_name'];
$current_page = basename($_SERVER['PHP_SELF']);

$error = '';
$success = '';
$email_sent = false;

// Get college details
$collegeStmt = $conn->prepare("SELECT name FROM colleges WHERE id = ?");
$collegeStmt->execute([$college_id]);
$college = $collegeStmt->fetch(PDO::FETCH_ASSOC);

// Get departments for suggestions
$deptStmt = $conn->prepare("SELECT DISTINCT department FROM students WHERE college_id = ? ORDER BY department ASC");
$deptStmt->execute([$college_id]);
$departments = $deptStmt->fetchAll(PDO::FETCH_COLUMN);

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $full_name = trim($_POST['full_name']);
    $email = trim($_POST['email']);
    $roll_number = trim($_POST['roll_number']);
    $department = trim($_POST['department']);
    $year = intval($_POST['year']);
    $division = trim($_POST['division'] ?? '');
    $password = $_POST['password'];
    $auto_verify = isset($_POST['auto_verify']) ? 1 : 0;
    $send_credentials = isset($_POST['send_credentials']) ? true : false;

    if (!$full_name || !$email || !$roll_number || !$department || !$year || !$password) {
        $error = "Please fill in all required fields.";
    } else {
        // Check if email already exists
        $check = $conn->prepare("SELECT COUNT(*) FROM students WHERE email = ?");
        $check->execute([$email]);
        $exists = $check->fetchColumn();
        
        if ($exists) {
            $error = "A student with this email already exists.";
        } else {
            // Check if roll number already exists in this college
            $rollCheck = $conn->prepare("SELECT COUNT(*) FROM students WHERE roll_number = ? AND college_id = ?");
            $rollCheck->execute([$roll_number, $college_id]);
            $rollExists = $rollCheck->fetchColumn();
            
            if ($rollExists) {
                $error = "A student with this roll number already exists in your college.";
            } else {
                try {
                    $hashed = password_hash($password, PASSWORD_DEFAULT);
                    $sql = "INSERT INTO students (college_id, full_name, email, roll_number, department, year, division, password, verified, created_at) 
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
                    $stmt = $conn->prepare($sql);
                    $stmt->execute([$college_id, $full_name, $email, $roll_number, $department, $year, $division, $hashed, $auto_verify]);
                    $student_id = $conn->lastInsertId();
                    
                    // Send credentials via email if requested
                    if ($send_credentials) {
                        $subject = "Your Student Account Details - " . htmlspecialchars($college['name']);
                        $message = "<p>Dear " . htmlspecialchars($full_name) . ",</p>";
                        $message .= "<p>Your student account has been created at " . htmlspecialchars($college['name']) . ".</p>";
                        $message .= "<p><strong>Email:</strong> " . htmlspecialchars($email) . "<br>";
                        $message .= "<strong>Password:</strong> " . htmlspecialchars($password) . "</p>";
                        $message .= "<p>You can login at: <a href='https://" . $_SERVER['HTTP_HOST'] . "/auth/student_login.php'>Student Login</a></p>";
                        $message .= "<p>Please keep your credentials secure.</p>";
                        $message .= "<p>Regards,<br>Administration Team<br>" . htmlspecialchars($college['name']) . "</p>";
                        
                        $email_sent = sendEmail($email, $full_name, $subject, $message);
                    }
                    
                    $success = "Student added successfully.";
                    if ($email_sent) {
                        $success .= " Login credentials have been sent to the student's email.";
                    }
                    
                    // Clear form data after successful submission
                    $full_name = $email = $roll_number = $department = $division = $password = '';
                    $year = '';
                } catch (PDOException $e) {
                    $error = "Database error: " . $e->getMessage();
                }
            }
        }
    }
}

// Include the header
require_once '../includes/header.php';
?>

    <!-- Main Content -->
    <main class="flex-grow py-6 px-4">
        <div class="max-w-7xl mx-auto">
            <!-- Page Header -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8">
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="bg-gradient-to-br from-green-500 to-green-600 p-3 rounded-xl shadow-lg">
                            <i class="fas fa-user-plus text-white text-xl"></i>
                        </div>
                        <div>
                            <h1 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                                Add New Student
                            </h1>
                            <nav class="text-sm text-gray-600 dark:text-gray-400 mt-1" aria-label="Breadcrumb">
                                <ol class="flex items-center space-x-2">
                                    <li>
                                        <a href="collegeadmin_dashboard.php" class="hover:text-green-600 dark:hover:text-green-400 transition-colors">
                                            Dashboard
                                        </a>
                                    </li>
                                    <li class="flex items-center">
                                        <i class="fas fa-chevron-right text-gray-400 text-xs mx-2"></i>
                                        <a href="list_students.php" class="hover:text-green-600 dark:hover:text-green-400 transition-colors">Student Management</a>
                                    </li>
                                    <li class="flex items-center">
                                        <i class="fas fa-chevron-right text-gray-400 text-xs mx-2"></i>
                                        <span class="text-gray-900 dark:text-white font-medium">Add Student</span>
                                    </li>
                                </ol>
                            </nav>
                            <p class="text-gray-600 dark:text-gray-400 mt-2">
                                Add a new student to <span class="font-medium text-green-600 dark:text-green-400"><?= htmlspecialchars($college['name'] ?? 'your college') ?></span>
                                <span class="mx-2">•</span>
                                <span class="text-sm"><?= date('l, F j, Y') ?></span>
                            </p>
                        </div>
                    </div>
                    <div class="mt-6 lg:mt-0 flex flex-wrap gap-3">
                        <a href="list_students.php" class="btn btn-outline flex items-center text-sm hover:shadow-md transition-all duration-200">
                            <i class="fas fa-arrow-left mr-2"></i> Back to Student List
                        </a>
                        <a href="import_students.php" class="btn btn-outline flex items-center text-sm hover:shadow-md transition-all duration-200">
                            <i class="fas fa-file-import mr-2"></i> Bulk Import
                        </a>
                    </div>
                </div>
            </div>

            <!-- Main Content Grid -->
            <div class="grid grid-cols-1 xl:grid-cols-4 gap-8">
                <!-- Left Column - Form (3/4 width) -->
                <div class="xl:col-span-3">
                    <!-- Alert Messages -->
                    <?php if ($error): ?>
                        <div class="bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 rounded-xl p-4 mb-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-exclamation-circle text-red-500 text-lg"></i>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-red-800 dark:text-red-300">Error</h3>
                                    <p class="text-sm text-red-700 dark:text-red-400 mt-1"><?= htmlspecialchars($error) ?></p>
                                </div>
                                <div class="ml-auto pl-3">
                                    <button type="button" class="text-red-400 hover:text-red-600 dark:hover:text-red-300" onclick="this.parentElement.parentElement.style.display='none';">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <?php if ($success): ?>
                        <div class="bg-green-50 dark:bg-green-900/30 border border-green-200 dark:border-green-800 rounded-xl p-4 mb-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-check-circle text-green-500 text-lg"></i>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-green-800 dark:text-green-300">Success</h3>
                                    <p class="text-sm text-green-700 dark:text-green-400 mt-1"><?= htmlspecialchars($success) ?></p>
                                    <?php if ($email_sent): ?>
                                        <p class="text-xs text-green-600 dark:text-green-400 mt-1">
                                            <i class="fas fa-envelope mr-1"></i> Login credentials have been sent to the student's email.
                                        </p>
                                    <?php endif; ?>
                                </div>
                                <div class="ml-auto pl-3">
                                    <button type="button" class="text-green-400 hover:text-green-600 dark:hover:text-green-300" onclick="this.parentElement.parentElement.style.display='none';">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Student Registration Form -->
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 mb-8">
                        <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
                            <h2 class="text-lg font-semibold text-gray-800 dark:text-white flex items-center">
                                <i class="fas fa-user-graduate mr-3 text-green-600"></i> Student Registration Form
                            </h2>
                            <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">Fill in the student's information to create their account</p>
                        </div>
                    
                        <form method="post" id="studentForm" class="p-6 space-y-8">
                            <!-- Personal Information Section -->
                            <div class="space-y-6">
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
                                        <i class="fas fa-user text-blue-600 dark:text-blue-400"></i>
                                    </div>
                                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Personal Information</h3>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div class="form-group">
                                        <label for="full_name" class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                                            Full Name <span class="text-red-500">*</span>
                                        </label>
                                        <div class="relative">
                                            <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                                <i class="fas fa-user text-gray-400"></i>
                                            </div>
                                            <input
                                                type="text"
                                                id="full_name"
                                                name="full_name"
                                                class="form-input pl-12 pr-4 py-3 w-full rounded-lg border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-green-500 focus:border-green-500"
                                                value="<?= htmlspecialchars($full_name ?? '') ?>"
                                                placeholder="Enter student's full name"
                                                required
                                            >
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label for="email" class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                                            Email Address <span class="text-red-500">*</span>
                                        </label>
                                        <div class="relative">
                                            <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                                <i class="fas fa-envelope text-gray-400"></i>
                                            </div>
                                            <input
                                                type="email"
                                                id="email"
                                                name="email"
                                                class="form-input pl-12 pr-4 py-3 w-full rounded-lg border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-green-500 focus:border-green-500"
                                                value="<?= htmlspecialchars($email ?? '') ?>"
                                                placeholder="<EMAIL>"
                                                required
                                            >
                                        </div>
                                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                            <i class="fas fa-info-circle mr-1"></i>
                                            Will be used as username for login
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <!-- Academic Information Section -->
                            <div class="space-y-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center">
                                        <i class="fas fa-graduation-cap text-purple-600 dark:text-purple-400"></i>
                                    </div>
                                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Academic Information</h3>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div class="form-group">
                                        <label for="roll_number" class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                                            Roll Number <span class="text-red-500">*</span>
                                        </label>
                                        <div class="relative">
                                            <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                                <i class="fas fa-id-card text-gray-400"></i>
                                            </div>
                                            <input
                                                type="text"
                                                id="roll_number"
                                                name="roll_number"
                                                class="form-input pl-12 pr-4 py-3 w-full rounded-lg border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-green-500 focus:border-green-500"
                                                value="<?= htmlspecialchars($roll_number ?? '') ?>"
                                                placeholder="e.g. 2023001"
                                                required
                                            >
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label for="department" class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                                            Department <span class="text-red-500">*</span>
                                        </label>
                                        <div class="relative">
                                            <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                                <i class="fas fa-building text-gray-400"></i>
                                            </div>
                                            <input
                                                type="text"
                                                id="department"
                                                name="department"
                                                class="form-input pl-12 pr-4 py-3 w-full rounded-lg border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-green-500 focus:border-green-500"
                                                value="<?= htmlspecialchars($department ?? '') ?>"
                                                placeholder="e.g. Computer Science"
                                                list="department-list"
                                                required
                                            >
                                        </div>
                                        <?php if (!empty($departments)): ?>
                                            <datalist id="department-list">
                                                <?php foreach ($departments as $dept): ?>
                                                    <option value="<?= htmlspecialchars($dept) ?>">
                                                <?php endforeach; ?>
                                            </datalist>
                                        <?php endif; ?>
                                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                            <i class="fas fa-lightbulb mr-1"></i>
                                            Start typing to see suggestions from existing departments
                                        </p>
                                    </div>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div class="form-group">
                                        <label for="year" class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                                            Academic Year <span class="text-red-500">*</span>
                                        </label>
                                        <div class="relative">
                                            <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                                <i class="fas fa-calendar-alt text-gray-400"></i>
                                            </div>
                                            <select
                                                id="year"
                                                name="year"
                                                class="form-select pl-12 pr-4 py-3 w-full rounded-lg border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-green-500 focus:border-green-500"
                                                required
                                            >
                                                <option value="" disabled <?= !isset($year) ? 'selected' : '' ?>>Select Academic Year</option>
                                                <option value="1" <?= (isset($year) && $year == 1) ? 'selected' : '' ?>>First Year</option>
                                                <option value="2" <?= (isset($year) && $year == 2) ? 'selected' : '' ?>>Second Year</option>
                                                <option value="3" <?= (isset($year) && $year == 3) ? 'selected' : '' ?>>Third Year</option>
                                                <option value="4" <?= (isset($year) && $year == 4) ? 'selected' : '' ?>>Fourth Year</option>
                                                <option value="5" <?= (isset($year) && $year == 5) ? 'selected' : '' ?>>Fifth Year</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label for="division" class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                                            Division <span class="text-gray-400">(Optional)</span>
                                        </label>
                                        <div class="relative">
                                            <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                                <i class="fas fa-layer-group text-gray-400"></i>
                                            </div>
                                            <input
                                                type="text"
                                                id="division"
                                                name="division"
                                                class="form-input pl-12 pr-4 py-3 w-full rounded-lg border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-green-500 focus:border-green-500"
                                                value="<?= htmlspecialchars($division ?? '') ?>"
                                                placeholder="e.g. A, B, C"
                                            >
                                        </div>
                                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                            <i class="fas fa-info-circle mr-1"></i>
                                            Class division or section (if applicable)
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <!-- Account Settings Section -->
                            <div class="space-y-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center">
                                        <i class="fas fa-key text-orange-600 dark:text-orange-400"></i>
                                    </div>
                                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Account Settings</h3>
                                </div>

                                <div class="form-group">
                                    <label for="password" class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                                        Password <span class="text-red-500">*</span>
                                    </label>
                                    <div class="relative">
                                        <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                            <i class="fas fa-lock text-gray-400"></i>
                                        </div>
                                        <input
                                            type="password"
                                            id="password"
                                            name="password"
                                            class="form-input pl-12 pr-32 py-3 w-full rounded-lg border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-green-500 focus:border-green-500"
                                            placeholder="Enter secure password"
                                            required
                                            minlength="8"
                                        >
                                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center space-x-2">
                                            <button type="button" id="togglePassword" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button type="button" id="generatePassword" class="text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300 text-sm font-medium transition-colors">
                                                Generate
                                            </button>
                                        </div>
                                    </div>
                                    <div class="mt-3">
                                        <div class="flex items-center justify-between mb-2">
                                            <span class="text-xs font-medium text-gray-500 dark:text-gray-400">Password strength:</span>
                                            <span id="passwordStrengthText" class="text-xs font-medium text-gray-500 dark:text-gray-400">Weak</span>
                                        </div>
                                        <div class="h-2 w-full bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                                            <div id="passwordStrength" class="h-full bg-red-400 rounded-full transition-all duration-300" style="width: 0%"></div>
                                        </div>
                                        <p id="passwordFeedback" class="text-xs text-gray-500 dark:text-gray-400 mt-2">
                                            <i class="fas fa-info-circle mr-1"></i>
                                            Minimum 8 characters with letters, numbers, and symbols
                                        </p>
                                    </div>
                                </div>

                                <!-- Account Options -->
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                                        <div class="flex items-center">
                                            <input
                                                type="checkbox"
                                                id="auto_verify"
                                                name="auto_verify"
                                                class="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                                                checked
                                            >
                                            <label for="auto_verify" class="ml-3 block text-sm font-medium text-blue-800 dark:text-blue-300">
                                                Auto-verify student account
                                            </label>
                                        </div>
                                        <p class="text-xs text-blue-700 dark:text-blue-400 mt-2 ml-7">
                                            Student can login immediately without email verification
                                        </p>
                                    </div>

                                    <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
                                        <div class="flex items-center">
                                            <input
                                                type="checkbox"
                                                id="send_credentials"
                                                name="send_credentials"
                                                class="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                                                checked
                                            >
                                            <label for="send_credentials" class="ml-3 block text-sm font-medium text-green-800 dark:text-green-300">
                                                Send login credentials via email
                                            </label>
                                        </div>
                                        <p class="text-xs text-green-700 dark:text-green-400 mt-2 ml-7">
                                            Email will include username and password for first login
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <!-- Form Actions -->
                            <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4 pt-6 border-t border-gray-200 dark:border-gray-700">
                                <div class="flex items-center text-sm text-gray-500 dark:text-gray-400">
                                    <i class="fas fa-info-circle mr-2"></i>
                                    All fields marked with <span class="text-red-500">*</span> are required
                                </div>
                                <div class="flex flex-col sm:flex-row gap-3">
                                    <a href="list_students.php" class="btn btn-outline hover:shadow-md transition-all duration-200">
                                        <i class="fas fa-arrow-left mr-2"></i> Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary shadow-lg hover:shadow-xl transition-all duration-200">
                                        <i class="fas fa-user-plus mr-2"></i> Create Student Account
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Right Column - Sidebar (1/4 width) -->
                <div class="xl:col-span-1">
                    <div class="space-y-6">
                        <!-- Quick Actions -->
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                            <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
                                <h3 class="text-lg font-semibold text-gray-800 dark:text-white flex items-center">
                                    <i class="fas fa-bolt mr-3 text-blue-600"></i> Quick Actions
                                </h3>
                            </div>
                            <div class="p-6 space-y-3">
                                <a href="list_students.php" class="flex items-center p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                                    <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-list text-blue-600 dark:text-blue-400"></i>
                                    </div>
                                    <div>
                                        <p class="font-medium text-gray-900 dark:text-white">All Students</p>
                                        <p class="text-sm text-gray-500 dark:text-gray-400">View student list</p>
                                    </div>
                                </a>

                                <a href="import_students.php" class="flex items-center p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                                    <div class="w-10 h-10 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-file-import text-orange-600 dark:text-orange-400"></i>
                                    </div>
                                    <div>
                                        <p class="font-medium text-gray-900 dark:text-white">Bulk Import</p>
                                        <p class="text-sm text-gray-500 dark:text-gray-400">Import multiple students</p>
                                    </div>
                                </a>

                                <a href="verify_student.php" class="flex items-center p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                                    <div class="w-10 h-10 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-user-check text-purple-600 dark:text-purple-400"></i>
                                    </div>
                                    <div>
                                        <p class="font-medium text-gray-900 dark:text-white">Verify Students</p>
                                        <p class="text-sm text-gray-500 dark:text-gray-400">Approve registrations</p>
                                    </div>
                                </a>
                            </div>
                        </div>

                        <!-- Form Tips -->
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                            <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
                                <h3 class="text-lg font-semibold text-gray-800 dark:text-white flex items-center">
                                    <i class="fas fa-lightbulb mr-3 text-yellow-600"></i> Form Tips
                                </h3>
                            </div>
                            <div class="p-6">
                                <div class="space-y-4">
                                    <div class="flex items-start space-x-3">
                                        <div class="w-6 h-6 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mt-0.5">
                                            <i class="fas fa-check text-green-600 dark:text-green-400 text-xs"></i>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-gray-900 dark:text-white">Email Validation</p>
                                            <p class="text-xs text-gray-500 dark:text-gray-400">Use institutional email addresses when possible</p>
                                        </div>
                                    </div>

                                    <div class="flex items-start space-x-3">
                                        <div class="w-6 h-6 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mt-0.5">
                                            <i class="fas fa-key text-blue-600 dark:text-blue-400 text-xs"></i>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-gray-900 dark:text-white">Strong Passwords</p>
                                            <p class="text-xs text-gray-500 dark:text-gray-400">Use the generator for secure passwords</p>
                                        </div>
                                    </div>

                                    <div class="flex items-start space-x-3">
                                        <div class="w-6 h-6 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center mt-0.5">
                                            <i class="fas fa-envelope text-purple-600 dark:text-purple-400 text-xs"></i>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-gray-900 dark:text-white">Email Credentials</p>
                                            <p class="text-xs text-gray-500 dark:text-gray-400">Students will receive login details via email</p>
                                        </div>
                                    </div>

                                    <div class="flex items-start space-x-3">
                                        <div class="w-6 h-6 bg-orange-100 dark:bg-orange-900/30 rounded-full flex items-center justify-center mt-0.5">
                                            <i class="fas fa-users text-orange-600 dark:text-orange-400 text-xs"></i>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-gray-900 dark:text-white">Bulk Import</p>
                                            <p class="text-xs text-gray-500 dark:text-gray-400">For multiple students, use CSV import feature</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Recent Activity -->
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                            <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
                                <h3 class="text-lg font-semibold text-gray-800 dark:text-white flex items-center">
                                    <i class="fas fa-clock mr-3 text-gray-600"></i> Quick Stats
                                </h3>
                            </div>
                            <div class="p-6">
                                <div class="space-y-4">
                                    <?php
                                    // Get quick stats
                                    $statsStmt = $conn->prepare("
                                        SELECT
                                            COUNT(*) as total_students,
                                            COUNT(CASE WHEN verified = 1 THEN 1 END) as verified_students,
                                            COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as recent_students
                                        FROM students
                                        WHERE college_id = ?
                                    ");
                                    $statsStmt->execute([$college_id]);
                                    $quickStats = $statsStmt->fetch(PDO::FETCH_ASSOC);
                                    ?>

                                    <div class="flex items-center justify-between">
                                        <span class="text-sm text-gray-600 dark:text-gray-400">Total Students</span>
                                        <span class="text-sm font-semibold text-gray-900 dark:text-white"><?= $quickStats['total_students'] ?></span>
                                    </div>

                                    <div class="flex items-center justify-between">
                                        <span class="text-sm text-gray-600 dark:text-gray-400">Verified</span>
                                        <span class="text-sm font-semibold text-green-600 dark:text-green-400"><?= $quickStats['verified_students'] ?></span>
                                    </div>

                                    <div class="flex items-center justify-between">
                                        <span class="text-sm text-gray-600 dark:text-gray-400">Added This Week</span>
                                        <span class="text-sm font-semibold text-blue-600 dark:text-blue-400"><?= $quickStats['recent_students'] ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

<?php
// Include the footer
require_once '../includes/footer.php';
?>

<script>
    // Password toggle functionality
    document.addEventListener('DOMContentLoaded', function() {
        const passwordInput = document.getElementById('password');
        const toggleButton = document.getElementById('togglePassword');
        const strengthBar = document.getElementById('passwordStrength');
        const strengthText = document.getElementById('passwordStrengthText');
        const feedbackText = document.getElementById('passwordFeedback');
        const generateButton = document.getElementById('generatePassword');

        // Password visibility toggle
        if (toggleButton && passwordInput) {
            toggleButton.addEventListener('click', function() {
                const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
                passwordInput.setAttribute('type', type);

                // Change the eye icon
                const icon = this.querySelector('i');
                if (type === 'text') {
                    icon.classList.remove('fa-eye');
                    icon.classList.add('fa-eye-slash');
                } else {
                    icon.classList.remove('fa-eye-slash');
                    icon.classList.add('fa-eye');
                }
            });
        }

        // Password strength calculation
        function calculatePasswordStrength(password) {
            let score = 0;
            let feedback = 'Very Weak';
            let color = 'bg-red-400';
            let width = 0;

            if (password.length === 0) {
                return { score: 0, feedback: 'Enter a password', color: 'bg-gray-300', width: 0 };
            }

            // Length check
            if (password.length >= 8) score += 25;
            if (password.length >= 12) score += 15;

            // Character variety checks
            if (/[a-z]/.test(password)) score += 15;
            if (/[A-Z]/.test(password)) score += 15;
            if (/[0-9]/.test(password)) score += 15;
            if (/[^A-Za-z0-9]/.test(password)) score += 15;

            // Determine feedback and styling
            if (score < 30) {
                feedback = 'Very Weak';
                color = 'bg-red-400';
                width = 20;
            } else if (score < 50) {
                feedback = 'Weak';
                color = 'bg-orange-400';
                width = 40;
            } else if (score < 70) {
                feedback = 'Fair';
                color = 'bg-yellow-400';
                width = 60;
            } else if (score < 85) {
                feedback = 'Good';
                color = 'bg-blue-400';
                width = 80;
            } else {
                feedback = 'Strong';
                color = 'bg-green-400';
                width = 100;
            }

            return { score, feedback, color, width };
        }

        // Password strength meter
        if (passwordInput && strengthBar && strengthText && feedbackText) {
            passwordInput.addEventListener('input', function() {
                const password = this.value;
                const result = calculatePasswordStrength(password);

                // Update strength bar
                strengthBar.style.width = result.width + '%';
                strengthBar.className = `h-full rounded-full transition-all duration-300 ${result.color}`;

                // Update strength text
                strengthText.textContent = result.feedback;

                // Update feedback text
                if (password.length === 0) {
                    feedbackText.innerHTML = '<i class="fas fa-info-circle mr-1"></i>Minimum 8 characters with letters, numbers, and symbols';
                } else if (result.score < 50) {
                    feedbackText.innerHTML = '<i class="fas fa-exclamation-triangle mr-1"></i>Try adding more characters, numbers, and symbols';
                } else if (result.score < 85) {
                    feedbackText.innerHTML = '<i class="fas fa-check-circle mr-1"></i>Good password! Consider adding more variety';
                } else {
                    feedbackText.innerHTML = '<i class="fas fa-shield-alt mr-1"></i>Excellent! This is a strong password';
                }
            });
        }

        // Generate password functionality
        if (generateButton && passwordInput) {
            generateButton.addEventListener('click', function() {
                const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
                let password = '';

                // Ensure at least one character from each category
                password += 'abcdefghijklmnopqrstuvwxyz'[Math.floor(Math.random() * 26)]; // lowercase
                password += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'[Math.floor(Math.random() * 26)]; // uppercase
                password += '0123456789'[Math.floor(Math.random() * 10)]; // number
                password += '!@#$%^&*'[Math.floor(Math.random() * 8)]; // symbol

                // Fill the rest randomly
                for (let i = 4; i < 12; i++) {
                    password += charset[Math.floor(Math.random() * charset.length)];
                }

                // Shuffle the password
                password = password.split('').sort(() => Math.random() - 0.5).join('');

                passwordInput.value = password;
                passwordInput.dispatchEvent(new Event('input')); // Trigger strength calculation
            });
        }
    });

    // Form validation
    document.getElementById('studentForm').addEventListener('submit', function(e) {
        const name = document.getElementById('full_name').value.trim();
        const email = document.getElementById('email').value.trim();
        const roll = document.getElementById('roll_number').value.trim();
        const department = document.getElementById('department').value.trim();
        const year = document.getElementById('year').value;
        const password = document.getElementById('password').value;

        if (!name || !email || !roll || !department || !year || !password) {
            e.preventDefault();
            alert('Please fill in all required fields.');
            return false;
        }

        if (password.length < 8) {
            e.preventDefault();
            alert('Password should be at least 8 characters long.');
            return false;
        }

        if (!email.includes('@')) {
            e.preventDefault();
            alert('Please enter a valid email address.');
            return false;
        }

        // Show loading state
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Creating Account...';
        submitBtn.disabled = true;

        // Re-enable button after 5 seconds in case of issues
        setTimeout(() => {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }, 5000);
    });

    // Theme toggle functionality
    const themeToggleBtn = document.getElementById('themeToggle');

    function getThemePreference() {
        return localStorage.getItem('color-theme') || 'light';
    }

    function setThemePreference(theme) {
        localStorage.setItem('color-theme', theme);
        if (theme === 'dark') {
            document.documentElement.classList.add('dark');
        } else {
            document.documentElement.classList.remove('dark');
        }
    }

    // Set initial theme
    setThemePreference(getThemePreference());

    // Toggle theme when button is clicked
    if (themeToggleBtn) {
        themeToggleBtn.addEventListener('click', function() {
            const currentTheme = getThemePreference();
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            setThemePreference(newTheme);
        });
    }

    // Notifications button
    document.getElementById('notificationsBtn')?.addEventListener('click', function() {
        alert('Notifications feature coming soon!');
    });

    // Auto-focus first input
    document.getElementById('full_name')?.focus();
</script>