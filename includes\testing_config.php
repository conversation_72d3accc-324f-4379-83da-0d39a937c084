<?php
/**
 * Testing Configuration
 * 
 * This file contains configuration settings for testing mode.
 * Set TESTING_MODE to true to enable testing features like OTP bypass.
 */

// Enable/Disable Testing Mode
define('TESTING_MODE', true);

// OTP Testing Settings
define('OTP_BYPASS_ENABLED', true);
define('OTP_DEFAULT_CODE', '123456');

// Email Testing Settings  
define('EMAIL_TESTING_MODE', true);
define('SKIP_EMAIL_SENDING', false);

// Database Testing Settings
define('DB_DEBUG_MODE', true);

// Security Testing Settings
define('SKIP_CSRF_VALIDATION', true);
define('SKIP_RATE_LIMITING', true);

/**
 * Check if testing mode is enabled
 */
function isTestingMode() {
    return defined('TESTING_MODE') && TESTING_MODE === true;
}

/**
 * Check if OTP bypass is enabled
 */
function isOtpBypassEnabled() {
    return defined('OTP_BYPASS_ENABLED') && OTP_BYPASS_ENABLED === true;
}

/**
 * Get default OTP code for testing
 */
function getTestingOtpCode() {
    return defined('OTP_DEFAULT_CODE') ? OTP_DEFAULT_CODE : '123456';
}

/**
 * Display testing notice
 */
function showTestingNotice($message = 'Testing Mode Active') {
    if (isTestingMode()) {
        echo '<div class="alert alert-warning">';
        echo '<strong>🧪 ' . htmlspecialchars($message) . '</strong><br>';
        echo 'Some security features are disabled for testing purposes.';
        echo '</div>';
    }
}

/**
 * Log testing actions
 */
function logTestingAction($action, $details = '') {
    if (isTestingMode() && defined('DB_DEBUG_MODE') && DB_DEBUG_MODE) {
        error_log("TESTING: $action - $details");
    }
}
?>
