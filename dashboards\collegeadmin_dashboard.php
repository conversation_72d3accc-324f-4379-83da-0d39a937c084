<?php
require_once '../includes/session_config.php';
require_once '../includes/db.php';

if (!isset($_SESSION['collegeadmin_id'])) {
    header("Location: ../auth/collegeadmin_login.php");
    exit;
}

$college_id = $_SESSION['college_id'];
$admin_name = $_SESSION['collegeadmin_name'];

// Set role explicitly for the sidebar
$role = 'collegeadmin';
$user_id = $_SESSION['collegeadmin_id'];
$user_name = $_SESSION['collegeadmin_name'];
$current_page = basename($_SERVER['PHP_SELF']);

// Get college details
$college_query = $conn->prepare("SELECT * FROM colleges WHERE id = ?");
$college_query->execute([$college_id]);
$college = $college_query->fetch(PDO::FETCH_ASSOC);

// Get Counts - Wrap in try-catch blocks to handle missing tables
try {
$faculty_count = $conn->prepare("SELECT COUNT(*) FROM faculties WHERE college_id = ?");
$faculty_count->execute([$college_id]);
$total_faculty = $faculty_count->fetchColumn();
} catch (PDOException $e) {
    $total_faculty = 0;
}

try {
$student_count = $conn->prepare("SELECT COUNT(*) FROM students WHERE college_id = ?");
$student_count->execute([$college_id]);
$total_students = $student_count->fetchColumn();
} catch (PDOException $e) {
    $total_students = 0;
}

try {
$alumni_count = $conn->prepare("SELECT COUNT(*) FROM alumni WHERE college_id = ?");
$alumni_count->execute([$college_id]);
$total_alumni = $alumni_count->fetchColumn();
} catch (PDOException $e) {
    $total_alumni = 0;
}

// Get recent faculty members
try {
    $recent_faculty = $conn->prepare("SELECT id, college_id, name, email, department, profile_photo, bio, created_at FROM faculties WHERE college_id = ? ORDER BY created_at DESC LIMIT 5");
    $recent_faculty->execute([$college_id]);
    $faculty_members = $recent_faculty->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $faculty_members = [];
}

// Get recent students
try {
    $recent_students = $conn->prepare("SELECT id, college_id, full_name, email, roll_number, department, year, division, profile_image, verified, status, created_at FROM students WHERE college_id = ? ORDER BY created_at DESC LIMIT 5");
    $recent_students->execute([$college_id]);
    $students = $recent_students->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $students = [];
}

// Get recent alumni
try {
    $recent_alumni = $conn->prepare("SELECT * FROM alumni WHERE college_id = ? ORDER BY created_at DESC LIMIT 5");
    $recent_alumni->execute([$college_id]);
    $alumni = $recent_alumni->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $alumni = [];
}

// Get recent announcements
try {
    $recent_announcements = $conn->prepare("SELECT * FROM announcements WHERE college_id = ? ORDER BY created_at DESC LIMIT 3");
    $recent_announcements->execute([$college_id]);
    $announcements = $recent_announcements->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $announcements = [];
}

// Get recent deals (campaigns in the existing database)
try {
    $recent_deals = $conn->prepare("SELECT * FROM deals WHERE college_id = ? ORDER BY created_at DESC LIMIT 3");
    $recent_deals->execute([$college_id]);
    $campaigns = $recent_deals->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $campaigns = [];
}

// Get recent events
try {
    $recent_events = $conn->prepare("SELECT * FROM events WHERE college_id = ? ORDER BY event_date DESC LIMIT 3");
    $recent_events->execute([$college_id]);
    $events = $recent_events->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $events = [];
}

// Get recent resources
try {
    $recent_resources = $conn->prepare("SELECT * FROM resources WHERE college_id = ? ORDER BY uploaded_at DESC LIMIT 3");
    $recent_resources->execute([$college_id]);
    $resources = $recent_resources->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $resources = [];
}

// Calculate growth rates (mock data for now)
$faculty_growth = 8;
$student_growth = 15;
$alumni_growth = 12;

// Include the header
require_once '../includes/header.php';
?>

    <!-- Main Content -->
    <main class="flex-grow py-6 px-4">
        <div class="max-w-7xl mx-auto">
            <!-- Dashboard Header -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6">
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="bg-gradient-to-br from-primary to-primary-dark p-3 rounded-xl shadow-lg">
                            <i class="fas fa-tachometer-alt text-white text-xl"></i>
                        </div>
                        <div>
                            <h1 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                                Dashboard Overview
                                <span class="ml-3 px-2 py-1 bg-primary/10 text-primary text-xs font-medium rounded-full">
                                    <?= htmlspecialchars($college['name'] ?? 'College') ?>
                                </span>
                            </h1>
                            <p class="text-gray-600 dark:text-gray-400 mt-1">
                                Welcome back, <span class="font-medium text-primary"><?= htmlspecialchars($admin_name) ?></span>
                                <span class="mx-2">•</span>
                                <span class="text-sm"><?= date('l, F j, Y') ?></span>
                            </p>
                        </div>
                    </div>
                    <div class="mt-6 lg:mt-0 flex flex-wrap gap-3">
                        <a href="post_announcement.php" class="btn btn-primary flex items-center text-sm shadow-lg hover:shadow-xl transition-all duration-200">
                            <i class="fas fa-bullhorn mr-2"></i> Post Announcement
                        </a>
                        <a href="../campaigns/create_campaign.php" class="btn btn-outline flex items-center text-sm hover:shadow-md transition-all duration-200">
                            <i class="fas fa-gift mr-2"></i> Create Campaign
                        </a>
                        <a href="../events/create_event.php" class="btn btn-outline flex items-center text-sm hover:shadow-md transition-all duration-200">
                            <i class="fas fa-calendar-plus mr-2"></i> New Event
                        </a>
                    </div>
                </div>

                <!-- System Status Indicators -->
                <div class="mt-6 grid grid-cols-2 md:grid-cols-4 gap-3">
                    <div class="bg-success-light dark:bg-success/20 px-3 py-2 rounded-lg flex items-center">
                        <i class="fas fa-users text-success mr-2"></i>
                        <span class="text-sm font-medium text-success">Active Users: <?= $total_students + $total_faculty + $total_alumni ?></span>
                    </div>
                    <div class="bg-primary-light dark:bg-primary/20 px-3 py-2 rounded-lg flex items-center">
                        <i class="fas fa-graduation-cap text-primary mr-2"></i>
                        <span class="text-sm font-medium text-primary">Total Students: <?= $total_students ?></span>
                    </div>
                    <div class="bg-accent-light dark:bg-accent/20 px-3 py-2 rounded-lg flex items-center">
                        <i class="fas fa-chalkboard-teacher text-accent mr-2"></i>
                        <span class="text-sm font-medium text-accent">Faculty: <?= $total_faculty ?></span>
                    </div>
                    <div class="bg-purple-100 dark:bg-purple-900/30 px-3 py-2 rounded-lg flex items-center">
                        <i class="fas fa-user-tie text-purple-600 dark:text-purple-400 mr-2"></i>
                        <span class="text-sm font-medium text-purple-600 dark:text-purple-400">Alumni: <?= $total_alumni ?></span>
                    </div>
                </div>
            </div>
            
            <?php if (isset($_SESSION['flash_message'])): ?>
                <div class="bg-blue-50 dark:bg-blue-900/30 border-l-4 border-blue-500 text-blue-700 dark:text-blue-400 p-4 mb-6 flex justify-between items-center" role="alert">
                    <p><?= $_SESSION['flash_message']; unset($_SESSION['flash_message']); ?></p>
                    <button type="button" class="text-blue-700 dark:text-blue-400 hover:text-blue-900" onclick="this.parentElement.style.display='none';">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            <?php endif; ?>
            
            <!-- Analytics Overview -->
            <div class="mb-8">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-xl font-semibold text-gray-800 dark:text-white flex items-center">
                        <i class="fas fa-chart-line mr-3 text-primary"></i> College Analytics
                    </h2>
                    <div class="flex text-sm">
                        <button class="px-3 py-1 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 rounded-l-md transition">Weekly</button>
                        <button class="px-3 py-1 bg-primary text-white rounded-r-md transition">Monthly</button>
                    </div>
                </div>

                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                    <!-- Faculty Card -->
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 transition-all duration-300 hover:shadow-lg hover:-translate-y-1">
                        <div class="flex items-center justify-between">
                            <div class="bg-blue-100 dark:bg-blue-900/30 p-3 rounded-xl">
                                <i class="fas fa-chalkboard-teacher text-blue-600 dark:text-blue-400 text-xl"></i>
                            </div>
                            <div class="text-right">
                                <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Faculty Members</h3>
                                <p class="text-2xl font-bold text-gray-900 dark:text-white"><?= $total_faculty ?></p>
                            </div>
                        </div>
                        <div class="mt-4">
                            <div class="flex items-center justify-between text-sm">
                                <span class="<?= $faculty_growth > 0 ? 'text-green-500' : 'text-red-500' ?> flex items-center font-medium">
                                    <i class="fas fa-<?= $faculty_growth > 0 ? 'arrow-up' : 'arrow-down' ?> mr-1"></i>
                                    <?= abs($faculty_growth) ?>%
                                </span>
                                <span class="text-gray-500 dark:text-gray-400">vs last month</span>
                            </div>
                            <div class="w-full bg-gray-200 dark:bg-gray-700 h-1.5 rounded-full mt-2 overflow-hidden">
                                <div class="bg-blue-600 dark:bg-blue-500 h-full rounded-full transition-all duration-500" style="width: <?= min(100, max(10, $faculty_growth * 2)) ?>%"></div>
                            </div>
                        </div>
                        <div class="mt-4">
                            <a href="list_faculty.php" class="text-blue-600 dark:text-blue-400 hover:underline text-sm flex items-center font-medium">
                                <span>Manage Faculty</span>
                                <i class="fas fa-chevron-right ml-1 text-xs"></i>
                            </a>
                        </div>
                    </div>

                    <!-- Students Card -->
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 transition-all duration-300 hover:shadow-lg hover:-translate-y-1">
                        <div class="flex items-center justify-between">
                            <div class="bg-green-100 dark:bg-green-900/30 p-3 rounded-xl">
                                <i class="fas fa-user-graduate text-green-600 dark:text-green-400 text-xl"></i>
                            </div>
                            <div class="text-right">
                                <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Students</h3>
                                <p class="text-2xl font-bold text-gray-900 dark:text-white"><?= $total_students ?></p>
                            </div>
                        </div>
                        <div class="mt-4">
                            <div class="flex items-center justify-between text-sm">
                                <span class="<?= $student_growth > 0 ? 'text-green-500' : 'text-red-500' ?> flex items-center font-medium">
                                    <i class="fas fa-<?= $student_growth > 0 ? 'arrow-up' : 'arrow-down' ?> mr-1"></i>
                                    <?= abs($student_growth) ?>%
                                </span>
                                <span class="text-gray-500 dark:text-gray-400">vs last month</span>
                            </div>
                            <div class="w-full bg-gray-200 dark:bg-gray-700 h-1.5 rounded-full mt-2 overflow-hidden">
                                <div class="bg-green-600 dark:bg-green-500 h-full rounded-full transition-all duration-500" style="width: <?= min(100, max(10, $student_growth * 2)) ?>%"></div>
                            </div>
                        </div>
                        <div class="mt-4">
                            <a href="list_students.php" class="text-green-600 dark:text-green-400 hover:underline text-sm flex items-center font-medium">
                                <span>View Students</span>
                                <i class="fas fa-chevron-right ml-1 text-xs"></i>
                            </a>
                        </div>
                    </div>

                    <!-- Alumni Card -->
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 transition-all duration-300 hover:shadow-lg hover:-translate-y-1">
                        <div class="flex items-center justify-between">
                            <div class="bg-purple-100 dark:bg-purple-900/30 p-3 rounded-xl">
                                <i class="fas fa-user-tie text-purple-600 dark:text-purple-400 text-xl"></i>
                            </div>
                            <div class="text-right">
                                <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Alumni</h3>
                                <p class="text-2xl font-bold text-gray-900 dark:text-white"><?= $total_alumni ?></p>
                            </div>
                        </div>
                        <div class="mt-4">
                            <div class="flex items-center justify-between text-sm">
                                <span class="<?= $alumni_growth > 0 ? 'text-green-500' : 'text-red-500' ?> flex items-center font-medium">
                                    <i class="fas fa-<?= $alumni_growth > 0 ? 'arrow-up' : 'arrow-down' ?> mr-1"></i>
                                    <?= abs($alumni_growth) ?>%
                                </span>
                                <span class="text-gray-500 dark:text-gray-400">vs last month</span>
                            </div>
                            <div class="w-full bg-gray-200 dark:bg-gray-700 h-1.5 rounded-full mt-2 overflow-hidden">
                                <div class="bg-purple-600 dark:bg-purple-500 h-full rounded-full transition-all duration-500" style="width: <?= min(100, max(10, $alumni_growth * 2)) ?>%"></div>
                            </div>
                        </div>
                        <div class="mt-4">
                            <a href="list_alumni.php" class="text-purple-600 dark:text-purple-400 hover:underline text-sm flex items-center font-medium">
                                <span>View Alumni</span>
                                <i class="fas fa-chevron-right ml-1 text-xs"></i>
                            </a>
                        </div>
                    </div>

                    <!-- Engagement Card -->
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 transition-all duration-300 hover:shadow-lg hover:-translate-y-1">
                        <div class="flex items-center justify-between">
                            <div class="bg-orange-100 dark:bg-orange-900/30 p-3 rounded-xl">
                                <i class="fas fa-chart-bar text-orange-600 dark:text-orange-400 text-xl"></i>
                            </div>
                            <div class="text-right">
                                <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Engagement</h3>
                                <p class="text-2xl font-bold text-gray-900 dark:text-white"><?= round((($total_students + $total_alumni) / max($total_students + $total_faculty + $total_alumni, 1)) * 100) ?>%</p>
                            </div>
                        </div>
                        <div class="mt-4">
                            <div class="flex items-center justify-between text-sm">
                                <span class="text-green-500 flex items-center font-medium">
                                    <i class="fas fa-arrow-up mr-1"></i>
                                    5.2%
                                </span>
                                <span class="text-gray-500 dark:text-gray-400">vs last month</span>
                            </div>
                            <div class="w-full bg-gray-200 dark:bg-gray-700 h-1.5 rounded-full mt-2 overflow-hidden">
                                <div class="bg-orange-600 dark:bg-orange-500 h-full rounded-full transition-all duration-500" style="width: 75%"></div>
                            </div>
                        </div>
                        <div class="mt-4">
                            <a href="reports.php" class="text-orange-600 dark:text-orange-400 hover:underline text-sm flex items-center font-medium">
                                <span>View Reports</span>
                                <i class="fas fa-chevron-right ml-1 text-xs"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Main Content Grid -->
            <div class="grid grid-cols-1 xl:grid-cols-3 gap-8">
                <!-- Left Column (Spans 2 columns) -->
                <div class="xl:col-span-2 space-y-8">
                    <!-- Recent Announcements -->
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                        <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4 flex justify-between items-center">
                            <h2 class="text-lg font-semibold text-gray-800 dark:text-white flex items-center">
                                <i class="fas fa-bullhorn mr-3 text-primary"></i> Recent Announcements
                            </h2>
                            <a href="post_announcement.php" class="text-sm text-primary hover:text-primary-dark font-medium flex items-center transition-colors">
                                <i class="fas fa-plus mr-1"></i> New Announcement
                            </a>
                        </div>
                        <div class="p-6">
                            <?php if (count($announcements) > 0): ?>
                                <div class="space-y-4">
                                    <?php foreach ($announcements as $announcement): ?>
                                        <div class="group p-4 border border-gray-200 dark:border-gray-700 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700/50 hover:border-primary/20 transition-all duration-200">
                                            <div class="flex items-start space-x-3">
                                                <div class="bg-primary/10 p-2 rounded-lg">
                                                    <i class="fas fa-bullhorn text-primary text-sm"></i>
                                                </div>
                                                <div class="flex-1 min-w-0">
                                                    <h3 class="font-medium text-gray-900 dark:text-white group-hover:text-primary transition-colors">
                                                        <?= htmlspecialchars($announcement['title'] ?? $announcement['message']) ?>
                                                    </h3>
                                                    <p class="text-gray-600 dark:text-gray-400 mt-1 text-sm line-clamp-2">
                                                        <?= htmlspecialchars(substr($announcement['message'] ?? $announcement['content'] ?? '', 0, 150)) ?>...
                                                    </p>
                                                    <div class="flex justify-between items-center mt-3">
                                                        <span class="text-xs text-gray-500 dark:text-gray-400 flex items-center">
                                                            <i class="far fa-clock mr-1"></i>
                                                            <?= date('M j, Y \a\t g:i A', strtotime($announcement['created_at'])) ?>
                                                        </span>
                                                        <a href="view_announcement.php?id=<?= $announcement['id'] ?>" class="text-primary text-sm hover:text-primary-dark font-medium transition-colors">
                                                            Read More →
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                                <div class="mt-6 text-center">
                                    <a href="list_announcements.php" class="inline-flex items-center text-primary hover:text-primary-dark font-medium text-sm transition-colors">
                                        View All Announcements
                                        <i class="fas fa-chevron-right ml-1 text-xs"></i>
                                    </a>
                                </div>
                            <?php else: ?>
                                <div class="text-center py-12">
                                    <div class="w-20 h-20 bg-gradient-to-br from-primary/10 to-primary/20 rounded-2xl flex items-center justify-center mx-auto mb-4">
                                        <i class="fas fa-bullhorn text-primary text-2xl"></i>
                                    </div>
                                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">No announcements yet</h3>
                                    <p class="text-gray-500 dark:text-gray-400 mb-6 max-w-sm mx-auto">Share important updates and news with your college community</p>
                                    <a href="post_announcement.php" class="btn btn-primary shadow-lg hover:shadow-xl transition-all duration-200">
                                        <i class="fas fa-plus mr-2"></i> Create Your First Announcement
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <!-- Active Campaigns -->
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                        <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4 flex justify-between items-center">
                            <h2 class="text-lg font-semibold text-gray-800 dark:text-white flex items-center">
                                <i class="fas fa-gift mr-3 text-primary"></i> Active Campaigns
                            </h2>
                            <a href="../campaigns/create_campaign.php" class="text-sm text-primary hover:text-primary-dark font-medium flex items-center transition-colors">
                                <i class="fas fa-plus mr-1"></i> New Campaign
                            </a>
                        </div>
                        <div class="p-6">
                            <?php if (count($campaigns) > 0): ?>
                                <div class="space-y-4">
                                    <?php foreach ($campaigns as $campaign): ?>
                                        <div class="group p-4 border border-gray-200 dark:border-gray-700 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700/50 hover:border-primary/20 transition-all duration-200">
                                            <div class="flex items-center space-x-4">
                                                <?php if (!empty($campaign['thumbnail_path'])): ?>
                                                    <div class="w-16 h-16 rounded-xl overflow-hidden flex-shrink-0">
                                                        <img src="../uploads/<?= htmlspecialchars($campaign['thumbnail_path']) ?>" alt="Campaign" class="w-full h-full object-cover">
                                                    </div>
                                                <?php else: ?>
                                                    <div class="w-16 h-16 rounded-xl bg-gradient-to-br from-primary/10 to-primary/20 flex items-center justify-center flex-shrink-0">
                                                        <i class="fas fa-gift text-primary text-xl"></i>
                                                    </div>
                                                <?php endif; ?>
                                                <div class="flex-1 min-w-0">
                                                    <h3 class="font-medium text-gray-900 dark:text-white group-hover:text-primary transition-colors">
                                                        <?= htmlspecialchars($campaign['title']) ?>
                                                    </h3>
                                                    <div class="mt-2">
                                                        <?php
                                                        // Check if goal_amount exists and is not zero
                                                        if (!empty($campaign['goal_amount']) && $campaign['goal_amount'] > 0):
                                                            // Calculate percentage based on pledges
                                                            $percentage = 0; // Default to 0%

                                                            // Get total pledges for this deal
                                                            try {
                                                                $pledgeStmt = $conn->prepare("SELECT SUM(amount) FROM alumni_pledges WHERE deal_id = ? AND status IN ('pending', 'in-progress', 'fulfilled')");
                                                                $pledgeStmt->execute([$campaign['id']]);
                                                                $pledged_amount = $pledgeStmt->fetchColumn() ?: 0;

                                                                if ($campaign['goal_amount'] > 0) {
                                                                    $percentage = ($pledged_amount / $campaign['goal_amount']) * 100;
                                                                    $percentage = min(100, $percentage);
                                                                }
                                                            } catch (PDOException $e) {
                                                                $percentage = 0;
                                                            }
                                                        ?>
                                                        <div class="flex items-center space-x-3">
                                                            <div class="flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                                                <div class="bg-gradient-to-r from-primary to-primary-dark h-2 rounded-full transition-all duration-500" style="width: <?= $percentage ?>%"></div>
                                                            </div>
                                                            <span class="text-sm font-medium text-gray-900 dark:text-white">
                                                                <?= number_format($percentage, 0) ?>%
                                                            </span>
                                                        </div>
                                                        <div class="flex justify-between items-center mt-1">
                                                            <span class="text-xs text-gray-500 dark:text-gray-400">
                                                                ₹<?= number_format($pledged_amount ?? 0) ?> raised of ₹<?= number_format($campaign['goal_amount']) ?>
                                                            </span>
                                                        </div>
                                                        <?php else: ?>
                                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200">
                                                            <?= htmlspecialchars($campaign['status']) ?>
                                                        </span>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                                <div class="flex items-center space-x-2">
                                                    <a href="../campaigns/edit_campaign.php?id=<?= $campaign['id'] ?>" class="p-2 text-gray-400 hover:text-primary hover:bg-primary/10 rounded-lg transition-all duration-200">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a href="../campaigns/view_contributions.php?id=<?= $campaign['id'] ?>" class="p-2 text-gray-400 hover:text-primary hover:bg-primary/10 rounded-lg transition-all duration-200">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                                <div class="mt-6 text-center">
                                    <a href="../campaigns/manage_campaigns.php" class="inline-flex items-center text-primary hover:text-primary-dark font-medium text-sm transition-colors">
                                        Manage All Campaigns
                                        <i class="fas fa-chevron-right ml-1 text-xs"></i>
                                    </a>
                                </div>
                            <?php else: ?>
                                <div class="text-center py-12">
                                    <div class="w-20 h-20 bg-gradient-to-br from-primary/10 to-primary/20 rounded-2xl flex items-center justify-center mx-auto mb-4">
                                        <i class="fas fa-gift text-primary text-2xl"></i>
                                    </div>
                                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">No active campaigns</h3>
                                    <p class="text-gray-500 dark:text-gray-400 mb-6 max-w-sm mx-auto">Create fundraising or support campaigns to engage your college community</p>
                                    <a href="../campaigns/create_campaign.php" class="btn btn-primary shadow-lg hover:shadow-xl transition-all duration-200">
                                        <i class="fas fa-plus mr-2"></i> Create Campaign
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <!-- Upcoming Events -->
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                        <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4 flex justify-between items-center">
                            <h2 class="text-lg font-semibold text-gray-800 dark:text-white flex items-center">
                                <i class="fas fa-calendar-alt mr-3 text-primary"></i> Upcoming Events
                            </h2>
                            <a href="../events/create_event.php" class="text-sm text-primary hover:text-primary-dark font-medium flex items-center transition-colors">
                                <i class="fas fa-plus mr-1"></i> New Event
                            </a>
                        </div>
                        <div class="p-6">
                            <?php if (count($events) > 0): ?>
                                <div class="space-y-4">
                                    <?php foreach ($events as $event): ?>
                                        <div class="group p-4 border border-gray-200 dark:border-gray-700 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700/50 hover:border-primary/20 transition-all duration-200">
                                            <div class="flex items-center space-x-4">
                                                <div class="w-16 h-16 rounded-xl bg-gradient-to-br from-orange-100 to-orange-200 dark:from-orange-900/30 dark:to-orange-800/30 flex items-center justify-center flex-shrink-0">
                                                    <div class="text-center">
                                                        <div class="text-xs font-semibold text-orange-700 dark:text-orange-300 uppercase">
                                                            <?= date('M', strtotime($event['event_date'])) ?>
                                                        </div>
                                                        <div class="text-lg font-bold text-orange-800 dark:text-orange-200">
                                                            <?= date('d', strtotime($event['event_date'])) ?>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="flex-1 min-w-0">
                                                    <h3 class="font-medium text-gray-900 dark:text-white group-hover:text-primary transition-colors">
                                                        <?= htmlspecialchars($event['title']) ?>
                                                    </h3>
                                                    <div class="flex items-center mt-1 text-sm text-gray-500 dark:text-gray-400">
                                                        <i class="far fa-clock mr-1"></i>
                                                        <span><?= date('h:i A', strtotime($event['event_time'])) ?></span>
                                                        <?php if (!empty($event['speaker_name'])): ?>
                                                            <span class="mx-2">•</span>
                                                            <i class="far fa-user mr-1"></i>
                                                            <span><?= htmlspecialchars($event['speaker_name']) ?></span>
                                                        <?php endif; ?>
                                                    </div>
                                                    <?php if (!empty($event['location'])): ?>
                                                        <div class="flex items-center mt-1 text-sm text-gray-500 dark:text-gray-400">
                                                            <i class="fas fa-map-marker-alt mr-1"></i>
                                                            <span><?= htmlspecialchars($event['location']) ?></span>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="flex items-center space-x-2">
                                                    <a href="../events/edit_event.php?id=<?= $event['id'] ?>" class="p-2 text-gray-400 hover:text-primary hover:bg-primary/10 rounded-lg transition-all duration-200">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a href="../events/view_participants.php?id=<?= $event['id'] ?>" class="p-2 text-gray-400 hover:text-primary hover:bg-primary/10 rounded-lg transition-all duration-200">
                                                        <i class="fas fa-users"></i>
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                                <div class="mt-6 text-center">
                                    <a href="../events/event_list.php" class="inline-flex items-center text-primary hover:text-primary-dark font-medium text-sm transition-colors">
                                        View All Events
                                        <i class="fas fa-chevron-right ml-1 text-xs"></i>
                                    </a>
                                </div>
                            <?php else: ?>
                                <div class="text-center py-12">
                                    <div class="w-20 h-20 bg-gradient-to-br from-orange-100 to-orange-200 dark:from-orange-900/30 dark:to-orange-800/30 rounded-2xl flex items-center justify-center mx-auto mb-4">
                                        <i class="fas fa-calendar-alt text-orange-600 dark:text-orange-400 text-2xl"></i>
                                    </div>
                                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">No upcoming events</h3>
                                    <p class="text-gray-500 dark:text-gray-400 mb-6 max-w-sm mx-auto">Schedule events to engage students and alumni in your college community</p>
                                    <a href="../events/create_event.php" class="btn btn-primary shadow-lg hover:shadow-xl transition-all duration-200">
                                        <i class="fas fa-plus mr-2"></i> Create Event
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Right Column -->
                <div class="space-y-8">
                    <!-- Quick Actions -->
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                        <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
                            <h2 class="text-lg font-semibold text-gray-800 dark:text-white flex items-center">
                                <i class="fas fa-bolt mr-3 text-primary"></i> Quick Actions
                            </h2>
                        </div>
                        <div class="p-6">
                            <div class="space-y-3">
                                <a href="add_faculty.php" class="group flex items-center p-4 border border-gray-200 dark:border-gray-700 rounded-xl hover:bg-gradient-to-r hover:from-blue-50 hover:to-blue-100 dark:hover:from-blue-900/20 dark:hover:to-blue-800/20 hover:border-blue-200 dark:hover:border-blue-700 transition-all duration-200">
                                    <div class="w-12 h-12 rounded-xl bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-900/30 dark:to-blue-800/30 flex items-center justify-center text-blue-600 dark:text-blue-400 mr-4 group-hover:scale-110 transition-transform duration-200">
                                        <i class="fas fa-user-plus text-lg"></i>
                                    </div>
                                    <div class="flex-1">
                                        <h4 class="font-medium text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">Add Faculty</h4>
                                        <p class="text-xs text-gray-500 dark:text-gray-400">Add new faculty members</p>
                                    </div>
                                    <i class="fas fa-chevron-right text-gray-400 group-hover:text-blue-500 transition-colors"></i>
                                </a>

                                <a href="approve_students.php" class="group flex items-center p-4 border border-gray-200 dark:border-gray-700 rounded-xl hover:bg-gradient-to-r hover:from-green-50 hover:to-green-100 dark:hover:from-green-900/20 dark:hover:to-green-800/20 hover:border-green-200 dark:hover:border-green-700 transition-all duration-200">
                                    <div class="w-12 h-12 rounded-xl bg-gradient-to-br from-green-100 to-green-200 dark:from-green-900/30 dark:to-green-800/30 flex items-center justify-center text-green-600 dark:text-green-400 mr-4 group-hover:scale-110 transition-transform duration-200">
                                        <i class="fas fa-user-check text-lg"></i>
                                    </div>
                                    <div class="flex-1">
                                        <h4 class="font-medium text-gray-900 dark:text-white group-hover:text-green-600 dark:group-hover:text-green-400 transition-colors">Approve Students</h4>
                                        <p class="text-xs text-gray-500 dark:text-gray-400">Review student registrations</p>
                                    </div>
                                    <i class="fas fa-chevron-right text-gray-400 group-hover:text-green-500 transition-colors"></i>
                                </a>

                                <a href="verify_alumni.php" class="group flex items-center p-4 border border-gray-200 dark:border-gray-700 rounded-xl hover:bg-gradient-to-r hover:from-purple-50 hover:to-purple-100 dark:hover:from-purple-900/20 dark:hover:to-purple-800/20 hover:border-purple-200 dark:hover:border-purple-700 transition-all duration-200">
                                    <div class="w-12 h-12 rounded-xl bg-gradient-to-br from-purple-100 to-purple-200 dark:from-purple-900/30 dark:to-purple-800/30 flex items-center justify-center text-purple-600 dark:text-purple-400 mr-4 group-hover:scale-110 transition-transform duration-200">
                                        <i class="fas fa-user-shield text-lg"></i>
                                    </div>
                                    <div class="flex-1">
                                        <h4 class="font-medium text-gray-900 dark:text-white group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors">Verify Alumni</h4>
                                        <p class="text-xs text-gray-500 dark:text-gray-400">Verify alumni registrations</p>
                                    </div>
                                    <i class="fas fa-chevron-right text-gray-400 group-hover:text-purple-500 transition-colors"></i>
                                </a>

                                <a href="../events/create_event.php" class="group flex items-center p-4 border border-gray-200 dark:border-gray-700 rounded-xl hover:bg-gradient-to-r hover:from-orange-50 hover:to-orange-100 dark:hover:from-orange-900/20 dark:hover:to-orange-800/20 hover:border-orange-200 dark:hover:border-orange-700 transition-all duration-200">
                                    <div class="w-12 h-12 rounded-xl bg-gradient-to-br from-orange-100 to-orange-200 dark:from-orange-900/30 dark:to-orange-800/30 flex items-center justify-center text-orange-600 dark:text-orange-400 mr-4 group-hover:scale-110 transition-transform duration-200">
                                        <i class="fas fa-calendar-plus text-lg"></i>
                                    </div>
                                    <div class="flex-1">
                                        <h4 class="font-medium text-gray-900 dark:text-white group-hover:text-orange-600 dark:group-hover:text-orange-400 transition-colors">Create Event</h4>
                                        <p class="text-xs text-gray-500 dark:text-gray-400">Schedule college events</p>
                                    </div>
                                    <i class="fas fa-chevron-right text-gray-400 group-hover:text-orange-500 transition-colors"></i>
                                </a>

                                <a href="../resources/upload.php" class="group flex items-center p-4 border border-gray-200 dark:border-gray-700 rounded-xl hover:bg-gradient-to-r hover:from-red-50 hover:to-red-100 dark:hover:from-red-900/20 dark:hover:to-red-800/20 hover:border-red-200 dark:hover:border-red-700 transition-all duration-200">
                                    <div class="w-12 h-12 rounded-xl bg-gradient-to-br from-red-100 to-red-200 dark:from-red-900/30 dark:to-red-800/30 flex items-center justify-center text-red-600 dark:text-red-400 mr-4 group-hover:scale-110 transition-transform duration-200">
                                        <i class="fas fa-file-upload text-lg"></i>
                                    </div>
                                    <div class="flex-1">
                                        <h4 class="font-medium text-gray-900 dark:text-white group-hover:text-red-600 dark:group-hover:text-red-400 transition-colors">Upload Resources</h4>
                                        <p class="text-xs text-gray-500 dark:text-gray-400">Share documents and resources</p>
                                    </div>
                                    <i class="fas fa-chevron-right text-gray-400 group-hover:text-red-500 transition-colors"></i>
                                </a>
                            </div>
                        </div>
                    </div>
    
                    <!-- Recent Resources -->
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                        <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4 flex justify-between items-center">
                            <h2 class="text-lg font-semibold text-gray-800 dark:text-white flex items-center">
                                <i class="fas fa-file-alt mr-3 text-primary"></i> Recent Resources
                            </h2>
                            <a href="../resources/upload.php" class="text-sm text-primary hover:text-primary-dark font-medium flex items-center transition-colors">
                                <i class="fas fa-plus mr-1"></i> Upload
                            </a>
                        </div>
                        <div class="p-6">
                            <?php if (count($resources) > 0): ?>
                                <div class="space-y-3">
                                    <?php foreach ($resources as $resource): ?>
                                        <a href="../resources/download.php?id=<?= $resource['id'] ?>" class="group flex items-center p-3 border border-gray-200 dark:border-gray-700 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700/50 hover:border-primary/20 transition-all duration-200">
                                            <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-indigo-100 to-indigo-200 dark:from-indigo-900/30 dark:to-indigo-800/30 flex items-center justify-center text-indigo-600 dark:text-indigo-400 mr-3">
                                                <i class="fas fa-file-alt"></i>
                                            </div>
                                            <div class="flex-1 min-w-0">
                                                <h4 class="font-medium text-gray-900 dark:text-white group-hover:text-primary transition-colors truncate">
                                                    <?= htmlspecialchars($resource['title']) ?>
                                                </h4>
                                                <p class="text-xs text-gray-500 dark:text-gray-400">
                                                    Uploaded <?= date('M d, Y', strtotime($resource['uploaded_at'])) ?>
                                                </p>
                                            </div>
                                            <i class="fas fa-download text-gray-400 group-hover:text-primary transition-colors"></i>
                                        </a>
                                    <?php endforeach; ?>
                                </div>
                                <div class="mt-4 text-center">
                                    <a href="../resources/resource_list.php" class="inline-flex items-center text-primary hover:text-primary-dark font-medium text-sm transition-colors">
                                        View All Resources
                                        <i class="fas fa-chevron-right ml-1 text-xs"></i>
                                    </a>
                                </div>
                            <?php else: ?>
                                <div class="text-center py-8">
                                    <div class="w-16 h-16 bg-gradient-to-br from-indigo-100 to-indigo-200 dark:from-indigo-900/30 dark:to-indigo-800/30 rounded-2xl flex items-center justify-center mx-auto mb-4">
                                        <i class="fas fa-file-alt text-indigo-600 dark:text-indigo-400 text-xl"></i>
                                    </div>
                                    <h3 class="text-base font-semibold text-gray-900 dark:text-white mb-2">No resources yet</h3>
                                    <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">Upload documents for students and faculty</p>
                                    <a href="../resources/upload.php" class="btn btn-primary text-sm shadow-lg hover:shadow-xl transition-all duration-200">
                                        <i class="fas fa-upload mr-2"></i> Upload Resource
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- System Status -->
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                        <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
                            <h2 class="text-lg font-semibold text-gray-800 dark:text-white flex items-center">
                                <i class="fas fa-server mr-3 text-primary"></i> System Status
                            </h2>
                        </div>
                        <div class="p-6">
                            <div class="space-y-4">
                                <div class="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                                    <div class="flex items-center">
                                        <i class="fas fa-database text-green-500 mr-2"></i>
                                        <span class="text-gray-700 dark:text-gray-300 font-medium">Database</span>
                                    </div>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400">
                                        <i class="fas fa-circle text-green-500 mr-1" style="font-size: 6px;"></i>
                                        Online
                                    </span>
                                </div>
                                <div class="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                                    <div class="flex items-center">
                                        <i class="fas fa-hdd text-green-500 mr-2"></i>
                                        <span class="text-gray-700 dark:text-gray-300 font-medium">Storage</span>
                                    </div>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400">
                                        <i class="fas fa-circle text-green-500 mr-1" style="font-size: 6px;"></i>
                                        78% Free
                                    </span>
                                </div>
                                <div class="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                                    <div class="flex items-center">
                                        <i class="fas fa-envelope text-green-500 mr-2"></i>
                                        <span class="text-gray-700 dark:text-gray-300 font-medium">Email Service</span>
                                    </div>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400">
                                        <i class="fas fa-circle text-green-500 mr-1" style="font-size: 6px;"></i>
                                        Operational
                                    </span>
                                </div>
                            </div>

                            <div class="mt-6">
                                <a href="system_check.php" class="btn btn-outline w-full flex justify-center items-center hover:bg-primary hover:text-white hover:border-primary transition-all duration-200">
                                    <i class="fas fa-sync-alt mr-2"></i> Run System Check
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

<?php
// Include the footer
require_once '../includes/footer.php';
?>