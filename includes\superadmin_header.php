<?php
require_once __DIR__ . '/session_config.php';

if (!isset($_SESSION['superadmin_id'])) {
    header("Location: ../auth/superadmin_login.php");
    exit;
}

$superadmin_id = $_SESSION['superadmin_id'];
$superadmin_name = $_SESSION['superadmin_name'];

// Determine active page
$current_page = basename($_SERVER['PHP_SELF']);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Super Admin - Mentoshri Platform</title>
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts - Inter -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        'primary': '#2563eb',
                        'primary-dark': '#1d4ed8',
                        'primary-light': '#dbeafe',
                        'accent': '#f59e0b',
                        'accent-light': '#fef3c7',
                        'success': '#10b981',
                        'success-light': '#d1fae5',
                        'danger': '#ef4444',
                        'danger-light': '#fee2e2',
                        'warning': '#f59e0b',
                        'warning-light': '#fef3c7',
                    },
                    fontFamily: {
                        'sans': ['Inter', 'sans-serif'],
                    },
                }
            }
        }
    </script>
    <!-- Design System -->
    <link rel="stylesheet" href="../assets/css/design-system.css">
    <!-- Custom styles -->
    <style>
        /* Super Admin specific styles */
        .superadmin-gradient {
            background: linear-gradient(to right, #2563eb, #3b82f6);
        }
        .nav-item {
            position: relative;
            transition: all 0.2s ease;
        }
        .nav-item.active {
            color: #2563eb;
            font-weight: 500;
        }
        .nav-item.active::after {
            content: '';
            position: absolute;
            bottom: -0.5rem;
            left: 0;
            width: 100%;
            height: 2px;
            background-color: #2563eb;
            border-radius: 2px;
        }
        .nav-item:hover:not(.active) {
            color: #4b5563;
        }
        .sidenav-item {
            transition: all 0.2s ease;
            border-radius: 0.375rem;
        }
        .sidenav-item:hover {
            background-color: #f3f4f6;
        }
        .sidenav-item.active {
            background-color: #dbeafe;
            color: #2563eb;
            font-weight: 500;
        }
        .dark .sidenav-item:hover {
            background-color: #374151;
        }
        .dark .sidenav-item.active {
            background-color: #1e40af;
            color: white;
        }
        .stat-card {
            transition: all 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        .slide-in {
            animation: slideIn 0.5s ease forwards;
        }
        @keyframes slideIn {
            from { transform: translateY(20px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
    </style>
</head>
<body class="font-sans bg-gray-50 dark:bg-gray-900 min-h-screen flex flex-col">
    <!-- Top Navigation Bar -->
    <header class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50">
        <div class="max-w-full mx-auto px-4">
            <div class="flex items-center justify-between h-16">
                <!-- Logo & Branding -->
                <div class="flex items-center">
                    <a href="../dashboards/superadmin_dashboard.php" class="flex-shrink-0 flex items-center">
                        <div class="bg-primary rounded-lg w-8 h-8 flex items-center justify-center text-white font-bold text-xl mr-2">M</div>
                        <span class="text-gray-900 dark:text-white font-bold text-xl hidden md:inline-block">Mentoshri <span class="text-primary font-normal">Platform</span></span>
                    </a>
                </div>
                
                <!-- Main Navigation (Desktop) -->
                <nav class="hidden md:flex space-x-8">
                    <a href="../dashboards/superadmin_dashboard.php" class="nav-item py-2 <?= $current_page === 'superadmin_dashboard.php' ? 'active' : '' ?>">
                        <span>Dashboard</span>
                    </a>
                    <a href="../dashboards/list_colleges.php" class="nav-item py-2 <?= $current_page === 'list_colleges.php' || $current_page === 'create_college.php' ? 'active' : '' ?>">
                        <span>Colleges</span>
                    </a>
                    <a href="../dashboards/system_check.php" class="nav-item py-2 <?= $current_page === 'system_check.php' || $current_page === 'system_settings.php' ? 'active' : '' ?>">
                        <span>System</span>
                    </a>
                </nav>
                
                <!-- Right Side Controls -->
                <div class="flex items-center space-x-4">
                    <!-- Notifications -->
                    <div class="relative">
                        <button class="text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary-light p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                            </svg>
                        </button>
                    </div>
                    
                    <!-- Dark Mode Toggle -->
                    <button id="themeToggle" class="text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary-light p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 dark:hidden" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
                        </svg>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 hidden dark:block" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                        </svg>
                    </button>
                    
                    <!-- User Menu -->
                    <div class="relative" id="userMenu">
                        <button id="userMenuButton" class="flex items-center text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary-light">
                            <div class="w-8 h-8 rounded-full bg-primary-light text-primary flex items-center justify-center font-medium mr-2">
                                <?= substr($superadmin_name, 0, 1) ?>
                            </div>
                            <span class="hidden sm:inline-block"><?= htmlspecialchars($superadmin_name) ?></span>
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </button>
                        
                        <!-- Dropdown Menu -->
                        <div id="userMenuDropdown" class="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg py-1 border border-gray-200 dark:border-gray-700 hidden transition-all duration-200 opacity-0 transform origin-top-right scale-95">
                            <div class="px-4 py-2 border-b border-gray-200 dark:border-gray-700">
                                <p class="text-sm font-medium text-gray-700 dark:text-gray-300">Signed in as</p>
                                <p class="text-sm font-bold text-gray-900 dark:text-white">Super Admin</p>
                            </div>
                            <a href="../dashboards/superadmin_profile.php" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                <i class="fas fa-user mr-2 text-gray-400 dark:text-gray-500"></i> Profile
                            </a>
                            <a href="../dashboards/system_settings.php" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                <i class="fas fa-cog mr-2 text-gray-400 dark:text-gray-500"></i> Settings
                            </a>
                            <div class="border-t border-gray-200 dark:border-gray-700"></div>
                            <a href="../auth/logout.php" class="block px-4 py-2 text-sm text-red-600 dark:text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20">
                                <i class="fas fa-sign-out-alt mr-2"></i> Sign out
                            </a>
                        </div>
                    </div>
                    
                    <!-- Mobile Menu Button -->
                    <button id="mobileMenuButton" class="md:hidden text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 focus:outline-none">
                        <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>
                </div>
            </div>
            
            <!-- Mobile Navigation Menu -->
            <div id="mobileMenu" class="md:hidden hidden pb-3">
                <div class="pt-2 space-y-1">
                    <a href="../dashboards/superadmin_dashboard.php" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 <?= $current_page === 'superadmin_dashboard.php' ? 'bg-gray-100 dark:bg-gray-700 text-primary dark:text-primary-light' : '' ?>">
                        <i class="fas fa-th-large mr-2"></i> Dashboard
                    </a>
                    
                    <div class="border-t border-gray-200 dark:border-gray-700 my-2 pt-2">
                        <p class="px-3 py-1 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider">College Management</p>
                        <a href="../dashboards/list_colleges.php" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 <?= $current_page === 'list_colleges.php' ? 'bg-gray-100 dark:bg-gray-700 text-primary dark:text-primary-light' : '' ?>">
                            <i class="fas fa-university mr-2"></i> Colleges
                        </a>
                        <a href="../dashboards/list_admins.php" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 <?= $current_page === 'list_admins.php' ? 'bg-gray-100 dark:bg-gray-700 text-primary dark:text-primary-light' : '' ?>">
                            <i class="fas fa-user-shield mr-2"></i> Admins
                        </a>
                    </div>
                    
                    <div class="border-t border-gray-200 dark:border-gray-700 my-2 pt-2">
                        <p class="px-3 py-1 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider">Users</p>
                        <a href="../dashboards/superadmin_list_students.php" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 <?= $current_page === 'superadmin_list_students.php' ? 'bg-gray-100 dark:bg-gray-700 text-primary dark:text-primary-light' : '' ?>">
                            <i class="fas fa-user-graduate mr-2"></i> Students
                        </a>
                        <a href="../dashboards/superadmin_list_faculty.php" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 <?= $current_page === 'superadmin_list_faculty.php' ? 'bg-gray-100 dark:bg-gray-700 text-primary dark:text-primary-light' : '' ?>">
                            <i class="fas fa-chalkboard-teacher mr-2"></i> Faculty
                        </a>
                        <a href="../dashboards/superadmin_list_alumni.php" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 <?= $current_page === 'superadmin_list_alumni.php' ? 'bg-gray-100 dark:bg-gray-700 text-primary dark:text-primary-light' : '' ?>">
                            <i class="fas fa-user-tie mr-2"></i> Alumni
                        </a>
                    </div>
                    
                    <div class="border-t border-gray-200 dark:border-gray-700 my-2 pt-2">
                        <p class="px-3 py-1 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider">System</p>
                        <a href="../dashboards/platform_analytics.php" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 <?= $current_page === 'platform_analytics.php' ? 'bg-gray-100 dark:bg-gray-700 text-primary dark:text-primary-light' : '' ?>">
                            <i class="fas fa-chart-line mr-2"></i> Analytics
                        </a>
                        <a href="../dashboards/system_check.php" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 <?= $current_page === 'system_check.php' ? 'bg-gray-100 dark:bg-gray-700 text-primary dark:text-primary-light' : '' ?>">
                            <i class="fas fa-sync-alt mr-2"></i> System Check
                        </a>
                        <a href="../dashboards/system_settings.php" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 <?= $current_page === 'system_settings.php' ? 'bg-gray-100 dark:bg-gray-700 text-primary dark:text-primary-light' : '' ?>">
                            <i class="fas fa-cog mr-2"></i> Settings
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content Area -->
    <div class="flex flex-1 overflow-hidden">
        <!-- Sidebar Navigation -->
        <aside class="hidden lg:block w-64 border-r border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 overflow-y-auto">
            <div class="px-6 pt-6 pb-4">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-1">Super Admin</h2>
                <p class="text-sm text-gray-500 dark:text-gray-400">Platform Management</p>
            </div>
            <nav class="mt-4 px-3 space-y-1">
                <div class="mb-6">
                    <h3 class="px-3 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider">Main</h3>
                    <div class="mt-2 space-y-1">
                        <a href="../dashboards/superadmin_dashboard.php" class="sidenav-item flex items-center px-3 py-2 text-sm font-medium <?= $current_page === 'superadmin_dashboard.php' ? 'active' : '' ?>">
                            <i class="fas fa-th-large w-5 h-5 mr-2"></i>
                            <span>Dashboard</span>
                        </a>
                        <a href="../dashboards/platform_analytics.php" class="sidenav-item flex items-center px-3 py-2 text-sm font-medium <?= $current_page === 'platform_analytics.php' ? 'active' : '' ?>">
                            <i class="fas fa-chart-line w-5 h-5 mr-2"></i>
                            <span>Analytics</span>
                        </a>
                    </div>
                </div>
                
                <div class="mb-6">
                    <h3 class="px-3 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider">College Management</h3>
                    <div class="mt-2 space-y-1">
                        <a href="../dashboards/list_colleges.php" class="sidenav-item flex items-center px-3 py-2 text-sm font-medium <?= $current_page === 'list_colleges.php' ? 'active' : '' ?>">
                            <i class="fas fa-university w-5 h-5 mr-2"></i>
                            <span>All Colleges</span>
                        </a>
                        <a href="../dashboards/create_college.php" class="sidenav-item flex items-center px-3 py-2 text-sm font-medium <?= $current_page === 'create_college.php' ? 'active' : '' ?>">
                            <i class="fas fa-plus-circle w-5 h-5 mr-2"></i>
                            <span>Add College</span>
                        </a>
                        <a href="../dashboards/list_admins.php" class="sidenav-item flex items-center px-3 py-2 text-sm font-medium <?= $current_page === 'list_admins.php' ? 'active' : '' ?>">
                            <i class="fas fa-user-shield w-5 h-5 mr-2"></i>
                            <span>College Admins</span>
                        </a>
                        <a href="../dashboards/assign_admin.php" class="sidenav-item flex items-center px-3 py-2 text-sm font-medium <?= $current_page === 'assign_admin.php' ? 'active' : '' ?>">
                            <i class="fas fa-user-plus w-5 h-5 mr-2"></i>
                            <span>Assign Admin</span>
                        </a>
                    </div>
                </div>
                
                <div class="mb-6">
                    <h3 class="px-3 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider">User Management</h3>
                    <div class="mt-2 space-y-1">
                        <a href="../dashboards/superadmin_list_students.php" class="sidenav-item flex items-center px-3 py-2 text-sm font-medium <?= $current_page === 'superadmin_list_students.php' || strpos($current_page, 'superadmin_view_student') !== false || strpos($current_page, 'superadmin_edit_student') !== false ? 'active' : '' ?>">
                            <i class="fas fa-user-graduate w-5 h-5 mr-2"></i>
                            <span>Students</span>
                        </a>
                        <a href="../dashboards/superadmin_list_faculty.php" class="sidenav-item flex items-center px-3 py-2 text-sm font-medium <?= $current_page === 'superadmin_list_faculty.php' || strpos($current_page, 'superadmin_view_faculty') !== false || strpos($current_page, 'superadmin_edit_faculty') !== false ? 'active' : '' ?>">
                            <i class="fas fa-chalkboard-teacher w-5 h-5 mr-2"></i>
                            <span>Faculty</span>
                        </a>
                        <a href="../dashboards/superadmin_list_alumni.php" class="sidenav-item flex items-center px-3 py-2 text-sm font-medium <?= $current_page === 'superadmin_list_alumni.php' || strpos($current_page, 'superadmin_view_alumni') !== false || strpos($current_page, 'superadmin_edit_alumni') !== false ? 'active' : '' ?>">
                            <i class="fas fa-user-tie w-5 h-5 mr-2"></i>
                            <span>Alumni</span>
                        </a>
                    </div>
                </div>
                
                <div class="mb-6">
                    <h3 class="px-3 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider">System</h3>
                    <div class="mt-2 space-y-1">
                        <a href="../dashboards/system_check.php" class="sidenav-item flex items-center px-3 py-2 text-sm font-medium <?= $current_page === 'system_check.php' ? 'active' : '' ?>">
                            <i class="fas fa-sync-alt w-5 h-5 mr-2"></i>
                            <span>System Check</span>
                        </a>
                        <a href="../dashboards/system_settings.php" class="sidenav-item flex items-center px-3 py-2 text-sm font-medium <?= $current_page === 'system_settings.php' ? 'active' : '' ?>">
                            <i class="fas fa-cog w-5 h-5 mr-2"></i>
                            <span>Settings</span>
                        </a>
                    </div>
                </div>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="flex-1 bg-gray-50 dark:bg-gray-900 overflow-auto p-6">
        
<script>
// User dropdown menu functionality
document.addEventListener('DOMContentLoaded', function() {
    const userMenuButton = document.getElementById('userMenuButton');
    const userMenuDropdown = document.getElementById('userMenuDropdown');
    const userMenu = document.getElementById('userMenu');
    
    // Function to show the dropdown
    function showDropdown() {
        userMenuDropdown.classList.remove('hidden', 'opacity-0', 'scale-95');
        userMenuDropdown.classList.add('opacity-100', 'scale-100');
    }
    
    // Function to hide the dropdown
    function hideDropdown() {
        userMenuDropdown.classList.add('opacity-0', 'scale-95');
        setTimeout(() => {
            userMenuDropdown.classList.add('hidden');
        }, 200);
    }
    
    // Toggle dropdown on button click
    userMenuButton.addEventListener('click', function(e) {
        e.stopPropagation();
        if (userMenuDropdown.classList.contains('hidden')) {
            showDropdown();
        } else {
            hideDropdown();
        }
    });
    
    // Show dropdown on hover (only on non-touch devices)
    if (window.matchMedia('(hover: hover)').matches) {
        userMenu.addEventListener('mouseenter', showDropdown);
        userMenu.addEventListener('mouseleave', hideDropdown);
    }
    
    // Hide dropdown when clicking outside
    document.addEventListener('click', function(e) {
        if (!userMenu.contains(e.target)) {
            hideDropdown();
        }
    });
    
    // Theme toggle functionality
    const themeToggle = document.getElementById('themeToggle');
    
    themeToggle.addEventListener('click', function() {
        document.documentElement.classList.toggle('dark');
        localStorage.setItem('theme', document.documentElement.classList.contains('dark') ? 'dark' : 'light');
    });
    
    // Set the initial theme based on user preference or system preference
    if (localStorage.theme === 'dark' || (!('theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
        document.documentElement.classList.add('dark');
    } else {
        document.documentElement.classList.remove('dark');
    }
    
    // Mobile menu functionality
    const mobileMenuButton = document.getElementById('mobileMenuButton');
    const mobileMenu = document.getElementById('mobileMenu');
    
    mobileMenuButton.addEventListener('click', function() {
        mobileMenu.classList.toggle('hidden');
    });
});
</script>

