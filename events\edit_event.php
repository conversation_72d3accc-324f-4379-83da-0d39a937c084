<?php
ini_set('display_errors', 1);
error_reporting(E_ALL);

require_once '../includes/db.php';

// Check session
if (!isset($_SESSION['faculty_id']) && !isset($_SESSION['alumni_id']) && !isset($_SESSION['collegeadmin_id'])) {
    // Redirect to appropriate login page instead of logout
    if (isset($_SERVER['HTTP_REFERER']) && strpos($_SERVER['HTTP_REFERER'], 'collegeadmin') !== false) {
        header("Location: ../auth/collegeadmin_login.php");
    } else if (isset($_SERVER['HTTP_REFERER']) && strpos($_SERVER['HTTP_REFERER'], 'faculty') !== false) {
        header("Location: ../auth/faculty_login.php");
    } else if (isset($_SERVER['HTTP_REFERER']) && strpos($_SERVER['HTTP_REFERER'], 'alumni') !== false) {
        header("Location: ../auth/alumni_login.php");
    } else {
        header("Location: ../auth/collegeadmin_login.php");
    }
    exit;
}

$college_id = $_SESSION['college_id'];

// Set role explicitly for the sidebar
if (isset($_SESSION['collegeadmin_id'])) {
    $role = 'collegeadmin';
    $user_id = $_SESSION['collegeadmin_id'];
    $user_name = $_SESSION['collegeadmin_name'] ?? 'Admin';
} elseif (isset($_SESSION['faculty_id'])) {
    $role = 'faculty';
    $user_id = $_SESSION['faculty_id'];
    $user_name = $_SESSION['faculty_name'] ?? 'Faculty';
} elseif (isset($_SESSION['alumni_id'])) {
    $role = 'alumni';
    $user_id = $_SESSION['alumni_id'];
    $user_name = $_SESSION['alumni_name'] ?? 'Alumni';
}
$current_page = basename($_SERVER['PHP_SELF']);

require_once '../includes/header.php';
require_once '../vendor/autoload.php';
require_once '../vendor/phpmailer/phpmailer/src/PHPMailer.php';
require_once '../vendor/phpmailer/phpmailer/src/SMTP.php';
require_once '../vendor/phpmailer/phpmailer/src/Exception.php';

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;
use Google_Client;
use Google_Service_Calendar;
use Google_Service_Calendar_Event;

// Google Auth Required
if (!isset($_SESSION['google_access_token'])) {
    header("Location: google_login.php");
    exit;
}

// Get event ID from URL
$event_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if (!$event_id) {
    $_SESSION['flash_message'] = "Invalid event ID.";
    header("Location: event_list.php");
    exit;
}

// Get event details
$stmt = $conn->prepare("SELECT * FROM events WHERE id = ? AND college_id = ?");
$stmt->execute([$event_id, $college_id]);
$event = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$event) {
    $_SESSION['flash_message'] = "Event not found or access denied.";
    header("Location: event_list.php");
    exit;
}

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $title = trim($_POST['title']);
    $description = trim($_POST['description']);
    $speaker = trim($_POST['speaker']);
    $event_date = $_POST['event_date'];
    $event_time = $_POST['event_time'];
    $google_event_id = $event['google_event_id'];

    if ($title && $event_date && $event_time) {
        // Update Google Calendar Event
        $client = new Google_Client();
        $client->setAuthConfig('../includes/google_credentials.json');
        $client->addScope(Google_Service_Calendar::CALENDAR);
        $client->setRedirectUri('https://darkviolet-vulture-501696.hostingersite.com/google_callback.php');
        $client->setAccessToken($_SESSION['google_access_token']);

        $service = new Google_Service_Calendar($client);

        try {
            // Get existing event
            $googleEvent = $service->events->get('primary', $google_event_id);

            // Update event details
            $googleEvent->setSummary($title);
            $googleEvent->setDescription($description);
            
            $startDateTime = new DateTime($event_date . ' ' . $event_time);
            $endDateTime = clone $startDateTime;
            $endDateTime->modify('+1 hour');
            
            $googleEvent->setStart([
                'dateTime' => $startDateTime->format('Y-m-d\TH:i:s'),
                'timeZone' => 'Asia/Kolkata',
            ]);
            
            $googleEvent->setEnd([
                'dateTime' => $endDateTime->format('Y-m-d\TH:i:s'),
                'timeZone' => 'Asia/Kolkata',
            ]);

            // Update the event
            $updatedEvent = $service->events->update('primary', $google_event_id, $googleEvent);
            $meet_link = $updatedEvent->getHangoutLink();

            // Update event in database
            $stmt = $conn->prepare("UPDATE events SET title = ?, description = ?, speaker_name = ?, event_date = ?, event_time = ?, meet_link = ? WHERE id = ?");
            $stmt->execute([$title, $description, $speaker, $event_date, $event_time, $meet_link, $event_id]);

            // Send update notification
            $users = [];

            $studentStmt = $conn->prepare("SELECT email FROM students WHERE college_id = ? AND verified = 1");
            $studentStmt->execute([$college_id]);
            $users = array_merge($users, $studentStmt->fetchAll(PDO::FETCH_COLUMN));

            $alumniStmt = $conn->prepare("SELECT email FROM alumni WHERE college_id = ? AND verified = 1");
            $alumniStmt->execute([$college_id]);
            $users = array_merge($users, $alumniStmt->fetchAll(PDO::FETCH_COLUMN));

            $facultyStmt = $conn->prepare("SELECT email FROM faculties WHERE college_id = ?");
            $facultyStmt->execute([$college_id]);
            $users = array_merge($users, $facultyStmt->fetchAll(PDO::FETCH_COLUMN));

            $mail = new PHPMailer(true);
            try {
                $mail->isSMTP();
                $mail->Host = 'smtp.gmail.com';
                $mail->SMTPAuth = true;
                $mail->Username = '<EMAIL>';
                $mail->Password = 'xoqrvepcjjpuycgo';
                $mail->SMTPSecure = 'tls';
                $mail->Port = 587;

                $mail->setFrom('<EMAIL>', 'Connect My Students');
                $mail->isHTML(true);
                $mail->Subject = "📢 Event Updated: " . $title;
                $mail->Body = "
                    <h3>📢 Event Update!</h3>
                    <p><strong>Title:</strong> {$title}</p>
                    <p><strong>Speaker:</strong> {$speaker}</p>
                    <p><strong>Date:</strong> {$event_date}</p>
                    <p><strong>Time:</strong> {$event_time}</p>
                    <p><strong>Join Meeting:</strong> <a href='{$meet_link}'>Click Here</a></p>
                    <br>
                    <a href='https://darkviolet-vulture-501696.hostingersite.com/events/event_list.php' style='background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>View Events</a>
                ";

                foreach ($users as $email) {
                    $mail->addAddress($email);
                }

                $mail->send();
            } catch (Exception $e) {
                // Silent fail
            }

            $success = "Event updated successfully with Google Meet and notifications sent.";
            
            // Refresh event data
            $stmt = $conn->prepare("SELECT * FROM events WHERE id = ?");
            $stmt->execute([$event_id]);
            $event = $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            $error = "Error updating event: " . $e->getMessage();
        }
    } else {
        $error = "Please fill in all required fields.";
    }
}
?>

<!-- Main Content -->
<main class="flex-grow py-8 px-4">
    <div class="container mx-auto">
        <!-- Breadcrumb -->
        <nav class="mb-6 text-sm" aria-label="Breadcrumb">
            <ol class="flex items-center space-x-2">
                <li>
                    <a href="../dashboards/collegeadmin_dashboard.php" class="text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary-light">
                        <i class="fas fa-home"></i>
                        <span class="sr-only">Dashboard</span>
                    </a>
                </li>
                <li class="flex items-center">
                    <i class="fas fa-chevron-right text-gray-400 text-xs mx-1"></i>
                    <a href="event_list.php" class="text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary-light">
                        Events
                    </a>
                </li>
                <li class="flex items-center">
                    <i class="fas fa-chevron-right text-gray-400 text-xs mx-1"></i>
                    <span class="text-gray-700 dark:text-gray-300">Edit Event</span>
                </li>
            </ol>
        </nav>

        <div class="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                    <i class="fas fa-edit text-primary mr-3"></i>
                    Edit Event
                </h1>
                <p class="text-gray-600 dark:text-gray-400 mt-1">
                    Update event details and Google Meet integration
                </p>
            </div>
            <div class="mt-4 md:mt-0">
                <a href="event_list.php" class="btn btn-outline flex items-center">
                    <i class="fas fa-list mr-2"></i> Back to Events
                </a>
            </div>
        </div>

        <?php if (!empty($error)): ?>
            <div class="bg-red-50 dark:bg-red-900/30 border-l-4 border-red-500 text-red-700 dark:text-red-400 p-4 mb-6 flex justify-between items-center" role="alert">
                <div class="flex items-center">
                    <i class="fas fa-exclamation-circle mr-2"></i>
                    <span><?= htmlspecialchars($error) ?></span>
                </div>
                <button type="button" class="text-red-700 dark:text-red-400 hover:opacity-75" onclick="this.parentElement.style.display='none';">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        <?php endif; ?>

        <?php if (!empty($success)): ?>
            <div class="bg-green-50 dark:bg-green-900/30 border-l-4 border-green-500 text-green-700 dark:text-green-400 p-4 mb-6 flex justify-between items-center" role="alert">
                <div class="flex items-center">
                    <i class="fas fa-check-circle mr-2"></i>
                    <span><?= htmlspecialchars($success) ?></span>
                </div>
                <button type="button" class="text-green-700 dark:text-green-400 hover:opacity-75" onclick="this.parentElement.style.display='none';">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        <?php endif; ?>

        <!-- Event Form -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700">
            <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                <h2 class="text-lg font-semibold text-gray-800 dark:text-white flex items-center">
                    <i class="fas fa-calendar-alt text-primary mr-2"></i> Event Details
                </h2>
                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    Update the details below to modify this event
                </p>
            </div>
            
            <div class="p-6">
                <form method="post" id="eventForm">
                    <div class="mb-6">
                        <label for="title" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Event Title <span class="text-red-500">*</span>
                        </label>
                        <input type="text" id="title" name="title" 
                            class="form-input w-full" 
                            value="<?= htmlspecialchars($event['title']) ?>"
                            placeholder="Enter a clear, descriptive title" required>
                    </div>
                    
                    <div class="mb-6">
                        <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Description
                        </label>
                        <textarea id="description" name="description" rows="4" 
                            class="form-input w-full" 
                            placeholder="Provide details about the event..."><?= htmlspecialchars($event['description']) ?></textarea>
                    </div>
                    
                    <div class="mb-6">
                        <label for="speaker" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Speaker Name
                        </label>
                        <input type="text" id="speaker" name="speaker" 
                            class="form-input w-full" 
                            value="<?= htmlspecialchars($event['speaker_name']) ?>"
                            placeholder="Name of the presenter or speaker">
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div>
                            <label for="event_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Event Date <span class="text-red-500">*</span>
                            </label>
                            <input type="date" id="event_date" name="event_date" 
                                class="form-input w-full" 
                                value="<?= htmlspecialchars($event['event_date']) ?>"
                                required>
                        </div>
                        
                        <div>
                            <label for="event_time" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Event Time <span class="text-red-500">*</span>
                            </label>
                            <input type="time" id="event_time" name="event_time" 
                                class="form-input w-full" 
                                value="<?= htmlspecialchars($event['event_time']) ?>"
                                required>
                        </div>
                    </div>
                    
                    <?php if (!empty($event['meet_link'])): ?>
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Google Meet Link
                        </label>
                        <div class="flex items-center">
                            <input type="text" readonly 
                                class="form-input w-full bg-gray-50 dark:bg-gray-700" 
                                value="<?= htmlspecialchars($event['meet_link']) ?>">
                            <a href="<?= htmlspecialchars($event['meet_link']) ?>" target="_blank" class="ml-2 btn btn-sm btn-primary">
                                <i class="fas fa-external-link-alt"></i>
                            </a>
                        </div>
                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                            This link will be updated automatically when you save changes.
                        </p>
                    </div>
                    <?php endif; ?>
                    
                    <div class="bg-blue-50 dark:bg-blue-900/20 border-l-4 border-blue-500 text-blue-700 dark:text-blue-400 p-4 mb-6">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-info-circle"></i>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm">
                                    When you update this event, all participants will receive an email notification with the updated details.
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="flex items-center justify-end space-x-3">
                        <a href="event_list.php" class="btn btn-outline">
                            <i class="fas fa-times mr-2"></i> Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save mr-2"></i> Update Event
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</main>

<?php require_once '../includes/footer.php'; ?> 