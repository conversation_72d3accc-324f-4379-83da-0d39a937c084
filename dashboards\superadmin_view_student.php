<?php
session_start();
require_once '../includes/db.php';
require_once '../includes/db_schema_check.php';

// Ensure only super admin can access this page
if (!isset($_SESSION['superadmin_id'])) {
    header("Location: ../auth/superadmin_login.php");
    exit;
}

$superadmin_id = $_SESSION['superadmin_id'];
$superadmin_name = $_SESSION['superadmin_name'] ?? 'Super Admin';
$id = $_GET['id'] ?? null;

if (!$id) {
    $_SESSION['flash_message'] = "Invalid student ID.";
    $_SESSION['flash_message_type'] = "error";
    header("Location: superadmin_list_students.php");
    exit;
}

// Get student details - no college_id restriction for super admin
$stmt = $conn->prepare("
    SELECT s.id, s.college_id, s.full_name, s.email, s.password, s.roll_number, s.department,
           s.year, s.division, s.profile_image, s.verified, s.status, s.created_at, s.profile_photo,
           s.bio, s.skills, s.goals, s.linkedin, s.github, s.twitter, s.website, s.phone,
           s.address, s.city, s.state, s.country, s.pincode, s.date_of_birth, s.gender,
           c.name as college_name
    FROM students s
    LEFT JOIN colleges c ON s.college_id = c.id
    WHERE s.id = ?
");
$stmt->execute([$id]);
$student = $stmt->fetch(PDO::FETCH_ASSOC);

// Try to get updated_at separately if the column exists
try {
    $updateStmt = $conn->prepare("SELECT updated_at FROM students WHERE id = ? LIMIT 1");
    $updateStmt->execute([$id]);
    $updateResult = $updateStmt->fetch(PDO::FETCH_ASSOC);
    if ($updateResult && isset($updateResult['updated_at'])) {
        $student['updated_at'] = $updateResult['updated_at'];
    }
} catch (PDOException $e) {
    // Column doesn't exist, that's fine - we'll handle it gracefully
    $student['updated_at'] = null;
}

if (!$student) {
    $_SESSION['flash_message'] = "Student not found.";
    $_SESSION['flash_message_type'] = "error";
    header("Location: superadmin_list_students.php");
    exit;
}

// Get student's mentorship requests if the table exists
$mentorships = [];
try {
    $mentorshipStmt = $conn->prepare("
        SELECT sr.*, a.full_name as alumni_name, a.profile_photo as alumni_photo 
        FROM student_requests sr
        JOIN alumni a ON sr.alumni_id = a.id
        WHERE sr.student_id = ?
        ORDER BY sr.created_at DESC
        LIMIT 5
    ");
    $mentorshipStmt->execute([$id]);
    $mentorships = $mentorshipStmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    // Table might not exist, ignore
}

// Profile photo handling
$photo = !empty($student['profile_photo']) && file_exists("../uploads/{$student['profile_photo']}")
    ? $student['profile_photo']
    : 'default.png';

// Include the header
require_once '../includes/header.php';
?>

<!-- Main Content -->
<div class="container mx-auto px-4 py-8 max-w-6xl">
    <!-- Page Header -->
    <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                <span class="bg-blue-500/10 dark:bg-blue-500/20 p-2 rounded-md mr-3">
                    <i class="fas fa-user-graduate text-blue-600 dark:text-blue-400"></i>
                </span>
                Student Profile
            </h1>
            <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">View detailed information about this student</p>
        </div>
        <div class="mt-4 md:mt-0 flex flex-wrap gap-2">
            <a href="superadmin_list_students.php" class="btn btn-outline flex items-center">
                <i class="fas fa-arrow-left mr-2"></i> Back to Students
            </a>
            <a href="superadmin_edit_student.php?id=<?= $student['id'] ?>" class="btn btn-primary flex items-center">
                <i class="fas fa-edit mr-2"></i> Edit Profile
            </a>
        </div>
    </div>
    
    <?php if (isset($_SESSION['flash_message'])): ?>
    <div class="mb-6 bg-<?= isset($_SESSION['flash_message_type']) ? $_SESSION['flash_message_type'] : 'success' ?>-50 dark:bg-<?= isset($_SESSION['flash_message_type']) ? $_SESSION['flash_message_type'] : 'success' ?>-900/30 border-l-4 border-<?= isset($_SESSION['flash_message_type']) ? $_SESSION['flash_message_type'] : 'success' ?>-500 text-<?= isset($_SESSION['flash_message_type']) ? $_SESSION['flash_message_type'] : 'success' ?>-700 dark:text-<?= isset($_SESSION['flash_message_type']) ? $_SESSION['flash_message_type'] : 'success' ?>-400 p-4 flex justify-between items-center" role="alert">
        <div>
            <i class="fas fa-<?= isset($_SESSION['flash_message_type']) && $_SESSION['flash_message_type'] === 'error' ? 'exclamation-circle' : 'check-circle' ?> mr-2"></i>
            <?= $_SESSION['flash_message'] ?>
        </div>
        <button type="button" class="text-<?= isset($_SESSION['flash_message_type']) ? $_SESSION['flash_message_type'] : 'success' ?>-700 dark:text-<?= isset($_SESSION['flash_message_type']) ? $_SESSION['flash_message_type'] : 'success' ?>-400 hover:text-<?= isset($_SESSION['flash_message_type']) ? $_SESSION['flash_message_type'] : 'success' ?>-900" onclick="this.parentElement.style.display='none';">
            <i class="fas fa-times"></i>
        </button>
    </div>
    <?php 
    unset($_SESSION['flash_message']);
    unset($_SESSION['flash_message_type']);
    endif; 
    ?>
    
    <!-- Profile Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Left Column - Profile Card -->
        <div class="lg:col-span-1">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 overflow-hidden">
                <div class="p-6 text-center">
                    <div class="relative w-32 h-32 mx-auto mb-4">
                        <?php if (file_exists("../uploads/{$photo}")): ?>
                            <img src="../uploads/<?= $photo ?>" alt="<?= htmlspecialchars($student['full_name']) ?>" class="w-32 h-32 rounded-full object-cover border-4 border-white dark:border-gray-700 shadow-md">
                        <?php else: ?>
                            <div class="w-32 h-32 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center text-blue-500 text-4xl border-4 border-white dark:border-gray-700 shadow-md">
                                <?= strtoupper(substr($student['full_name'] ?? 'S', 0, 1)) ?>
                            </div>
                        <?php endif; ?>
                        
                        <span class="absolute bottom-0 right-0 w-8 h-8 rounded-full bg-<?= $student['verified'] ? 'green' : 'yellow' ?>-500 border-4 border-white dark:border-gray-700 flex items-center justify-center text-white">
                            <i class="fas fa-<?= $student['verified'] ? 'check' : 'clock' ?> text-sm"></i>
                        </span>
                    </div>
                    
                    <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-1"><?= htmlspecialchars($student['full_name']) ?></h2>
                    <p class="text-gray-500 dark:text-gray-400 mb-3"><?= htmlspecialchars($student['email']) ?></p>
                    
                    <div class="flex justify-center space-x-2 mb-4">
                        <?php if (!empty($student['linkedin_url'])): ?>
                        <a href="<?= htmlspecialchars($student['linkedin_url']) ?>" target="_blank" class="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-full text-blue-600 dark:text-blue-400 hover:bg-blue-200 dark:hover:bg-blue-800/50 transition-colors">
                            <i class="fab fa-linkedin-in"></i>
                        </a>
                        <?php endif; ?>
                        
                        <?php if (!empty($student['github_url'])): ?>
                        <a href="<?= htmlspecialchars($student['github_url']) ?>" target="_blank" class="p-2 bg-gray-100 dark:bg-gray-700 rounded-full text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
                            <i class="fab fa-github"></i>
                        </a>
                        <?php endif; ?>
                        
                        <?php if (!empty($student['portfolio_url'])): ?>
                        <a href="<?= htmlspecialchars($student['portfolio_url']) ?>" target="_blank" class="p-2 bg-purple-100 dark:bg-purple-900/30 rounded-full text-purple-600 dark:text-purple-400 hover:bg-purple-200 dark:hover:bg-purple-800/50 transition-colors">
                            <i class="fas fa-globe"></i>
                        </a>
                        <?php endif; ?>
                    </div>
                    
                    <div class="border-t border-gray-200 dark:border-gray-700 pt-4 mt-2">
                        <div class="flex justify-between items-center mb-2">
                            <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Status:</span>
                            <span class="px-2 py-1 rounded-full text-xs font-medium <?= $student['verified'] ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400' : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400' ?>">
                                <?= $student['verified'] ? 'Verified' : 'Pending Verification' ?>
                            </span>
                        </div>
                        <div class="flex justify-between items-center mb-2">
                            <span class="text-sm font-medium text-gray-500 dark:text-gray-400">College:</span>
                            <span class="text-sm text-gray-900 dark:text-white"><?= htmlspecialchars($student['college_name'] ?? 'Not assigned') ?></span>
                        </div>
                        <div class="flex justify-between items-center mb-2">
                            <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Department:</span>
                            <span class="text-sm text-gray-900 dark:text-white"><?= htmlspecialchars($student['department'] ?? 'Not specified') ?></span>
                        </div>
                        <div class="flex justify-between items-center mb-2">
                            <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Year:</span>
                            <span class="text-sm text-gray-900 dark:text-white"><?= htmlspecialchars($student['year'] ?? 'Not specified') ?></span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Joined:</span>
                            <span class="text-sm text-gray-900 dark:text-white"><?= date('M j, Y', strtotime($student['created_at'])) ?></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Right Column - Details -->
        <div class="lg:col-span-2">
            <!-- Tabs -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 mb-6">
                <div class="border-b border-gray-200 dark:border-gray-700">
                    <ul class="flex flex-wrap -mb-px">
                        <li class="mr-2">
                            <a href="#" class="inline-block py-4 px-4 text-sm font-medium text-center border-b-2 border-primary text-primary dark:text-primary-light dark:border-primary-light rounded-t-lg active" onclick="showTab('profile'); return false;">Profile</a>
                        </li>
                        <li class="mr-2">
                            <a href="#" class="inline-block py-4 px-4 text-sm font-medium text-center text-gray-500 dark:text-gray-400 border-b-2 border-transparent hover:text-gray-600 hover:border-gray-300 dark:hover:text-gray-300 rounded-t-lg" onclick="showTab('mentorships'); return false;">Mentorships</a>
                        </li>
                        <li class="mr-2">
                            <a href="#" class="inline-block py-4 px-4 text-sm font-medium text-center text-gray-500 dark:text-gray-400 border-b-2 border-transparent hover:text-gray-600 hover:border-gray-300 dark:hover:text-gray-300 rounded-t-lg" onclick="showTab('activity'); return false;">Activity</a>
                        </li>
                    </ul>
                </div>
                
                <!-- Tab Content -->
                <div class="p-6">
                    <!-- Profile Tab -->
                    <div id="profile-tab" class="tab-content">
                        <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4">Personal Information</h3>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Full Name</label>
                                <p class="text-gray-900 dark:text-white"><?= htmlspecialchars($student['full_name']) ?></p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Email Address</label>
                                <p class="text-gray-900 dark:text-white"><?= htmlspecialchars($student['email']) ?></p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Phone Number</label>
                                <p class="text-gray-900 dark:text-white"><?= !empty($student['phone']) ? htmlspecialchars($student['phone']) : 'Not provided' ?></p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Roll Number</label>
                                <p class="text-gray-900 dark:text-white"><?= !empty($student['roll_number']) ? htmlspecialchars($student['roll_number']) : 'Not provided' ?></p>
                            </div>
                        </div>
                        
                        <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4">Academic Information</h3>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">College</label>
                                <p class="text-gray-900 dark:text-white"><?= htmlspecialchars($student['college_name'] ?? 'Not assigned') ?></p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Department</label>
                                <p class="text-gray-900 dark:text-white"><?= !empty($student['department']) ? htmlspecialchars($student['department']) : 'Not specified' ?></p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Year</label>
                                <p class="text-gray-900 dark:text-white"><?= !empty($student['year']) ? htmlspecialchars($student['year']) : 'Not specified' ?></p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Graduation Year</label>
                                <p class="text-gray-900 dark:text-white"><?= !empty($student['graduation_year']) ? htmlspecialchars($student['graduation_year']) : 'Not specified' ?></p>
                            </div>
                        </div>
                        
                        <?php if (!empty($student['bio'])): ?>
                        <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4">Bio</h3>
                        <div class="bg-gray-50 dark:bg-gray-750 p-4 rounded-lg mb-6">
                            <p class="text-gray-700 dark:text-gray-300 whitespace-pre-line"><?= nl2br(htmlspecialchars($student['bio'])) ?></p>
                        </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($student['skills'])): ?>
                        <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4">Skills</h3>
                        <div class="flex flex-wrap gap-2 mb-6">
                            <?php foreach (explode(',', $student['skills']) as $skill): ?>
                                <span class="px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400">
                                    <?= htmlspecialchars(trim($skill)) ?>
                                </span>
                            <?php endforeach; ?>
                        </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($student['interests'])): ?>
                        <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4">Interests</h3>
                        <div class="flex flex-wrap gap-2 mb-6">
                            <?php foreach (explode(',', $student['interests']) as $interest): ?>
                                <span class="px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400">
                                    <?= htmlspecialchars(trim($interest)) ?>
                                </span>
                            <?php endforeach; ?>
                        </div>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Mentorships Tab -->
                    <div id="mentorships-tab" class="tab-content hidden">
                        <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4">Mentorship Requests</h3>
                        
                        <?php if (count($mentorships) > 0): ?>
                            <div class="space-y-4">
                                <?php foreach ($mentorships as $mentorship): ?>
                                    <div class="bg-gray-50 dark:bg-gray-750 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center">
                                                <?php if (!empty($mentorship['alumni_photo']) && file_exists("../uploads/{$mentorship['alumni_photo']}")): ?>
                                                    <img src="../uploads/<?= $mentorship['alumni_photo'] ?>" alt="<?= htmlspecialchars($mentorship['alumni_name']) ?>" class="w-10 h-10 rounded-full object-cover mr-3">
                                                <?php else: ?>
                                                    <div class="w-10 h-10 rounded-full bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center text-purple-500 mr-3">
                                                        <?= strtoupper(substr($mentorship['alumni_name'] ?? 'A', 0, 1)) ?>
                                                    </div>
                                                <?php endif; ?>
                                                <div>
                                                    <h4 class="font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($mentorship['alumni_name']) ?></h4>
                                                    <p class="text-sm text-gray-500 dark:text-gray-400">
                                                        <?= date('M j, Y', strtotime($mentorship['created_at'])) ?>
                                                    </p>
                                                </div>
                                            </div>
                                            <span class="px-2 py-1 rounded-full text-xs font-medium 
                                                <?php
                                                switch ($mentorship['status']) {
                                                    case 'approved':
                                                        echo 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400';
                                                        break;
                                                    case 'pending':
                                                        echo 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400';
                                                        break;
                                                    case 'rejected':
                                                        echo 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400';
                                                        break;
                                                    default:
                                                        echo 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
                                                }
                                                ?>">
                                                <?= ucfirst($mentorship['status']) ?>
                                            </span>
                                        </div>
                                        <?php if (!empty($mentorship['message'])): ?>
                                            <div class="mt-3 text-sm text-gray-700 dark:text-gray-300">
                                                <p class="italic">"<?= htmlspecialchars($mentorship['message']) ?>"</p>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-8">
                                <div class="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <i class="fas fa-handshake text-gray-400 text-xl"></i>
                                </div>
                                <h4 class="text-gray-900 dark:text-white font-medium mb-1">No Mentorship Requests</h4>
                                <p class="text-gray-500 dark:text-gray-400">This student hasn't requested any mentorships yet.</p>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Activity Tab -->
                    <div id="activity-tab" class="tab-content hidden">
                        <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4">Recent Activity</h3>
                        
                        <div class="text-center py-8">
                            <div class="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-chart-line text-gray-400 text-xl"></i>
                            </div>
                            <h4 class="text-gray-900 dark:text-white font-medium mb-1">Activity Tracking Coming Soon</h4>
                            <p class="text-gray-500 dark:text-gray-400">We're working on tracking student activities on the platform.</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Admin Actions Card -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700">
                <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
                    <h3 class="font-semibold text-gray-800 dark:text-white">Admin Actions</h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <a href="superadmin_edit_student.php?id=<?= $student['id'] ?>" class="btn btn-outline flex items-center justify-center">
                            <i class="fas fa-edit mr-2"></i> Edit Profile
                        </a>
                        <?php if ($student['verified']): ?>
                            <form action="verify_student.php" method="post" class="inline-block">
                                <input type="hidden" name="student_id" value="<?= $student['id'] ?>">
                                <input type="hidden" name="action" value="unverify">
                                <button type="submit" class="btn btn-outline w-full flex items-center justify-center text-yellow-600 border-yellow-600 hover:bg-yellow-50 dark:text-yellow-400 dark:border-yellow-400 dark:hover:bg-yellow-900/20">
                                    <i class="fas fa-user-clock mr-2"></i> Mark as Unverified
                                </button>
                            </form>
                        <?php else: ?>
                            <form action="verify_student.php" method="post" class="inline-block">
                                <input type="hidden" name="student_id" value="<?= $student['id'] ?>">
                                <input type="hidden" name="action" value="verify">
                                <button type="submit" class="btn btn-outline w-full flex items-center justify-center text-green-600 border-green-600 hover:bg-green-50 dark:text-green-400 dark:border-green-400 dark:hover:bg-green-900/20">
                                    <i class="fas fa-user-check mr-2"></i> Verify Student
                                </button>
                            </form>
                        <?php endif; ?>
                        <button type="button" onclick="resetPassword(<?= $student['id'] ?>)" class="btn btn-outline flex items-center justify-center text-blue-600 border-blue-600 hover:bg-blue-50 dark:text-blue-400 dark:border-blue-400 dark:hover:bg-blue-900/20">
                            <i class="fas fa-key mr-2"></i> Reset Password
                        </button>
                        <button type="button" onclick="confirmDelete(<?= $student['id'] ?>)" class="btn btn-outline flex items-center justify-center text-red-600 border-red-600 hover:bg-red-50 dark:text-red-400 dark:border-red-400 dark:hover:bg-red-900/20">
                            <i class="fas fa-trash-alt mr-2"></i> Delete Student
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Tab functionality
function showTab(tabName) {
    // Hide all tabs
    document.querySelectorAll('.tab-content').forEach(tab => {
        tab.classList.add('hidden');
    });
    
    // Show selected tab
    document.getElementById(tabName + '-tab').classList.remove('hidden');
    
    // Update tab buttons
    document.querySelectorAll('ul li a').forEach(link => {
        link.classList.remove('text-primary', 'dark:text-primary-light', 'border-primary', 'dark:border-primary-light');
        link.classList.add('text-gray-500', 'dark:text-gray-400', 'border-transparent');
    });
    
    const activeLink = document.querySelector(`a[onclick="showTab('${tabName}'); return false;"]`);
    activeLink.classList.remove('text-gray-500', 'dark:text-gray-400', 'border-transparent');
    activeLink.classList.add('text-primary', 'dark:text-primary-light', 'border-primary', 'dark:border-primary-light');
}

// Reset password functionality
function resetPassword(studentId) {
    if (confirm('Are you sure you want to reset this student\'s password? They will receive an email with instructions.')) {
        // In a real implementation, this would make an AJAX call or submit a form
        alert('Password reset functionality will be implemented soon.');
    }
}

// Delete student functionality
function confirmDelete(studentId) {
    if (confirm('Are you sure you want to delete this student? This action cannot be undone.')) {
        window.location.href = `delete_student.php?id=${studentId}`;
    }
}
</script>