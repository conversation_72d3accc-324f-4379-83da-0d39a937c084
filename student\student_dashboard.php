<?php
require_once '../includes/db.php';
require_once '../includes/header.php';
require_once '../includes/gamify.php';

if (!isset($_SESSION['student_id'])) {
    header("Location: ../auth/student_login.php");
    exit;
}

$student_id = $_SESSION['student_id'];
$student_name = $_SESSION['student_name'];

$stmt = $conn->prepare("SELECT s.profile_photo, s.full_name, s.department, s.year, s.college_id, p.points 
    FROM students s 
    LEFT JOIN user_points p ON s.id = p.user_id AND p.role = 'student' 
    WHERE s.id = ?");
$stmt->execute([$student_id]);
$student = $stmt->fetch(PDO::FETCH_ASSOC);
$college_id = $student['college_id'];
$photo = (!empty($student['profile_photo']) && file_exists("../uploads/{$student['profile_photo']}"))
    ? $student['profile_photo']
    : 'profile.gif';
    
$xp_data = getStudentXP($conn, $student_id);
$xp = $xp_data['xp'];
$level = $xp_data['level'];
$badges = getStudentBadges($conn, $student_id);
?>

<h2 class="mb-4">Welcome back, <?= htmlspecialchars($student['full_name'] ?? $student_name) ?> 👋</h2>

<!-- 🧑 Profile Summary -->
<div class="card mb-4 p-3 shadow-sm">
    <div class="row align-items-center">
        <div class="col-md-2 text-center">
            <img src="../uploads/<?= $photo ?>" width="80" height="80" class="rounded-circle border shadow-sm">
        </div>
        <div class="col-md-7">
            <h5><?= htmlspecialchars($student['full_name'] ?? '') ?></h5>
            <small><?= htmlspecialchars($student['department'] ?? '') ?>, Year <?= htmlspecialchars($student['year'] ?? '') ?></small>
        </div>
        <div class="col-md-3 text-end">
            <span class="badge bg-success fs-6">XP: <?= $xp ?></span><br>
            <a href="student_profile.php" class="btn btn-sm btn-outline-primary mt-2">Edit Profile</a>
        </div>
    </div>
</div>

<!-- ⚡ Quick Actions -->
<div class="row text-center mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white shadow mb-3">
            <div class="card-body">
                <h6>Find Alumni</h6>
                <p class="small">Connect with verified mentors</p>
                <a href="view_alumni.php" class="btn btn-light btn-sm">Browse Alumni</a>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white shadow mb-3">
            <div class="card-body">
                <h6>Mentorship</h6>
                <p class="small">Check your request status</p>
                <a href="my_requests.php" class="btn btn-light btn-sm">My Requests</a>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-dark text-white shadow mb-3">
            <div class="card-body">
                <h6>Study Resources</h6>
                <p class="small">View and download files</p>
                <a href="../resources/resource_list.php" class="btn btn-light btn-sm">Browse Resources</a>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white shadow mb-3">
            <div class="card-body">
                <h6>Upcoming Events</h6>
                <p class="small">Webinars, talks, alumni sessions</p>
                <a href="../events/event_list.php" class="btn btn-light btn-sm">Check Events</a>
            </div>
        </div>
    </div>
</div>

<!-- 📩 Mentorship Request Summary -->
<?php
$req = $conn->prepare("SELECT status, COUNT(*) as total FROM student_requests WHERE student_id = ? GROUP BY status");
$req->execute([$student_id]);
$status_counts = ['pending' => 0, 'accepted' => 0, 'rejected' => 0];
foreach ($req->fetchAll(PDO::FETCH_ASSOC) as $row) {
    $status_counts[$row['status']] = $row['total'];
}
?>
<div class="card shadow-sm mb-4">
    <div class="card-header bg-light fw-bold">📩 Your Mentorship Requests</div>
    <div class="card-body">
        <div class="row text-center">
            <div class="col-md-4"><h6 class="text-primary mb-0">Pending</h6><p><?= $status_counts['pending'] ?> request(s)</p></div>
            <div class="col-md-4"><h6 class="text-success mb-0">Accepted</h6><p><?= $status_counts['accepted'] ?> mentor(s)</p></div>
            <div class="col-md-4"><h6 class="text-danger mb-0">Rejected</h6><p><?= $status_counts['rejected'] ?> declined</p></div>
        </div>
        <div class="text-center">
            <a href="view_alumni.php" class="btn btn-outline-primary btn-sm">Find Alumni</a>
        </div>
    </div>
</div>

<!-- 💬 Chat Access -->
<?php
$chatStmt = $conn->prepare("SELECT sr.alumni_id, a.full_name FROM student_requests sr 
    JOIN alumni a ON sr.alumni_id = a.id 
    WHERE sr.student_id = ? AND sr.status = 'accepted'");
$chatStmt->execute([$student_id]);
$chats = $chatStmt->fetchAll(PDO::FETCH_ASSOC);
?>
<?php if ($chats): ?>
<div class="card shadow-sm mb-4">
    <div class="card-header bg-light fw-bold">💬 Chat With Your Mentors</div>
    <div class="card-body">
        <ul class="list-unstyled mb-0">
            <?php foreach ($chats as $chat): ?>
            <li class="mb-3 d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">Mentor: <?= htmlspecialchars($chat['full_name']) ?></h6>
                    <p class="mb-0 text-muted small">You’ve been matched. Start the conversation!</p>
                </div>
                <a href="../chat/chat.php?alumni_id=<?= $chat['alumni_id'] ?>" class="btn btn-success btn-sm">Start Chat</a>
            </li>
            <?php endforeach; ?>
        </ul>
    </div>
</div>
<?php else: ?>
<div class="card shadow-sm mb-4">
    <div class="card-header bg-light fw-bold">💬 Chat With Your Mentors</div>
    <div class="card-body text-center">
        <p class="text-muted">No active mentorship chats at the moment.</p>
    </div>
</div>
<?php endif; ?>

<!-- 📅 Upcoming Events -->
<?php
$today = date('Y-m-d');
$eventStmt = $conn->prepare("SELECT title, speaker_name, event_date, event_time 
    FROM events 
    WHERE college_id = ? AND event_date >= ? 
    ORDER BY event_date ASC LIMIT 2");
$eventStmt->execute([$college_id, $today]);
$events = $eventStmt->fetchAll(PDO::FETCH_ASSOC);
?>
<?php if ($events): ?>
<div class="card shadow-sm mb-4">
    <div class="card-header bg-light fw-bold">📅 Upcoming Events</div>
    <div class="card-body">
        <ul class="list-unstyled mb-0">
            <?php foreach ($events as $e): ?>
            <li class="mb-3">
                <h6 class="mb-1"><?= htmlspecialchars($e['title']) ?></h6>
                <p class="mb-1 text-muted small">Speaker: <?= htmlspecialchars($e['speaker_name']) ?></p>
                <p class="mb-1 text-muted small">Date: <?= date("F j, Y", strtotime($e['event_date'])) ?> at <?= date("g:i A", strtotime($e['event_time'])) ?></p>
            </li>
            <?php endforeach; ?>
        </ul>
    </div>
</div>
<?php endif; ?>

<!-- 📢 Announcements -->
<?php
$annStmt = $conn->prepare("SELECT title, message, created_at FROM announcements 
    WHERE college_id = ? ORDER BY created_at DESC LIMIT 2");
$annStmt->execute([$college_id]);
$announcements = $annStmt->fetchAll(PDO::FETCH_ASSOC);
?>
<div class="card shadow-sm mb-4">
    <div class="card-header bg-light fw-bold">📢 College Announcements</div>
    <div class="card-body">
        <?php if ($announcements): ?>
            <ul class="list-unstyled">
                <?php foreach ($announcements as $a): ?>
                    <li class="mb-3">
                        <strong><?= htmlspecialchars($a['title']) ?></strong><br>
                        <span class="text-muted small"><?= date("F j, Y", strtotime($a['created_at'])) ?></span>
                        <p class="mb-0"><?= nl2br(htmlspecialchars($a['message'])) ?></p>
                    </li>
                <?php endforeach; ?>
            </ul>
        <?php else: ?>
            <p class="text-muted small">No announcements at the moment.</p>
        <?php endif; ?>
    </div>
</div>

<!-- 🎮 Gamification -->
<div class="card shadow-sm text-center p-3 mb-4">
    <h5>🎮 Your Gamification Progress</h5>
    <p class="text-muted">Level <?= $level ?> | <?= $xp ?> XP</p>
    <div class="progress my-2" style="height: 20px;">
        <?php
        $currentLevelXP = ($level - 1) * 100;
        $nextLevelXP = $level * 100;
        $progress = $xp - $currentLevelXP;
        $toNext = $nextLevelXP - $currentLevelXP;
        $percent = min(100, ($progress / $toNext) * 100);
        ?>
        <div class="progress-bar bg-success" role="progressbar" style="width: <?= $percent ?>%;" aria-valuenow="<?= $progress ?>" aria-valuemin="0" aria-valuemax="<?= $toNext ?>">
            <?= $toNext - $progress ?> XP to Level <?= $level + 1 ?> (<?= $progress ?> / <?= $toNext ?>)

        </div>
    </div>
    <?php if (!empty($badges)): ?>
        <div class="mt-3">
            <h6>🏅 Your Badges</h6>
            <div class="d-flex justify-content-center flex-wrap">
                <?php foreach ($badges as $badge): ?>
                    <div class="m-2 text-center">
                        <img src="../badges/<?= htmlspecialchars($badge['icon']) ?>" alt="<?= htmlspecialchars($badge['name']) ?>" width="150" height="150">
                        <div class="small"><?= htmlspecialchars($badge['name']) ?></div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    <?php else: ?>
        <p class="text-muted">No badges earned yet.</p>
    <?php endif; ?>
</div>

<!-- 🏆 Leaderboard -->
<?php
$lbStmt = $conn->prepare("SELECT s.full_name, s.profile_photo, x.xp, x.level 
    FROM students s 
    JOIN user_xp x ON s.id = x.user_id AND x.role = 'student' 
    WHERE s.college_id = ? 
    ORDER BY x.xp DESC LIMIT 5");
$lbStmt->execute([$college_id]);
$leaders = $lbStmt->fetchAll(PDO::FETCH_ASSOC);
?>
<div class="card shadow-sm mb-4">
    <div class="card-header bg-light fw-bold">🏆 Top 5 Students (Your College)</div>
    <div class="card-body">
        <?php if ($leaders): ?>
            <ol class="list-unstyled">
                <?php foreach ($leaders as $rank => $l): ?>
                    <li class="mb-2 d-flex align-items-center">
                        <span class="me-3"><?= $rank + 1 ?>.</span>
                        <img src="../uploads/<?= file_exists("../uploads/{$l['profile_photo']}") ? $l['profile_photo'] : 'default.png' ?>" class="rounded-circle me-2" width="40" height="40">
                        <strong><?= htmlspecialchars($l['full_name']) ?></strong>
                        <span class="ms-auto badge bg-primary">Level <?= $l['level'] ?> • <?= $l['xp'] ?> XP</span>
                    </li>
                <?php endforeach; ?>
            </ol>
        <?php else: ?>
            <p class="text-muted">Leaderboard will appear once students earn XP.</p>
        <?php endif; ?>
    </div>
</div>

<?php require_once '../includes/footer.php'; ?>
