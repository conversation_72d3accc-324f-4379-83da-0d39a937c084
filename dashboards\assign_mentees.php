<?php
// Include session configuration
require_once '../includes/session_config.php';
require_once '../includes/db.php';
require_once '../includes/db_schema_check.php';

if (!isset($_SESSION['collegeadmin_id'])) {
    header("Location: ../auth/collegeadmin_login.php");
    exit;
}

$college_id = $_SESSION['college_id'];
$admin_name = $_SESSION['collegeadmin_name'] ?? 'Admin';

// Set role explicitly for the sidebar
$role = 'collegeadmin';
$user_id = $_SESSION['collegeadmin_id'];
$user_name = $_SESSION['collegeadmin_name'];
$current_page = basename($_SERVER['PHP_SELF']);

// Get alumni ID from URL
$id = $_GET['id'] ?? null;

if (!$id) {
    $_SESSION['flash_message'] = "Invalid alumni ID.";
    header("Location: list_alumni.php");
    exit;
}

// Get college details
$collegeStmt = $conn->prepare("SELECT name FROM colleges WHERE id = ?");
$collegeStmt->execute([$college_id]);
$college = $collegeStmt->fetch(PDO::FETCH_ASSOC);

// Get alumni details
$stmt = $conn->prepare("SELECT * FROM alumni WHERE id = ? AND college_id = ?");
$stmt->execute([$id, $college_id]);
$alumni = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$alumni) {
    $_SESSION['flash_message'] = "Alumni not found.";
    header("Location: list_alumni.php");
    exit;
}

$error = '';
$success = '';

// Handle mentorship assignment
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['assign_mentee'])) {
    $student_id = $_POST['student_id'] ?? '';
    
    if (empty($student_id)) {
        $error = "Please select a student to assign.";
    } else {
        try {
            // Check if mentorship already exists
            $checkStmt = $conn->prepare("SELECT id FROM mentorships WHERE alumni_id = ? AND student_id = ?");
            $checkStmt->execute([$id, $student_id]);
            
            if ($checkStmt->fetch()) {
                $error = "This student is already assigned to this alumni as a mentee.";
            } else {
                // Create mentorship
                $insertStmt = $conn->prepare("
                    INSERT INTO mentorships (alumni_id, student_id, status, start_date, created_at) 
                    VALUES (?, ?, 'active', CURDATE(), NOW())
                ");
                $insertStmt->execute([$id, $student_id]);
                
                $success = "Student successfully assigned as mentee!";
            }
        } catch (PDOException $e) {
            $error = "Database error: " . $e->getMessage();
        }
    }
}

// Get available students (not already assigned to this alumni)
$studentsStmt = $conn->prepare("
    SELECT s.* 
    FROM students s 
    WHERE s.college_id = ? 
    AND s.verified = 1 
    AND s.id NOT IN (
        SELECT m.student_id 
        FROM mentorships m 
        WHERE m.alumni_id = ? AND m.status = 'active'
    )
    ORDER BY s.full_name ASC
");
$studentsStmt->execute([$college_id, $id]);
$available_students = $studentsStmt->fetchAll(PDO::FETCH_ASSOC);

// Get current mentees
$menteesStmt = $conn->prepare("
    SELECT m.*, s.full_name, s.email, s.department, s.year, s.profile_photo
    FROM mentorships m
    JOIN students s ON m.student_id = s.id
    WHERE m.alumni_id = ? AND m.status = 'active'
    ORDER BY m.created_at DESC
");
$menteesStmt->execute([$id]);
$current_mentees = $menteesStmt->fetchAll(PDO::FETCH_ASSOC);

// Include the header
require_once '../includes/header.php';
?>

    <!-- Main Content -->
    <main class="flex-grow py-6 px-4">
        <div class="max-w-7xl mx-auto">
            <!-- Page Header -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8">
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="bg-gradient-to-br from-purple-500 to-purple-600 p-3 rounded-xl shadow-lg">
                            <i class="fas fa-users text-white text-xl"></i>
                        </div>
                        <div>
                            <h1 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                                Assign Mentees
                            </h1>
                            <nav class="text-sm text-gray-600 dark:text-gray-400 mt-1" aria-label="Breadcrumb">
                                <ol class="flex items-center space-x-2">
                                    <li>
                                        <a href="collegeadmin_dashboard.php" class="hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
                                            Dashboard
                                        </a>
                                    </li>
                                    <li class="flex items-center">
                                        <i class="fas fa-chevron-right text-gray-400 text-xs mx-2"></i>
                                        <a href="list_alumni.php" class="hover:text-blue-600 dark:hover:text-blue-400 transition-colors">Alumni Management</a>
                                    </li>
                                    <li class="flex items-center">
                                        <i class="fas fa-chevron-right text-gray-400 text-xs mx-2"></i>
                                        <a href="view_alumni.php?id=<?= $id ?>" class="hover:text-blue-600 dark:hover:text-blue-400 transition-colors"><?= htmlspecialchars($alumni['full_name']) ?></a>
                                    </li>
                                    <li class="flex items-center">
                                        <i class="fas fa-chevron-right text-gray-400 text-xs mx-2"></i>
                                        <span class="text-gray-900 dark:text-white font-medium">Assign Mentees</span>
                                    </li>
                                </ol>
                            </nav>
                            <p class="text-gray-600 dark:text-gray-400 mt-2">
                                Manage mentee assignments for <span class="font-medium text-purple-600 dark:text-purple-400"><?= htmlspecialchars($alumni['full_name']) ?></span>
                            </p>
                        </div>
                    </div>
                    <div class="mt-4 lg:mt-0 flex flex-wrap gap-3">
                        <a href="view_alumni.php?id=<?= $id ?>" class="inline-flex items-center px-4 py-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-medium rounded-lg transition-colors">
                            <i class="fas fa-arrow-left mr-2"></i> Back to Profile
                        </a>
                        <a href="list_alumni.php" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors">
                            <i class="fas fa-list mr-2"></i> All Alumni
                        </a>
                    </div>
                </div>
            </div>

            <!-- Alert Messages -->
            <?php if ($error): ?>
                <div class="bg-red-50 dark:bg-red-900/50 border border-red-200 dark:border-red-800 rounded-xl p-4 mb-6">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <i class="fas fa-exclamation-circle text-red-600 dark:text-red-400"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-red-800 dark:text-red-200">Error</h3>
                            <p class="text-sm text-red-700 dark:text-red-300 mt-1"><?= htmlspecialchars($error) ?></p>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <?php if ($success): ?>
                <div class="bg-green-50 dark:bg-green-900/50 border border-green-200 dark:border-green-800 rounded-xl p-4 mb-6">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <i class="fas fa-check-circle text-green-600 dark:text-green-400"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-green-800 dark:text-green-200">Success</h3>
                            <p class="text-sm text-green-700 dark:text-green-300 mt-1"><?= htmlspecialchars($success) ?></p>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Main Content Grid -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Left Column: Assign New Mentee -->
                <div>
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                            <i class="fas fa-user-plus text-purple-600 mr-2"></i>
                            Assign New Mentee
                        </h3>
                        
                        <?php if (count($available_students) > 0): ?>
                            <form method="post" class="space-y-4">
                                <div>
                                    <label for="student_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Select Student</label>
                                    <select id="student_id" name="student_id" required
                                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:text-white">
                                        <option value="">Choose a student...</option>
                                        <?php foreach ($available_students as $student): ?>
                                            <option value="<?= $student['id'] ?>">
                                                <?= htmlspecialchars($student['full_name']) ?> 
                                                (<?= htmlspecialchars($student['department'] ?? 'N/A') ?> - Year <?= htmlspecialchars($student['year'] ?? 'N/A') ?>)
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <button type="submit" name="assign_mentee" class="w-full bg-purple-600 hover:bg-purple-700 text-white font-medium py-3 px-4 rounded-lg transition-colors">
                                    <i class="fas fa-plus mr-2"></i> Assign as Mentee
                                </button>
                            </form>
                        <?php else: ?>
                            <div class="text-center py-8">
                                <div class="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <i class="fas fa-users text-gray-400 text-2xl"></i>
                                </div>
                                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Available Students</h3>
                                <p class="text-gray-600 dark:text-gray-400">All verified students are already assigned or there are no students available for mentorship.</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Right Column: Current Mentees -->
                <div>
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                            <i class="fas fa-users text-purple-600 mr-2"></i>
                            Current Mentees (<?= count($current_mentees) ?>)
                        </h3>
                        
                        <?php if (count($current_mentees) > 0): ?>
                            <div class="space-y-4">
                                <?php foreach ($current_mentees as $mentee): ?>
                                    <div class="flex items-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                        <div class="flex-shrink-0">
                                            <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center">
                                                <?php if (!empty($mentee['profile_photo']) && file_exists("../uploads/{$mentee['profile_photo']}")): ?>
                                                    <img src="../uploads/<?= htmlspecialchars($mentee['profile_photo']) ?>" alt="<?= htmlspecialchars($mentee['full_name']) ?>" class="w-12 h-12 rounded-full object-cover">
                                                <?php else: ?>
                                                    <i class="fas fa-user text-purple-600 dark:text-purple-400"></i>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                        <div class="ml-4 flex-grow">
                                            <h4 class="font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($mentee['full_name']) ?></h4>
                                            <p class="text-sm text-gray-600 dark:text-gray-400"><?= htmlspecialchars($mentee['email']) ?></p>
                                            <p class="text-sm text-gray-500 dark:text-gray-500">
                                                <?= htmlspecialchars($mentee['department'] ?? 'N/A') ?> - Year <?= htmlspecialchars($mentee['year'] ?? 'N/A') ?>
                                            </p>
                                        </div>
                                        <div class="text-right">
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                                Active
                                            </span>
                                            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                                Since <?= date('M d, Y', strtotime($mentee['created_at'])) ?>
                                            </p>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-8">
                                <div class="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <i class="fas fa-user-friends text-gray-400 text-2xl"></i>
                                </div>
                                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Mentees Yet</h3>
                                <p class="text-gray-600 dark:text-gray-400">This alumni doesn't have any assigned mentees yet. Use the form on the left to assign students.</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </main>

<script>
function toggleDropdown(id) {
    const dropdown = document.getElementById(id);
    dropdown.classList.toggle('hidden');
}

// Close dropdown when clicking outside
document.addEventListener('click', function(event) {
    const dropdowns = document.querySelectorAll('[id$="Dropdown"]');
    dropdowns.forEach(dropdown => {
        if (!dropdown.contains(event.target) && !event.target.closest('button[onclick*="' + dropdown.id + '"]')) {
            dropdown.classList.add('hidden');
        }
    });
});
</script>

<?php require_once '../includes/footer.php'; ?>
