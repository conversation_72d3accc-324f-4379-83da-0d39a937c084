<?php
session_start();
require_once '../includes/db.php';

// Ensure only college admin can access this page
if (!isset($_SESSION['collegeadmin_id'])) {
    header("Location: ../auth/collegeadmin_login.php");
    exit;
}

$college_id = $_SESSION['college_id'];
$admin_name = $_SESSION['collegeadmin_name'] ?? 'Admin';
$faculty_id = $_GET['id'] ?? null;

// Validate faculty ID
if (!$faculty_id) {
    $_SESSION['flash_message'] = "Invalid faculty ID.";
    header("Location: list_faculty.php");
    exit;
}

// Check if faculty exists and belongs to this college
$stmt = $conn->prepare("
    SELECT id, college_id, name, email, department
    FROM faculties
    WHERE id = ? AND college_id = ?
");
$stmt->execute([$faculty_id, $college_id]);
$faculty = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$faculty) {
    $_SESSION['flash_message'] = "Faculty not found or access denied.";
    header("Location: list_faculty.php");
    exit;
}

// Get college details
$collegeStmt = $conn->prepare("SELECT name FROM colleges WHERE id = ?");
$collegeStmt->execute([$college_id]);
$college = $collegeStmt->fetch(PDO::FETCH_ASSOC);

// Handle deletion if confirmed
if (isset($_POST['confirm_delete']) && $_POST['confirm_delete'] === 'yes') {
    try {
        // Start transaction
        $conn->beginTransaction();
        
        // Delete faculty
        $delete = $conn->prepare("DELETE FROM faculties WHERE id = ? AND college_id = ?");
        $delete->execute([$faculty_id, $college_id]);
        
        // Commit transaction
        $conn->commit();
        
        $_SESSION['flash_message'] = "Faculty member '" . htmlspecialchars($faculty['name']) . "' has been permanently removed.";
        header("Location: list_faculty.php");
        exit;
    } catch (Exception $e) {
        // Rollback transaction on error
        $conn->rollBack();
        
        $_SESSION['flash_message'] = "Error removing faculty: " . $e->getMessage();
        header("Location: edit_faculty.php?id=" . $faculty_id);
        exit;
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Delete Faculty - Mentoshri</title>
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts - Inter -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        'primary': '#0a66c2',
                        'primary-dark': '#004182',
                        'primary-light': '#e7f3ff',
                    },
                    fontFamily: {
                        'sans': ['Inter', 'sans-serif'],
                    },
                }
            }
        }
    </script>
    <!-- Design System -->
    <link rel="stylesheet" href="../assets/css/design-system.css">
</head>
<body class="font-sans bg-gray-50 dark:bg-gray-900 min-h-screen flex flex-col">
    <!-- Header -->
    <header class="bg-white dark:bg-gray-800 shadow-sm">
        <div class="container mx-auto px-4 py-3 flex items-center justify-between">
            <div class="flex items-center">
                <a href="collegeadmin_dashboard.php" class="text-primary font-bold text-2xl">Mentoshri</a>
            </div>
            
            <div class="flex items-center space-x-4">
                <button id="themeToggle" class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700">
                    <i class="fas fa-moon dark:hidden"></i>
                    <i class="fas fa-sun hidden dark:block"></i>
                </button>
                <div class="relative">
                    <button id="notificationsBtn" class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 relative">
                        <i class="fas fa-bell"></i>
                        <span class="absolute top-0 right-0 h-4 w-4 bg-red-500 rounded-full text-xs text-white flex items-center justify-center">2</span>
                    </button>
                </div>
                <div class="relative group">
                    <button class="flex items-center space-x-1">
                        <div class="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center text-blue-600 dark:text-blue-400">
                            <i class="fas fa-user-shield"></i>
                        </div>
                        <span class="text-gray-700 dark:text-gray-300"><?= htmlspecialchars($admin_name) ?></span>
                        <i class="fas fa-chevron-down text-gray-400 text-xs"></i>
                    </button>
                    <div class="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg border border-gray-200 dark:border-gray-700 hidden group-hover:block z-10">
                        <div class="py-1">
                            <a href="collegeadmin_profile.php" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                <i class="fas fa-user mr-2"></i> Profile
                            </a>
                            <a href="collegeadmin_settings.php" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                <i class="fas fa-cog mr-2"></i> Settings
                            </a>
                            <div class="border-t border-gray-200 dark:border-gray-700"></div>
                            <a href="../auth/logout.php" class="block px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-700">
                                <i class="fas fa-sign-out-alt mr-2"></i> Logout
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="flex-grow py-8 px-4">
        <div class="container mx-auto">
            <!-- Breadcrumb -->
            <nav class="mb-6 text-sm" aria-label="Breadcrumb">
                <ol class="flex items-center space-x-2">
                    <li>
                        <a href="collegeadmin_dashboard.php" class="text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary-light">
                            <i class="fas fa-home"></i>
                            <span class="sr-only">Dashboard</span>
                        </a>
                    </li>
                    <li class="flex items-center">
                        <i class="fas fa-chevron-right text-gray-400 text-xs mx-1"></i>
                        <a href="list_faculty.php" class="text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary-light">Faculty Management</a>
                    </li>
                    <li class="flex items-center">
                        <i class="fas fa-chevron-right text-gray-400 text-xs mx-1"></i>
                        <a href="edit_faculty.php?id=<?= $faculty_id ?>" class="text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary-light">
                            <?= htmlspecialchars($faculty['name']) ?>
                        </a>
                    </li>
                    <li class="flex items-center">
                        <i class="fas fa-chevron-right text-gray-400 text-xs mx-1"></i>
                        <span class="text-gray-700 dark:text-gray-300">Delete Faculty</span>
                    </li>
                </ol>
            </nav>

            <div class="max-w-2xl mx-auto">
                <!-- Page Header -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 p-6 mb-6">
                    <div class="flex items-center justify-between">
                        <h1 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                            <i class="fas fa-trash-alt text-red-600 dark:text-red-400 mr-3"></i>
                            Delete Faculty Member
                        </h1>
                        <a href="edit_faculty.php?id=<?= $faculty_id ?>" class="btn btn-outline flex items-center">
                            <i class="fas fa-arrow-left mr-2"></i> Back
                        </a>
                    </div>
                </div>

                <!-- Delete Confirmation Card -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 overflow-hidden">
                    <!-- Faculty Info -->
                    <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                        <div class="flex items-center">
                            <?php if (!empty($faculty['profile_image'])): ?>
                                <div class="w-16 h-16 rounded-full overflow-hidden mr-4">
                                    <img src="../uploads/<?= htmlspecialchars($faculty['profile_image']) ?>" alt="Profile" class="w-full h-full object-cover">
                                </div>
                            <?php else: ?>
                                <div class="w-16 h-16 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center text-blue-600 dark:text-blue-400 mr-4">
                                    <i class="fas fa-chalkboard-teacher text-2xl"></i>
                                </div>
                            <?php endif; ?>
                            <div>
                                <h2 class="text-xl font-semibold text-gray-900 dark:text-white"><?= htmlspecialchars($faculty['name']) ?></h2>
                                <div class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                                    <?php if (!empty($faculty['position'])): ?>
                                        <span class="mr-3"><?= htmlspecialchars($faculty['position']) ?></span>
                                    <?php endif; ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300">
                                        <?= htmlspecialchars($faculty['department']) ?>
                                    </span>
                                </div>
                                <div class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                                    <i class="fas fa-envelope mr-1"></i> <?= htmlspecialchars($faculty['email']) ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Warning Message -->
                    <div class="p-6">
                        <div class="bg-red-50 dark:bg-red-900/20 border-l-4 border-red-500 text-red-700 dark:text-red-400 p-5 mb-6">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-exclamation-triangle text-xl"></i>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-lg font-medium">Permanent Deletion Warning</h3>
                                    <div class="mt-2">
                                        <p class="text-sm">
                                            You are about to permanently delete faculty member <strong><?= htmlspecialchars($faculty['name']) ?></strong>. This action cannot be undone and will remove all associated data.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <h4 class="text-lg font-medium text-gray-800 dark:text-gray-200 mb-4">Deletion Consequences:</h4>
                        
                        <div class="space-y-4 mb-6">
                            <div class="flex items-start">
                                <div class="flex-shrink-0 h-5 w-5 text-red-500">
                                    <i class="fas fa-user-slash"></i>
                                </div>
                                <div class="ml-3">
                                    <p class="text-gray-700 dark:text-gray-300">
                                        <span class="font-medium">Account Access:</span> Their login credentials will be revoked immediately.
                                    </p>
                                </div>
                            </div>
                            
                            <div class="flex items-start">
                                <div class="flex-shrink-0 h-5 w-5 text-red-500">
                                    <i class="fas fa-user-graduate"></i>
                                </div>
                                <div class="ml-3">
                                    <p class="text-gray-700 dark:text-gray-300">
                                        <span class="font-medium">Mentorship:</span> All mentorship assignments will be removed.
                                    </p>
                                </div>
                            </div>
                            
                            <div class="flex items-start">
                                <div class="flex-shrink-0 h-5 w-5 text-red-500">
                                    <i class="fas fa-id-card"></i>
                                </div>
                                <div class="ml-3">
                                    <p class="text-gray-700 dark:text-gray-300">
                                        <span class="font-medium">Directory:</span> Their profile will be removed from the faculty directory.
                                    </p>
                                </div>
                            </div>
                            
                            <div class="flex items-start">
                                <div class="flex-shrink-0 h-5 w-5 text-red-500">
                                    <i class="fas fa-comments"></i>
                                </div>
                                <div class="ml-3">
                                    <p class="text-gray-700 dark:text-gray-300">
                                        <span class="font-medium">Communications:</span> All message history will be preserved but marked as from a deleted user.
                                    </p>
                                </div>
                            </div>
                        </div>
                        
                        <form method="post" class="mt-8">
                            <div class="flex items-center mb-6">
                                <input type="checkbox" id="confirmCheck" class="h-4 w-4 text-primary border-gray-300 rounded focus:ring-primary" required>
                                <label for="confirmCheck" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                                    I understand that this action is permanent and cannot be undone.
                                </label>
                            </div>
                            
                            <div class="flex justify-between items-center">
                                <a href="edit_faculty.php?id=<?= $faculty_id ?>" class="btn btn-outline">
                                    <i class="fas fa-times mr-2"></i> Cancel
                                </a>
                                <button type="submit" name="confirm_delete" value="yes" class="btn bg-red-600 hover:bg-red-700 text-white" id="deleteBtn" disabled>
                                    <i class="fas fa-trash-alt mr-2"></i> Permanently Delete
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 py-4 mt-auto">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                <div class="text-center md:text-left mb-2 md:mb-0">
                    <p class="text-sm text-gray-600 dark:text-gray-400">
                        &copy; <?= date('Y') ?> Mentoshri. All rights reserved.
                    </p>
                </div>
                <div class="flex justify-center md:justify-end space-x-4">
                    <a href="#" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300">
                        <i class="fab fa-facebook"></i>
                    </a>
                    <a href="#" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300">
                        <i class="fab fa-twitter"></i>
                    </a>
                    <a href="#" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300">
                        <i class="fab fa-linkedin"></i>
                    </a>
                    <a href="#" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300">
                        <i class="fab fa-instagram"></i>
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Enable delete button when checkbox is checked
        const confirmCheck = document.getElementById('confirmCheck');
        const deleteBtn = document.getElementById('deleteBtn');
        
        confirmCheck.addEventListener('change', function() {
            deleteBtn.disabled = !this.checked;
        });
        
        // Theme Toggle
        const themeToggleBtn = document.getElementById('themeToggle');
        
        function getThemePreference() {
            if (localStorage.getItem('color-theme')) {
                return localStorage.getItem('color-theme');
            }
            return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
        }
        
        function setThemePreference(theme) {
            localStorage.setItem('color-theme', theme);
            if (theme === 'dark') {
                document.documentElement.classList.add('dark');
            } else {
                document.documentElement.classList.remove('dark');
            }
        }
        
        // Set initial theme
        setThemePreference(getThemePreference());
        
        // Toggle theme when button is clicked
        themeToggleBtn.addEventListener('click', function() {
            const currentTheme = getThemePreference();
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            setThemePreference(newTheme);
        });
        
        // Notifications dropdown (simplified example)
        document.getElementById('notificationsBtn').addEventListener('click', function() {
            alert('Notifications feature coming soon!');
        });
    </script>
</body>
</html> 