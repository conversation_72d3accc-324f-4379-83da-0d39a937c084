<?php
session_start();
require_once '../includes/db.php';
require_once '../includes/db_schema_check.php';
require_once '../includes/header.php';

// Ensure only super admin can access this page
if (!isset($_SESSION['superadmin_id'])) {
    header("Location: ../auth/superadmin_login.php");
    exit;
}

$superadmin_id = $_SESSION['superadmin_id'];
$superadmin_name = $_SESSION['superadmin_name'] ?? 'Super Admin';
$alumni_id = $_GET['id'] ?? null;

// Validate alumni ID
if (!$alumni_id) {
    $_SESSION['flash_message'] = "Invalid alumni ID.";
    $_SESSION['flash_message_type'] = "error";
    header("Location: superadmin_list_alumni.php");
    exit;
}

// Get alumni details - no college_id restriction for super admin
$stmt = $conn->prepare("SELECT a.*, c.name as college_name FROM alumni a 
                       LEFT JOIN colleges c ON a.college_id = c.id 
                       WHERE a.id = ?");
$stmt->execute([$alumni_id]);
$alumni = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$alumni) {
    $_SESSION['flash_message'] = "Alumni not found.";
    $_SESSION['flash_message_type'] = "error";
    header("Location: superadmin_list_alumni.php");
    exit;
}

// Get mentorship counts if the table exists
$mentorshipCount = 0;
try {
    // Try to get mentorship count
    $mentorshipStmt = $conn->prepare("SELECT COUNT(*) FROM mentorships WHERE alumni_id = ?");
    $mentorshipStmt->execute([$alumni_id]);
    $mentorshipCount = $mentorshipStmt->fetchColumn();
} catch (PDOException $e) {
    // Table might not exist, ignore
}

// Get students mentored by this alumni
$mentees = [];
try {
    $menteesStmt = $conn->prepare("
        SELECT s.id, s.full_name, s.profile_photo, s.department, s.year
        FROM mentorships m
        JOIN students s ON m.student_id = s.id
        WHERE m.alumni_id = ?
        ORDER BY m.created_at DESC
        LIMIT 5
    ");
    $menteesStmt->execute([$alumni_id]);
    $mentees = $menteesStmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    // Table might not exist, ignore
}

// Get giveback contributions if the table exists
$contributions = [];
try {
    $contributionsStmt = $conn->prepare("
        SELECT gc.*, c.name as campaign_name
        FROM giveback_contributions gc
        LEFT JOIN campaigns c ON gc.campaign_id = c.id
        WHERE gc.alumni_id = ?
        ORDER BY gc.created_at DESC
        LIMIT 5
    ");
    $contributionsStmt->execute([$alumni_id]);
    $contributions = $contributionsStmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    // Table might not exist, ignore
}

// Profile photo handling
$photo = !empty($alumni['profile_photo']) && file_exists("../uploads/{$alumni['profile_photo']}")
    ? $alumni['profile_photo']
    : 'default.png';

?>

<!-- Main Content -->
<div class="container mx-auto px-4 py-8 max-w-6xl">
    <!-- Page Header -->
    <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                <span class="bg-purple-500/10 dark:bg-purple-500/20 p-2 rounded-md mr-3">
                    <i class="fas fa-user-tie text-purple-600 dark:text-purple-400"></i>
                </span>
                Alumni Profile
            </h1>
            <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">View detailed information about this alumni</p>
        </div>
        <div class="mt-4 md:mt-0 flex flex-wrap gap-2">
            <a href="superadmin_list_alumni.php" class="btn btn-outline flex items-center">
                <i class="fas fa-arrow-left mr-2"></i> Back to Alumni
            </a>
            <a href="superadmin_edit_alumni.php?id=<?= $alumni['id'] ?>" class="btn btn-primary flex items-center">
                <i class="fas fa-edit mr-2"></i> Edit Profile
            </a>
        </div>
    </div>
    
    <?php if (isset($_SESSION['flash_message'])): ?>
    <div class="mb-6 bg-<?= isset($_SESSION['flash_message_type']) ? $_SESSION['flash_message_type'] : 'success' ?>-50 dark:bg-<?= isset($_SESSION['flash_message_type']) ? $_SESSION['flash_message_type'] : 'success' ?>-900/30 border-l-4 border-<?= isset($_SESSION['flash_message_type']) ? $_SESSION['flash_message_type'] : 'success' ?>-500 text-<?= isset($_SESSION['flash_message_type']) ? $_SESSION['flash_message_type'] : 'success' ?>-700 dark:text-<?= isset($_SESSION['flash_message_type']) ? $_SESSION['flash_message_type'] : 'success' ?>-400 p-4 flex justify-between items-center" role="alert">
        <div>
            <i class="fas fa-<?= isset($_SESSION['flash_message_type']) && $_SESSION['flash_message_type'] === 'error' ? 'exclamation-circle' : 'check-circle' ?> mr-2"></i>
            <?= $_SESSION['flash_message'] ?>
        </div>
        <button type="button" class="text-<?= isset($_SESSION['flash_message_type']) ? $_SESSION['flash_message_type'] : 'success' ?>-700 dark:text-<?= isset($_SESSION['flash_message_type']) ? $_SESSION['flash_message_type'] : 'success' ?>-400 hover:text-<?= isset($_SESSION['flash_message_type']) ? $_SESSION['flash_message_type'] : 'success' ?>-900" onclick="this.parentElement.style.display='none';">
            <i class="fas fa-times"></i>
        </button>
    </div>
    <?php 
    unset($_SESSION['flash_message']);
    unset($_SESSION['flash_message_type']);
    endif; 
    ?>
    
    <!-- Profile Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Left Column - Profile Card -->
        <div class="lg:col-span-1">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 overflow-hidden">
                <div class="p-6 text-center">
                    <div class="relative w-32 h-32 mx-auto mb-4">
                        <?php if (file_exists("../uploads/{$photo}")): ?>
                            <img src="../uploads/<?= $photo ?>" alt="<?= htmlspecialchars($alumni['full_name']) ?>" class="w-32 h-32 rounded-full object-cover border-4 border-white dark:border-gray-700 shadow-md">
                        <?php else: ?>
                            <div class="w-32 h-32 rounded-full bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center text-purple-500 text-4xl border-4 border-white dark:border-gray-700 shadow-md">
                                <?= strtoupper(substr($alumni['full_name'] ?? 'A', 0, 1)) ?>
                            </div>
                        <?php endif; ?>
                        
                        <span class="absolute bottom-0 right-0 w-8 h-8 rounded-full bg-<?= $alumni['verified'] ? 'green' : 'yellow' ?>-500 border-4 border-white dark:border-gray-700 flex items-center justify-center text-white">
                            <i class="fas fa-<?= $alumni['verified'] ? 'check' : 'clock' ?> text-sm"></i>
                        </span>
                    </div>
                    
                    <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-1"><?= htmlspecialchars($alumni['full_name']) ?></h2>
                    <p class="text-gray-500 dark:text-gray-400 mb-1"><?= htmlspecialchars($alumni['email']) ?></p>
                    <p class="text-sm text-primary dark:text-primary-light mb-3">
                        <?= !empty($alumni['job_title']) ? htmlspecialchars($alumni['job_title']) : 'Alumni' ?>
                        <?= !empty($alumni['company']) ? ' at ' . htmlspecialchars($alumni['company']) : '' ?>
                    </p>
                    
                    <div class="flex justify-center space-x-2 mb-4">
                        <?php if (!empty($alumni['linkedin_url'])): ?>
                        <a href="<?= htmlspecialchars($alumni['linkedin_url']) ?>" target="_blank" class="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-full text-blue-600 dark:text-blue-400 hover:bg-blue-200 dark:hover:bg-blue-800/50 transition-colors">
                            <i class="fab fa-linkedin-in"></i>
                        </a>
                        <?php endif; ?>
                        
                        <?php if (!empty($alumni['website'])): ?>
                        <a href="<?= htmlspecialchars($alumni['website']) ?>" target="_blank" class="p-2 bg-purple-100 dark:bg-purple-900/30 rounded-full text-purple-600 dark:text-purple-400 hover:bg-purple-200 dark:hover:bg-purple-800/50 transition-colors">
                            <i class="fas fa-globe"></i>
                        </a>
                        <?php endif; ?>
                        
                        <?php if (!empty($alumni['phone'])): ?>
                        <a href="tel:<?= htmlspecialchars($alumni['phone']) ?>" class="p-2 bg-green-100 dark:bg-green-900/30 rounded-full text-green-600 dark:text-green-400 hover:bg-green-200 dark:hover:bg-green-800/50 transition-colors">
                            <i class="fas fa-phone"></i>
                        </a>
                        <?php endif; ?>
                    </div>
                    
                    <div class="border-t border-gray-200 dark:border-gray-700 pt-4 mt-2">
                        <div class="flex justify-between items-center mb-2">
                            <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Status:</span>
                            <span class="px-2 py-1 rounded-full text-xs font-medium <?= $alumni['verified'] ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400' : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400' ?>">
                                <?= $alumni['verified'] ? 'Verified' : 'Pending Verification' ?>
                            </span>
                        </div>
                        <div class="flex justify-between items-center mb-2">
                            <span class="text-sm font-medium text-gray-500 dark:text-gray-400">College:</span>
                            <span class="text-sm text-gray-900 dark:text-white"><?= htmlspecialchars($alumni['college_name'] ?? 'Not assigned') ?></span>
                        </div>
                        <div class="flex justify-between items-center mb-2">
                            <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Industry:</span>
                            <span class="text-sm text-gray-900 dark:text-white"><?= htmlspecialchars($alumni['industry'] ?? 'Not specified') ?></span>
                        </div>
                        <div class="flex justify-between items-center mb-2">
                            <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Graduation Year:</span>
                            <span class="text-sm text-gray-900 dark:text-white"><?= htmlspecialchars($alumni['graduation_year'] ?? 'Not specified') ?></span>
                        </div>
                        <div class="flex justify-between items-center mb-2">
                            <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Mentees:</span>
                            <span class="text-sm text-gray-900 dark:text-white"><?= $mentorshipCount ?></span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm font-medium text-gray-500 dark:text-gray-400">Joined:</span>
                            <span class="text-sm text-gray-900 dark:text-white"><?= date('M j, Y', strtotime($alumni['created_at'])) ?></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Right Column - Details -->
        <div class="lg:col-span-2">
            <!-- Tabs -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 mb-6">
                <div class="border-b border-gray-200 dark:border-gray-700">
                    <ul class="flex flex-wrap -mb-px">
                        <li class="mr-2">
                            <a href="#" class="inline-block py-4 px-4 text-sm font-medium text-center border-b-2 border-primary text-primary dark:text-primary-light dark:border-primary-light rounded-t-lg active" onclick="showTab('profile'); return false;">Profile</a>
                        </li>
                        <li class="mr-2">
                            <a href="#" class="inline-block py-4 px-4 text-sm font-medium text-center text-gray-500 dark:text-gray-400 border-b-2 border-transparent hover:text-gray-600 hover:border-gray-300 dark:hover:text-gray-300 rounded-t-lg" onclick="showTab('mentees'); return false;">Mentees</a>
                        </li>
                        <li class="mr-2">
                            <a href="#" class="inline-block py-4 px-4 text-sm font-medium text-center text-gray-500 dark:text-gray-400 border-b-2 border-transparent hover:text-gray-600 hover:border-gray-300 dark:hover:text-gray-300 rounded-t-lg" onclick="showTab('contributions'); return false;">Contributions</a>
                        </li>
                    </ul>
                </div>
                
                <!-- Tab Content -->
                <div class="p-6">
                    <!-- Profile Tab -->
                    <div id="profile-tab" class="tab-content">
                        <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4">Personal Information</h3>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Full Name</label>
                                <p class="text-gray-900 dark:text-white"><?= htmlspecialchars($alumni['full_name']) ?></p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Email Address</label>
                                <p class="text-gray-900 dark:text-white"><?= htmlspecialchars($alumni['email']) ?></p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Phone Number</label>
                                <p class="text-gray-900 dark:text-white"><?= !empty($alumni['phone']) ? htmlspecialchars($alumni['phone']) : 'Not provided' ?></p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Location</label>
                                <p class="text-gray-900 dark:text-white"><?= !empty($alumni['location']) ? htmlspecialchars($alumni['location']) : 'Not provided' ?></p>
                            </div>
                        </div>
                        
                        <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4">Professional Information</h3>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Company</label>
                                <p class="text-gray-900 dark:text-white"><?= !empty($alumni['company']) ? htmlspecialchars($alumni['company']) : 'Not specified' ?></p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Job Title</label>
                                <p class="text-gray-900 dark:text-white"><?= !empty($alumni['job_title']) ? htmlspecialchars($alumni['job_title']) : 'Not specified' ?></p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Industry</label>
                                <p class="text-gray-900 dark:text-white"><?= !empty($alumni['industry']) ? htmlspecialchars($alumni['industry']) : 'Not specified' ?></p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Experience (Years)</label>
                                <p class="text-gray-900 dark:text-white"><?= !empty($alumni['experience_years']) ? htmlspecialchars($alumni['experience_years']) : 'Not specified' ?></p>
                            </div>
                        </div>
                        
                        <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4">Education</h3>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">College</label>
                                <p class="text-gray-900 dark:text-white"><?= htmlspecialchars($alumni['college_name'] ?? 'Not assigned') ?></p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Degree</label>
                                <p class="text-gray-900 dark:text-white"><?= !empty($alumni['degree']) ? htmlspecialchars($alumni['degree']) : 'Not specified' ?></p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Major</label>
                                <p class="text-gray-900 dark:text-white"><?= !empty($alumni['major']) ? htmlspecialchars($alumni['major']) : 'Not specified' ?></p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Graduation Year</label>
                                <p class="text-gray-900 dark:text-white"><?= !empty($alumni['graduation_year']) ? htmlspecialchars($alumni['graduation_year']) : 'Not specified' ?></p>
                            </div>
                        </div>
                        
                        <?php if (!empty($alumni['bio'])): ?>
                        <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4">Bio</h3>
                        <div class="bg-gray-50 dark:bg-gray-750 p-4 rounded-lg mb-6">
                            <p class="text-gray-700 dark:text-gray-300 whitespace-pre-line"><?= nl2br(htmlspecialchars($alumni['bio'])) ?></p>
                        </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($alumni['skills'])): ?>
                        <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4">Skills</h3>
                        <div class="flex flex-wrap gap-2 mb-6">
                            <?php foreach (explode(',', $alumni['skills']) as $skill): ?>
                                <span class="px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400">
                                    <?= htmlspecialchars(trim($skill)) ?>
                                </span>
                            <?php endforeach; ?>
                        </div>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Mentees Tab -->
                    <div id="mentees-tab" class="tab-content hidden">
                        <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4">Students Mentored</h3>
                        
                        <?php if (count($mentees) > 0): ?>
                            <div class="space-y-4">
                                <?php foreach ($mentees as $mentee): ?>
                                    <div class="bg-gray-50 dark:bg-gray-750 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                                        <div class="flex items-center">
                                            <?php if (!empty($mentee['profile_photo']) && file_exists("../uploads/{$mentee['profile_photo']}")): ?>
                                                <img src="../uploads/<?= $mentee['profile_photo'] ?>" alt="<?= htmlspecialchars($mentee['full_name']) ?>" class="w-10 h-10 rounded-full object-cover mr-3">
                                            <?php else: ?>
                                                <div class="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center text-blue-500 mr-3">
                                                    <?= strtoupper(substr($mentee['full_name'] ?? 'S', 0, 1)) ?>
                                                </div>
                                            <?php endif; ?>
                                            <div>
                                                <h4 class="font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($mentee['full_name']) ?></h4>
                                                <p class="text-sm text-gray-500 dark:text-gray-400">
                                                    <?= !empty($mentee['department']) ? htmlspecialchars($mentee['department']) : 'Department not specified' ?> 
                                                    <?= !empty($mentee['year']) ? '• ' . htmlspecialchars($mentee['year']) . ' Year' : '' ?>
                                                </p>
                                            </div>
                                            <a href="superadmin_view_student.php?id=<?= $mentee['id'] ?>" class="ml-auto p-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 rounded-full text-gray-600 dark:text-gray-400 transition-colors">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-8">
                                <div class="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <i class="fas fa-user-graduate text-gray-400 text-xl"></i>
                                </div>
                                <h4 class="text-gray-900 dark:text-white font-medium mb-1">No Mentees Found</h4>
                                <p class="text-gray-500 dark:text-gray-400">This alumni isn't mentoring any students yet.</p>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Contributions Tab -->
                    <div id="contributions-tab" class="tab-content hidden">
                        <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4">Giveback Contributions</h3>
                        
                        <?php if (count($contributions) > 0): ?>
                            <div class="space-y-4">
                                <?php foreach ($contributions as $contribution): ?>
                                    <div class="bg-gray-50 dark:bg-gray-750 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                                        <div class="flex items-center justify-between mb-2">
                                            <div>
                                                <h4 class="font-medium text-gray-900 dark:text-white">
                                                    <?= ucfirst(str_replace('_', ' ', $contribution['contribution_type'])) ?>
                                                    <?php if ($contribution['contribution_type'] === 'financial'): ?>
                                                    <span class="font-bold text-green-600 dark:text-green-400">
                                                        <?= !empty($contribution['amount']) ? '$' . number_format($contribution['amount'], 2) : '' ?>
                                                    </span>
                                                    <?php endif; ?>
                                                </h4>
                                                <p class="text-sm text-gray-500 dark:text-gray-400">
                                                    <?= date('M j, Y', strtotime($contribution['created_at'])) ?>
                                                    <?= !empty($contribution['campaign_name']) ? ' • ' . htmlspecialchars($contribution['campaign_name']) : '' ?>
                                                </p>
                                            </div>
                                            <span class="px-2 py-1 rounded-full text-xs font-medium 
                                                <?php
                                                switch ($contribution['status']) {
                                                    case 'approved':
                                                        echo 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400';
                                                        break;
                                                    case 'pending':
                                                        echo 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400';
                                                        break;
                                                    case 'completed':
                                                        echo 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400';
                                                        break;
                                                    default:
                                                        echo 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
                                                }
                                                ?>">
                                                <?= ucfirst($contribution['status']) ?>
                                            </span>
                                        </div>
                                        <?php if (!empty($contribution['description'])): ?>
                                            <div class="mt-2 text-sm text-gray-700 dark:text-gray-300">
                                                <p><?= htmlspecialchars($contribution['description']) ?></p>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-8">
                                <div class="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <i class="fas fa-hand-holding-heart text-gray-400 text-xl"></i>
                                </div>
                                <h4 class="text-gray-900 dark:text-white font-medium mb-1">No Contributions Found</h4>
                                <p class="text-gray-500 dark:text-gray-400">This alumni hasn't made any giveback contributions yet.</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <!-- Admin Actions Card -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700">
                <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
                    <h3 class="font-semibold text-gray-800 dark:text-white">Admin Actions</h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <a href="superadmin_edit_alumni.php?id=<?= $alumni['id'] ?>" class="btn btn-outline flex items-center justify-center">
                            <i class="fas fa-edit mr-2"></i> Edit Profile
                        </a>
                        <?php if ($alumni['verified']): ?>
                            <form action="verify_alumni.php" method="post" class="inline-block">
                                <input type="hidden" name="alumni_id" value="<?= $alumni['id'] ?>">
                                <input type="hidden" name="action" value="unverify">
                                <button type="submit" class="btn btn-outline w-full flex items-center justify-center text-yellow-600 border-yellow-600 hover:bg-yellow-50 dark:text-yellow-400 dark:border-yellow-400 dark:hover:bg-yellow-900/20">
                                    <i class="fas fa-user-clock mr-2"></i> Mark as Unverified
                                </button>
                            </form>
                        <?php else: ?>
                            <form action="verify_alumni.php" method="post" class="inline-block">
                                <input type="hidden" name="alumni_id" value="<?= $alumni['id'] ?>">
                                <input type="hidden" name="action" value="verify">
                                <button type="submit" class="btn btn-outline w-full flex items-center justify-center text-green-600 border-green-600 hover:bg-green-50 dark:text-green-400 dark:border-green-400 dark:hover:bg-green-900/20">
                                    <i class="fas fa-user-check mr-2"></i> Verify Alumni
                                </button>
                            </form>
                        <?php endif; ?>
                        <button type="button" onclick="resetPassword(<?= $alumni['id'] ?>)" class="btn btn-outline flex items-center justify-center text-blue-600 border-blue-600 hover:bg-blue-50 dark:text-blue-400 dark:border-blue-400 dark:hover:bg-blue-900/20">
                            <i class="fas fa-key mr-2"></i> Reset Password
                        </button>
                        <button type="button" onclick="confirmDelete(<?= $alumni['id'] ?>)" class="btn btn-outline flex items-center justify-center text-red-600 border-red-600 hover:bg-red-50 dark:text-red-400 dark:border-red-400 dark:hover:bg-red-900/20">
                            <i class="fas fa-trash-alt mr-2"></i> Delete Alumni
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Tab functionality
function showTab(tabName) {
    // Hide all tabs
    document.querySelectorAll('.tab-content').forEach(tab => {
        tab.classList.add('hidden');
    });
    
    // Show selected tab
    document.getElementById(tabName + '-tab').classList.remove('hidden');
    
    // Update tab buttons
    document.querySelectorAll('ul li a').forEach(link => {
        link.classList.remove('text-primary', 'dark:text-primary-light', 'border-primary', 'dark:border-primary-light');
        link.classList.add('text-gray-500', 'dark:text-gray-400', 'border-transparent');
    });
    
    const activeLink = document.querySelector(`a[onclick="showTab('${tabName}'); return false;"]`);
    activeLink.classList.remove('text-gray-500', 'dark:text-gray-400', 'border-transparent');
    activeLink.classList.add('text-primary', 'dark:text-primary-light', 'border-primary', 'dark:border-primary-light');
}

// Reset password functionality
function resetPassword(alumniId) {
    if (confirm('Are you sure you want to reset this alumni\'s password? They will receive an email with instructions.')) {
        // In a real implementation, this would make an AJAX call or submit a form
        alert('Password reset functionality will be implemented soon.');
    }
}

// Delete alumni functionality
function confirmDelete(alumniId) {
    if (confirm('Are you sure you want to delete this alumni? This action cannot be undone.')) {
        window.location.href = `delete_alumni.php?id=${alumniId}`;
    }
}
</script>