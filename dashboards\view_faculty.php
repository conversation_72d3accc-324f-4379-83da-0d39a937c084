<?php
// Include session configuration
require_once '../includes/session_config.php';
require_once '../includes/db.php';
require_once '../includes/db_schema_check.php';

if (!isset($_SESSION['collegeadmin_id'])) {
    header("Location: ../auth/collegeadmin_login.php");
    exit;
}

$college_id = $_SESSION['college_id'];
$admin_name = $_SESSION['collegeadmin_name'];
$faculty_id = $_GET['id'] ?? null;

// Validate faculty ID
if (!$faculty_id) {
    $_SESSION['flash_message'] = "Invalid faculty ID.";
    header("Location: list_faculty.php");
    exit;
}

// Check if faculty exists and belongs to this college
$stmt = $conn->prepare("
    SELECT id, college_id, name, email, password, department, profile_photo, bio, created_at
    FROM faculties
    WHERE id = ? AND college_id = ?
");
$stmt->execute([$faculty_id, $college_id]);
$faculty = $stmt->fetch(PDO::FETCH_ASSOC);

// Try to get additional columns if they exist
if ($faculty) {
    try {
        $extraStmt = $conn->prepare("SELECT position, phone, status, profile_image, updated_at FROM faculties WHERE id = ? LIMIT 1");
        $extraStmt->execute([$faculty_id]);
        $extraData = $extraStmt->fetch(PDO::FETCH_ASSOC);
        if ($extraData) {
            $faculty = array_merge($faculty, $extraData);
        }
    } catch (PDOException $e) {
        // Some columns don't exist, set defaults
        $faculty['position'] = $faculty['position'] ?? null;
        $faculty['phone'] = $faculty['phone'] ?? null;
        $faculty['status'] = $faculty['status'] ?? 'active';
        $faculty['profile_image'] = $faculty['profile_image'] ?? null;
        $faculty['updated_at'] = $faculty['updated_at'] ?? null;
    }
}

if (!$faculty) {
    $_SESSION['flash_message'] = "Faculty not found or access denied.";
    header("Location: list_faculty.php");
    exit;
}

// Get college details
$collegeStmt = $conn->prepare("SELECT name FROM colleges WHERE id = ?");
$collegeStmt->execute([$college_id]);
$college = $collegeStmt->fetch(PDO::FETCH_ASSOC);

// Check and create mentorships table if needed
$mentorshipSchemaUpdates = checkAndCreateMentorshipsTable($conn);
$schemaUpdateMessages = [];
if (!empty($mentorshipSchemaUpdates) && !isset($mentorshipSchemaUpdates['error'])) {
    $schemaUpdateMessages = $mentorshipSchemaUpdates;
}

// Check if mentorships table exists
$tableCheckStmt = $conn->prepare("SHOW TABLES LIKE 'mentorships'");
$tableCheckStmt->execute();
$mentorshipTableExists = $tableCheckStmt->rowCount() > 0;

// Get mentorship counts
$mentorshipCount = 0;

try {
    // Try to get mentorship count
    $mentorshipStmt = $conn->prepare("SELECT COUNT(*) FROM mentorships WHERE faculty_id = ?");
    $mentorshipStmt->execute([$faculty_id]);
    $mentorshipCount = $mentorshipStmt->fetchColumn();
} catch (PDOException $e) {
    // If table doesn't exist or has wrong schema, set count to 0
    $mentorshipCount = 0;
    
    // Try to create the table if it doesn't exist
    try {
        $createTableSQL = "CREATE TABLE IF NOT EXISTS `mentorships` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `student_id` int(11) DEFAULT NULL,
            `faculty_id` int(11) DEFAULT NULL,
            `alumni_id` int(11) DEFAULT NULL,
            `status` enum('active','completed','cancelled') DEFAULT 'active',
            `start_date` date DEFAULT NULL,
            `end_date` date DEFAULT NULL,
            `goals` text DEFAULT NULL,
            `notes` text DEFAULT NULL,
            `created_at` timestamp NULL DEFAULT current_timestamp(),
            `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
            PRIMARY KEY (`id`),
            KEY `student_id` (`student_id`),
            KEY `faculty_id` (`faculty_id`),
            KEY `alumni_id` (`alumni_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";
        
        $conn->exec($createTableSQL);
        $schemaUpdateMessages[] = "Created mentorships table successfully";
    } catch (PDOException $createError) {
        // Silently fail if we can't create the table
    }
}

// Set role explicitly for the sidebar
$role = 'collegeadmin';
$user_id = $_SESSION['collegeadmin_id'];
$user_name = $_SESSION['collegeadmin_name'];
$current_page = basename($_SERVER['PHP_SELF']);

// Set dashboard URL for header
$dashboard_url = 'collegeadmin_dashboard.php';

// Include the header
require_once '../includes/header.php';
?>

    <!-- Main Content -->
    <main class="flex-grow py-6 px-4">
        <div class="max-w-7xl mx-auto">
            <!-- Page Header -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8">
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="bg-gradient-to-br from-blue-500 to-blue-600 p-3 rounded-xl shadow-lg">
                            <i class="fas fa-user-graduate text-white text-xl"></i>
                        </div>
                        <div>
                            <h1 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                                Faculty Profile
                            </h1>
                            <nav class="text-sm text-gray-600 dark:text-gray-400 mt-1" aria-label="Breadcrumb">
                                <ol class="flex items-center space-x-2">
                                    <li>
                                        <a href="collegeadmin_dashboard.php" class="hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
                                            Dashboard
                                        </a>
                                    </li>
                                    <li class="flex items-center">
                                        <i class="fas fa-chevron-right text-gray-400 text-xs mx-2"></i>
                                        <a href="list_faculty.php" class="hover:text-blue-600 dark:hover:text-blue-400 transition-colors">Faculty Management</a>
                                    </li>
                                    <li class="flex items-center">
                                        <i class="fas fa-chevron-right text-gray-400 text-xs mx-2"></i>
                                        <span class="text-gray-900 dark:text-white font-medium"><?= htmlspecialchars($faculty['name']) ?></span>
                                    </li>
                                </ol>
                            </nav>
                        </div>
                    </div>
                    <div class="mt-6 lg:mt-0 flex flex-wrap gap-3">
                        <a href="list_faculty.php" class="btn btn-outline flex items-center text-sm hover:shadow-md transition-all duration-200">
                            <i class="fas fa-arrow-left mr-2"></i> Back to Faculty List
                        </a>
                        <a href="edit_faculty.php?id=<?= $faculty['id'] ?>" class="btn btn-primary flex items-center text-sm hover:shadow-md transition-all duration-200">
                            <i class="fas fa-edit mr-2"></i> Edit Profile
                        </a>
                    </div>
                </div>
            </div>

            <!-- Profile Header -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 mb-8 overflow-hidden">
                <!-- Cover Background -->
                <div class="h-32 bg-gradient-to-r from-blue-500 via-blue-600 to-blue-700 relative">
                    <div class="absolute inset-0 bg-black bg-opacity-20"></div>
                    <!-- Decorative Pattern -->
                    <div class="absolute inset-0 opacity-10">
                        <div class="absolute top-4 left-4 w-8 h-8 border-2 border-white rounded-full"></div>
                        <div class="absolute top-8 right-8 w-4 h-4 border border-white rounded-full"></div>
                        <div class="absolute bottom-4 left-8 w-6 h-6 border border-white rounded-full"></div>
                        <div class="absolute bottom-8 right-4 w-3 h-3 bg-white rounded-full"></div>
                    </div>
                </div>

                <!-- Profile Info -->
                <div class="px-6 pb-6">
                    <div class="flex flex-col sm:flex-row sm:items-end sm:space-x-6">
                        <!-- Profile Picture -->
                        <div class="relative -mt-16 mb-4 sm:mb-0">
                            <?php if (!empty($faculty['profile_image'])): ?>
                                <div class="w-32 h-32 rounded-2xl overflow-hidden border-4 border-white dark:border-gray-800 shadow-xl">
                                    <img src="../uploads/<?= htmlspecialchars($faculty['profile_image']) ?>" alt="Profile" class="w-full h-full object-cover">
                                </div>
                            <?php else: ?>
                                <div class="w-32 h-32 rounded-2xl bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-900/30 dark:to-blue-800/30 flex items-center justify-center text-blue-600 dark:text-blue-400 border-4 border-white dark:border-gray-800 shadow-xl">
                                    <i class="fas fa-chalkboard-teacher text-4xl"></i>
                                </div>
                            <?php endif; ?>
                            <!-- Status Indicator -->
                            <div class="absolute -bottom-2 -right-2">
                                <?php if (isset($faculty['status']) && $faculty['status'] === 'active'): ?>
                                    <div class="w-8 h-8 bg-green-500 rounded-full border-4 border-white dark:border-gray-800 flex items-center justify-center">
                                        <i class="fas fa-check text-white text-xs"></i>
                                    </div>
                                <?php else: ?>
                                    <div class="w-8 h-8 bg-gray-500 rounded-full border-4 border-white dark:border-gray-800 flex items-center justify-center">
                                        <i class="fas fa-pause text-white text-xs"></i>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Profile Details -->
                        <div class="flex-1 min-w-0">
                            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                                <div>
                                    <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
                                        <?= htmlspecialchars($faculty['name']) ?>
                                    </h1>
                                    <div class="flex flex-wrap items-center gap-3 mt-2">
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300">
                                            <i class="fas fa-building mr-1"></i>
                                            <?= htmlspecialchars($faculty['department']) ?>
                                        </span>
                                        <?php if (!empty($faculty['position'])): ?>
                                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300">
                                                <i class="fas fa-user-tie mr-1"></i>
                                                <?= htmlspecialchars($faculty['position']) ?>
                                            </span>
                                        <?php endif; ?>
                                        <?php if (isset($faculty['status']) && $faculty['status'] === 'active'): ?>
                                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300">
                                                <i class="fas fa-circle text-green-500 mr-1" style="font-size: 6px;"></i>
                                                Active
                                            </span>
                                        <?php else: ?>
                                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300">
                                                <i class="fas fa-circle text-red-500 mr-1" style="font-size: 6px;"></i>
                                                Inactive
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                    <div class="flex items-center mt-3 text-gray-600 dark:text-gray-400">
                                        <i class="fas fa-envelope mr-2"></i>
                                        <a href="mailto:<?= htmlspecialchars($faculty['email']) ?>" class="hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
                                            <?= htmlspecialchars($faculty['email']) ?>
                                        </a>
                                        <?php if (!empty($faculty['phone'])): ?>
                                            <span class="mx-3">•</span>
                                            <i class="fas fa-phone mr-2"></i>
                                            <a href="tel:<?= htmlspecialchars($faculty['phone']) ?>" class="hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
                                                <?= htmlspecialchars($faculty['phone']) ?>
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <?php if (!empty($schemaUpdateMessages)): ?>
                <div class="bg-blue-50 dark:bg-blue-900/30 border-l-4 border-blue-500 text-blue-700 dark:text-blue-400 p-4 mb-6 flex justify-between items-center" role="alert">
                    <div>
                        <p class="font-medium">Database schema has been updated</p>
                        <ul class="text-sm mt-1 list-disc list-inside">
                            <?php foreach ($schemaUpdateMessages as $message): ?>
                                <li><?= htmlspecialchars($message) ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                    <button type="button" class="text-blue-700 dark:text-blue-400 hover:text-blue-900" onclick="this.parentElement.style.display='none';">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            <?php endif; ?>
            
            <!-- Faculty Profile Information -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Left Column - Main Information -->
                <div class="lg:col-span-2 space-y-8">
                    <!-- Personal Information Card -->
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                        <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
                            <h2 class="text-lg font-semibold text-gray-800 dark:text-white flex items-center">
                                <i class="fas fa-user mr-3 text-blue-600"></i> Personal Information
                            </h2>
                        </div>

                        <div class="p-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div class="space-y-1">
                                    <h3 class="text-sm font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wide">Full Name</h3>
                                    <p class="text-lg font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($faculty['name']) ?></p>
                                </div>

                                <div class="space-y-1">
                                    <h3 class="text-sm font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wide">Email Address</h3>
                                    <div class="flex items-center space-x-2">
                                        <p class="text-lg font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($faculty['email']) ?></p>
                                        <a href="mailto:<?= htmlspecialchars($faculty['email']) ?>" class="text-blue-600 hover:text-blue-800 transition-colors">
                                            <i class="fas fa-external-link-alt text-sm"></i>
                                        </a>
                                    </div>
                                </div>

                                <div class="space-y-1">
                                    <h3 class="text-sm font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wide">Phone Number</h3>
                                    <?php if (!empty($faculty['phone'])): ?>
                                        <div class="flex items-center space-x-2">
                                            <p class="text-lg font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($faculty['phone']) ?></p>
                                            <a href="tel:<?= htmlspecialchars($faculty['phone']) ?>" class="text-blue-600 hover:text-blue-800 transition-colors">
                                                <i class="fas fa-external-link-alt text-sm"></i>
                                            </a>
                                        </div>
                                    <?php else: ?>
                                        <p class="text-lg font-medium text-gray-400 dark:text-gray-500 italic">Not provided</p>
                                    <?php endif; ?>
                                    </p>
                                </div>

                                <div class="space-y-1">
                                    <h3 class="text-sm font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wide">Account Status</h3>
                                    <?php if (isset($faculty['status']) && $faculty['status'] === 'active'): ?>
                                        <span class="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300">
                                            <i class="fas fa-circle text-green-500 mr-2" style="font-size: 8px;"></i>
                                            Active
                                        </span>
                                    <?php else: ?>
                                        <span class="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300">
                                            <i class="fas fa-circle text-red-500 mr-2" style="font-size: 8px;"></i>
                                            Inactive
                                        </span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Professional Information Card -->
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                        <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
                            <h2 class="text-lg font-semibold text-gray-800 dark:text-white flex items-center">
                                <i class="fas fa-briefcase mr-3 text-green-600"></i> Professional Information
                            </h2>
                        </div>

                        <div class="p-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                                <div class="space-y-1">
                                    <h3 class="text-sm font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wide">Department</h3>
                                    <p class="text-lg font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($faculty['department']) ?></p>
                                </div>

                                <div class="space-y-1">
                                    <h3 class="text-sm font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wide">Position/Designation</h3>
                                    <?php if (!empty($faculty['position'])): ?>
                                        <p class="text-lg font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($faculty['position']) ?></p>
                                    <?php else: ?>
                                        <p class="text-lg font-medium text-gray-400 dark:text-gray-500 italic">Not specified</p>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <?php if (!empty($faculty['bio'])): ?>
                                <div class="space-y-1">
                                    <h3 class="text-sm font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wide">Biography</h3>
                                    <div class="bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-700/30 dark:to-gray-800/30 rounded-lg p-4 text-gray-700 dark:text-gray-300 leading-relaxed">
                                        <?= nl2br(htmlspecialchars($faculty['bio'])) ?>
                                    </div>
                                </div>
                            <?php else: ?>
                                <div class="bg-gray-50 dark:bg-gray-700/30 rounded-lg p-4 text-center">
                                    <i class="fas fa-user-edit text-gray-400 text-2xl mb-2"></i>
                                    <p class="text-gray-500 dark:text-gray-400 italic">No biography information provided.</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Right Column - Sidebar -->
                <div class="lg:col-span-1">
                    <div class="space-y-6">
                        <!-- Quick Stats Card -->
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                            <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
                                <h3 class="text-lg font-semibold text-gray-800 dark:text-white flex items-center">
                                    <i class="fas fa-chart-line mr-3 text-purple-600"></i> Quick Stats
                                </h3>
                            </div>

                            <div class="p-6 space-y-4">
                                <div class="flex items-center justify-between p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                                    <div class="flex items-center">
                                        <i class="fas fa-users text-blue-600 mr-2"></i>
                                        <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Mentorships</span>
                                    </div>
                                    <span class="text-lg font-bold text-blue-600"><?= $mentorshipCount ?></span>
                                </div>

                                <div class="flex items-center justify-between p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                                    <div class="flex items-center">
                                        <i class="fas fa-calendar-plus text-green-600 mr-2"></i>
                                        <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Member Since</span>
                                    </div>
                                    <span class="text-sm font-semibold text-green-600"><?= date('M Y', strtotime($faculty['created_at'])) ?></span>
                                </div>

                                <div class="flex items-center justify-between p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
                                    <div class="flex items-center">
                                        <i class="fas fa-clock text-orange-600 mr-2"></i>
                                        <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Last Updated</span>
                                    </div>
                                    <span class="text-sm font-semibold text-orange-600">
                                        <?= isset($faculty['updated_at']) ? date('M d', strtotime($faculty['updated_at'])) : date('M d', strtotime($faculty['created_at'])) ?>
                                    </span>
                                </div>
                            </div>
                        </div>

                        <!-- Quick Actions Card -->
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                            <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
                                <h3 class="text-lg font-semibold text-gray-800 dark:text-white flex items-center">
                                    <i class="fas fa-bolt mr-3 text-blue-600"></i> Quick Actions
                                </h3>
                            </div>

                            <div class="p-6 space-y-3">
                                <a href="edit_faculty.php?id=<?= $faculty['id'] ?>" class="flex items-center p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors group">
                                    <i class="fas fa-edit text-blue-600 mr-3 group-hover:scale-110 transition-transform"></i>
                                    <div>
                                        <p class="text-sm font-medium text-gray-900 dark:text-white">Edit Profile</p>
                                        <p class="text-xs text-gray-600 dark:text-gray-400">Update faculty information</p>
                                    </div>
                                </a>

                                <?php if (isset($faculty['status']) && $faculty['status'] === 'active'): ?>
                                    <a href="edit_faculty.php?id=<?= $faculty['id'] ?>&action=deactivate" class="flex items-center p-3 rounded-lg hover:bg-yellow-50 dark:hover:bg-yellow-900/20 transition-colors group">
                                        <i class="fas fa-user-slash text-yellow-600 mr-3 group-hover:scale-110 transition-transform"></i>
                                        <div>
                                            <p class="text-sm font-medium text-gray-900 dark:text-white">Deactivate Account</p>
                                            <p class="text-xs text-gray-600 dark:text-gray-400">Suspend faculty access</p>
                                        </div>
                                    </a>
                                <?php else: ?>
                                    <a href="edit_faculty.php?id=<?= $faculty['id'] ?>&action=activate" class="flex items-center p-3 rounded-lg hover:bg-green-50 dark:hover:bg-green-900/20 transition-colors group">
                                        <i class="fas fa-user-check text-green-600 mr-3 group-hover:scale-110 transition-transform"></i>
                                        <div>
                                            <p class="text-sm font-medium text-gray-900 dark:text-white">Activate Account</p>
                                            <p class="text-xs text-gray-600 dark:text-gray-400">Restore faculty access</p>
                                        </div>
                                    </a>
                                <?php endif; ?>

                                <a href="list_faculty.php" class="flex items-center p-3 rounded-lg hover:bg-purple-50 dark:hover:bg-purple-900/20 transition-colors group">
                                    <i class="fas fa-list text-purple-600 mr-3 group-hover:scale-110 transition-transform"></i>
                                    <div>
                                        <p class="text-sm font-medium text-gray-900 dark:text-white">All Faculty</p>
                                        <p class="text-xs text-gray-600 dark:text-gray-400">View faculty list</p>
                                    </div>
                                </a>

                                <a href="add_faculty.php" class="flex items-center p-3 rounded-lg hover:bg-green-50 dark:hover:bg-green-900/20 transition-colors group">
                                    <i class="fas fa-user-plus text-green-600 mr-3 group-hover:scale-110 transition-transform"></i>
                                    <div>
                                        <p class="text-sm font-medium text-gray-900 dark:text-white">Add New Faculty</p>
                                        <p class="text-xs text-gray-600 dark:text-gray-400">Create faculty member</p>
                                    </div>
                                </a>
                            </div>
                        </div>

                        <!-- Contact Information Card -->
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                            <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
                                <h3 class="text-lg font-semibold text-gray-800 dark:text-white flex items-center">
                                    <i class="fas fa-address-card mr-3 text-green-600"></i> Contact Information
                                </h3>
                            </div>

                            <div class="p-6 space-y-4">
                                <div class="flex items-center space-x-3">
                                    <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
                                        <i class="fas fa-envelope text-blue-600 dark:text-blue-400"></i>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <p class="text-sm font-medium text-gray-900 dark:text-white">Email</p>
                                        <a href="mailto:<?= htmlspecialchars($faculty['email']) ?>" class="text-sm text-blue-600 dark:text-blue-400 hover:underline truncate block">
                                            <?= htmlspecialchars($faculty['email']) ?>
                                        </a>
                                    </div>
                                </div>

                                <?php if (!empty($faculty['phone'])): ?>
                                <div class="flex items-center space-x-3">
                                    <div class="w-10 h-10 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center">
                                        <i class="fas fa-phone text-green-600 dark:text-green-400"></i>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <p class="text-sm font-medium text-gray-900 dark:text-white">Phone</p>
                                        <a href="tel:<?= htmlspecialchars($faculty['phone']) ?>" class="text-sm text-green-600 dark:text-green-400 hover:underline">
                                            <?= htmlspecialchars($faculty['phone']) ?>
                                        </a>
                                    </div>
                                </div>
                                <?php endif; ?>

                                <div class="flex items-center space-x-3">
                                    <div class="w-10 h-10 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center">
                                        <i class="fas fa-building text-purple-600 dark:text-purple-400"></i>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <p class="text-sm font-medium text-gray-900 dark:text-white">Department</p>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">
                                            <?= htmlspecialchars($faculty['department']) ?>
                                        </p>
                                    </div>
                                </div>

                                <?php if (!empty($faculty['position'])): ?>
                                <div class="flex items-center space-x-3">
                                    <div class="w-10 h-10 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center">
                                        <i class="fas fa-user-tie text-orange-600 dark:text-orange-400"></i>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <p class="text-sm font-medium text-gray-900 dark:text-white">Position</p>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">
                                            <?= htmlspecialchars($faculty['position']) ?>
                                        </p>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    
    <!-- Reset Password Modal -->
    <div id="resetPasswordModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4">
            <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4 flex justify-between items-center">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">Reset Faculty Password</h3>
                <button type="button" class="text-gray-400 hover:text-gray-500" onclick="closeResetPasswordModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form action="edit_faculty.php" method="post" id="resetPasswordForm">
                <div class="p-6">
                    <input type="hidden" name="id" value="<?= $faculty_id ?>">
                    <input type="hidden" name="action" value="reset_password">
                    
                    <div class="mb-4">
                        <label for="new_password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">New Password</label>
                        <input 
                            type="password" 
                            id="new_password" 
                            name="password" 
                            class="form-input w-full" 
                            required
                            minlength="8"
                        >
                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Minimum 8 characters</p>
                    </div>
                    
                    <div class="mb-4">
                        <div class="flex items-center">
                            <input type="checkbox" id="send_email" name="send_credentials" class="h-4 w-4 text-primary border-gray-300 rounded focus:ring-primary" checked>
                            <label for="send_email" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                                Send new password to faculty via email
                            </label>
                        </div>
                    </div>
                    
                    <div class="flex justify-end space-x-3 mt-6">
                        <button type="button" class="btn btn-outline" onclick="closeResetPasswordModal()">
                            Cancel
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-key mr-2"></i> Reset Password
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <script>
        // Theme Toggle
        const themeToggleBtn = document.getElementById('themeToggle');
        
        function getThemePreference() {
            if (localStorage.getItem('color-theme')) {
                return localStorage.getItem('color-theme');
            }
            return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
        }
        
        function setThemePreference(theme) {
            localStorage.setItem('color-theme', theme);
            if (theme === 'dark') {
                document.documentElement.classList.add('dark');
            } else {
                document.documentElement.classList.remove('dark');
            }
        }
        
        // Set initial theme
        setThemePreference(getThemePreference());
        
        // Toggle theme when button is clicked
        themeToggleBtn.addEventListener('click', function() {
            const currentTheme = getThemePreference();
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            setThemePreference(newTheme);
        });
        
        // Reset Password Modal
        function showResetPasswordModal() {
            document.getElementById('resetPasswordModal').classList.remove('hidden');
        }
        
        function closeResetPasswordModal() {
            document.getElementById('resetPasswordModal').classList.add('hidden');
        }
        
        // Close modal when clicking outside
        document.getElementById('resetPasswordModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeResetPasswordModal();
            }
        });
        
        // Notifications dropdown (simplified example)
        document.getElementById('notificationsBtn')?.addEventListener('click', function() {
            alert('Notifications feature coming soon!');
        });
    </script>

<?php
// Include the footer
require_once '../includes/footer.php';
?>