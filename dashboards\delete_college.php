<?php
session_start();
require_once '../includes/db.php';

// Ensure only super admin can access this page
if (!isset($_SESSION['superadmin_id'])) {
    header("Location: ../auth/superadmin_login.php");
    exit;
}

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $_SESSION['flash_message'] = "Invalid request method.";
    header("Location: superadmin_dashboard.php");
    exit;
}

$college_id = $_POST['college_id'] ?? null;

if (!$college_id) {
    $_SESSION['flash_message'] = "Invalid college ID.";
    header("Location: superadmin_dashboard.php");
    exit;
}

// Fetch college details for confirmation message
$stmt = $conn->prepare("SELECT name FROM colleges WHERE id = ?");
$stmt->execute([$college_id]);
$college = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$college) {
    $_SESSION['flash_message'] = "College not found.";
    header("Location: superadmin_dashboard.php");
    exit;
}

try {
    // Start transaction
    $conn->beginTransaction();
    
    // Delete related records first (to maintain referential integrity)
    // This assumes you have foreign keys set up with ON DELETE CASCADE
    // If not, you'll need to delete records from related tables manually
    
    // Example: Delete college admins
    $stmt = $conn->prepare("DELETE FROM college_admins WHERE college_id = ?");
    $stmt->execute([$college_id]);
    
    // Example: Delete faculty
    $stmt = $conn->prepare("DELETE FROM faculties WHERE college_id = ?");
    $stmt->execute([$college_id]);
    
    // Example: Delete students
    $stmt = $conn->prepare("DELETE FROM students WHERE college_id = ?");
    $stmt->execute([$college_id]);
    
    // Example: Delete alumni
    $stmt = $conn->prepare("DELETE FROM alumni WHERE college_id = ?");
    $stmt->execute([$college_id]);
    
    // Finally, delete the college itself
    $stmt = $conn->prepare("DELETE FROM colleges WHERE id = ?");
    $stmt->execute([$college_id]);
    
    // Commit transaction
    $conn->commit();
    
    $_SESSION['flash_message'] = "College '" . htmlspecialchars($college['name']) . "' has been permanently deleted.";
    header("Location: superadmin_dashboard.php");
    exit;
    
} catch (Exception $e) {
    // Rollback transaction on error
    $conn->rollBack();
    
    $_SESSION['flash_message'] = "Error deleting college: " . $e->getMessage();
    header("Location: edit_college.php?id=" . $college_id);
    exit;
}
?> 