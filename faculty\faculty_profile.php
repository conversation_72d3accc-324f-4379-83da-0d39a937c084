<?php
require_once '../includes/db.php';
require_once '../includes/header.php';

if (!isset($_SESSION['faculty_id'])) {
    header("Location: ../auth/faculty_login.php");
    exit;
}

$faculty_id = $_SESSION['faculty_id'];
$success = $error = "";

// Fetch faculty data
$stmt = $conn->prepare("
    SELECT id, college_id, name, email, password, department, profile_photo, bio, created_at
    FROM faculties
    WHERE id = ?
");
$stmt->execute([$faculty_id]);
$faculty = $stmt->fetch(PDO::FETCH_ASSOC);

// Try to get additional columns if they exist
if ($faculty) {
    try {
        $extraStmt = $conn->prepare("SELECT position, phone, status, profile_image, updated_at FROM faculties WHERE id = ? LIMIT 1");
        $extraStmt->execute([$faculty_id]);
        $extraData = $extraStmt->fetch(PDO::FETCH_ASSOC);
        if ($extraData) {
            $faculty = array_merge($faculty, $extraData);
        }
    } catch (PDOException $e) {
        // Some columns don't exist, set defaults
        $faculty['position'] = $faculty['position'] ?? null;
        $faculty['phone'] = $faculty['phone'] ?? null;
        $faculty['status'] = $faculty['status'] ?? 'active';
        $faculty['profile_image'] = $faculty['profile_image'] ?? null;
        $faculty['updated_at'] = $faculty['updated_at'] ?? null;
    }
}

// Handle update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_profile'])) {
    $fields = [
        'department' => trim($_POST['department']),
        'bio' => trim($_POST['bio']),
    ];

    // Handle profile photo
    if (!empty($_FILES['profile_photo']['name'])) {
        $ext = pathinfo($_FILES['profile_photo']['name'], PATHINFO_EXTENSION);
        $photoName = "faculty_" . $faculty_id . "." . $ext;
        move_uploaded_file($_FILES['profile_photo']['tmp_name'], "../uploads/$photoName");
        $fields['profile_photo'] = $photoName;
    }

    $updateQuery = "UPDATE faculties SET " . implode(", ", array_map(fn($k) => "$k = ?", array_keys($fields))) . " WHERE id = ?";
    $stmt = $conn->prepare($updateQuery);
    $stmt->execute([...array_values($fields), $faculty_id]);

    $success = "Profile updated successfully!";
    header("Refresh:0");
}

// Password update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_password'])) {
    if (!password_verify($_POST['current_password'], $faculty['password'])) {
        $error = "Current password is incorrect.";
    } else {
        $newHashed = password_hash($_POST['new_password'], PASSWORD_DEFAULT);
        $stmt = $conn->prepare("UPDATE faculties SET password = ? WHERE id = ?");
        $stmt->execute([$newHashed, $faculty_id]);
        $success = "Password changed successfully!";
    }
}

// Fallback image
$photo = (!empty($faculty['profile_photo']) && file_exists("../uploads/{$faculty['profile_photo']}"))
    ? $faculty['profile_photo']
    : 'default.png';
?>

<h2 class="mb-4">Faculty Profile</h2>

<?php if ($error): ?><div class="alert alert-danger"><?= $error ?></div><?php endif; ?>
<?php if ($success): ?><div class="alert alert-success"><?= $success ?></div><?php endif; ?>

<form method="post" enctype="multipart/form-data" class="card p-4 shadow mb-4">
    <input type="hidden" name="update_profile" value="1">
    <div class="row mb-4 align-items-center">
        <div class="col-md-3 text-center">
            <img src="../uploads/<?= $photo ?>" width="100" height="100" class="rounded-circle border shadow-sm">
            <input type="file" name="profile_photo" class="form-control mt-2">
        </div>
        <div class="col-md-9">
            <div class="mb-3">
                <label class="form-label">Department</label>
                <input type="text" name="department" value="<?= htmlspecialchars($faculty['department']) ?>" class="form-control" required>
            </div>
            <div class="mb-3">
                <label class="form-label">Bio</label>
                <textarea name="bio" rows="3" class="form-control"><?= htmlspecialchars($faculty['bio']) ?></textarea>
            </div>
            <button class="btn btn-primary">Update Profile</button>
        </div>
    </div>
</form>

<form method="post" class="card p-4 shadow">
    <input type="hidden" name="update_password" value="1">
    <h5 class="mb-3">Change Password</h5>
    <div class="row">
        <div class="col-md-4"><input type="password" name="current_password" class="form-control" placeholder="Current Password" required></div>
        <div class="col-md-4"><input type="password" name="new_password" class="form-control" placeholder="New Password" required></div>
        <div class="col-md-4"><button class="btn btn-warning w-100">Update Password</button></div>
    </div>
</form>

<?php require_once '../includes/footer.php'; ?>
