/* LinkedIn-inspired Professional UI */

:root {
  --linkedin-blue: #0a66c2;
  --linkedin-blue-hover: #004182;
  --linkedin-light-blue: #e7f3ff;
  --linkedin-black: #191919;
  --linkedin-dark-gray: #38434f;
  --linkedin-gray: #86888a;
  --linkedin-light-gray: #f3f2ef;
  --linkedin-border: #e0e0e0;
  --linkedin-success: #057642;
  --linkedin-white: #ffffff;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--linkedin-light-gray);
}

::-webkit-scrollbar-thumb {
  background: #c7c7c7;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--linkedin-gray);
}

/* Dark mode scrollbar */
.dark ::-webkit-scrollbar-track {
  background: #1d2226;
}

.dark ::-webkit-scrollbar-thumb {
  background: #4e4e4e;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: #6e6e6e;
}

/* Global styles */
body {
  font-family: -apple-system, system-ui, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  background-color: var(--linkedin-light-gray);
  color: var(--linkedin-black);
}

.dark body {
  background-color: #121212;
  color: #f3f2ef;
}

/* Card styles */
.card {
  @apply bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 transition-shadow hover:shadow-md;
}

/* Avatar styles */
.avatar {
  @apply rounded-full object-cover border-2 border-white dark:border-gray-800;
}

/* Button styles */
.btn-primary {
  @apply bg-[#0a66c2] hover:bg-[#004182] text-white font-medium py-2 px-4 rounded transition-colors;
}

.btn-outline {
  @apply border border-[#0a66c2] text-[#0a66c2] hover:bg-[#e7f3ff] font-medium py-2 px-4 rounded transition-colors;
}

.btn-text {
  @apply text-[#0a66c2] hover:underline font-medium transition-colors;
}

.btn-secondary {
  @apply bg-gray-100 hover:bg-gray-200 text-gray-800 dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-gray-200 font-medium py-2 px-4 rounded transition-colors;
}

/* Navigation */
.nav-link {
  @apply text-gray-600 dark:text-gray-300 hover:text-[#0a66c2] dark:hover:text-[#0a66c2] transition-colors;
}

.nav-link.active {
  @apply text-[#0a66c2] dark:text-[#0a66c2] font-medium;
}

/* Post/feed styles */
.post {
  @apply bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-4 mb-4;
}

.post-header {
  @apply flex items-center mb-3;
}

.post-content {
  @apply text-gray-800 dark:text-gray-200 mb-3;
}

.post-actions {
  @apply flex items-center justify-between pt-3 border-t border-gray-200 dark:border-gray-700;
}

.action-button {
  @apply flex items-center justify-center px-3 py-2 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors;
}

/* Profile styles */
.profile-card {
  @apply bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 overflow-hidden;
}

.profile-cover {
  @apply h-24 md:h-32 lg:h-40 w-full object-cover;
}

.profile-photo-container {
  @apply relative -mt-12 md:-mt-16 px-4 pb-4;
}

.profile-photo {
  @apply w-24 h-24 md:w-32 md:h-32 rounded-full border-4 border-white dark:border-gray-800 object-cover;
}

.profile-info {
  @apply px-4 pb-4;
}

/* Section headers */
.section-header {
  @apply text-lg font-semibold text-gray-800 dark:text-white mb-4 flex items-center;
}

/* Stats card */
.stats-card {
  @apply bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-4 mb-4;
}

.stat-item {
  @apply flex justify-between items-center py-2;
}

.stat-label {
  @apply text-gray-600 dark:text-gray-400;
}

.stat-value {
  @apply font-medium text-gray-800 dark:text-white;
}

/* Quick action cards */
.quick-action {
  @apply flex items-center p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors;
}

.quick-action-icon {
  @apply text-[#0a66c2] text-xl mr-3 flex-shrink-0;
}

.quick-action-text {
  @apply font-medium text-gray-800 dark:text-white;
}

.quick-action-subtext {
  @apply text-sm text-gray-600 dark:text-gray-400;
}

/* Dropdown menu */
.dropdown-menu {
  @apply bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700;
}

.dropdown-item {
  @apply block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors;
}

/* Fix dark dropdown menu hover */
.dropdown-menu.bg-dark .dropdown-item {
  color: #ffffff;
}

.dropdown-menu.bg-dark .dropdown-item:hover,
.dropdown-menu.bg-dark .dropdown-item:focus {
  background-color: #343a40;
  color: #ffffff;
}

/* Badge styles */
.badge {
  @apply inline-flex items-center px-2 py-1 rounded-full text-xs font-medium;
}

.badge-blue {
  @apply bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200;
}

.badge-green {
  @apply bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200;
}

.badge-yellow {
  @apply bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200;
}

.badge-red {
  @apply bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200;
}

/* Input styles */
.form-input {
  @apply w-full px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-[#0a66c2] dark:focus:ring-[#0a66c2];
}

/* Search box */
.search-box {
  @apply bg-[#eef3f8] dark:bg-gray-700 flex items-center rounded-md px-3 py-1.5;
}

.search-input {
  @apply bg-transparent border-none focus:outline-none text-gray-800 dark:text-white w-full;
}

/* Notification badge */
.notification-badge {
  @apply absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center;
}