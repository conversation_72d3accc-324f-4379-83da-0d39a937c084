<?php
require_once '../includes/session_config.php';
require_once '../includes/db.php';

if (!isset($_SESSION['faculty_id'])) {
    header("Location: ../auth/faculty_login.php");
    exit;
}

$faculty_id = $_SESSION['faculty_id'];
$faculty_name = $_SESSION['faculty_name'];
$college_id = $_SESSION['college_id'];

// Get faculty profile
$stmt = $conn->prepare("SELECT profile_photo, department, bio FROM faculties WHERE id = ?");
$stmt->execute([$faculty_id]);
$faculty = $stmt->fetch(PDO::FETCH_ASSOC);

// Stat cards
$pending_students = $conn->prepare("SELECT COUNT(*) FROM students WHERE college_id = ? AND verified = 0");
$pending_students->execute([$college_id]);
$student_count = $pending_students->fetchColumn();

$unverified_alumni = $conn->prepare("SELECT COUNT(*) FROM alumni WHERE college_id = ? AND verified = 0");
$unverified_alumni->execute([$college_id]);
$alumni_count = $unverified_alumni->fetchColumn();

// Fallback profile
$photo = (!empty($faculty['profile_photo']) && file_exists("../uploads/{$faculty['profile_photo']}"))
    ? $faculty['profile_photo']
    : 'profile.gif';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Faculty Dashboard - Mentoshri</title>
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts - Inter -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        'primary': '#2563eb', /* Mentoshri Blue */
                        'primary-dark': '#1d4ed8',
                        'primary-light': '#dbeafe',
                        'accent': '#f59e0b', /* Gold/Highlight */
                        'accent-light': '#fef3c7',
                        'success': '#10b981',
                        'success-light': '#d1fae5',
                        'danger': '#ef4444',
                        'danger-light': '#fee2e2',
                        'warning': '#f59e0b',
                        'warning-light': '#fef3c7',
                    },
                    fontFamily: {
                        'sans': ['Inter', 'sans-serif'],
                    },
                    borderRadius: {
                        'card': '6px',
                    }
                }
            }
        }
    </script>
    <!-- Design System -->
    <link rel="stylesheet" href="../assets/css/design-system.css">
</head>
<body class="font-sans bg-gray-50 dark:bg-gray-900 min-h-screen flex flex-col">
    <!-- Header - LinkedIn Inspired -->
    <header class="bg-white dark:bg-gray-800 shadow-sm sticky top-0 z-50 border-b border-gray-200 dark:border-gray-700">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex items-center justify-between h-16">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="faculty_dashboard.php" class="flex-shrink-0">
                        <span class="text-primary dark:text-primary font-bold text-2xl">M<span class="hidden sm:inline">entoshri</span></span>
                    </a>
                    
                    <!-- Search Box (Desktop) -->
                    <div class="hidden md:block ml-4 lg:ml-6 w-64 lg:w-80">
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-search text-gray-400 dark:text-gray-500"></i>
                            </div>
                            <input type="text" placeholder="Search students, alumni, events..." 
                                class="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-full 
                                text-sm bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-gray-100 
                                focus:outline-none focus:ring-2 focus:ring-primary focus:bg-white dark:focus:bg-gray-800"
                                data-search-modal>
                        </div>
                    </div>
                </div>
                
                <!-- Navigation (Desktop) -->
                <nav class="hidden md:flex items-center space-x-1 lg:space-x-2">
                    <a href="faculty_dashboard.php" class="px-2 py-1 flex flex-col items-center text-center text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary">
                        <i class="fas fa-home text-xl"></i>
                        <span class="text-xs mt-1">Home</span>
                    </a>
                    
                    <a href="../chat/chat.php" class="px-2 py-1 flex flex-col items-center text-center text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary">
                        <div class="relative">
                            <i class="fas fa-comment-dots text-xl"></i>
                            <span class="absolute -top-1 -right-1 h-4 w-4 bg-red-500 rounded-full text-xs text-white flex items-center justify-center">3</span>
                        </div>
                        <span class="text-xs mt-1">Messaging</span>
                    </a>
                    
                    <a href="../faculty/invite_alumni.php" class="px-2 py-1 flex flex-col items-center text-center text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary">
                        <i class="fas fa-user-plus text-xl"></i>
                        <span class="text-xs mt-1">Invite</span>
                    </a>
                    
                    <a href="../faculty/approve_students.php" class="px-2 py-1 flex flex-col items-center text-center text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary">
                        <i class="fas fa-user-check text-xl"></i>
                        <span class="text-xs mt-1">Approve</span>
                    </a>
                    
                    <a href="../events/event_list.php" class="px-2 py-1 flex flex-col items-center text-center text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary">
                        <i class="fas fa-calendar-alt text-xl"></i>
                        <span class="text-xs mt-1">Events</span>
                    </a>
                    
                    <a href="../resources/resource_list.php" class="px-2 py-1 flex flex-col items-center text-center text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary">
                        <i class="fas fa-file-alt text-xl"></i>
                        <span class="text-xs mt-1">Resources</span>
                    </a>
                </nav>
                
                <!-- Right Side -->
                <div class="flex items-center space-x-3">
                    <!-- Search Button (Mobile) -->
                    <button data-search-modal class="md:hidden text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary">
                        <i class="fas fa-search"></i>
                    </button>
                    
                    <!-- Notifications -->
                    <div class="relative">
                        <button class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300 relative">
                            <i class="fas fa-bell"></i>
                            <span class="absolute -top-1 -right-1 h-4 w-4 bg-red-500 rounded-full text-xs text-white flex items-center justify-center">2</span>
                        </button>
                    </div>
                    
                    <!-- Theme Toggle -->
                    <button id="themeToggle" class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300">
                        <i class="fas fa-moon dark:hidden"></i>
                        <i class="fas fa-sun hidden dark:block"></i>
                    </button>
                    
                    <!-- User Menu -->
                    <div class="relative" data-dropdown>
                        <button class="flex items-center space-x-1 text-gray-700 dark:text-gray-300" data-dropdown-trigger>
                            <div class="w-8 h-8 rounded-full bg-primary-light dark:bg-primary-dark/30 flex items-center justify-center text-primary dark:text-primary-light overflow-hidden">
                                <i class="fas fa-user-tie"></i>
                            </div>
                            <span class="hidden md:inline-block"><?= htmlspecialchars($faculty_name) ?></span>
                            <i class="fas fa-chevron-down text-gray-400 text-xs"></i>
                        </button>
                        
                        <div class="absolute right-0 mt-2 w-64 bg-white dark:bg-gray-800 rounded-md shadow-lg border border-gray-200 dark:border-gray-700 hidden z-10" data-dropdown-menu>
                            <div class="p-3 border-b border-gray-200 dark:border-gray-700">
                                <div class="flex items-center">
                                    <div class="w-12 h-12 rounded-full bg-primary-light dark:bg-primary-dark/30 flex items-center justify-center text-primary dark:text-primary-light overflow-hidden mr-3">
                                        <i class="fas fa-user-tie text-xl"></i>
                                    </div>
                                    <div>
                                        <div class="font-medium text-gray-800 dark:text-white"><?= htmlspecialchars($faculty_name) ?></div>
                                        <div class="text-sm text-gray-600 dark:text-gray-400">Faculty</div>
                                    </div>
                                </div>
                                
                                <a href="../faculty/faculty_profile.php" class="mt-3 block w-full text-center py-2 bg-primary hover:bg-primary-dark text-white rounded text-sm font-medium transition duration-150 ease-in-out">
                                    View Profile
                                </a>
                            </div>
                            
                            <div class="py-1">
                                <a href="faculty_dashboard.php" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                    <i class="fas fa-home mr-2 text-gray-500 dark:text-gray-400"></i> Dashboard
                                </a>
                                <a href="../faculty/invite_alumni.php" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                    <i class="fas fa-user-plus mr-2 text-gray-500 dark:text-gray-400"></i> Invite Alumni
                                </a>
                                <a href="../faculty/approve_students.php" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                    <i class="fas fa-user-check mr-2 text-gray-500 dark:text-gray-400"></i> Approve Students
                                </a>
                                <a href="../faculty/faculty_settings.php" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                    <i class="fas fa-cog mr-2 text-gray-500 dark:text-gray-400"></i> Settings
                                </a>
                                
                                <div class="border-t border-gray-200 dark:border-gray-700 mt-1 pt-1">
                                    <button id="themeToggleMenu" class="w-full text-left block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                        <i class="fas fa-moon mr-2 text-gray-500 dark:text-gray-400 dark:hidden"></i>
                                        <i class="fas fa-sun mr-2 text-gray-500 dark:text-gray-400 hidden dark:inline-block"></i>
                                        <span class="dark:hidden">Dark Mode</span>
                                        <span class="hidden dark:inline-block">Light Mode</span>
                                    </button>
                                    <a href="../auth/logout.php" class="block px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20">
                                        <i class="fas fa-sign-out-alt mr-2"></i> Sign Out
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Mobile Menu Button -->
                    <button class="md:hidden text-gray-700 dark:text-gray-300" data-mobile-menu>
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>
            
            <!-- Mobile Menu -->
            <div class="md:hidden hidden bg-white dark:bg-gray-800 pt-2 pb-4 border-t border-gray-200 dark:border-gray-700" data-mobile-menu-panel>
                <a href="faculty_dashboard.php" class="flex items-center px-4 py-3 hover:bg-gray-100 dark:hover:bg-gray-700">
                    <i class="fas fa-home text-primary w-6"></i>
                    <span class="ml-3 text-gray-700 dark:text-gray-300">Dashboard</span>
                </a>
                
                <a href="../chat/chat.php" class="flex items-center px-4 py-3 hover:bg-gray-100 dark:hover:bg-gray-700">
                    <i class="fas fa-comment-dots text-primary w-6"></i>
                    <span class="ml-3 text-gray-700 dark:text-gray-300">Messages</span>
                    <span class="ml-auto bg-red-500 text-white text-xs rounded-full px-2 py-0.5">3</span>
                </a>
                
                <a href="../faculty/invite_alumni.php" class="flex items-center px-4 py-3 hover:bg-gray-100 dark:hover:bg-gray-700">
                    <i class="fas fa-user-plus text-primary w-6"></i>
                    <span class="ml-3 text-gray-700 dark:text-gray-300">Invite Alumni</span>
                </a>
                
                <a href="../faculty/approve_students.php" class="flex items-center px-4 py-3 hover:bg-gray-100 dark:hover:bg-gray-700">
                    <i class="fas fa-user-check text-primary w-6"></i>
                    <span class="ml-3 text-gray-700 dark:text-gray-300">Approve Students</span>
                </a>
                
                <a href="../events/event_list.php" class="flex items-center px-4 py-3 hover:bg-gray-100 dark:hover:bg-gray-700">
                    <i class="fas fa-calendar-alt text-primary w-6"></i>
                    <span class="ml-3 text-gray-700 dark:text-gray-300">Events</span>
                </a>
                
                <a href="../resources/resource_list.php" class="flex items-center px-4 py-3 hover:bg-gray-100 dark:hover:bg-gray-700">
                    <i class="fas fa-file-alt text-primary w-6"></i>
                    <span class="ml-3 text-gray-700 dark:text-gray-300">Resources</span>
                </a>
                
                <div class="border-t dark:border-gray-700 mt-2 pt-2">
                    <a href="../faculty/faculty_profile.php" class="flex items-center px-4 py-3 hover:bg-gray-100 dark:hover:bg-gray-700">
                        <i class="fas fa-user text-primary w-6"></i>
                        <span class="ml-3 text-gray-700 dark:text-gray-300">Profile</span>
                    </a>
                    <a href="../faculty/faculty_settings.php" class="flex items-center px-4 py-3 hover:bg-gray-100 dark:hover:bg-gray-700">
                        <i class="fas fa-cog text-primary w-6"></i>
                        <span class="ml-3 text-gray-700 dark:text-gray-300">Settings</span>
                    </a>
                    <a href="../auth/logout.php" class="flex items-center px-4 py-3 text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20">
                        <i class="fas fa-sign-out-alt w-6"></i>
                        <span class="ml-3">Sign Out</span>
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content - LinkedIn Inspired -->
    <main class="max-w-7xl mx-auto px-4 py-6 flex-grow">
        <!-- Welcome Header with Profile Summary -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6">
            <div class="flex flex-col md:flex-row items-start md:items-center gap-6">
                <!-- Profile Photo -->
                <div class="flex-shrink-0">
                    <div class="w-20 h-20 rounded-full bg-primary-light dark:bg-primary-dark/30 flex items-center justify-center text-primary dark:text-primary-light text-3xl overflow-hidden border-4 border-white dark:border-gray-800 shadow-md">
                        <?php if (!empty($photo) && file_exists("../uploads/{$photo}")): ?>
                            <img src="../uploads/<?= $photo ?>" alt="Profile" class="w-full h-full object-cover">
                        <?php else: ?>
                            <i class="fas fa-user-tie"></i>
                        <?php endif; ?>
                    </div>
                </div>
                
                <!-- Welcome Text & Profile Info -->
                <div class="flex-grow">
                    <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Welcome back, <?= htmlspecialchars($faculty_name) ?></h1>
                    <p class="text-gray-600 dark:text-gray-400 mt-1"><?= htmlspecialchars($faculty['department']) ?> Department</p>
                    <p class="text-gray-600 dark:text-gray-400 text-sm mt-1"><?= htmlspecialchars($faculty['bio'] ?? 'No bio available') ?></p>
                </div>
                
                <!-- Action Buttons -->
                <div class="flex flex-wrap gap-3 mt-4 md:mt-0">
                    <a href="../faculty/faculty_profile.php" class="inline-flex items-center justify-center px-4 py-2 border border-primary text-sm font-medium rounded-md text-primary bg-white dark:bg-gray-800 hover:bg-primary-light dark:hover:bg-primary-dark/20 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors duration-150 ease-in-out">
                        <i class="fas fa-edit mr-2"></i> Edit Profile
                    </a>
                    <a href="../events/create_event.php" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors duration-150 ease-in-out">
                        <i class="fas fa-calendar-plus mr-2"></i> Create Event
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <!-- Pending Students Card -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-5">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Pending Students</p>
                        <h3 class="text-2xl font-bold text-gray-800 dark:text-white mt-1"><?= $student_count ?></h3>
                    </div>
                    <div class="w-10 h-10 rounded-full bg-yellow-100 dark:bg-yellow-900/30 flex items-center justify-center text-yellow-600 dark:text-yellow-400">
                        <i class="fas fa-user-clock"></i>
                    </div>
                </div>
                <div class="mt-3">
                    <a href="../faculty/approve_students.php" class="text-primary hover:text-primary-dark dark:hover:text-primary-light text-sm font-medium flex items-center">
                        <span>Review Students</span>
                        <i class="fas fa-arrow-right ml-1 text-xs"></i>
                    </a>
                </div>
                    </div>
                    
                    <!-- Unverified Alumni Card -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-5">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm text-gray-600 dark:text-gray-400">Unverified Alumni</p>
                                <h3 class="text-2xl font-bold text-gray-800 dark:text-white mt-1"><?= $alumni_count ?></h3>
                            </div>
                            <div class="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center text-blue-600 dark:text-blue-400">
                                <i class="fas fa-user-graduate"></i>
                            </div>
                        </div>
                        <div class="mt-3">
                            <a href="../faculty/verify_alumni.php" class="text-primary hover:text-primary-dark dark:hover:text-primary-light text-sm font-medium flex items-center">
                                <span>Verify Alumni</span>
                                <i class="fas fa-arrow-right ml-1 text-xs"></i>
                            </a>
                        </div>
                    </div>
                    
                    <!-- Campaigns Card -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-5">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm text-gray-600 dark:text-gray-400">Active Campaigns</p>
                                <h3 class="text-2xl font-bold text-gray-800 dark:text-white mt-1">3</h3>
                            </div>
                            <div class="w-10 h-10 rounded-full bg-indigo-100 dark:bg-indigo-900/30 flex items-center justify-center text-indigo-600 dark:text-indigo-400">
                                <i class="fas fa-bullhorn"></i>
                            </div>
                        </div>
                        <div class="mt-3">
                            <a href="../campaigns/my_campaigns.php" class="text-primary hover:text-primary-dark dark:hover:text-primary-light text-sm font-medium flex items-center">
                                <span>View Campaigns</span>
                                <i class="fas fa-arrow-right ml-1 text-xs"></i>
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- Quick Actions Grid -->
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                    <a href="../faculty/invite_alumni.php" class="flex flex-col items-center justify-center p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 transition duration-150 ease-in-out">
                        <div class="w-12 h-12 rounded-full bg-primary-light dark:bg-primary-dark/30 flex items-center justify-center text-primary dark:text-primary-light mb-3">
                            <i class="fas fa-user-plus text-lg"></i>
                        </div>
                        <span class="text-sm font-medium text-gray-800 dark:text-white text-center">Invite Alumni</span>
                    </a>
                    
                    <a href="../faculty/approve_students.php" class="flex flex-col items-center justify-center p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 transition duration-150 ease-in-out">
                        <div class="w-12 h-12 rounded-full bg-yellow-100 dark:bg-yellow-900/30 flex items-center justify-center text-yellow-600 dark:text-yellow-400 mb-3">
                            <i class="fas fa-user-check text-lg"></i>
                        </div>
                        <span class="text-sm font-medium text-gray-800 dark:text-white text-center">Approve Students</span>
                    </a>
                    
                    <a href="../faculty/verify_alumni.php" class="flex flex-col items-center justify-center p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 transition duration-150 ease-in-out">
                        <div class="w-12 h-12 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center text-blue-600 dark:text-blue-400 mb-3">
                            <i class="fas fa-user-graduate text-lg"></i>
                        </div>
                        <span class="text-sm font-medium text-gray-800 dark:text-white text-center">Verify Alumni</span>
                    </a>
                    
                    <a href="../campaigns/create_campaign.php" class="flex flex-col items-center justify-center p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 transition duration-150 ease-in-out">
                        <div class="w-12 h-12 rounded-full bg-indigo-100 dark:bg-indigo-900/30 flex items-center justify-center text-indigo-600 dark:text-indigo-400 mb-3">
                            <i class="fas fa-bullhorn text-lg"></i>
                        </div>
                        <span class="text-sm font-medium text-gray-800 dark:text-white text-center">Create Campaign</span>
                    </a>
                </div>
            
            <!-- Recent Activity & Upcoming Events -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <!-- Recent Activity -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden hover:shadow-md transition-shadow duration-300">
                    <div class="px-5 py-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
                        <h3 class="font-semibold text-gray-800 dark:text-white flex items-center">
                            <i class="fas fa-history mr-2 text-primary"></i>
                            <span>Recent Activity</span>
                            <span class="ml-2 text-xs font-normal text-gray-500 dark:text-gray-400">Last 7 days</span>
                        </h3>
                    </div>
                    <div class="p-5">
                        <div class="space-y-5">
                            <!-- Activity Item -->
                            <div class="flex items-start group hover:bg-gray-50 dark:hover:bg-gray-700/50 p-3 rounded-lg transition-colors duration-150 border border-transparent hover:border-gray-200 dark:hover:border-gray-700">
                                <div class="w-12 h-12 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center text-blue-600 dark:text-blue-400 flex-shrink-0 group-hover:bg-blue-200 dark:group-hover:bg-blue-800/40 transition-colors duration-150 shadow-sm group-hover:shadow group-hover:scale-105">
                                    <i class="fas fa-user-check text-lg"></i>
                                </div>
                                <div class="ml-4 flex-grow">
                                    <div class="flex justify-between items-start">
                                        <p class="text-sm font-medium text-gray-800 dark:text-gray-200">Student Verification</p>
                                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                            <i class="fas fa-check-circle mr-1 opacity-70"></i> Approved
                                        </span>
                                    </div>
                                    <p class="text-sm text-gray-700 dark:text-gray-300 mt-1">You approved <a href="../dashboards/view_student.php?id=123" class="font-medium text-primary dark:text-primary-light hover:underline">Rahul Sharma's</a> student verification request</p>
                                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-2 flex items-center">
                                        <i class="fas fa-clock mr-1 opacity-70"></i> 2 hours ago
                                    </p>
                                </div>
                            </div>
                            
                            <!-- Activity Item -->
                            <div class="flex items-start group hover:bg-gray-50 dark:hover:bg-gray-700/50 p-3 rounded-lg transition-colors duration-150 border border-transparent hover:border-gray-200 dark:hover:border-gray-700">
                                <div class="w-12 h-12 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center text-green-600 dark:text-green-400 flex-shrink-0 group-hover:bg-green-200 dark:group-hover:bg-green-800/40 transition-colors duration-150 shadow-sm group-hover:shadow group-hover:scale-105">
                                    <i class="fas fa-file-upload text-lg"></i>
                                </div>
                                <div class="ml-4 flex-grow">
                                    <div class="flex justify-between items-start">
                                        <p class="text-sm font-medium text-gray-800 dark:text-gray-200">Resource Upload</p>
                                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                            <i class="fas fa-file-pdf mr-1 opacity-70"></i> PDF
                                        </span>
                                    </div>
                                    <p class="text-sm text-gray-700 dark:text-gray-300 mt-1">You uploaded <a href="../resources/view.php?id=456" class="font-medium text-primary dark:text-primary-light hover:underline">Interview Preparation Guide</a> to resources</p>
                                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-2 flex items-center">
                                        <i class="fas fa-clock mr-1 opacity-70"></i> Yesterday at 3:45 PM
                                    </p>
                                </div>
                            </div>
                            
                            <!-- Activity Item -->
                            <div class="flex items-start group hover:bg-gray-50 dark:hover:bg-gray-700/50 p-3 rounded-lg transition-colors duration-150 border border-transparent hover:border-gray-200 dark:hover:border-gray-700">
                                <div class="w-12 h-12 rounded-full bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center text-purple-600 dark:text-purple-400 flex-shrink-0 group-hover:bg-purple-200 dark:group-hover:bg-purple-800/40 transition-colors duration-150 shadow-sm group-hover:shadow group-hover:scale-105">
                                    <i class="fas fa-envelope text-lg"></i>
                                </div>
                                <div class="ml-4 flex-grow">
                                    <div class="flex justify-between items-start">
                                        <p class="text-sm font-medium text-gray-800 dark:text-gray-200">Alumni Invitation</p>
                                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                                            <i class="fas fa-paper-plane mr-1 opacity-70"></i> Sent
                                        </span>
                                    </div>
                                    <p class="text-sm text-gray-700 dark:text-gray-300 mt-1">You invited <a href="../faculty/invite_alumni.php" class="font-medium text-primary dark:text-primary-light hover:underline">15 alumni</a> to join the Mentoshri platform</p>
                                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-2 flex items-center">
                                        <i class="fas fa-clock mr-1 opacity-70"></i> 2 days ago at 11:20 AM
                                    </p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-5 pt-3 border-t border-gray-200 dark:border-gray-700">
                            <a href="../activity/all_activity.php" class="text-primary hover:text-primary-dark dark:hover:text-primary-light text-sm font-medium flex items-center justify-center group transition-all duration-200 hover:underline">
                                <span>View All Activity</span>
                                <i class="fas fa-arrow-right ml-1 text-xs group-hover:translate-x-1 transition-transform duration-200"></i>
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- Upcoming Events -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden hover:shadow-md transition-shadow duration-300">
                    <div class="px-5 py-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
                        <h3 class="font-semibold text-gray-800 dark:text-white flex items-center">
                            <i class="fas fa-calendar-alt mr-2 text-primary"></i>
                            Upcoming Events
                        </h3>
                    </div>
                    <div class="p-5">
                        <div class="space-y-5">
                            <!-- Event Item -->
                            <div class="flex items-start group hover:bg-gray-50 dark:hover:bg-gray-700/50 p-3 rounded-lg transition-colors duration-150 border border-transparent hover:border-gray-200 dark:hover:border-gray-700">
                                <div class="flex-shrink-0 w-16 h-16 flex flex-col items-center justify-center bg-primary text-white rounded-lg shadow group-hover:shadow-md group-hover:scale-105 transition-all duration-200">
                                    <span class="text-xs font-medium uppercase tracking-wider">OCT</span>
                                    <span class="text-xl font-bold leading-none">15</span>
                                </div>
                                <div class="ml-4 flex-grow">
                                    <div class="flex justify-between items-start">
                                        <p class="text-sm font-medium text-gray-800 dark:text-white group-hover:text-primary dark:group-hover:text-primary-light transition-colors duration-150">Career Fair 2023</p>
                                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                            <i class="fas fa-tag mr-1 opacity-70"></i> Career
                                        </span>
                                    </div>
                                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-1 flex items-center">
                                        <i class="fas fa-clock mr-1 opacity-70"></i> 10:00 AM - 4:00 PM
                                    </p>
                                    <div class="mt-2 flex items-center space-x-2">
                                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                            <i class="fas fa-map-marker-alt mr-1 opacity-70"></i> Main Campus
                                        </span>
                                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                            <i class="fas fa-users mr-1 opacity-70"></i> 120 Registered
                                        </span>
                                    </div>
                                    <div class="mt-3 flex justify-between items-center">
                                        <a href="../events/event_details.php?id=1" class="text-xs text-primary hover:text-primary-dark dark:hover:text-primary-light font-medium">View Details</a>
                                        <a href="../events/event_register.php?id=1" class="inline-flex items-center justify-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors duration-150">
                                            <i class="fas fa-calendar-check mr-1"></i> Register
                                        </a>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Event Item -->
                            <div class="flex items-start group hover:bg-gray-50 dark:hover:bg-gray-700/50 p-3 rounded-lg transition-colors duration-150 border border-transparent hover:border-gray-200 dark:hover:border-gray-700">
                                <div class="flex-shrink-0 w-16 h-16 flex flex-col items-center justify-center bg-purple-600 text-white rounded-lg shadow group-hover:shadow-md group-hover:scale-105 transition-all duration-200">
                                    <span class="text-xs font-medium uppercase tracking-wider">NOV</span>
                                    <span class="text-xl font-bold leading-none">05</span>
                                </div>
                                <div class="ml-4 flex-grow">
                                    <div class="flex justify-between items-start">
                                        <p class="text-sm font-medium text-gray-800 dark:text-white group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors duration-150">Alumni Networking Mixer</p>
                                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                                            <i class="fas fa-tag mr-1 opacity-70"></i> Networking
                                        </span>
                                    </div>
                                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-1 flex items-center">
                                        <i class="fas fa-clock mr-1 opacity-70"></i> 6:00 PM - 9:00 PM
                                    </p>
                                    <div class="mt-2 flex items-center space-x-2">
                                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                                            <i class="fas fa-map-marker-alt mr-1 opacity-70"></i> Virtual
                                        </span>
                                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                                            <i class="fas fa-users mr-1 opacity-70"></i> 45 Registered
                                        </span>
                                    </div>
                                    <div class="mt-3 flex justify-between items-center">
                                        <a href="../events/event_details.php?id=2" class="text-xs text-purple-600 hover:text-purple-800 dark:hover:text-purple-400 font-medium">View Details</a>
                                        <a href="../events/event_register.php?id=2" class="inline-flex items-center justify-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors duration-150">
                                            <i class="fas fa-calendar-check mr-1"></i> Register
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-5 pt-3 border-t border-gray-200 dark:border-gray-700">
                            <a href="../events/event_list.php" class="text-primary hover:text-primary-dark dark:hover:text-primary-light text-sm font-medium flex items-center justify-center group transition-all duration-200 hover:underline">
                                <span>View All Events</span>
                                <i class="fas fa-arrow-right ml-1 text-xs group-hover:translate-x-1 transition-transform duration-200"></i>
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- Quick Links -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden hover:shadow-md transition-shadow duration-300">
                    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
                        <h3 class="font-semibold text-gray-800 dark:text-white flex items-center">
                            <i class="fas fa-link mr-2 text-primary"></i>
                            <span>Quick Links</span>
                            <span class="ml-2 text-xs font-normal text-gray-500 dark:text-gray-400">Faculty Actions</span>
                        </h3>
                    </div>
                    <div class="p-5">
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-5">
                            <a href="../events/create_event.php" class="group flex flex-col items-center justify-center p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg hover:bg-blue-50 dark:hover:bg-blue-900/20 hover:shadow transition-all duration-200 ease-in-out border border-transparent hover:border-blue-200 dark:hover:border-blue-800/30">
                                <div class="w-14 h-14 rounded-full bg-primary-light dark:bg-primary-dark/30 flex items-center justify-center text-primary dark:text-primary-light mb-3 group-hover:scale-110 group-hover:bg-blue-200 dark:group-hover:bg-blue-800/40 transition-all duration-200 shadow-sm group-hover:shadow">
                                    <i class="fas fa-calendar-plus text-xl"></i>
                                </div>
                                <span class="text-sm font-medium text-gray-800 dark:text-white text-center group-hover:text-primary dark:group-hover:text-primary-light transition-colors duration-200">Create Event</span>
                                <span class="text-xs text-gray-500 dark:text-gray-400 mt-1 text-center">Schedule new events for students & alumni</span>
                            </a>
                            
                            <a href="../resources/upload.php" class="group flex flex-col items-center justify-center p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg hover:bg-purple-50 dark:hover:bg-purple-900/20 hover:shadow transition-all duration-200 ease-in-out border border-transparent hover:border-purple-200 dark:hover:border-purple-800/30">
                                <div class="w-14 h-14 rounded-full bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center text-purple-600 dark:text-purple-400 mb-3 group-hover:scale-110 group-hover:bg-purple-200 dark:group-hover:bg-purple-800/40 transition-all duration-200 shadow-sm group-hover:shadow">
                                    <i class="fas fa-file-upload text-xl"></i>
                                </div>
                                <span class="text-sm font-medium text-gray-800 dark:text-white text-center group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors duration-200">Upload Resource</span>
                                <span class="text-xs text-gray-500 dark:text-gray-400 mt-1 text-center">Share learning materials & documents</span>
                            </a>
                            
                            <a href="../faculty/invite_alumni.php" class="group flex flex-col items-center justify-center p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg hover:bg-green-50 dark:hover:bg-green-900/20 hover:shadow transition-all duration-200 ease-in-out border border-transparent hover:border-green-200 dark:hover:border-green-800/30">
                                <div class="w-14 h-14 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center text-green-600 dark:text-green-400 mb-3 group-hover:scale-110 group-hover:bg-green-200 dark:group-hover:bg-green-800/40 transition-all duration-200 shadow-sm group-hover:shadow">
                                    <i class="fas fa-user-plus text-xl"></i>
                                </div>
                                <span class="text-sm font-medium text-gray-800 dark:text-white text-center group-hover:text-green-600 dark:group-hover:text-green-400 transition-colors duration-200">Invite Alumni</span>
                                <span class="text-xs text-gray-500 dark:text-gray-400 mt-1 text-center">Grow your mentorship network</span>
                            </a>
                            
                            <a href="../campaigns/create_campaign.php" class="group flex flex-col items-center justify-center p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg hover:bg-indigo-50 dark:hover:bg-indigo-900/20 hover:shadow transition-all duration-200 ease-in-out border border-transparent hover:border-indigo-200 dark:hover:border-indigo-800/30">
                                <div class="w-14 h-14 rounded-full bg-indigo-100 dark:bg-indigo-900/30 flex items-center justify-center text-indigo-600 dark:text-indigo-400 mb-3 group-hover:scale-110 group-hover:bg-indigo-200 dark:group-hover:bg-indigo-800/40 transition-all duration-200 shadow-sm group-hover:shadow">
                                    <i class="fas fa-bullhorn text-xl"></i>
                                </div>
                                <span class="text-sm font-medium text-gray-800 dark:text-white text-center group-hover:text-indigo-600 dark:group-hover:text-indigo-400 transition-colors duration-200">Create Campaign</span>
                                <span class="text-xs text-gray-500 dark:text-gray-400 mt-1 text-center">Launch new initiatives & projects</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer - LinkedIn Inspired -->
    <footer class="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 mt-auto">
        <div class="max-w-7xl mx-auto px-4 py-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <!-- Logo and Copyright -->
                <div class="col-span-1">
                    <div class="flex items-center mb-4">
                        <span class="text-lg font-semibold text-primary dark:text-primary">M<span class="hidden sm:inline">entoshri</span></span>
                    </div>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">&copy; <?= date('Y') ?> Mentoshri. All rights reserved.</p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-500 hover:text-primary dark:text-gray-400 dark:hover:text-primary-light transition-colors duration-150">
                            <i class="fab fa-facebook"></i>
                        </a>
                        <a href="#" class="text-gray-500 hover:text-primary dark:text-gray-400 dark:hover:text-primary-light transition-colors duration-150">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="text-gray-500 hover:text-primary dark:text-gray-400 dark:hover:text-primary-light transition-colors duration-150">
                            <i class="fab fa-linkedin"></i>
                        </a>
                        <a href="#" class="text-gray-500 hover:text-primary dark:text-gray-400 dark:hover:text-primary-light transition-colors duration-150">
                            <i class="fab fa-instagram"></i>
                        </a>
                    </div>
                </div>
                
                <!-- Navigation Links -->
                <div class="col-span-1">
                    <h3 class="text-sm font-semibold text-gray-900 dark:text-white uppercase tracking-wider mb-4">Navigation</h3>
                    <ul class="space-y-2">
                        <li><a href="faculty_dashboard.php" class="text-sm text-gray-600 hover:text-primary dark:text-gray-400 dark:hover:text-primary-light transition-colors duration-150">Dashboard</a></li>
                        <li><a href="../faculty/faculty_profile.php" class="text-sm text-gray-600 hover:text-primary dark:text-gray-400 dark:hover:text-primary-light transition-colors duration-150">Profile</a></li>
                        <li><a href="../chat/chat.php" class="text-sm text-gray-600 hover:text-primary dark:text-gray-400 dark:hover:text-primary-light transition-colors duration-150">Messages</a></li>
                        <li><a href="../events/event_list.php" class="text-sm text-gray-600 hover:text-primary dark:text-gray-400 dark:hover:text-primary-light transition-colors duration-150">Events</a></li>
                        <li><a href="../resources/resource_list.php" class="text-sm text-gray-600 hover:text-primary dark:text-gray-400 dark:hover:text-primary-light transition-colors duration-150">Resources</a></li>
                    </ul>
                </div>
                
                <!-- Quick Links -->
                <div class="col-span-1">
                    <h3 class="text-sm font-semibold text-gray-900 dark:text-white uppercase tracking-wider mb-4">Quick Links</h3>
                    <ul class="space-y-2">
                        <li><a href="../faculty/invite_alumni.php" class="text-sm text-gray-600 hover:text-primary dark:text-gray-400 dark:hover:text-primary-light transition-colors duration-150">Invite Alumni</a></li>
                        <li><a href="../faculty/approve_students.php" class="text-sm text-gray-600 hover:text-primary dark:text-gray-400 dark:hover:text-primary-light transition-colors duration-150">Approve Students</a></li>
                        <li><a href="../faculty/verify_alumni.php" class="text-sm text-gray-600 hover:text-primary dark:text-gray-400 dark:hover:text-primary-light transition-colors duration-150">Verify Alumni</a></li>
                        <li><a href="../campaigns/create_campaign.php" class="text-sm text-gray-600 hover:text-primary dark:text-gray-400 dark:hover:text-primary-light transition-colors duration-150">Create Campaign</a></li>
                        <li><a href="../help/faculty_guide.php" class="text-sm text-gray-600 hover:text-primary dark:text-gray-400 dark:hover:text-primary-light transition-colors duration-150">Help & Support</a></li>
                    </ul>
                </div>
                
                <!-- Theme Toggle and Settings -->
                <div class="col-span-1">
                    <h3 class="text-sm font-semibold text-gray-900 dark:text-white uppercase tracking-wider mb-4">Settings</h3>
                    <div class="flex items-center mb-4">
                        <span class="text-sm text-gray-600 dark:text-gray-400 mr-2">Theme:</span>
                        <button id="themeToggle" class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300">
                            <i class="fas fa-moon dark:hidden"></i>
                            <i class="fas fa-sun hidden dark:block"></i>
                        </button>
                    </div>
                    <ul class="space-y-2">
                        <li><a href="../faculty/faculty_settings.php" class="text-sm text-gray-600 hover:text-primary dark:text-gray-400 dark:hover:text-primary-light transition-colors duration-150">Account Settings</a></li>
                        <li><a href="../faculty/privacy_settings.php" class="text-sm text-gray-600 hover:text-primary dark:text-gray-400 dark:hover:text-primary-light transition-colors duration-150">Privacy Settings</a></li>
                        <li><a href="../faculty/notification_settings.php" class="text-sm text-gray-600 hover:text-primary dark:text-gray-400 dark:hover:text-primary-light transition-colors duration-150">Notification Settings</a></li>
                    </ul>
                </div>
            </div>
            
            <!-- Bottom Footer -->
            <div class="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
                <p class="text-xs text-gray-500 dark:text-gray-400">Mentoshri connects students with alumni for mentorship, networking, and career guidance. Our platform helps educational institutions build stronger communities.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript for Dark Mode Toggle and Dropdowns -->
    <script>
        // Check for saved theme preference or use system preference
        const themeToggleBtn = document.getElementById('themeToggle');
        const themeToggleMenuBtn = document.getElementById('themeToggleMenu');
        
        // Set the initial theme based on localStorage or system preference
        if (localStorage.getItem('color-theme') === 'dark' || (!('color-theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
            document.documentElement.classList.add('dark');
        } else {
            document.documentElement.classList.remove('dark');
        }
        
        // Theme toggle functionality for header button
        themeToggleBtn.addEventListener('click', function() {
            document.documentElement.classList.toggle('dark');
            localStorage.setItem('color-theme', document.documentElement.classList.contains('dark') ? 'dark' : 'light');
        });
        
        // Theme toggle functionality for menu button
        if (themeToggleMenuBtn) {
            themeToggleMenuBtn.addEventListener('click', function() {
                document.documentElement.classList.toggle('dark');
                localStorage.setItem('color-theme', document.documentElement.classList.contains('dark') ? 'dark' : 'light');
            });
        }
        
        // Initialize dropdowns
        document.addEventListener('DOMContentLoaded', function() {
            // Dropdown functionality
            const dropdownTriggers = document.querySelectorAll('[data-dropdown-trigger]');
            
            dropdownTriggers.forEach(trigger => {
                trigger.addEventListener('click', function() {
                    const dropdown = this.closest('[data-dropdown]').querySelector('[data-dropdown-menu]');
                    dropdown.classList.toggle('hidden');
                });
            });
            
            // Close dropdowns when clicking outside
            document.addEventListener('click', function(event) {
                if (!event.target.closest('[data-dropdown]')) {
                    document.querySelectorAll('[data-dropdown-menu]').forEach(menu => {
                        menu.classList.add('hidden');
                    });
                }
            });
            
            // Mobile menu toggle
            const mobileMenuBtn = document.querySelector('[data-mobile-menu]');
            const mobileMenuPanel = document.querySelector('[data-mobile-menu-panel]');
            
            if (mobileMenuBtn && mobileMenuPanel) {
                mobileMenuBtn.addEventListener('click', function() {
                    mobileMenuPanel.classList.toggle('hidden');
                });
            }
        });
    </script>
</body>
</html>
