<?php
require_once '../includes/db.php';

$id = intval($_POST['id'] ?? 0); // Change to POST
$role = $_POST['role'] ?? ''; // Change to POST

if (!in_array($role, ['student', 'alumni'])) {
    echo "<div class='alert alert-danger'>Invalid role.</div>";
    exit;
}

$table = $role === 'student' ? 'students' : 'alumni';
$stmt = $conn->prepare("SELECT full_name, profile_photo, bio, skills, linkedin, github, twitter, website FROM $table WHERE id = ?");
$stmt->execute([$id]);
$user = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$user) {
    echo "<div class='alert alert-warning'>User  not found.</div>";
    exit;
}

$photo = (!empty($user['profile_photo']) && file_exists("../uploads/{$user['profile_photo']}")) 
    ? $user['profile_photo'] 
    : 'profile.gif';
?>

<div class="text-center mb-3">
    <img src="../uploads/<?= htmlspecialchars($photo) ?>" class="rounded-circle" width="100" height="100">
    <h5 class="mt-2"><?= htmlspecialchars($user['full_name']) ?></h5>
</div>

<?php if (!empty($user['bio'])): ?>
    <p><strong>Bio:</strong><br><?= nl2br(htmlspecialchars($user['bio'])) ?></p>
<?php endif; ?>

<?php if (!empty($user['skills'])): ?>
    <p><strong>Skills:</strong><br><?= htmlspecialchars($user['skills']) ?></p>
<?php endif; ?>

<?php if ($user['linkedin'] || $user['github'] || $user['twitter'] || $user['website']): ?>
    <p><strong>Links:</strong><br>
        <?php if ($user['linkedin']): ?><a href="<?= htmlspecialchars($user['linkedin']) ?>" target="_blank">LinkedIn</a><br><?php endif; ?>
        <?php if ($user['github']): ?><a href="<?= htmlspecialchars($user['github']) ?>" target="_blank">GitHub</a><br><?php endif; ?>
        <?php if ($user['twitter']): ?><a href="<?= htmlspecialchars($user['twitter']) ?>" target="_blank">Twitter</a><br><?php endif; ?>
        <?php if ($user['website']): ?><a href="<?= htmlspecialchars($user['website']) ?>" target="_blank">Website</a><?php endif; ?>
    </p>
<?php endif; ?>