<?php
session_start();
require_once '../includes/db.php';
require_once '../includes/db_schema_check.php';
require_once '../notify/send_email.php';

if (!isset($_SESSION['collegeadmin_id'])) {
    header("Location: ../auth/collegeadmin_login.php");
    exit;
}

$college_id = $_SESSION['college_id'];
$admin_name = $_SESSION['collegeadmin_name'] ?? 'Admin';

// Set role explicitly for the sidebar
$role = 'collegeadmin';
$user_id = $_SESSION['collegeadmin_id'];
$user_name = $_SESSION['collegeadmin_name'];
$current_page = basename($_SERVER['PHP_SELF']);

$success = '';
$error = '';

// Helper function for ordinal suffixes
function getOrdinalSuffix($number) {
    if (!in_array(($number % 100), array(11, 12, 13))) {
        switch ($number % 10) {
            case 1:  return 'st';
            case 2:  return 'nd';
            case 3:  return 'rd';
        }
    }
    return 'th';
}

// Get college details
$collegeStmt = $conn->prepare("SELECT name FROM colleges WHERE id = ?");
$collegeStmt->execute([$college_id]);
$college = $collegeStmt->fetch(PDO::FETCH_ASSOC);

// Handle bulk approve action
if (isset($_POST['bulk_action']) && isset($_POST['student_ids']) && is_array($_POST['student_ids'])) {
    $student_ids = $_POST['student_ids'];
    $action = $_POST['bulk_action'];
    
    if (!empty($student_ids)) {
        if ($action === 'approve') {
            try {
                $placeholders = implode(',', array_fill(0, count($student_ids), '?'));
                $params = array_merge($student_ids, [$college_id]);
                
                $stmt = $conn->prepare("UPDATE students SET verified = 1 WHERE id IN ($placeholders) AND college_id = ?");
                $stmt->execute($params);
                
                $count = $stmt->rowCount();
                $success = "$count student" . ($count !== 1 ? "s" : "") . " approved successfully.";
                
                // Send email notifications
                if (isset($_POST['send_notifications'])) {
                    $emailStmt = $conn->prepare("SELECT id, full_name, email FROM students WHERE id IN ($placeholders)");
                    $emailStmt->execute($student_ids);
                    $students = $emailStmt->fetchAll(PDO::FETCH_ASSOC);
                    
                    foreach ($students as $student) {
                        $subject = "Your Student Account Has Been Approved - " . htmlspecialchars($college['name']);
                        $message = "<p>Dear " . htmlspecialchars($student['full_name']) . ",</p>";
                        $message .= "<p>Your student account at " . htmlspecialchars($college['name']) . " has been approved.</p>";
                        $message .= "<p>You can now log in and access all features of the platform.</p>";
                        $message .= "<p>Login at: <a href='https://" . $_SERVER['HTTP_HOST'] . "/auth/student_login.php'>Student Login</a></p>";
                        $message .= "<p>Regards,<br>" . htmlspecialchars($admin_name) . "<br>Administration Team<br>" . htmlspecialchars($college['name']) . "</p>";
                        
                        sendEmail($student['email'], $student['full_name'], $subject, $message);
                    }
                    
                    $success .= " Email notifications have been sent.";
                }
            } catch (PDOException $e) {
                $error = "Error approving students: " . $e->getMessage();
            }
        } elseif ($action === 'reject') {
            try {
                $placeholders = implode(',', array_fill(0, count($student_ids), '?'));
                $params = array_merge($student_ids, [$college_id]);
                
                $stmt = $conn->prepare("DELETE FROM students WHERE id IN ($placeholders) AND college_id = ?");
                $stmt->execute($params);
                
                $count = $stmt->rowCount();
                $success = "$count student" . ($count !== 1 ? "s" : "") . " rejected and removed.";
            } catch (PDOException $e) {
                $error = "Error rejecting students: " . $e->getMessage();
            }
        }
    } else {
        $error = "No students selected.";
    }
}

// Handle individual approve action
if (isset($_GET['approve']) && !empty($_GET['approve'])) {
    $student_id = intval($_GET['approve']);
    
    try {
        $stmt = $conn->prepare("UPDATE students SET verified = 1 WHERE id = ? AND college_id = ?");
        $stmt->execute([$student_id, $college_id]);
        
        if ($stmt->rowCount() > 0) {
            // Get student details for email
            $studentStmt = $conn->prepare("SELECT full_name, email FROM students WHERE id = ?");
            $studentStmt->execute([$student_id]);
            $student = $studentStmt->fetch(PDO::FETCH_ASSOC);
            
            // Send email notification
            if ($student && isset($_GET['notify']) && $_GET['notify'] == '1') {
                $subject = "Your Student Account Has Been Approved - " . htmlspecialchars($college['name']);
                $message = "<p>Dear " . htmlspecialchars($student['full_name']) . ",</p>";
                $message .= "<p>Your student account at " . htmlspecialchars($college['name']) . " has been approved.</p>";
                $message .= "<p>You can now log in and access all features of the platform.</p>";
                $message .= "<p>Login at: <a href='https://" . $_SERVER['HTTP_HOST'] . "/auth/student_login.php'>Student Login</a></p>";
                $message .= "<p>Regards,<br>" . htmlspecialchars($admin_name) . "<br>Administration Team<br>" . htmlspecialchars($college['name']) . "</p>";
                
                $email_sent = sendEmail($student['email'], $student['full_name'], $subject, $message);
                
                $success = "Student " . htmlspecialchars($student['full_name']) . " approved successfully.";
                if ($email_sent) {
                    $success .= " Notification email has been sent.";
                }
            } else {
                $success = "Student approved successfully.";
            }
        } else {
            $error = "Student not found or already approved.";
        }
    } catch (PDOException $e) {
        $error = "Error approving student: " . $e->getMessage();
    }
}

// Handle individual reject action
if (isset($_GET['reject']) && !empty($_GET['reject'])) {
    $student_id = intval($_GET['reject']);
    
    try {
        // Get student name before deleting
        $nameStmt = $conn->prepare("SELECT full_name FROM students WHERE id = ? AND college_id = ?");
        $nameStmt->execute([$student_id, $college_id]);
        $student_name = $nameStmt->fetchColumn();
        
        $stmt = $conn->prepare("DELETE FROM students WHERE id = ? AND college_id = ?");
        $stmt->execute([$student_id, $college_id]);
        
        if ($stmt->rowCount() > 0) {
            $success = "Student " . ($student_name ? htmlspecialchars($student_name) : "ID #$student_id") . " rejected and removed.";
        } else {
            $error = "Student not found.";
        }
    } catch (PDOException $e) {
        $error = "Error rejecting student: " . $e->getMessage();
    }
}

// Get pending students
$stmt = $conn->prepare("SELECT * FROM students WHERE verified = 0 AND college_id = ? ORDER BY created_at DESC");
$stmt->execute([$college_id]);
$students = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Count total pending students
$countStmt = $conn->prepare("SELECT COUNT(*) FROM students WHERE verified = 0 AND college_id = ?");
$countStmt->execute([$college_id]);
$totalPending = $countStmt->fetchColumn();

// Include the header
require_once '../includes/header.php';
?>

    <!-- Main Content -->
    <main class="flex-grow py-8 px-4">
        <div class="container mx-auto">
            <!-- Breadcrumb -->
            <nav class="mb-6 text-sm" aria-label="Breadcrumb">
                <ol class="flex items-center space-x-2">
                    <li>
                        <a href="collegeadmin_dashboard.php" class="text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary-light">
                            <i class="fas fa-home"></i>
                            <span class="sr-only">Dashboard</span>
                        </a>
                    </li>
                    <li class="flex items-center">
                        <i class="fas fa-chevron-right text-gray-400 text-xs mx-1"></i>
                        <a href="list_students.php" class="text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary-light">
                            Student Management
                        </a>
                    </li>
                    <li class="flex items-center">
                        <i class="fas fa-chevron-right text-gray-400 text-xs mx-1"></i>
                        <span class="text-gray-700 dark:text-gray-300">Approve Students</span>
                    </li>
                </ol>
            </nav>

            <div class="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                        <i class="fas fa-user-check text-primary mr-3"></i>
                        Approve Student Registrations
                    </h1>
                    <p class="text-gray-600 dark:text-gray-400 mt-1">
                        Review and approve pending student registrations for <?= htmlspecialchars($college['name'] ?? 'your college') ?>
                    </p>
                </div>
                <div class="mt-4 md:mt-0">
                    <a href="list_students.php" class="btn btn-outline flex items-center">
                        <i class="fas fa-arrow-left mr-2"></i> Back to Students
                    </a>
                </div>
            </div>
            
            <?php if ($error): ?>
                <div class="bg-red-50 dark:bg-red-900/30 border-l-4 border-red-500 text-red-700 dark:text-red-400 p-4 mb-6 flex justify-between items-center" role="alert">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-circle mr-2"></i>
                        <span><?= htmlspecialchars($error) ?></span>
                    </div>
                    <button type="button" class="text-red-700 dark:text-red-400 hover:opacity-75" onclick="this.parentElement.style.display='none';">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="bg-green-50 dark:bg-green-900/30 border-l-4 border-green-500 text-green-700 dark:text-green-400 p-4 mb-6 flex justify-between items-center" role="alert">
                    <div class="flex items-center">
                        <i class="fas fa-check-circle mr-2"></i>
                        <span><?= htmlspecialchars($success) ?></span>
                    </div>
                    <button type="button" class="text-green-700 dark:text-green-400 hover:opacity-75" onclick="this.parentElement.style.display='none';">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            <?php endif; ?>
            
            <!-- Main Content -->
            <?php if (count($students) > 0): ?>
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 overflow-hidden mb-6">
                    <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4 flex justify-between items-center">
                        <h2 class="font-semibold text-gray-800 dark:text-white flex items-center">
                            <i class="fas fa-user-clock mr-2 text-primary"></i> 
                            Pending Students <span class="ml-2 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300 text-xs font-medium px-2.5 py-0.5 rounded-full"><?= $totalPending ?></span>
                        </h2>
                        <div class="text-sm text-gray-600 dark:text-gray-400">
                            <?= date('F j, Y') ?>
                        </div>
                    </div>
                    
                    <form method="post" id="bulkActionForm">
                        <div class="overflow-x-auto">
                            <table class="w-full text-left">
                                <thead class="bg-gray-50 dark:bg-gray-700 text-xs uppercase">
                                    <tr>
                                        <th class="px-6 py-3 text-gray-500 dark:text-gray-400 font-medium">
                                            <div class="flex items-center">
                                                <input 
                                                    type="checkbox" 
                                                    id="selectAll" 
                                                    class="w-4 h-4 text-primary bg-gray-100 border-gray-300 rounded focus:ring-primary-light dark:bg-gray-700 dark:border-gray-600"
                                                >
                                                <label for="selectAll" class="sr-only">Select All</label>
                                            </div>
                                        </th>
                                        <th class="px-6 py-3 text-gray-500 dark:text-gray-400 font-medium">Student</th>
                                        <th class="px-6 py-3 text-gray-500 dark:text-gray-400 font-medium">Roll Number</th>
                                        <th class="px-6 py-3 text-gray-500 dark:text-gray-400 font-medium">Department</th>
                                        <th class="px-6 py-3 text-gray-500 dark:text-gray-400 font-medium">Year</th>
                                        <th class="px-6 py-3 text-gray-500 dark:text-gray-400 font-medium">Registration Date</th>
                                        <th class="px-6 py-3 text-gray-500 dark:text-gray-400 font-medium">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($students as $student): ?>
                                        <tr class="border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700/30">
                                            <td class="px-6 py-4">
                                                <div class="flex items-center">
                                                    <input 
                                                        type="checkbox" 
                                                        name="student_ids[]" 
                                                        value="<?= $student['id'] ?>" 
                                                        class="student-checkbox w-4 h-4 text-primary bg-gray-100 border-gray-300 rounded focus:ring-primary-light dark:bg-gray-700 dark:border-gray-600"
                                                    >
                                                </div>
                                            </td>
                                            <td class="px-6 py-4">
                                                <div class="flex items-center">
                                                    <div class="w-10 h-10 rounded-full bg-gray-100 dark:bg-gray-700 overflow-hidden mr-3 flex-shrink-0">
                                                        <?php if (!empty($student['profile_photo']) && file_exists("../uploads/{$student['profile_photo']}")): ?>
                                                            <img src="../uploads/<?= htmlspecialchars($student['profile_photo']) ?>" alt="Student" class="w-full h-full object-cover">
                                                        <?php else: ?>
                                                            <div class="w-full h-full flex items-center justify-center text-gray-400 dark:text-gray-500">
                                                                <i class="fas fa-user-graduate"></i>
                                                            </div>
                                                        <?php endif; ?>
                                                    </div>
                                                    <div>
                                                        <div class="font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($student['full_name']) ?></div>
                                                        <div class="text-sm text-gray-500 dark:text-gray-400"><?= htmlspecialchars($student['email']) ?></div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 text-gray-700 dark:text-gray-300">
                                                <?= htmlspecialchars($student['roll_number']) ?>
                                            </td>
                                            <td class="px-6 py-4">
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300">
                                                    <?= htmlspecialchars($student['department']) ?>
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 text-gray-700 dark:text-gray-300">
                                                <?= $student['year'] ?><?= getOrdinalSuffix($student['year']) ?> Year
                                            </td>
                                            <td class="px-6 py-4 text-gray-700 dark:text-gray-300">
                                                <?= date('M d, Y', strtotime($student['created_at'])) ?>
                                            </td>
                                            <td class="px-6 py-4">
                                                <div class="flex space-x-2">
                                                    <a href="?approve=<?= $student['id'] ?>&notify=1" class="text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300" title="Approve with notification">
                                                        <i class="fas fa-check-circle"></i>
                                                    </a>
                                                    <a href="?approve=<?= $student['id'] ?>" class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300" title="Approve without notification">
                                                        <i class="fas fa-check"></i>
                                                    </a>
                                                    <a href="?reject=<?= $student['id'] ?>" class="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300" onclick="return confirm('Are you sure you want to reject this student? This action cannot be undone.');" title="Reject">
                                                        <i class="fas fa-times-circle"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="p-6 bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
                            <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                                <div class="flex items-center mb-4 md:mb-0">
                                    <span class="mr-2 text-sm text-gray-700 dark:text-gray-300">With selected:</span>
                                    <select name="bulk_action" class="form-select text-sm py-1" required>
                                        <option value="">Choose action</option>
                                        <option value="approve">Approve</option>
                                        <option value="reject">Reject</option>
                                    </select>
                                    <button type="submit" id="bulkActionBtn" class="btn btn-sm btn-primary ml-2" disabled>
                                        Apply
                                    </button>
                                </div>
                                
                                <div class="flex items-center">
                                    <input 
                                        type="checkbox" 
                                        id="send_notifications" 
                                        name="send_notifications" 
                                        checked
                                        class="w-4 h-4 text-primary bg-gray-100 border-gray-300 rounded focus:ring-primary-light dark:bg-gray-700 dark:border-gray-600"
                                    >
                                    <label for="send_notifications" class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                                        Send email notifications
                                    </label>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                
                <!-- Tips Card -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 p-6">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
                        <i class="fas fa-lightbulb text-yellow-500 mr-2"></i> Tips for Student Verification
                    </h3>
                    <ul class="space-y-3 text-gray-600 dark:text-gray-400">
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                            <span>Verify that the student's email domain matches your institution's domain.</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                            <span>Check that the roll number follows your institution's format.</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                            <span>Confirm department and year information is accurate.</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                            <span>Use bulk actions to approve multiple verified students at once.</span>
                        </li>
                    </ul>
                </div>
            <?php else: ?>
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 p-10 text-center">
                    <div class="w-20 h-20 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-check-double text-gray-400 dark:text-gray-500 text-3xl"></i>
                    </div>
                    <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-2">All caught up!</h2>
                    <p class="text-gray-600 dark:text-gray-400 mb-6">
                        There are no pending student registrations to approve at this time.
                    </p>
                    <div class="flex justify-center space-x-4">
                        <a href="list_students.php" class="btn btn-outline">
                            <i class="fas fa-users mr-2"></i> View All Students
                        </a>
                        <a href="add_student.php" class="btn btn-primary">
                            <i class="fas fa-user-plus mr-2"></i> Add Student
                        </a>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </main>

<?php
// Include the footer
require_once '../includes/footer.php';
?>

<script>
    // Select all checkbox functionality
    document.getElementById('selectAll').addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('.student-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateBulkActionButton();
    });
    
    // Update bulk action button state
    function updateBulkActionButton() {
        const checkboxes = document.querySelectorAll('.student-checkbox:checked');
        const bulkActionBtn = document.getElementById('bulkActionBtn');
        
        if (checkboxes.length > 0) {
            bulkActionBtn.disabled = false;
            bulkActionBtn.classList.remove('opacity-50', 'cursor-not-allowed');
        } else {
            bulkActionBtn.disabled = true;
            bulkActionBtn.classList.add('opacity-50', 'cursor-not-allowed');
        }
    }
    
    // Add event listeners to all checkboxes
    document.querySelectorAll('.student-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateBulkActionButton();
            
            // Update select all checkbox
            const allCheckboxes = document.querySelectorAll('.student-checkbox');
            const checkedCheckboxes = document.querySelectorAll('.student-checkbox:checked');
            document.getElementById('selectAll').checked = allCheckboxes.length === checkedCheckboxes.length;
        });
    });
    
    // Initialize bulk action button state
    updateBulkActionButton();
    
    // Confirm bulk reject action
    document.getElementById('bulkActionForm').addEventListener('submit', function(e) {
        const action = document.querySelector('select[name="bulk_action"]').value;
        const checkboxes = document.querySelectorAll('.student-checkbox:checked');
        
        if (action === 'reject' && checkboxes.length > 0) {
            if (!confirm(`Are you sure you want to reject ${checkboxes.length} student(s)? This action cannot be undone.`)) {
                e.preventDefault();
            }
        }
    });
</script> 