<?php
session_start();
require_once '../includes/db.php';

// Check if user is logged in as college admin
if (!isset($_SESSION['collegeadmin_id'])) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

// Check if template ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Invalid template ID']);
    exit;
}

$template_id = (int)$_GET['id'];
$college_id = $_SESSION['college_id'];

try {
    // Check if email_templates table exists, create if not
    try {
        $conn->query("SELECT 1 FROM email_templates LIMIT 1");
    } catch (PDOException $e) {
        // Table doesn't exist, create it
        $conn->exec("CREATE TABLE IF NOT EXISTS email_templates (
            id INT AUTO_INCREMENT PRIMARY KEY,
            college_id INT,
            title VARCHAR(255) NOT NULL,
            subject VARCHAR(255) NOT NULL,
            content TEXT NOT NULL,
            created_at DATETIME NOT NULL,
            updated_at DATETIME NULL
        )");
    }
    
    // Get template details
    $stmt = $conn->prepare("SELECT * FROM email_templates WHERE id = ? AND (college_id = ? OR college_id IS NULL)");
    $stmt->execute([$template_id, $college_id]);
    $template = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($template) {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'subject' => $template['subject'],
            'content' => $template['content']
        ]);
    } else {
        header('Content-Type: application/json');
        echo json_encode(['success' => false, 'message' => 'Template not found']);
    }
} catch (PDOException $e) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
}
?> 