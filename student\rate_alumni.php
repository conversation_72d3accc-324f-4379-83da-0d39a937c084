<?php
require_once '../includes/db.php';
require_once '../includes/header.php';
require_once '../includes/gamify.php'; // Include the gamification logic

if (!isset($_SESSION['student_id'])) {
    header("Location: ../auth/student_login.php");
    exit;
}

$student_id = $_SESSION['student_id'];
$alumni_id = $_GET['alumni_id'] ?? null;
$success = $error = '';

// Validate the mentorship link
if ($alumni_id) {
    $stmt = $conn->prepare("SELECT COUNT(*) FROM student_requests WHERE student_id = ? AND alumni_id = ? AND status = 'accepted'");
    $stmt->execute([$student_id, $alumni_id]);
    $has_access = $stmt->fetchColumn();
    if (!$has_access) {
        $error = "You do not have permission to rate this alumni.";
        $alumni_id = null;
    }
} else {
    $error = "Invalid request.";
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $alumni_id) {
    $score = (int) $_POST['score'];
    $feedback = trim($_POST['feedback']);

    if ($score >= 1 && $score <= 5) {
        $stmt = $conn->prepare("INSERT INTO ratings (student_id, alumni_id, score, feedback) VALUES (?, ?, ?, ?)");
        if ($stmt->execute([$student_id, $alumni_id, $score, $feedback])) {
            // Award XP for the alumni rating
            awardXPForAlumniRating($conn, $student_id);

            $success = "Thanks for your feedback!";
        } else {
            $error = "An error occurred while submitting feedback.";
        }
    } else {
        $error = "Please select a valid rating.";
    }
}
?>

<div class="container mt-4">
    <h3>⭐ Rate Your Alumni Mentor</h3>
    <?php if ($error): ?><div class="alert alert-danger"><?= htmlspecialchars($error) ?></div><?php endif; ?>
    <?php if ($success): ?><div class="alert alert-success"><?= htmlspecialchars($success) ?></div><?php endif; ?>

    <?php if ($alumni_id && !$success): ?>
    <form method="POST" class="card p-4 shadow-sm">
        <div class="mb-3">
            <label class="form-label">Rating (1 to 5)</label>
            <select name="score" class="form-select" required>
                <option value="">Select</option>
                <?php for ($i = 1; $i <= 5; $i++): ?>
                    <option value="<?= $i ?>"><?= $i ?> Star<?= $i > 1 ? 's' : '' ?></option>
                <?php endfor; ?>
            </select>
        </div>
        <div class="mb-3">
            <label class="form-label">Feedback</label>
            <textarea name="feedback" rows="4" class="form-control" placeholder="Write your experience..."></textarea>
        </div>
        <button class="btn btn-primary">Submit Feedback</button>
    </form>
    <?php endif; ?>
</div>

<?php require_once '../includes/footer.php'; ?>