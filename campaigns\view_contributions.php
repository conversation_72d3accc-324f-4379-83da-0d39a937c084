<?php
require_once '../includes/db.php';
require_once '../includes/header.php';
require_once '../includes/campaign_helpers.php';
require_once '../includes/contribution_helpers.php';

session_start();

// Determine role and permissions
$is_admin = isset($_SESSION['collegeadmin_id']);
$is_faculty = isset($_SESSION['faculty_id']);
$user_id = $is_admin ? $_SESSION['collegeadmin_id'] : ($_SESSION['faculty_id'] ?? null);
$college_id = $_SESSION['college_id'] ?? null;

if (!$user_id || !$college_id) {
    header("Location: ../auth/alumni_login.php");
    exit;
}

$campaign_id = $_GET['campaign_id'] ?? null;
if (!$campaign_id) {
    echo "<div class='alert alert-danger m-4'>No campaign selected.</div>";
    exit;
}

// Fetch campaign with access control
$campaign = getCampaignById($conn, $campaign_id);
if (!$campaign || $campaign['college_id'] != $college_id) {
    echo "<div class='alert alert-danger m-4'>Campaign not found or access denied.</div>";
    exit;
}

// Faculty can only view their own campaigns
if (
    $is_faculty &&
    ($campaign['creator_role'] !== 'faculty' || $campaign['created_by_user_id'] != $user_id)
) {
    echo "<div class='alert alert-warning m-4'>You can only view contributions to campaigns you created.</div>";
    exit;
}

// Handle status update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['pledge_id'], $_POST['status'])) {
    $statusUpdate = $conn->prepare("UPDATE alumni_pledges SET status = ? WHERE id = ?");
    $statusUpdate->execute([$_POST['status'], $_POST['pledge_id']]);
    header("Location: view_contributions.php?campaign_id=" . $campaign_id);
    exit;
}

// Fetch pledges
$pledges = getPledgesByCampaign($conn, $campaign_id);
?>

<div class="container mt-4">
    <h2>🎁 Contributions for: <?= htmlspecialchars($campaign['title']) ?></h2>
    <p class="text-muted">Track alumni pledges and update their status.</p>

    <?php if (count($pledges) === 0): ?>
        <div class="alert alert-info">No contributions yet.</div>
    <?php endif; ?>

    <?php foreach ($pledges as $pledge): ?>
        <div class="card mb-3 shadow-sm">
            <div class="card-body">
                <h5><?= $pledge['is_anonymous'] ? 'Anonymous Alumni' : htmlspecialchars($pledge['full_name']) ?></h5>
                <p class="mb-1"><strong>Type:</strong> <?= ucfirst($pledge['pledge_type']) ?></p>

                <?php if ($pledge['pledge_type'] === 'monetary' && $pledge['amount']): ?>
                    <p class="mb-1"><strong>Amount:</strong> ₹<?= number_format($pledge['amount']) ?></p>
                <?php endif; ?>

                <p class="mb-1"><strong>Description:</strong> <?= nl2br(htmlspecialchars($pledge['description'])) ?></p>

                <form method="POST" class="d-inline-block mb-2">
                    <input type="hidden" name="pledge_id" value="<?= $pledge['id'] ?>">
                    <label class="form-label me-2"><strong>Status:</strong></label>
                    <select name="status" class="form-select form-select-sm d-inline-block w-auto" onchange="this.form.submit()">
                        <?php foreach (['pending', 'in-progress', 'fulfilled', 'declined'] as $status): ?>
                            <option value="<?= $status ?>" <?= $pledge['status'] === $status ? 'selected' : '' ?>>
                                <?= ucfirst($status) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </form>

                <?php if (!$pledge['is_anonymous']): ?>
                    <p class="mb-0"><strong>Email:</strong> <?= htmlspecialchars($pledge['email']) ?></p>
                <?php endif; ?>

                <p class="text-muted small">Pledged on <?= date("d M Y", strtotime($pledge['created_at'])) ?></p>
            </div>
        </div>
    <?php endforeach; ?>
</div>

<?php require_once '../includes/footer.php'; ?>
