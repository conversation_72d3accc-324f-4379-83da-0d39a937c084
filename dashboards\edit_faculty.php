<?php
// Include session configuration
require_once '../includes/session_config.php';
require_once '../includes/db.php';
require_once '../includes/db_schema_check.php';
require_once '../notify/send_email.php';

if (!isset($_SESSION['collegeadmin_id'])) {
    header("Location: ../auth/collegeadmin_login.php");
    exit;
}

$college_id = $_SESSION['college_id'];
$admin_name = $_SESSION['collegeadmin_name'];
$id = $_GET['id'] ?? null;
$email_sent = false;

if (!$id) {
    $_SESSION['flash_message'] = "Invalid faculty ID.";
    header("Location: list_faculty.php");
    exit;
}

// Get college details
$collegeStmt = $conn->prepare("SELECT name FROM colleges WHERE id = ?");
$collegeStmt->execute([$college_id]);
$college = $collegeStmt->fetch(PDO::FETCH_ASSOC);

// Get faculty details
$stmt = $conn->prepare("
    SELECT id, college_id, name, email, password, department, profile_photo, bio, created_at
    FROM faculties
    WHERE id = ? AND college_id = ?
");
$stmt->execute([$id, $college_id]);
$faculty = $stmt->fetch(PDO::FETCH_ASSOC);

// Try to get additional columns if they exist
if ($faculty) {
    try {
        $extraStmt = $conn->prepare("SELECT position, phone, status, profile_image, updated_at FROM faculties WHERE id = ? LIMIT 1");
        $extraStmt->execute([$id]);
        $extraData = $extraStmt->fetch(PDO::FETCH_ASSOC);
        if ($extraData) {
            $faculty = array_merge($faculty, $extraData);
        }
    } catch (PDOException $e) {
        // Some columns don't exist, set defaults
        $faculty['position'] = $faculty['position'] ?? null;
        $faculty['phone'] = $faculty['phone'] ?? null;
        $faculty['status'] = $faculty['status'] ?? 'active';
        $faculty['profile_image'] = $faculty['profile_image'] ?? null;
        $faculty['updated_at'] = $faculty['updated_at'] ?? null;
    }
}

if (!$faculty) {
    $_SESSION['flash_message'] = "Faculty not found or access denied.";
    header("Location: list_faculty.php");
    exit;
}

// Get departments for suggestions
$deptStmt = $conn->prepare("SELECT DISTINCT department FROM faculties WHERE college_id = ? AND id != ? ORDER BY department ASC");
$deptStmt->execute([$college_id, $id]);
$departments = $deptStmt->fetchAll(PDO::FETCH_COLUMN);

$error = $success = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = trim($_POST['name']);
    $email = trim($_POST['email']);
    $department = trim($_POST['department']);
    $position = trim($_POST['position'] ?? '');
    $bio = trim($_POST['bio'] ?? '');
    $phone = trim($_POST['phone'] ?? '');
    $status = $_POST['status'] ?? 'active';
    $password = $_POST['password'] ?? '';
    $send_credentials = isset($_POST['send_credentials']);

    if (!$name || !$email || !$department) {
        $error = "Name, email, and department are required.";
    } else {
        // Check if email already exists for another faculty
        $check = $conn->prepare("SELECT COUNT(*) FROM faculties WHERE email = ? AND id != ? AND college_id = ?");
        $check->execute([$email, $id, $college_id]);
        $exists = $check->fetchColumn();
        
        if ($exists) {
            $error = "A faculty member with this email already exists.";
        } else {
            try {
                // Try to update with all fields, fall back to basic fields if some columns don't exist
                try {
                    // Build update SQL with all fields
                    $updateFields = [];
                    $updateValues = [];

                    // Always include required fields
                    $updateFields[] = "name = ?";
                    $updateFields[] = "email = ?";
                    $updateFields[] = "department = ?";
                    $updateValues = [$name, $email, $department];

                    // Add optional fields
                    $updateFields[] = "position = ?";
                    $updateValues[] = $position;

                    $updateFields[] = "bio = ?";
                    $updateValues[] = $bio;

                    $updateFields[] = "phone = ?";
                    $updateValues[] = $phone;

                    $updateFields[] = "status = ?";
                    $updateValues[] = $status;

                    // Add password if provided
                    if ($password) {
                        $hashed = password_hash($password, PASSWORD_DEFAULT);
                        $updateFields[] = "password = ?";
                        $updateValues[] = $hashed;
                    }

                    // Add updated_at
                    $updateFields[] = "updated_at = NOW()";

                    // Add ID and college_id to values array
                    $updateValues[] = $id;
                    $updateValues[] = $college_id;

                    $sql = "UPDATE faculties SET " . implode(', ', $updateFields) . " WHERE id = ? AND college_id = ?";
                    $update = $conn->prepare($sql);
                    $update->execute($updateValues);

                } catch (PDOException $e) {
                    // If some columns don't exist, try with basic fields only
                    $basicFields = ["name = ?", "email = ?", "department = ?"];
                    $basicValues = [$name, $email, $department];

                    // Add password if provided
                    if ($password) {
                        $hashed = password_hash($password, PASSWORD_DEFAULT);
                        $basicFields[] = "password = ?";
                        $basicValues[] = $hashed;
                    }

                    // Add bio if it exists in the original schema
                    if (isset($faculty['bio'])) {
                        $basicFields[] = "bio = ?";
                        $basicValues[] = $bio;
                    }

                    $basicValues[] = $id;
                    $basicValues[] = $college_id;

                    $basicSql = "UPDATE faculties SET " . implode(', ', $basicFields) . " WHERE id = ? AND college_id = ?";
                    $basicUpdate = $conn->prepare($basicSql);
                    $basicUpdate->execute($basicValues);
                }
                
                $success = "Faculty updated successfully.";
                
                // Refresh data
                $stmt->execute([$id, $college_id]);
                $faculty = $stmt->fetch(PDO::FETCH_ASSOC);
                
            } catch (PDOException $e) {
                $error = "Error updating faculty member: " . $e->getMessage();
            }
        }
    }
}

// Handle delete request
if (isset($_GET['action']) && $_GET['action'] === 'delete' && isset($_GET['confirm']) && $_GET['confirm'] === 'yes') {
    try {
        $deleteStmt = $conn->prepare("DELETE FROM faculties WHERE id = ? AND college_id = ?");
        $deleteStmt->execute([$id, $college_id]);
        
        $_SESSION['flash_message'] = "Faculty member deleted successfully.";
        $_SESSION['flash_type'] = 'success';
        header("Location: list_faculty.php");
        exit;
        
    } catch (PDOException $e) {
        $_SESSION['flash_message'] = "Error deleting faculty member: " . $e->getMessage();
        $_SESSION['flash_type'] = 'error';
    }
}

// Set role explicitly for the sidebar
$role = 'collegeadmin';
$user_id = $_SESSION['collegeadmin_id'];
$user_name = $_SESSION['collegeadmin_name'];
$current_page = basename($_SERVER['PHP_SELF']);

// Set dashboard URL for header
$dashboard_url = 'collegeadmin_dashboard.php';

// Include the header
require_once '../includes/header.php';
?>

    <!-- Main Content -->
    <main class="flex-grow py-6 px-4">
        <div class="max-w-7xl mx-auto">
            <!-- Page Header -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8">
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="bg-gradient-to-br from-green-500 to-green-600 p-3 rounded-xl shadow-lg">
                            <i class="fas fa-user-edit text-white text-xl"></i>
                        </div>
                        <div>
                            <h1 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                                Edit Faculty Member
                                <span class="ml-3 px-3 py-1 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 text-sm font-medium rounded-full">
                                    ID: <?= $faculty['id'] ?>
                                </span>
                            </h1>
                            <p class="text-gray-600 dark:text-gray-400 mt-1">
                                Update information for <span class="font-medium text-green-600 dark:text-green-400"><?= htmlspecialchars($faculty['name']) ?></span>
                                <span class="mx-2">•</span>
                                <span class="text-sm"><?= htmlspecialchars($faculty['department'] ?? 'No Department') ?></span>
                            </p>
                        </div>
                    </div>
                    <div class="mt-6 lg:mt-0 flex flex-wrap gap-3">
                        <a href="list_faculty.php" class="btn btn-outline flex items-center text-sm hover:shadow-md transition-all duration-200">
                            <i class="fas fa-arrow-left mr-2"></i> Back to List
                        </a>
                        <a href="view_faculty.php?id=<?= $faculty['id'] ?>" class="btn btn-outline flex items-center text-sm hover:shadow-md transition-all duration-200">
                            <i class="fas fa-eye mr-2"></i> View Profile
                        </a>
                        <button type="button" class="btn btn-primary flex items-center text-sm hover:shadow-md transition-all duration-200" onclick="document.getElementById('facultyForm').scrollIntoView()">
                            <i class="fas fa-edit mr-2"></i> Edit Details
                        </button>
                    </div>
                </div>
            </div>

            <!-- Flash Messages -->
            <?php if ($error): ?>
                <div class="bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 rounded-xl p-4 mb-6">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-circle text-red-500 mr-3"></i>
                        <span class="text-red-700 dark:text-red-300"><?= htmlspecialchars($error) ?></span>
                    </div>
                </div>
            <?php endif; ?>

            <?php if ($success): ?>
                <div class="bg-green-50 dark:bg-green-900/30 border border-green-200 dark:border-green-800 rounded-xl p-4 mb-6">
                    <div class="flex items-center">
                        <i class="fas fa-check-circle text-green-500 mr-3"></i>
                        <span class="text-green-700 dark:text-green-300"><?= htmlspecialchars($success) ?></span>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Main Content Grid -->
            <div class="grid grid-cols-1 xl:grid-cols-4 gap-8">
                <!-- Left Column - Main Form (3/4 width) -->
                <div class="xl:col-span-3">
                    <!-- Current Profile Summary -->
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 mb-8">
                        <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
                            <h2 class="text-lg font-semibold text-gray-800 dark:text-white flex items-center">
                                <i class="fas fa-id-card mr-3 text-gray-600"></i> Current Profile
                            </h2>
                        </div>
                        <div class="p-6">
                            <div class="flex items-center space-x-4">
                                <?php if (!empty($faculty['profile_image'])): ?>
                                    <div class="w-20 h-20 rounded-xl overflow-hidden border-2 border-gray-200 dark:border-gray-700 shadow-lg">
                                        <img src="../uploads/<?= htmlspecialchars($faculty['profile_image']) ?>" alt="Profile" class="w-full h-full object-cover">
                                    </div>
                                <?php else: ?>
                                    <div class="w-20 h-20 rounded-xl bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-900/30 dark:to-blue-800/30 flex items-center justify-center text-blue-600 dark:text-blue-400 border-2 border-gray-200 dark:border-gray-700 shadow-lg">
                                        <i class="fas fa-chalkboard-teacher text-2xl"></i>
                                    </div>
                                <?php endif; ?>
                                <div class="flex-1">
                                    <h3 class="font-semibold text-gray-900 dark:text-white text-xl"><?= htmlspecialchars($faculty['name']) ?></h3>
                                    <div class="flex flex-wrap items-center gap-2 mt-2">
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300">
                                            <i class="fas fa-building mr-1"></i>
                                            <?= htmlspecialchars($faculty['department']) ?>
                                        </span>
                                        <?php if (isset($faculty['status']) && $faculty['status'] === 'active'): ?>
                                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300">
                                                <i class="fas fa-circle text-green-500 mr-1" style="font-size: 6px;"></i>
                                                Active
                                            </span>
                                        <?php else: ?>
                                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300">
                                                <i class="fas fa-circle text-red-500 mr-1" style="font-size: 6px;"></i>
                                                Inactive
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                    <div class="mt-2 text-sm text-gray-600 dark:text-gray-400 flex items-center">
                                        <i class="fas fa-envelope mr-2"></i>
                                        <?= htmlspecialchars($faculty['email']) ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Edit Form -->
                    <form method="post" id="facultyForm" class="space-y-8">
                        <!-- Personal Information Card -->
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                            <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
                                <h2 class="text-lg font-semibold text-gray-800 dark:text-white flex items-center">
                                    <i class="fas fa-user mr-3 text-blue-600"></i> Personal Information
                                </h2>
                                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">Update basic details about the faculty member</p>
                            </div>
                            <div class="p-6 space-y-6">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div class="form-group">
                                        <label for="name" class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                                            Full Name <span class="text-red-500">*</span>
                                        </label>
                                        <div class="relative">
                                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <i class="fas fa-user text-gray-400"></i>
                                            </div>
                                            <input
                                                type="text"
                                                id="name"
                                                name="name"
                                                class="form-input pl-10 w-full rounded-lg border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                                                value="<?= htmlspecialchars($faculty['name']) ?>"
                                                required
                                            >
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label for="email" class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                                            Email Address <span class="text-red-500">*</span>
                                        </label>
                                        <div class="relative">
                                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <i class="fas fa-envelope text-gray-400"></i>
                                            </div>
                                            <input
                                                type="email"
                                                id="email"
                                                name="email"
                                                class="form-input pl-10 w-full rounded-lg border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                                                value="<?= htmlspecialchars($faculty['email']) ?>"
                                                required
                                            >
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label for="phone" class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                                            Phone Number
                                        </label>
                                        <div class="relative">
                                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <i class="fas fa-phone text-gray-400"></i>
                                            </div>
                                            <input
                                                type="tel"
                                                id="phone"
                                                name="phone"
                                                class="form-input pl-10 w-full rounded-lg border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                                                value="<?= htmlspecialchars($faculty['phone'] ?? '') ?>"
                                            >
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label for="status" class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                                            Account Status
                                        </label>
                                        <select
                                            id="status"
                                            name="status"
                                            class="form-select w-full rounded-lg border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                                        >
                                            <option value="active" <?= ($faculty['status'] ?? 'active') === 'active' ? 'selected' : '' ?>>Active</option>
                                            <option value="inactive" <?= ($faculty['status'] ?? 'active') === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Professional Information Card -->
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                            <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
                                <h2 class="text-lg font-semibold text-gray-800 dark:text-white flex items-center">
                                    <i class="fas fa-briefcase mr-3 text-green-600"></i> Professional Information
                                </h2>
                                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">Update academic and professional details</p>
                            </div>

                            <div class="p-6 space-y-6">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div class="form-group">
                                        <label for="department" class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                                            Department <span class="text-red-500">*</span>
                                        </label>
                                        <div class="relative">
                                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <i class="fas fa-building text-gray-400"></i>
                                            </div>
                                            <input
                                                type="text"
                                                id="department"
                                                name="department"
                                                class="form-input pl-10 w-full rounded-lg border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                                                value="<?= htmlspecialchars($faculty['department']) ?>"
                                                list="departments"
                                                required
                                            >
                                            <datalist id="departments">
                                                <?php foreach ($departments as $dept): ?>
                                                    <option value="<?= htmlspecialchars($dept) ?>">
                                                <?php endforeach; ?>
                                            </datalist>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label for="position" class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                                            Position/Designation
                                        </label>
                                        <div class="relative">
                                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <i class="fas fa-user-tie text-gray-400"></i>
                                            </div>
                                            <input
                                                type="text"
                                                id="position"
                                                name="position"
                                                class="form-input pl-10 w-full rounded-lg border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                                                value="<?= htmlspecialchars($faculty['position'] ?? '') ?>"
                                                placeholder="e.g., Professor, Assistant Professor"
                                            >
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="bio" class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                                        Bio/Description
                                    </label>
                                    <textarea
                                        id="bio"
                                        name="bio"
                                        rows="4"
                                        class="form-textarea w-full rounded-lg border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                                        placeholder="Brief description about the faculty member..."
                                    ><?= htmlspecialchars($faculty['bio'] ?? '') ?></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- Account Information Card -->
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                            <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
                                <h2 class="text-lg font-semibold text-gray-800 dark:text-white flex items-center">
                                    <i class="fas fa-key mr-3 text-orange-600"></i> Account Information
                                </h2>
                                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">Update login credentials and account settings</p>
                            </div>

                            <div class="p-6 space-y-6">
                                <div class="form-group">
                                    <label for="password" class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                                        New Password
                                    </label>
                                    <div class="relative">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <i class="fas fa-lock text-gray-400"></i>
                                        </div>
                                        <input
                                            type="password"
                                            id="password"
                                            name="password"
                                            class="form-input pl-10 w-full rounded-lg border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                                            placeholder="Leave blank to keep current password"
                                        >
                                    </div>
                                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                        <i class="fas fa-info-circle mr-1"></i>
                                        Leave blank to keep the current password unchanged
                                    </p>
                                </div>

                                <div class="form-group">
                                    <div class="flex items-center">
                                        <input
                                            type="checkbox"
                                            id="send_credentials"
                                            name="send_credentials"
                                            class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                        >
                                        <label for="send_credentials" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                                            Send updated credentials to faculty email
                                        </label>
                                    </div>
                                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-1 ml-6">
                                        Only sends email if password is changed
                                    </p>
                                </div>

                                <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
                                    <a href="list_faculty.php" class="btn btn-outline">
                                        <i class="fas fa-times mr-2"></i> Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary shadow-lg hover:shadow-xl transition-all duration-200">
                                        <i class="fas fa-save mr-2"></i> Update Faculty
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Right Column - Sidebar (1/4 width) -->
                <div class="xl:col-span-1">
                    <div class="space-y-6">
                        <!-- Quick Actions -->
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
                            <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
                                <h3 class="text-lg font-semibold text-gray-800 dark:text-white flex items-center">
                                    <i class="fas fa-bolt mr-3 text-blue-600"></i> Quick Actions
                                </h3>
                            </div>
                            <div class="p-6 space-y-3">
                                <a href="view_faculty.php?id=<?= $faculty['id'] ?>" class="flex items-center p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                                    <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-eye text-blue-600 dark:text-blue-400"></i>
                                    </div>
                                    <div>
                                        <p class="font-medium text-gray-900 dark:text-white">View Profile</p>
                                        <p class="text-sm text-gray-500 dark:text-gray-400">See complete faculty details</p>
                                    </div>
                                </a>

                                <a href="add_faculty.php" class="flex items-center p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                                    <div class="w-10 h-10 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-user-plus text-green-600 dark:text-green-400"></i>
                                    </div>
                                    <div>
                                        <p class="font-medium text-gray-900 dark:text-white">Add New Faculty</p>
                                        <p class="text-sm text-gray-500 dark:text-gray-400">Create another faculty member</p>
                                    </div>
                                </a>

                                <a href="list_faculty.php" class="flex items-center p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                                    <div class="w-10 h-10 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-list text-purple-600 dark:text-purple-400"></i>
                                    </div>
                                    <div>
                                        <p class="font-medium text-gray-900 dark:text-white">All Faculty</p>
                                        <p class="text-sm text-gray-500 dark:text-gray-400">Manage all faculty members</p>
                                    </div>
                                </a>
                            </div>
                        </div>

                        <!-- Danger Zone -->
                        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-red-200 dark:border-red-800">
                            <div class="border-b border-red-200 dark:border-red-800 px-6 py-4">
                                <h3 class="text-lg font-semibold text-red-800 dark:text-red-300 flex items-center">
                                    <i class="fas fa-exclamation-triangle mr-3"></i> Danger Zone
                                </h3>
                            </div>
                            <div class="p-6">
                                <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-4">
                                    <h4 class="font-semibold text-red-800 dark:text-red-300 mb-2">Remove Faculty Member</h4>
                                    <p class="text-sm text-red-700 dark:text-red-400 mb-4">
                                        This will permanently remove the faculty member from the system. This action cannot be undone.
                                    </p>
                                    <button
                                        type="button"
                                        onclick="openDeleteModal()"
                                        class="w-full bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center"
                                    >
                                        <i class="fas fa-trash-alt mr-2"></i> Remove Faculty
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4">
            <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">Confirm Deletion</h3>
            </div>
            <div class="p-6">
                <p class="text-gray-700 dark:text-gray-300 mb-4">
                    Are you sure you want to delete faculty member <span id="facultyName" class="font-medium"><?= htmlspecialchars($faculty['name']) ?></span>? This action cannot be undone.
                </p>
                <div class="flex justify-end space-x-3">
                    <button type="button" class="btn btn-outline" onclick="closeDeleteModal()">
                        Cancel
                    </button>
                    <a href="edit_faculty.php?id=<?= $faculty['id'] ?>&action=delete&confirm=yes" class="btn btn-danger">
                        <i class="fas fa-trash-alt mr-2"></i> Delete
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Delete modal functions
        function openDeleteModal() {
            document.getElementById('deleteModal').classList.remove('hidden');
        }

        function closeDeleteModal() {
            document.getElementById('deleteModal').classList.add('hidden');
        }

        // Close modal when clicking outside
        document.getElementById('deleteModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeDeleteModal();
            }
        });

        // Theme toggle functionality
        const themeToggleBtn = document.getElementById('themeToggle');

        function getThemePreference() {
            return localStorage.getItem('color-theme') || 'light';
        }

        function setThemePreference(theme) {
            localStorage.setItem('color-theme', theme);
            if (theme === 'dark') {
                document.documentElement.classList.add('dark');
            } else {
                document.documentElement.classList.remove('dark');
            }
        }

        // Set initial theme
        setThemePreference(getThemePreference());

        // Toggle theme when button is clicked
        if (themeToggleBtn) {
            themeToggleBtn.addEventListener('click', function() {
                const currentTheme = getThemePreference();
                const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
                setThemePreference(newTheme);
            });
        }

        // Form validation
        document.getElementById('facultyForm').addEventListener('submit', function(e) {
            const name = document.getElementById('name').value.trim();
            const email = document.getElementById('email').value.trim();
            const department = document.getElementById('department').value.trim();

            if (!name || !email || !department) {
                e.preventDefault();
                alert('Please fill in all required fields (Name, Email, Department).');
                return false;
            }

            // Email validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                e.preventDefault();
                alert('Please enter a valid email address.');
                return false;
            }
        });

        // Notifications button
        document.getElementById('notificationsBtn')?.addEventListener('click', function() {
            alert('Notifications feature coming soon!');
        });
    </script>

<?php
// Include the footer
require_once '../includes/footer.php';
?>
