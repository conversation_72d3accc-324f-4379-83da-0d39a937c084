<?php
require_once '../includes/db.php';
require_once '../notify/send_email.php';
require_once '../includes/gamify.php'; // Include the gamification logic

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $sender_id = $_POST['sender_id'] ?? null;
    $receiver_id = $_POST['receiver_id'] ?? null;
    $message = trim($_POST['message'] ?? '');
    $sender_role = $_POST['sender_role'] ?? '';

    if ($sender_id && $receiver_id && $message && in_array($sender_role, ['student', 'alumni'])) {
        $stmt = $conn->prepare("INSERT INTO messages (sender_id, receiver_id, sender_role, message) VALUES (?, ?, ?, ?)");
        $stmt->execute([$sender_id, $receiver_id, $sender_role, $message]);

        // Award XP if the sender is a student
        if ($sender_role === 'student') {
            updateStudentXP($conn, $sender_id, 3); // Award 3 XP for sending a message
        }

        $senderTable = $sender_role === 'student' ? 'students' : 'alumni';
        $receiverTable = $sender_role === 'student' ? 'alumni' : 'students';

        $senderQuery = $conn->prepare("SELECT full_name FROM $senderTable WHERE id = ?");
        $senderQuery->execute([$sender_id]);
        $sender = $senderQuery->fetch(PDO::FETCH_ASSOC);

        $receiverQuery = $conn->prepare("SELECT full_name, email FROM $receiverTable WHERE id = ?");
        $receiverQuery->execute([$receiver_id]);
        $receiver = $receiverQuery->fetch(PDO::FETCH_ASSOC);

        if ($receiver && $sender) {
            $subject = "New Message from {$sender['full_name']}";
            $body = "
                <p>Dear {$receiver['full_name']},</p>
                <p>You’ve received a new message from <strong>{$sender['full_name']}</strong>.</p>
                <p><a href='https://darkviolet-vulture-501696.hostingersite.com/chat/chat.php?" .
                    ($sender_role === 'student' ? "student_id=$sender_id" : "alumni_id=$sender_id") .
                "'>Click here to reply</a></p>
                <p>Best regards,<br>Connect My Students</p>
            ";
            sendEmail($receiver['email'], $receiver['full_name'], $subject, $body);
        }
    }
}
?>