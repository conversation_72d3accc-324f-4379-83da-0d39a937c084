<?php
ini_set('display_errors', 1);
error_reporting(E_ALL);
require_once '../includes/db.php';
require_once '../includes/header.php';
require_once '../includes/email_templates.php';
require_once '../includes/contribution_helpers.php';
require_once '../notify/send_email.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}


if (!isset($_SESSION['alumni_id'])) {
    header("Location: ../auth/alumni_login.php");
    exit;
}

$alumni_id = $_SESSION['alumni_id'];
$campaign_id = $_GET['campaign_id'] ?? null;

if (!$campaign_id) {
    echo "<div class='alert alert-danger'>Invalid campaign ID.</div>";
    exit;
}

$campaign = getApprovedCampaignById($conn, $campaign_id);
if (!$campaign) {
    echo "<div class='alert alert-danger'>Campaign not found or not approved yet.</div>";
    exit;
}

$errors = [];
$success = false;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $pledge_type = $_POST['pledge_type'] ?? '';
    $amount = $pledge_type === 'monetary' ? floatval($_POST['amount']) : null;
    $description = trim($_POST['description']);
    $allow_contact = isset($_POST['allow_contact']) ? 1 : 0;
    $contact_method = $_POST['contact_method'] ?? 'email';
    $is_anonymous = isset($_POST['is_anonymous']) ? 1 : 0;
    $message_to_admin = trim($_POST['message'] ?? '');

    // Validation
    if (!$pledge_type || ($pledge_type === 'monetary' && !$amount)) {
        $errors[] = "Please fill in all required fields.";
    }

    if (empty($errors)) {
        savePledge($conn, [
            'deal_id' => $campaign_id,
            'alumni_id' => $alumni_id,
            'pledge_type' => $pledge_type,
            'amount' => $amount,
            'description' => $description,
            'allow_contact' => $allow_contact,
            'contact_method' => $contact_method,
            'is_anonymous' => $is_anonymous,
        ]);

        // Send Email Notification
        if (!empty($campaign['contact_email'])) {
            $alumni_name = getAlumniName($conn, $alumni_id);
            $emailBody = getAlumniPledgeEmail($alumni_name, $campaign['title'], $pledge_type);
            sendEmail($campaign['contact_email'], $campaign['contact_name'] ?? $campaign['contact_email'], "New Alumni Contribution", $emailBody);
        }

        $success = true;
    }
}
?>

<div class="container mt-4">
    <h2>🎁 Contribute to: <?= htmlspecialchars($campaign['title']) ?></h2>

    <?php if ($success): ?>
        <div class="alert alert-success">Thank you! Your pledge has been submitted.</div>
    <?php elseif (!empty($errors)): ?>
        <div class="alert alert-danger"><?= implode('<br>', $errors) ?></div>
    <?php endif; ?>

    <form method="POST" class="row g-3">
        <div class="col-md-6">
            <label class="form-label">Pledge Type *</label>
            <select name="pledge_type" class="form-select" required>
                <option value="">-- Select --</option>
                <option value="monetary">Monetary</option>
                <option value="in-kind">In-Kind</option>
                <option value="service">Service</option>
            </select>
        </div>

        <div class="col-md-6">
            <label class="form-label">Amount (if monetary)</label>
            <input type="number" name="amount" class="form-control" step="0.01">
        </div>

        <div class="col-md-12">
            <label class="form-label">Item/Service Details</label>
            <textarea name="description" class="form-control" rows="3" placeholder="e.g., 50 books, 2 guest lectures..."></textarea>
        </div>

        <div class="col-md-12">
            <label class="form-label">Message to Campaign Owner (Optional)</label>
            <textarea name="message" class="form-control" rows="2"></textarea>
        </div>

        <div class="col-md-6">
            <div class="form-check">
                <input type="checkbox" name="allow_contact" value="1" class="form-check-input" id="contactOk">
                <label class="form-check-label" for="contactOk">I’m open to being contacted for coordination</label>
            </div>
        </div>

        <div class="col-md-6">
            <label class="form-label">Preferred Contact Method</label><br>
            <div class="form-check form-check-inline">
                <input type="radio" name="contact_method" value="email" class="form-check-input" checked>
                <label class="form-check-label">Email</label>
            </div>
            <div class="form-check form-check-inline">
                <input type="radio" name="contact_method" value="phone" class="form-check-input">
                <label class="form-check-label">Phone</label>
            </div>
            <div class="form-check form-check-inline">
                <input type="radio" name="contact_method" value="both" class="form-check-input">
                <label class="form-check-label">Both</label>
            </div>
        </div>

        <div class="col-md-12">
            <div class="form-check">
                <input type="checkbox" name="is_anonymous" value="1" class="form-check-input" id="anon">
                <label class="form-check-label" for="anon">I prefer to contribute anonymously</label>
            </div>
        </div>

        <div class="col-12 text-end">
            <button type="submit" class="btn btn-success">Submit Pledge</button>
        </div>
    </form>
</div>

<?php require_once '../includes/footer.php'; ?>
