<?php
// Include session configuration
require_once '../includes/session_config.php';
require_once '../includes/db.php';

if (!isset($_SESSION['superadmin_id'])) {
    header("Location: ../auth/superadmin_login.php");
    exit;
}

// Mock email test for now
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_email'])) {
    $testEmail = $_POST['test_email_address'] ?? '';
    
    if (!filter_var($testEmail, FILTER_VALIDATE_EMAIL)) {
        $message = "Please provide a valid email address for testing.";
        $messageType = "error";
    } else {
        // Mock email test for now
        $message = "Test email sent to $testEmail. Please check your inbox.";
        $messageType = "info";
        
        // In a real implementation, you would use PHPMailer or similar to send a test email
        // using the SMTP settings from the database
    }
}

// Set role explicitly for the sidebar
$role = 'superadmin';
$user_id = $_SESSION['superadmin_id'];
$user_name = $_SESSION['superadmin_name'];
$current_page = basename($_SERVER['PHP_SELF']);

// Check if settings table exists
$settingsTableExists = false;
try {
    $checkTableStmt = $conn->prepare("SHOW TABLES LIKE 'settings'");
    $checkTableStmt->execute();
    $settingsTableExists = $checkTableStmt->rowCount() > 0;
    
    if (!$settingsTableExists) {
        // Create settings table if it doesn't exist
        $createTableSql = "CREATE TABLE IF NOT EXISTS settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            setting_key VARCHAR(100) NOT NULL UNIQUE,
            setting_value TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )";
        $conn->exec($createTableSql);
        $settingsTableExists = true;
    }
} catch (PDOException $e) {
    $error = "Database error: " . $e->getMessage();
}

// Define setting categories and their settings
$settingCategories = [
    'general' => [
        'platform_name' => [
            'label' => 'Platform Name',
            'type' => 'text',
            'default' => 'Mentoshri',
            'description' => 'The name of the platform displayed across the site.'
        ],
        'contact_email' => [
            'label' => 'Contact Email',
            'type' => 'email',
            'default' => '<EMAIL>',
            'description' => 'Primary contact email for the platform.'
        ],
        'enable_maintenance_mode' => [
            'label' => 'Maintenance Mode',
            'type' => 'toggle',
            'default' => 'off',
            'description' => 'Put the site in maintenance mode. Only super admins can access the site.'
        ]
    ],
    'security' => [
        'password_min_length' => [
            'label' => 'Minimum Password Length',
            'type' => 'number',
            'default' => '8',
            'description' => 'Minimum number of characters required for passwords.'
        ],
        'require_special_chars' => [
            'label' => 'Require Special Characters',
            'type' => 'toggle',
            'default' => 'on',
            'description' => 'Require special characters in passwords.'
        ],
        'login_attempts' => [
            'label' => 'Max Login Attempts',
            'type' => 'number',
            'default' => '5',
            'description' => 'Maximum number of failed login attempts before temporary lockout.'
        ],
        'session_timeout' => [
            'label' => 'Session Timeout (minutes)',
            'type' => 'number',
            'default' => '30',
            'description' => 'Number of minutes of inactivity before automatic logout.'
        ],
        'force_2fa_for_admins' => [
            'label' => 'Force 2FA for Admins',
            'type' => 'toggle',
            'default' => 'off',
            'description' => 'Require two-factor authentication for all admin users.'
        ]
    ],
    'emails' => [
        'smtp_host' => [
            'label' => 'SMTP Host',
            'type' => 'text',
            'default' => '',
            'description' => 'SMTP server hostname.'
        ],
        'smtp_port' => [
            'label' => 'SMTP Port',
            'type' => 'number',
            'default' => '587',
            'description' => 'SMTP server port.'
        ],
        'smtp_username' => [
            'label' => 'SMTP Username',
            'type' => 'text',
            'default' => '',
            'description' => 'SMTP authentication username.'
        ],
        'smtp_password' => [
            'label' => 'SMTP Password',
            'type' => 'password',
            'default' => '',
            'description' => 'SMTP authentication password.'
        ],
        'smtp_encryption' => [
            'label' => 'SMTP Encryption',
            'type' => 'select',
            'options' => ['none', 'ssl', 'tls'],
            'default' => 'tls',
            'description' => 'Type of encryption to use for SMTP connections.'
        ],
        'sender_name' => [
            'label' => 'Sender Name',
            'type' => 'text',
            'default' => 'Mentoshri Platform',
            'description' => 'Name to use in the "From" field of emails.'
        ],
        'sender_email' => [
            'label' => 'Sender Email',
            'type' => 'email',
            'default' => '<EMAIL>',
            'description' => 'Email address to use in the "From" field of emails.'
        ]
    ],
    'integrations' => [
        'google_client_id' => [
            'label' => 'Google Client ID',
            'type' => 'text',
            'default' => '',
            'description' => 'Client ID for Google OAuth integration.'
        ],
        'google_client_secret' => [
            'label' => 'Google Client Secret',
            'type' => 'password',
            'default' => '',
            'description' => 'Client Secret for Google OAuth integration.'
        ],
        'enable_social_login' => [
            'label' => 'Enable Social Login',
            'type' => 'toggle',
            'default' => 'off',
            'description' => 'Allow users to sign in using social media accounts.'
        ]
    ],
    'backup' => [
        'auto_backup_enabled' => [
            'label' => 'Enable Automatic Backups',
            'type' => 'toggle',
            'default' => 'off',
            'description' => 'Automatically create backups of the database and files.'
        ],
        'backup_frequency' => [
            'label' => 'Backup Frequency',
            'type' => 'select',
            'options' => ['daily', 'weekly', 'monthly'],
            'default' => 'weekly',
            'description' => 'How often automatic backups should be created.'
        ],
        'backup_retention' => [
            'label' => 'Backup Retention (days)',
            'type' => 'number',
            'default' => '30',
            'description' => 'Number of days to keep backups before automatic deletion.'
        ]
    ]
];

// Process form submission
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_settings'])) {
    try {
        foreach ($_POST as $key => $value) {
            // Skip non-setting fields like the submit button
            if ($key === 'update_settings') continue;
            
            // Check if the setting already exists
            $checkSql = "SELECT * FROM settings WHERE setting_key = ?";
            $checkStmt = $conn->prepare($checkSql);
            $checkStmt->execute([$key]);
            
            if ($checkStmt->rowCount() > 0) {
                // Update existing setting
                $updateSql = "UPDATE settings SET setting_value = ? WHERE setting_key = ?";
                $updateStmt = $conn->prepare($updateSql);
                $updateStmt->execute([$value, $key]);
            } else {
                // Insert new setting
                $insertSql = "INSERT INTO settings (setting_key, setting_value) VALUES (?, ?)";
                $insertStmt = $conn->prepare($insertSql);
                $insertStmt->execute([$key, $value]);
            }
        }
        
        $message = "Settings updated successfully.";
        $messageType = "success";
    } catch (PDOException $e) {
        $message = "Error updating settings: " . $e->getMessage();
        $messageType = "error";
    }
}

// Fetch current settings from database
$currentSettings = [];
if ($settingsTableExists) {
    try {
        $stmt = $conn->query("SELECT setting_key, setting_value FROM settings");
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $currentSettings[$row['setting_key']] = $row['setting_value'];
        }
    } catch (PDOException $e) {
        $message = "Error fetching settings: " . $e->getMessage();
        $messageType = "error";
    }
}

// Include the header
require_once '../includes/header.php';
?>

<!-- Main Content -->
<div class="container mx-auto px-4 py-8 max-w-6xl">
    <!-- Page Header -->
    <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                <span class="bg-indigo-500/10 dark:bg-indigo-500/20 p-2 rounded-md mr-3">
                    <i class="fas fa-cog text-indigo-600 dark:text-indigo-400"></i>
                </span>
                System Settings
            </h1>
            <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">Configure platform-wide settings and preferences</p>
        </div>
        <div class="mt-4 md:mt-0">
            <button type="button" onclick="document.getElementById('settingsForm').submit();" class="btn btn-primary flex items-center">
                <i class="fas fa-save mr-2"></i> Save All Settings
            </button>
        </div>
    </div>
    
    <!-- Action Message -->
    <?php if (!empty($message)): ?>
        <div class="mb-6 bg-<?= $messageType ?>-50 dark:bg-<?= $messageType ?>-900/30 border-l-4 border-<?= $messageType ?>-500 text-<?= $messageType ?>-700 dark:text-<?= $messageType ?>-400 p-4 flex justify-between items-center" role="alert">
            <div>
                <i class="fas fa-<?= $messageType === 'success' ? 'check-circle' : ($messageType === 'info' ? 'info-circle' : 'exclamation-circle') ?> mr-2"></i>
                <?= $message ?>
            </div>
            <button type="button" class="text-<?= $messageType ?>-700 dark:text-<?= $messageType ?>-400 hover:text-<?= $messageType ?>-900" onclick="this.parentElement.style.display='none';">
                <i class="fas fa-times"></i>
            </button>
        </div>
    <?php endif; ?>
    
    <!-- Settings Form -->
    <div class="grid grid-cols-12 gap-6">
        <!-- Categories Navigation -->
        <div class="col-span-12 md:col-span-3">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 sticky top-24">
                <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="font-medium text-gray-700 dark:text-gray-300">Settings Categories</h3>
                </div>
                <nav class="p-4 space-y-1">
                    <?php foreach ($settingCategories as $categoryKey => $categorySettings): ?>
                        <a href="#<?= $categoryKey ?>" class="flex items-center px-3 py-2 rounded-md text-sm font-medium hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300">
                            <i class="fas fa-<?= 
                                $categoryKey === 'general' ? 'cogs' : 
                                ($categoryKey === 'security' ? 'shield-alt' : 
                                ($categoryKey === 'emails' ? 'envelope' : 
                                ($categoryKey === 'integrations' ? 'plug' : 'database'))) 
                            ?> mr-2 text-gray-500 dark:text-gray-400"></i>
                            <?= ucfirst($categoryKey) ?>
                        </a>
                    <?php endforeach; ?>
                </nav>
            </div>
        </div>
        
        <!-- Settings Form -->
        <div class="col-span-12 md:col-span-9">
            <form id="settingsForm" method="POST" action="">
                <?php foreach ($settingCategories as $categoryKey => $categorySettings): ?>
                    <div id="<?= $categoryKey ?>" class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 mb-6">
                        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                            <h2 class="font-semibold text-gray-800 dark:text-white">
                                <?= ucfirst($categoryKey) ?> Settings
                            </h2>
                        </div>
                        <div class="p-6 space-y-6">
                            <?php foreach ($categorySettings as $settingKey => $setting): ?>
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 items-center">
                                    <div>
                                        <label for="<?= $settingKey ?>" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                            <?= $setting['label'] ?>
                                        </label>
                                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400"><?= $setting['description'] ?></p>
                                    </div>
                                    <div class="md:col-span-2">
                                        <?php 
                                        $value = isset($currentSettings[$settingKey]) ? $currentSettings[$settingKey] : $setting['default'];
                                        
                                        switch ($setting['type']):
                                            case 'toggle':
                                        ?>
                                            <label class="inline-flex items-center cursor-pointer">
                                                <input type="checkbox" name="<?= $settingKey ?>" value="on" class="sr-only peer" <?= $value === 'on' ? 'checked' : '' ?>>
                                                <div class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/30 dark:peer-focus:ring-primary/40 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary"></div>
                                                <span class="ml-3 text-sm font-medium text-gray-700 dark:text-gray-300">
                                                    <?= $value === 'on' ? 'Enabled' : 'Disabled' ?>
                                                </span>
                                            </label>
                                        <?php
                                            break;
                                            case 'select':
                                        ?>
                                            <select name="<?= $settingKey ?>" class="form-input">
                                                <?php foreach ($setting['options'] as $option): ?>
                                                    <option value="<?= $option ?>" <?= $value === $option ? 'selected' : '' ?>>
                                                        <?= ucfirst($option) ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        <?php
                                            break;
                                            case 'password':
                                        ?>
                                            <div class="relative">
                                                <input type="password" id="<?= $settingKey ?>" name="<?= $settingKey ?>" class="form-input pr-10" value="<?= $value ?>" placeholder="<?= $setting['default'] ? '••••••••' : 'Enter ' . strtolower($setting['label']) ?>">
                                                <button type="button" class="toggle-password absolute inset-y-0 right-0 pr-3 flex items-center text-gray-600 dark:text-gray-400" data-target="<?= $settingKey ?>">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                            </div>
                                        <?php
                                            break;
                                            default: // text, email, number
                                        ?>
                                            <input type="<?= $setting['type'] ?>" id="<?= $settingKey ?>" name="<?= $settingKey ?>" class="form-input" value="<?= htmlspecialchars($value) ?>" placeholder="<?= $setting['default'] ? htmlspecialchars($setting['default']) : 'Enter ' . strtolower($setting['label']) ?>">
                                        <?php endswitch; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
                
                <input type="hidden" name="update_settings" value="1">
                
                <!-- Test Email Section -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 mb-6">
                    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                        <h2 class="font-semibold text-gray-800 dark:text-white">
                            Test Email Configuration
                        </h2>
                    </div>
                    <div class="p-6">
                        <div class="flex flex-col md:flex-row md:items-end gap-4">
                            <div class="flex-grow">
                                <label for="test_email_address" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    Test Email Address
                                </label>
                                <input type="email" id="test_email_address" name="test_email_address" class="form-input" placeholder="Enter email to send test to">
                            </div>
                            <div>
                                <button type="submit" name="test_email" value="1" class="btn btn-primary">
                                    <i class="fas fa-paper-plane mr-2"></i> Send Test Email
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="flex justify-between">
                    <button type="button" class="btn btn-outline" onclick="window.location.href='superadmin_dashboard.php'">
                        <i class="fas fa-arrow-left mr-2"></i> Back to Dashboard
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save mr-2"></i> Save All Settings
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Toggle password visibility
    const toggleButtons = document.querySelectorAll('.toggle-password');
    
    toggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetId = this.getAttribute('data-target');
            const inputField = document.getElementById(targetId);
            
            if (inputField.type === 'password') {
                inputField.type = 'text';
                this.querySelector('i').classList.remove('fa-eye');
                this.querySelector('i').classList.add('fa-eye-slash');
            } else {
                inputField.type = 'password';
                this.querySelector('i').classList.remove('fa-eye-slash');
                this.querySelector('i').classList.add('fa-eye');
            }
        });
    });
    
    // Smooth scroll to sections
    document.querySelectorAll('nav a').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            
            window.scrollTo({
                top: targetElement.offsetTop - 20,
                behavior: 'smooth'
            });
        });
    });
});
</script> 

<?php require_once '../includes/footer.php'; ?> 