<?php
// Include session configuration
require_once '../includes/session_config.php';
require_once '../includes/db.php';
require_once '../includes/db_schema_check.php';

if (!isset($_SESSION['collegeadmin_id'])) {
    header("Location: ../auth/collegeadmin_login.php");
    exit;
}

$college_id = $_SESSION['college_id'];
$admin_name = $_SESSION['collegeadmin_name'] ?? 'Admin';

// Set role explicitly for the sidebar
$role = 'collegeadmin';
$user_id = $_SESSION['collegeadmin_id'];
$user_name = $_SESSION['collegeadmin_name'];
$current_page = basename($_SERVER['PHP_SELF']);

// Get college details
$collegeStmt = $conn->prepare("SELECT name FROM colleges WHERE id = ?");
$collegeStmt->execute([$college_id]);
$college = $collegeStmt->fetch(PDO::FETCH_ASSOC);

$error = '';
$success = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $title = trim($_POST['title'] ?? '');
    $content = trim($_POST['content'] ?? '');
    $audience = $_POST['audience'] ?? [];
    $is_important = isset($_POST['is_important']) ? 1 : 0;
    $expires_at = $_POST['expires_at'] ?? null;
    
    // Validation
    if (empty($title)) {
        $error = "Announcement title is required.";
    } elseif (empty($content)) {
        $error = "Announcement content is required.";
    } elseif (empty($audience)) {
        $error = "Please select at least one target audience.";
    } else {
        try {
            // Check if announcements table exists, create if not
            try {
                $conn->query("SELECT 1 FROM announcements LIMIT 1");
            } catch (PDOException $e) {
                // Create announcements table
                $conn->exec("CREATE TABLE IF NOT EXISTS announcements (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    college_id INT NOT NULL,
                    title VARCHAR(255) NOT NULL,
                    content TEXT NOT NULL,
                    audience JSON NOT NULL,
                    is_important BOOLEAN DEFAULT FALSE,
                    expires_at DATETIME NULL,
                    created_by INT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX idx_college_id (college_id),
                    INDEX idx_created_at (created_at),
                    INDEX idx_is_important (is_important)
                )");
            }
            
            // Insert announcement
            $stmt = $conn->prepare("
                INSERT INTO announcements (college_id, title, content, audience, is_important, expires_at, created_by) 
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $college_id,
                $title,
                $content,
                json_encode($audience),
                $is_important,
                $expires_at ?: null,
                $_SESSION['collegeadmin_id']
            ]);
            
            $success = "Announcement posted successfully! It will be visible to the selected audience.";
            
            // Clear form data
            $title = $content = '';
            $audience = [];
            $is_important = 0;
            $expires_at = '';
            
        } catch (PDOException $e) {
            $error = "Database error: " . $e->getMessage();
        }
    }
}

// Get recent announcements count
$recentCount = 0;
try {
    $countStmt = $conn->prepare("SELECT COUNT(*) FROM announcements WHERE college_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)");
    $countStmt->execute([$college_id]);
    $recentCount = $countStmt->fetchColumn();
} catch (PDOException $e) {
    // Table doesn't exist yet
    $recentCount = 0;
}

// Include the header
require_once '../includes/header.php';
?>

    <!-- Main Content -->
    <main class="flex-grow py-6 px-4">
        <div class="max-w-7xl mx-auto">
            <!-- Modern Page Header with Gradient -->
            <div class="relative bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-800 rounded-2xl shadow-xl overflow-hidden mb-8">
                <!-- Background Pattern -->
                <div class="absolute inset-0 bg-black/10">
                    <div class="absolute inset-0" style="background-image: radial-gradient(circle at 1px 1px, rgba(255,255,255,0.15) 1px, transparent 0); background-size: 20px 20px;"></div>
                </div>
                
                <!-- Content -->
                <div class="relative p-8">
                    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                        <div class="flex items-center space-x-6">
                            <div class="bg-white/20 backdrop-blur-sm p-4 rounded-2xl shadow-lg">
                                <i class="fas fa-bullhorn text-white text-3xl"></i>
                            </div>
                            <div>
                                <h1 class="text-3xl font-bold text-white mb-2">
                                    Create Announcement
                                </h1>
                                <nav class="text-blue-100 text-sm mb-3" aria-label="Breadcrumb">
                                    <ol class="flex items-center space-x-2">
                                        <li>
                                            <a href="collegeadmin_dashboard.php" class="hover:text-white transition-colors">
                                                <i class="fas fa-home mr-1"></i>Dashboard
                                            </a>
                                        </li>
                                        <li class="flex items-center">
                                            <i class="fas fa-chevron-right text-blue-300 text-xs mx-2"></i>
                                            <span class="text-white font-medium">Communication</span>
                                        </li>
                                        <li class="flex items-center">
                                            <i class="fas fa-chevron-right text-blue-300 text-xs mx-2"></i>
                                            <span class="text-white font-medium">Post Announcement</span>
                                        </li>
                                    </ol>
                                </nav>
                                <p class="text-blue-100 text-lg">
                                    Share important updates with your college community
                                </p>
                            </div>
                        </div>
                        <div class="mt-6 lg:mt-0 flex flex-wrap gap-3">
                            <a href="bulk_email_students.php" class="inline-flex items-center px-6 py-3 bg-white/20 hover:bg-white/30 backdrop-blur-sm text-white font-medium rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl">
                                <i class="fas fa-envelope mr-2"></i> Bulk Email
                            </a>
                            <a href="collegeadmin_dashboard.php" class="inline-flex items-center px-6 py-3 bg-white/10 hover:bg-white/20 backdrop-blur-sm text-white font-medium rounded-xl transition-all duration-200 border border-white/20">
                                <i class="fas fa-arrow-left mr-2"></i> Dashboard
                            </a>
                        </div>
                    </div>
                    
                    <!-- Quick Stats -->
                    <div class="mt-8 grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20">
                            <div class="flex items-center">
                                <div class="bg-green-500/20 p-2 rounded-lg">
                                    <i class="fas fa-chart-line text-green-300"></i>
                                </div>
                                <div class="ml-3">
                                    <p class="text-blue-100 text-sm">This Week</p>
                                    <p class="text-white text-xl font-bold"><?= $recentCount ?></p>
                                </div>
                            </div>
                        </div>
                        <div class="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20">
                            <div class="flex items-center">
                                <div class="bg-blue-500/20 p-2 rounded-lg">
                                    <i class="fas fa-users text-blue-300"></i>
                                </div>
                                <div class="ml-3">
                                    <p class="text-blue-100 text-sm">Reach</p>
                                    <p class="text-white text-xl font-bold">All Users</p>
                                </div>
                            </div>
                        </div>
                        <div class="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20">
                            <div class="flex items-center">
                                <div class="bg-purple-500/20 p-2 rounded-lg">
                                    <i class="fas fa-clock text-purple-300"></i>
                                </div>
                                <div class="ml-3">
                                    <p class="text-blue-100 text-sm">Status</p>
                                    <p class="text-white text-xl font-bold">Live</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Alert Messages -->
            <?php if ($error): ?>
                <div class="bg-red-50 dark:bg-red-900/50 border-l-4 border-red-500 rounded-r-xl p-6 mb-6 shadow-lg">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <div class="bg-red-100 dark:bg-red-800 p-2 rounded-full">
                                <i class="fas fa-exclamation-triangle text-red-600 dark:text-red-400"></i>
                            </div>
                        </div>
                        <div class="ml-4">
                            <h3 class="text-lg font-semibold text-red-800 dark:text-red-200">Error</h3>
                            <p class="text-red-700 dark:text-red-300 mt-1"><?= htmlspecialchars($error) ?></p>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <?php if ($success): ?>
                <div class="bg-green-50 dark:bg-green-900/50 border-l-4 border-green-500 rounded-r-xl p-6 mb-6 shadow-lg">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <div class="bg-green-100 dark:bg-green-800 p-2 rounded-full">
                                <i class="fas fa-check-circle text-green-600 dark:text-green-400"></i>
                            </div>
                        </div>
                        <div class="ml-4">
                            <h3 class="text-lg font-semibold text-green-800 dark:text-green-200">Success</h3>
                            <p class="text-green-700 dark:text-green-300 mt-1"><?= htmlspecialchars($success) ?></p>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Main Content Grid -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Left Column: Announcement Form -->
                <div class="lg:col-span-2">
                    <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 overflow-hidden">
                        <!-- Form Header -->
                        <div class="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-700 dark:to-gray-800 p-6 border-b border-gray-200 dark:border-gray-700">
                            <div class="flex items-center">
                                <div class="bg-blue-100 dark:bg-blue-900/30 p-3 rounded-xl mr-4">
                                    <i class="fas fa-edit text-blue-600 dark:text-blue-400 text-xl"></i>
                                </div>
                                <div>
                                    <h2 class="text-xl font-bold text-gray-900 dark:text-white">Compose Announcement</h2>
                                    <p class="text-gray-600 dark:text-gray-400 mt-1">Create and publish announcements for your college community</p>
                                </div>
                            </div>
                        </div>

                        <!-- Form Content -->
                        <div class="p-8">
                            <form method="POST" action="" id="announcementForm" class="space-y-8">
                                <!-- Title Field -->
                                <div class="group">
                                    <label for="title" class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                                        <i class="fas fa-heading text-blue-500 mr-2"></i>
                                        Announcement Title <span class="text-red-500">*</span>
                                    </label>
                                    <div class="relative">
                                        <input type="text" id="title" name="title" value="<?= htmlspecialchars($title ?? '') ?>"
                                            class="w-full px-4 py-4 text-lg border-2 border-gray-200 dark:border-gray-600 rounded-xl focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 dark:bg-gray-700 dark:text-white transition-all duration-200 group-hover:border-gray-300"
                                            placeholder="Enter a clear, descriptive title..." required>
                                        <div class="absolute inset-y-0 right-0 flex items-center pr-4">
                                            <i class="fas fa-pen text-gray-400"></i>
                                        </div>
                                    </div>
                                </div>

                                <!-- Content Field -->
                                <div class="group">
                                    <label for="content" class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                                        <i class="fas fa-align-left text-blue-500 mr-2"></i>
                                        Announcement Content <span class="text-red-500">*</span>
                                    </label>
                                    <div class="relative">
                                        <textarea id="content" name="content" rows="6"
                                            class="w-full px-4 py-4 border-2 border-gray-200 dark:border-gray-600 rounded-xl focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 dark:bg-gray-700 dark:text-white transition-all duration-200 group-hover:border-gray-300 resize-none"
                                            placeholder="Enter the announcement details here..."
                                            required><?= htmlspecialchars($content ?? '') ?></textarea>
                                        <div class="absolute bottom-3 right-3">
                                            <span id="charCount" class="text-xs text-gray-400">0 characters</span>
                                        </div>
                                    </div>
                                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-2 flex items-center">
                                        <i class="fas fa-info-circle mr-1"></i>
                                        You can use basic formatting: **bold**, *italic*, [link text](url)
                                    </p>
                                </div>

                                <!-- Audience Selection -->
                                <div class="group">
                                    <label class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-4">
                                        <i class="fas fa-users text-blue-500 mr-2"></i>
                                        Target Audience <span class="text-red-500">*</span>
                                    </label>
                                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                        <!-- Students -->
                                        <label class="relative group cursor-pointer">
                                            <input type="checkbox" name="audience[]" value="students"
                                                class="sr-only peer"
                                                <?= in_array('students', $audience ?? []) ? 'checked' : '' ?>>
                                            <div class="p-6 border-2 border-gray-200 dark:border-gray-600 rounded-xl transition-all duration-200 peer-checked:border-blue-500 peer-checked:bg-blue-50 dark:peer-checked:bg-blue-900/20 hover:border-gray-300 dark:hover:border-gray-500 hover:shadow-md">
                                                <div class="flex items-center justify-between mb-3">
                                                    <div class="bg-blue-100 dark:bg-blue-900/30 p-2 rounded-lg">
                                                        <i class="fas fa-graduation-cap text-blue-600 dark:text-blue-400"></i>
                                                    </div>
                                                    <div class="w-5 h-5 border-2 border-gray-300 rounded peer-checked:border-blue-500 peer-checked:bg-blue-500 flex items-center justify-center">
                                                        <i class="fas fa-check text-white text-xs opacity-0 peer-checked:opacity-100"></i>
                                                    </div>
                                                </div>
                                                <h3 class="font-semibold text-gray-900 dark:text-white">Students</h3>
                                                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">All enrolled students</p>
                                            </div>
                                        </label>

                                        <!-- Faculty -->
                                        <label class="relative group cursor-pointer">
                                            <input type="checkbox" name="audience[]" value="faculty"
                                                class="sr-only peer"
                                                <?= in_array('faculty', $audience ?? []) ? 'checked' : '' ?>>
                                            <div class="p-6 border-2 border-gray-200 dark:border-gray-600 rounded-xl transition-all duration-200 peer-checked:border-green-500 peer-checked:bg-green-50 dark:peer-checked:bg-green-900/20 hover:border-gray-300 dark:hover:border-gray-500 hover:shadow-md">
                                                <div class="flex items-center justify-between mb-3">
                                                    <div class="bg-green-100 dark:bg-green-900/30 p-2 rounded-lg">
                                                        <i class="fas fa-chalkboard-teacher text-green-600 dark:text-green-400"></i>
                                                    </div>
                                                    <div class="w-5 h-5 border-2 border-gray-300 rounded peer-checked:border-green-500 peer-checked:bg-green-500 flex items-center justify-center">
                                                        <i class="fas fa-check text-white text-xs opacity-0 peer-checked:opacity-100"></i>
                                                    </div>
                                                </div>
                                                <h3 class="font-semibold text-gray-900 dark:text-white">Faculty</h3>
                                                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">Teaching staff</p>
                                            </div>
                                        </label>

                                        <!-- Alumni -->
                                        <label class="relative group cursor-pointer">
                                            <input type="checkbox" name="audience[]" value="alumni"
                                                class="sr-only peer"
                                                <?= in_array('alumni', $audience ?? []) ? 'checked' : '' ?>>
                                            <div class="p-6 border-2 border-gray-200 dark:border-gray-600 rounded-xl transition-all duration-200 peer-checked:border-purple-500 peer-checked:bg-purple-50 dark:peer-checked:bg-purple-900/20 hover:border-gray-300 dark:hover:border-gray-500 hover:shadow-md">
                                                <div class="flex items-center justify-between mb-3">
                                                    <div class="bg-purple-100 dark:bg-purple-900/30 p-2 rounded-lg">
                                                        <i class="fas fa-user-tie text-purple-600 dark:text-purple-400"></i>
                                                    </div>
                                                    <div class="w-5 h-5 border-2 border-gray-300 rounded peer-checked:border-purple-500 peer-checked:bg-purple-500 flex items-center justify-center">
                                                        <i class="fas fa-check text-white text-xs opacity-0 peer-checked:opacity-100"></i>
                                                    </div>
                                                </div>
                                                <h3 class="font-semibold text-gray-900 dark:text-white">Alumni</h3>
                                                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">Graduated students</p>
                                            </div>
                                        </label>
                                    </div>
                                </div>

                                <!-- Additional Options -->
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <!-- Important Flag -->
                                    <div class="group">
                                        <label class="flex items-start p-4 border-2 border-gray-200 dark:border-gray-600 rounded-xl cursor-pointer hover:border-gray-300 dark:hover:border-gray-500 transition-all duration-200">
                                            <input type="checkbox" name="is_important" value="1"
                                                class="w-5 h-5 text-red-600 bg-gray-100 border-gray-300 rounded focus:ring-red-500 dark:focus:ring-red-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600 mt-1"
                                                <?= ($is_important ?? 0) ? 'checked' : '' ?>>
                                            <div class="ml-3">
                                                <div class="flex items-center">
                                                    <i class="fas fa-exclamation-triangle text-red-500 mr-2"></i>
                                                    <span class="font-semibold text-gray-900 dark:text-white">Mark as Important</span>
                                                </div>
                                                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">Important announcements will be highlighted and pinned to the top</p>
                                            </div>
                                        </label>
                                    </div>

                                    <!-- Expiration Date -->
                                    <div class="group">
                                        <label for="expires_at" class="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                                            <i class="fas fa-calendar-times text-blue-500 mr-2"></i>
                                            Expiration Date (Optional)
                                        </label>
                                        <input type="datetime-local" id="expires_at" name="expires_at" value="<?= htmlspecialchars($expires_at ?? '') ?>"
                                            class="w-full px-4 py-3 border-2 border-gray-200 dark:border-gray-600 rounded-xl focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 dark:bg-gray-700 dark:text-white transition-all duration-200">
                                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Leave empty for permanent announcement</p>
                                    </div>
                                </div>

                                <!-- Form Actions -->
                                <div class="flex flex-col sm:flex-row gap-4 pt-6 border-t border-gray-200 dark:border-gray-700">
                                    <button type="submit" class="flex-1 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold py-4 px-8 rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 flex items-center justify-center">
                                        <i class="fas fa-paper-plane mr-3"></i>
                                        Publish Announcement
                                    </button>
                                    <button type="reset" class="flex-1 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-semibold py-4 px-8 rounded-xl transition-all duration-200 flex items-center justify-center">
                                        <i class="fas fa-undo mr-3"></i>
                                        Reset Form
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Right Sidebar -->
                <div class="space-y-6">
                    <!-- Tips Section -->
                    <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 overflow-hidden">
                        <div class="bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-gray-700 dark:to-gray-800 p-4 border-b border-gray-200 dark:border-gray-700">
                            <h3 class="font-bold text-gray-900 dark:text-white flex items-center">
                                <i class="fas fa-lightbulb text-yellow-500 mr-2"></i>
                                Writing Tips
                            </h3>
                        </div>
                        <div class="p-6 space-y-4">
                            <div class="flex items-start">
                                <div class="flex-shrink-0">
                                    <div class="w-8 h-8 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
                                        <i class="fas fa-check text-green-600 dark:text-green-400 text-sm"></i>
                                    </div>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm text-gray-700 dark:text-gray-300 font-medium">Clear & Concise</p>
                                    <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">Use simple language and get straight to the point</p>
                                </div>
                            </div>
                            <div class="flex items-start">
                                <div class="flex-shrink-0">
                                    <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center">
                                        <i class="fas fa-calendar text-blue-600 dark:text-blue-400 text-sm"></i>
                                    </div>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm text-gray-700 dark:text-gray-300 font-medium">Include Dates</p>
                                    <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">Mention important dates, times, and deadlines</p>
                                </div>
                            </div>
                            <div class="flex items-start">
                                <div class="flex-shrink-0">
                                    <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center">
                                        <i class="fas fa-users text-purple-600 dark:text-purple-400 text-sm"></i>
                                    </div>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm text-gray-700 dark:text-gray-300 font-medium">Right Audience</p>
                                    <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">Select only relevant groups to avoid spam</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 overflow-hidden">
                        <div class="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-700 dark:to-gray-800 p-4 border-b border-gray-200 dark:border-gray-700">
                            <h3 class="font-bold text-gray-900 dark:text-white flex items-center">
                                <i class="fas fa-bolt text-blue-600 mr-2"></i>
                                Quick Actions
                            </h3>
                        </div>
                        <div class="p-6 space-y-3">
                            <a href="bulk_email_students.php" class="w-full inline-flex items-center justify-center px-4 py-3 bg-purple-600 hover:bg-purple-700 text-white font-medium rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl">
                                <i class="fas fa-envelope mr-2"></i> Bulk Email
                            </a>
                            <a href="collegeadmin_dashboard.php" class="w-full inline-flex items-center justify-center px-4 py-3 bg-gray-600 hover:bg-gray-700 text-white font-medium rounded-xl transition-all duration-200">
                                <i class="fas fa-home mr-2"></i> Dashboard
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

<script>
// Character counter
const contentTextarea = document.getElementById('content');
const charCount = document.getElementById('charCount');

if (contentTextarea && charCount) {
    function updateCharCount() {
        const count = contentTextarea.value.length;
        charCount.textContent = `${count} characters`;

        if (count > 500) {
            charCount.classList.add('text-yellow-500');
        } else {
            charCount.classList.remove('text-yellow-500');
        }
    }

    contentTextarea.addEventListener('input', updateCharCount);
    updateCharCount(); // Initial count
}

// Form validation
document.getElementById('announcementForm').addEventListener('submit', function(e) {
    const title = document.getElementById('title').value.trim();
    const content = document.getElementById('content').value.trim();
    const audienceCheckboxes = document.querySelectorAll('input[name="audience[]"]:checked');

    if (!title || !content) {
        e.preventDefault();
        alert('Please fill in all required fields.');
        return false;
    }

    if (audienceCheckboxes.length === 0) {
        e.preventDefault();
        alert('Please select at least one target audience.');
        return false;
    }

    // Show loading state
    const submitBtn = e.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Publishing...';
    submitBtn.disabled = true;

    // Re-enable after 3 seconds (in case of error)
    setTimeout(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }, 3000);

    return true;
});

// Enhanced checkbox interactions
document.querySelectorAll('input[name="audience[]"]').forEach(checkbox => {
    checkbox.addEventListener('change', function() {
        const label = this.closest('label');
        const checkIcon = label.querySelector('.fa-check');

        if (this.checked) {
            checkIcon.style.opacity = '1';
            label.querySelector('div').classList.add('scale-105');
            setTimeout(() => {
                label.querySelector('div').classList.remove('scale-105');
            }, 200);
        } else {
            checkIcon.style.opacity = '0';
        }
    });
});

// Auto-resize textarea
if (contentTextarea) {
    contentTextarea.addEventListener('input', function() {
        this.style.height = 'auto';
        this.style.height = Math.min(this.scrollHeight, 300) + 'px';
    });
}
</script>

<?php require_once '../includes/footer.php'; ?>
