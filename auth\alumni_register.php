<?php
require_once '../includes/db.php';
require_once '../includes/header.php';

$token = $_GET['token'] ?? '';
$invalid = false;
$success = $error = '';

// Check token validity
$stmt = $conn->prepare("SELECT * FROM alumni_invites WHERE token = ? AND status = 'pending'");
$stmt->execute([$token]);
$invite = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$invite) {
    $invalid = true;
}

// Handle registration
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $full_name = trim($_POST['full_name']);
    $email = $invite['email'];
    $username = trim($_POST['username']);
    $password = password_hash($_POST['password'], PASSWORD_DEFAULT);
    $college_id = $invite['college_id'];

    // Insert alumni
    $stmt = $conn->prepare("INSERT INTO alumni (college_id, full_name, email, username, password) VALUES (?, ?, ?, ?, ?)");
    if ($stmt->execute([$college_id, $full_name, $email, $username, $password])) {
        // Mark invite used
        $conn->prepare("UPDATE alumni_invites SET status = 'used' WHERE id = ?")->execute([$invite['id']]);
        $success = "Account created! Awaiting faculty verification.";
    } else {
        $error = "An error occurred. Try again.";
    }
}
?>

<div class="container mt-5">
    <?php if ($invalid): ?>
        <div class="alert alert-danger">Invalid or expired invite token.</div>
    <?php elseif ($success): ?>
        <div class="alert alert-success"><?= $success ?></div>
        <a href="alumni_login.php" class="btn btn-primary mt-2">Login Now</a>
    <?php else: ?>
        <h3>🎓 Alumni Registration</h3>
        <p class="text-muted">You were invited by your college faculty. Complete your registration below.</p>

        <?php if ($error): ?><div class="alert alert-danger"><?= $error ?></div><?php endif; ?>

        <form method="POST">
            <div class="mb-3">
                <label class="form-label">Full Name</label>
                <input type="text" name="full_name" class="form-control" required>
            </div>

            <div class="mb-3">
                <label class="form-label">Username</label>
                <input type="text" name="username" class="form-control" required>
            </div>

            <div class="mb-3">
                <label class="form-label">Set Password</label>
                <input type="password" name="password" class="form-control" required>
            </div>

            <button class="btn btn-success">Create My Account</button>
        </form>
    <?php endif; ?>
</div>

<?php require_once '../includes/footer.php'; ?>
