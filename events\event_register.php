<?php
require_once '../includes/db.php';
require_once '../includes/gamify.php'; // Include the gamification logic

// Check session
if (!isset($_SESSION['student_id'])) {
    header("Location: ../auth/student_login.php");
    exit;
}

$student_id = $_SESSION['student_id'];
$college_id = $_SESSION['college_id'];
$event_id = intval($_GET['event_id'] ?? 0);

// Set role explicitly for the sidebar
$role = 'student';
$user_id = $_SESSION['student_id'];
$user_name = $_SESSION['student_name'] ?? 'Student';
$current_page = basename($_SERVER['PHP_SELF']);

$success = '';
$error = '';

if ($event_id) {
    $stmt = $conn->prepare("SELECT * FROM event_attendees WHERE student_id = ? AND event_id = ?");
    $stmt->execute([$student_id, $event_id]);
    
    if ($stmt->rowCount() == 0) {
        // Register the student for the event
        $insert = $conn->prepare("INSERT INTO event_attendees (event_id, student_id) VALUES (?, ?)");
        if ($insert->execute([$event_id, $student_id])) {
            // Award XP for event participation
            awardXPForEventParticipation($conn, $student_id); // Award XP only if registered for the first time
            $success = "Registered for the event successfully!";
        } else {
            $error = "An error occurred while registering for the event.";
        }
    } else {
        $error = "You are already registered for this event.";
    }
} else {
    $error = "Invalid event.";
}

// Get event details
$eventStmt = $conn->prepare("SELECT * FROM events WHERE id = ?");
$eventStmt->execute([$event_id]);
$event = $eventStmt->fetch(PDO::FETCH_ASSOC);

require_once '../includes/header.php';
?>

<!-- Main Content -->
<main class="flex-grow py-8 px-4">
    <div class="container mx-auto">
        <!-- Breadcrumb -->
        <nav class="mb-6 text-sm" aria-label="Breadcrumb">
            <ol class="flex items-center space-x-2">
                <li>
                    <a href="../student/student_dashboard.php" class="text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary-light">
                        <i class="fas fa-home"></i>
                        <span class="sr-only">Dashboard</span>
                    </a>
                </li>
                <li class="flex items-center">
                    <i class="fas fa-chevron-right text-gray-400 text-xs mx-1"></i>
                    <a href="event_list.php" class="text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary-light">
                        Events
                    </a>
                </li>
                <li class="flex items-center">
                    <i class="fas fa-chevron-right text-gray-400 text-xs mx-1"></i>
                    <span class="text-gray-700 dark:text-gray-300">Register</span>
                </li>
            </ol>
        </nav>

        <div class="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                    <i class="fas fa-calendar-check text-primary mr-3"></i>
                    Event Registration
                </h1>
                <p class="text-gray-600 dark:text-gray-400 mt-1">
                    Registration status for the selected event
                </p>
            </div>
            <div class="mt-4 md:mt-0">
                <a href="event_list.php" class="btn btn-outline flex items-center">
                    <i class="fas fa-arrow-left mr-2"></i> Back to Events
                </a>
            </div>
        </div>

        <?php if ($success): ?>
            <div class="bg-green-50 dark:bg-green-900/30 border-l-4 border-green-500 text-green-700 dark:text-green-400 p-4 mb-6 flex justify-between items-center" role="alert">
                <div class="flex items-center">
                    <i class="fas fa-check-circle mr-2"></i>
                    <span><?= htmlspecialchars($success) ?></span>
                </div>
                <button type="button" class="text-green-700 dark:text-green-400 hover:opacity-75" onclick="this.parentElement.style.display='none';">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="bg-red-50 dark:bg-red-900/30 border-l-4 border-red-500 text-red-700 dark:text-red-400 p-4 mb-6 flex justify-between items-center" role="alert">
                <div class="flex items-center">
                    <i class="fas fa-exclamation-circle mr-2"></i>
                    <span><?= htmlspecialchars($error) ?></span>
                </div>
                <button type="button" class="text-red-700 dark:text-red-400 hover:opacity-75" onclick="this.parentElement.style.display='none';">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        <?php endif; ?>

        <?php if ($event): ?>
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 p-6">
                <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-4"><?= htmlspecialchars($event['title']) ?></h2>
                
                <div class="space-y-4">
                    <div class="flex items-center">
                        <i class="fas fa-calendar text-primary mr-3"></i>
                        <span class="text-gray-700 dark:text-gray-300"><?= date("F j, Y", strtotime($event['event_date'])) ?></span>
                    </div>
                    
                    <div class="flex items-center">
                        <i class="fas fa-clock text-primary mr-3"></i>
                        <span class="text-gray-700 dark:text-gray-300"><?= date("g:i A", strtotime($event['event_time'])) ?></span>
                    </div>
                    
                    <?php if (!empty($event['speaker_name'])): ?>
                    <div class="flex items-center">
                        <i class="fas fa-user-tie text-primary mr-3"></i>
                        <span class="text-gray-700 dark:text-gray-300"><?= htmlspecialchars($event['speaker_name']) ?></span>
                    </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($event['meet_link']) && empty($error)): ?>
                    <div class="mt-6">
                        <a href="<?= htmlspecialchars($event['meet_link']) ?>" target="_blank" class="btn btn-primary">
                            <i class="fas fa-video mr-2"></i> Join Meeting
                        </a>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>
    </div>
</main>

<?php require_once '../includes/footer.php'; ?>