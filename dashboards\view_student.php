<?php
session_start();
require_once '../includes/db.php';
require_once '../includes/db_schema_check.php';

if (!isset($_SESSION['collegeadmin_id'])) {
    header("Location: ../auth/collegeadmin_login.php");
    exit;
}

$college_id = $_SESSION['college_id'];
$admin_name = $_SESSION['collegeadmin_name'] ?? 'Admin';

// Set role explicitly for the sidebar
$role = 'collegeadmin';
$user_id = $_SESSION['collegeadmin_id'];
$user_name = $_SESSION['collegeadmin_name'];
$current_page = basename($_SERVER['PHP_SELF']);

$id = $_GET['id'] ?? null;

if (!$id) {
    $_SESSION['flash_message'] = "Invalid student ID.";
    header("Location: list_students.php");
    exit;
}

// Get college details
$collegeStmt = $conn->prepare("SELECT name FROM colleges WHERE id = ?");
$collegeStmt->execute([$college_id]);
$college = $collegeStmt->fetch(PDO::FETCH_ASSOC);

// Get student details
$stmt = $conn->prepare("
    SELECT id, college_id, full_name, email, password, roll_number, department, year, division,
           profile_image, verified, status, created_at, profile_photo, bio, skills, goals,
           linkedin, github, twitter, website, phone, address, city, state, country,
           pincode, date_of_birth, gender, cover_image
    FROM students
    WHERE id = ? AND college_id = ?
");
$stmt->execute([$id, $college_id]);
$student = $stmt->fetch(PDO::FETCH_ASSOC);

// Try to get updated_at separately if the column exists
try {
    $updateStmt = $conn->prepare("SELECT updated_at FROM students WHERE id = ? LIMIT 1");
    $updateStmt->execute([$id]);
    $updateResult = $updateStmt->fetch(PDO::FETCH_ASSOC);
    if ($updateResult && isset($updateResult['updated_at'])) {
        $student['updated_at'] = $updateResult['updated_at'];
    }
} catch (PDOException $e) {
    // Column doesn't exist, that's fine - we'll handle it gracefully
    $student['updated_at'] = null;
}

if (!$student) {
    $_SESSION['flash_message'] = "Student not found or access denied.";
    header("Location: list_students.php");
    exit;
}

// Get student's mentorship requests
$mentorshipStmt = $conn->prepare("
    SELECT sr.*, a.full_name as alumni_name, a.profile_photo as alumni_photo 
    FROM student_requests sr
    JOIN alumni a ON sr.alumni_id = a.id
    WHERE sr.student_id = ? AND a.college_id = ?
    ORDER BY sr.created_at DESC
    LIMIT 5
");
$mentorshipStmt->execute([$id, $college_id]);
$mentorships = $mentorshipStmt->fetchAll(PDO::FETCH_ASSOC);

// Profile photo handling
$photo = !empty($student['profile_photo']) && file_exists("../uploads/{$student['profile_photo']}")
    ? $student['profile_photo']
    : 'default.png';

// Cover image handling
$cover_image_url = '../assets/Images/cover_default.jpg'; // Default cover
if (!empty($student['cover_image']) && file_exists("../uploads/{$student['cover_image']}")) {
    $cover_image_url = "../uploads/{$student['cover_image']}";
}

// Include the header
require_once '../includes/header.php';
?>

    <!-- Main Content -->
    <main class="flex-grow py-6 px-4">
        <div class="container mx-auto max-w-7xl">
            <!-- Breadcrumb -->
            <nav class="mb-6 text-sm" aria-label="Breadcrumb">
                <ol class="flex items-center space-x-2">
                    <li>
                        <a href="collegeadmin_dashboard.php" class="text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary-light transition-colors">
                            <i class="fas fa-home"></i>
                            <span class="sr-only">Dashboard</span>
                        </a>
                    </li>
                    <li class="flex items-center">
                        <i class="fas fa-chevron-right text-gray-400 text-xs mx-1"></i>
                        <a href="list_students.php" class="text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary-light transition-colors">
                            Student Management
                        </a>
                    </li>
                    <li class="flex items-center">
                        <i class="fas fa-chevron-right text-gray-400 text-xs mx-1"></i>
                        <span class="text-gray-700 dark:text-gray-300 font-medium"><?= htmlspecialchars($student['full_name']) ?></span>
                    </li>
                </ol>
            </nav>

            <!-- Enhanced Header with Quick Actions -->
            <div class="mb-8">
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900 dark:text-white flex items-center">
                            <div class="w-12 h-12 bg-gradient-to-br from-blue-600 to-blue-800 rounded-xl flex items-center justify-center mr-4 shadow-lg">
                                <i class="fas fa-user-graduate text-white text-xl"></i>
                            </div>
                            Student Profile
                        </h1>
                        <p class="text-gray-600 dark:text-gray-400 mt-2 text-lg">
                            Complete overview of <?= htmlspecialchars($student['full_name']) ?>'s academic profile
                        </p>
                    </div>

                    <!-- Quick Actions -->
                    <div class="mt-6 lg:mt-0 flex flex-wrap gap-3">
                        <a href="edit_student.php?id=<?= $student['id'] ?>" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200">
                            <i class="fas fa-edit mr-2"></i> Edit Profile
                        </a>
                        <?php if (!$student['verified']): ?>
                        <a href="verify_student.php?id=<?= $student['id'] ?>" class="inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200">
                            <i class="fas fa-check-circle mr-2"></i> Verify Student
                        </a>
                        <?php endif; ?>
                        <div class="relative">
                            <button onclick="toggleDropdown('actionsDropdown')" class="inline-flex items-center px-4 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 font-medium rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-200">
                                <i class="fas fa-ellipsis-v mr-2"></i> More Actions
                                <i class="fas fa-chevron-down ml-2 text-xs"></i>
                            </button>
                            <div id="actionsDropdown" class="hidden absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700 z-10">
                                <a href="delete_student.php?id=<?= $student['id'] ?>" class="block px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/30 rounded-lg">
                                    <i class="fas fa-trash-alt mr-2"></i> Delete Student
                                </a>
                                <a href="list_students.php" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg">
                                    <i class="fas fa-arrow-left mr-2"></i> Back to List
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Profile Header -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 mb-8 overflow-hidden">
                <!-- Cover Background -->
                <div class="h-48 bg-cover bg-center relative" style="background-image: url('<?= htmlspecialchars($cover_image_url) ?>');">
                    <div class="absolute inset-0 bg-black bg-opacity-20"></div>
                </div>

                <!-- Profile Info -->
                <div class="px-6 pb-6">
                    <div class="flex flex-col sm:flex-row sm:items-end sm:space-x-6">
                        <!-- Profile Picture -->
                        <div class="relative -mt-16 mb-4 sm:mb-0">
                            <div class="w-32 h-32 rounded-2xl overflow-hidden border-4 border-white dark:border-gray-800 bg-gray-100 dark:bg-gray-700 flex items-center justify-center shadow-xl">
                                <?php if ($photo === 'default.png' || empty($photo)) : ?>
                                    <i class="fas fa-user-graduate text-5xl text-gray-400 dark:text-gray-500"></i>
                                <?php else: ?>
                                    <img src="../uploads/<?= htmlspecialchars($photo) ?>" alt="<?= htmlspecialchars($student['full_name']) ?>" class="w-full h-full object-cover">
                                <?php endif; ?>
                            </div>
                            <!-- Status Indicator -->
                            <div class="absolute -bottom-2 -right-2">
                                <?php if ($student['verified']): ?>
                                    <div class="w-8 h-8 bg-green-500 rounded-full border-4 border-white dark:border-gray-800 flex items-center justify-center" title="Verified">
                                        <i class="fas fa-check text-white text-xs"></i>
                                    </div>
                                <?php else: ?>
                                    <div class="w-8 h-8 bg-yellow-500 rounded-full border-4 border-white dark:border-gray-800 flex items-center justify-center" title="Pending Verification">
                                        <i class="fas fa-clock text-white text-xs"></i>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Profile Details -->
                        <div class="flex-1 min-w-0">
                            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                                <div>
                                    <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
                                        <?= htmlspecialchars($student['full_name']) ?>
                                    </h1>
                                    <div class="flex flex-wrap items-center gap-3 mt-2">
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300">
                                            <i class="fas fa-building mr-1"></i>
                                            <?= htmlspecialchars($student['department']) ?>
                                        </span>
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300">
                                            <i class="fas fa-calendar-alt mr-1"></i>
                                            Year <?= $student['year'] ?>
                                        </span>
                                    </div>
                                    <div class="flex items-center mt-3 text-gray-600 dark:text-gray-400">
                                        <i class="fas fa-envelope mr-2"></i>
                                        <a href="mailto:<?= htmlspecialchars($student['email']) ?>" class="hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
                                            <?= htmlspecialchars($student['email']) ?>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tabbed Interface -->
            <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 overflow-hidden">
                <!-- Tab Navigation -->
                <div class="border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900">
                    <nav class="flex space-x-8 px-8" aria-label="Tabs">
                        <button onclick="switchTab('overview')" id="tab-overview" class="tab-button active border-b-2 border-blue-600 text-blue-600 py-4 px-1 text-sm font-medium transition-all duration-200">
                            <i class="fas fa-user mr-2"></i>Overview
                        </button>
                        <button onclick="switchTab('academic')" id="tab-academic" class="tab-button border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 py-4 px-1 text-sm font-medium transition-all duration-200">
                            <i class="fas fa-graduation-cap mr-2"></i>Academic
                        </button>
                        <button onclick="switchTab('personal')" id="tab-personal" class="tab-button border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 py-4 px-1 text-sm font-medium transition-all duration-200">
                            <i class="fas fa-address-card mr-2"></i>Personal
                        </button>
                        <button onclick="switchTab('activity')" id="tab-activity" class="tab-button border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 py-4 px-1 text-sm font-medium transition-all duration-200">
                            <i class="fas fa-chart-line mr-2"></i>Activity
                        </button>
                    </nav>
                </div>

                <!-- Tab Content -->
                <div class="p-8 bg-white dark:bg-gray-800">
                    <!-- Overview Tab -->
                    <div id="content-overview" class="tab-content">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                            <!-- About Section -->
                            <div class="bg-gray-50 dark:bg-gray-700 rounded-xl p-6 border border-gray-200 dark:border-gray-600">
                                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                                    <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-user text-blue-600 dark:text-blue-300 text-sm"></i>
                                    </div>
                                    About Student
                                </h3>
                                <?php if (!empty($student['bio'])): ?>
                                    <p class="text-gray-700 dark:text-gray-300 leading-relaxed"><?= nl2br(htmlspecialchars($student['bio'])) ?></p>
                                <?php else: ?>
                                    <div class="text-center py-8">
                                        <i class="fas fa-user-edit text-4xl text-gray-300 dark:text-gray-500 mb-3"></i>
                                        <p class="text-gray-500 dark:text-gray-400">No bio information available.</p>
                                        <a href="edit_student.php?id=<?= $student['id'] ?>" class="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 text-sm font-medium">Add bio information</a>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- Skills & Goals Section -->
                            <div class="space-y-6">
                                <!-- Skills -->
                                <div class="bg-gray-50 dark:bg-gray-700 rounded-xl p-6 border border-gray-200 dark:border-gray-600">
                                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                                        <div class="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mr-3">
                                            <i class="fas fa-tools text-green-600 dark:text-green-300 text-sm"></i>
                                        </div>
                                        Skills & Expertise
                                    </h3>
                                    <?php if (!empty($student['skills'])): ?>
                                        <div class="flex flex-wrap gap-3">
                                            <?php foreach (explode(',', $student['skills']) as $skill): ?>
                                                <span class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 text-gray-800 dark:text-gray-200 px-4 py-2 rounded-lg text-sm font-medium shadow-sm hover:shadow-md transition-shadow">
                                                    <i class="fas fa-check-circle text-green-500 mr-2 text-xs"></i>
                                                    <?= htmlspecialchars(trim($skill)) ?>
                                                </span>
                                            <?php endforeach; ?>
                                        </div>
                                    <?php else: ?>
                                        <div class="text-center py-8">
                                            <i class="fas fa-tools text-4xl text-gray-300 dark:text-gray-500 mb-3"></i>
                                            <p class="text-gray-500 dark:text-gray-400">No skills listed yet.</p>
                                            <a href="edit_student.php?id=<?= $student['id'] ?>" class="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 text-sm font-medium">Add skills</a>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <!-- Learning Goals -->
                                <div class="bg-gray-50 dark:bg-gray-700 rounded-xl p-6 border border-gray-200 dark:border-gray-600">
                                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                                        <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mr-3">
                                            <i class="fas fa-bullseye text-purple-600 dark:text-purple-300 text-sm"></i>
                                        </div>
                                        Learning Goals
                                    </h3>
                                    <?php if (!empty($student['goals'])): ?>
                                        <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
                                            <p class="text-gray-700 dark:text-gray-300 leading-relaxed"><?= nl2br(htmlspecialchars($student['goals'])) ?></p>
                                        </div>
                                    <?php else: ?>
                                        <div class="text-center py-8">
                                            <i class="fas fa-bullseye text-4xl text-gray-300 dark:text-gray-500 mb-3"></i>
                                            <p class="text-gray-500 dark:text-gray-400">No learning goals specified.</p>
                                            <a href="edit_student.php?id=<?= $student['id'] ?>" class="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 text-sm font-medium">Set learning goals</a>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                    <!-- Academic Tab -->
                    <div id="content-academic" class="tab-content hidden">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                            <!-- Academic Information -->
                            <div class="bg-gray-50 dark:bg-gray-700 rounded-xl p-6 border border-gray-200 dark:border-gray-600">
                                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-6 flex items-center">
                                    <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-graduation-cap text-blue-600 dark:text-blue-300 text-sm"></i>
                                    </div>
                                    Academic Information
                                </h3>
                                <div class="space-y-4">
                                    <div class="flex justify-between items-center py-3 border-b border-gray-200 dark:border-gray-600">
                                        <span class="text-gray-600 dark:text-gray-400">Department</span>
                                        <span class="font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($student['department']) ?></span>
                                    </div>
                                    <div class="flex justify-between items-center py-3 border-b border-gray-200 dark:border-gray-600">
                                        <span class="text-gray-600 dark:text-gray-400">Year</span>
                                        <span class="font-medium text-gray-900 dark:text-white"><?= $student['year'] ?></span>
                                    </div>
                                    <div class="flex justify-between items-center py-3 border-b border-gray-200 dark:border-gray-600">
                                        <span class="text-gray-600 dark:text-gray-400">Roll Number</span>
                                        <span class="font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($student['roll_number']) ?></span>
                                    </div>
                                    <div class="flex justify-between items-center py-3">
                                        <span class="text-gray-600 dark:text-gray-400">Division</span>
                                        <span class="font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($student['division'] ?? 'Not specified') ?></span>
                                    </div>
                                </div>
                            </div>

                            <!-- Academic Status -->
                            <div class="bg-gray-50 dark:bg-gray-700 rounded-xl p-6 border border-gray-200 dark:border-gray-600">
                                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-6 flex items-center">
                                    <div class="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-chart-line text-green-600 dark:text-green-300 text-sm"></i>
                                    </div>
                                    Academic Status
                                </h3>
                                <div class="space-y-4">
                                    <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
                                        <div class="flex items-center justify-between">
                                            <span class="text-gray-600 dark:text-gray-400">Verification Status</span>
                                            <?php if ($student['verified']): ?>
                                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300">
                                                    <i class="fas fa-check-circle mr-1"></i> Verified
                                                </span>
                                            <?php else: ?>
                                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300">
                                                    <i class="fas fa-clock mr-1"></i> Pending
                                                </span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
                                        <div class="flex items-center justify-between">
                                            <span class="text-gray-600 dark:text-gray-400">Member Since</span>
                                            <span class="font-medium text-gray-900 dark:text-white"><?= date('F j, Y', strtotime($student['created_at'])) ?></span>
                                        </div>
                                    </div>
                                    <?php if (isset($student['updated_at']) && $student['updated_at']): ?>
                                    <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
                                        <div class="flex items-center justify-between">
                                            <span class="text-gray-600 dark:text-gray-400">Last Updated</span>
                                            <span class="font-medium text-gray-900 dark:text-white"><?= date('F j, Y', strtotime($student['updated_at'])) ?></span>
                                        </div>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Personal Tab -->
                    <div id="content-personal" class="tab-content hidden">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                            <!-- Contact Information -->
                            <div class="bg-gray-50 dark:bg-gray-700 rounded-xl p-6 border border-gray-200 dark:border-gray-600">
                                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-6 flex items-center">
                                    <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-address-card text-blue-600 dark:text-blue-300 text-sm"></i>
                                    </div>
                                    Contact Information
                                </h3>
                                <div class="space-y-4">
                                    <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
                                        <div class="flex items-center">
                                            <i class="fas fa-envelope text-blue-500 mr-3"></i>
                                            <div>
                                                <p class="text-xs text-gray-500 dark:text-gray-400 uppercase tracking-wide">Email</p>
                                                <p class="text-gray-900 dark:text-white font-medium"><?= htmlspecialchars($student['email']) ?></p>
                                            </div>
                                        </div>
                                    </div>

                                    <?php if (!empty($student['phone'])): ?>
                                    <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
                                        <div class="flex items-center">
                                            <i class="fas fa-phone text-green-500 mr-3"></i>
                                            <div>
                                                <p class="text-xs text-gray-500 dark:text-gray-400 uppercase tracking-wide">Phone</p>
                                                <p class="text-gray-900 dark:text-white font-medium"><?= htmlspecialchars($student['phone']) ?></p>
                                            </div>
                                        </div>
                                    </div>
                                    <?php endif; ?>

                                    <!-- Personal Details -->
                                    <?php if (!empty($student['date_of_birth']) || !empty($student['gender'])): ?>
                                    <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
                                        <h4 class="font-medium text-gray-900 dark:text-white mb-3">Personal Details</h4>
                                        <?php if (!empty($student['date_of_birth'])): ?>
                                        <div class="flex justify-between items-center py-2">
                                            <span class="text-gray-600 dark:text-gray-400">Date of Birth</span>
                                            <span class="text-gray-900 dark:text-white"><?= date('F j, Y', strtotime($student['date_of_birth'])) ?></span>
                                        </div>
                                        <?php endif; ?>
                                        <?php if (!empty($student['gender'])): ?>
                                        <div class="flex justify-between items-center py-2">
                                            <span class="text-gray-600 dark:text-gray-400">Gender</span>
                                            <span class="text-gray-900 dark:text-white"><?= htmlspecialchars($student['gender']) ?></span>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <!-- Social Profiles & Address -->
                            <div class="space-y-6">
                                <!-- Social Profiles -->
                                <div class="bg-gray-50 dark:bg-gray-700 rounded-xl p-6 border border-gray-200 dark:border-gray-600">
                                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-6 flex items-center">
                                        <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mr-3">
                                            <i class="fas fa-share-alt text-purple-600 dark:text-purple-300 text-sm"></i>
                                        </div>
                                        Social Profiles
                                    </h3>
                                    <div class="grid grid-cols-2 gap-4">
                                        <?php if (!empty($student['linkedin'])): ?>
                                        <a href="<?= htmlspecialchars($student['linkedin']) ?>" target="_blank" class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-600 hover:shadow-md transition-shadow">
                                            <div class="flex items-center">
                                                <i class="fab fa-linkedin text-blue-600 text-xl mr-3"></i>
                                                <span class="text-gray-900 dark:text-white font-medium">LinkedIn</span>
                                            </div>
                                        </a>
                                        <?php endif; ?>

                                        <?php if (!empty($student['github'])): ?>
                                        <a href="<?= htmlspecialchars($student['github']) ?>" target="_blank" class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-600 hover:shadow-md transition-shadow">
                                            <div class="flex items-center">
                                                <i class="fab fa-github text-gray-800 dark:text-gray-200 text-xl mr-3"></i>
                                                <span class="text-gray-900 dark:text-white font-medium">GitHub</span>
                                            </div>
                                        </a>
                                        <?php endif; ?>

                                        <?php if (!empty($student['twitter'])): ?>
                                        <a href="<?= htmlspecialchars($student['twitter']) ?>" target="_blank" class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-600 hover:shadow-md transition-shadow">
                                            <div class="flex items-center">
                                                <i class="fab fa-twitter text-blue-400 text-xl mr-3"></i>
                                                <span class="text-gray-900 dark:text-white font-medium">Twitter</span>
                                            </div>
                                        </a>
                                        <?php endif; ?>

                                        <?php if (!empty($student['website'])): ?>
                                        <a href="<?= htmlspecialchars($student['website']) ?>" target="_blank" class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-600 hover:shadow-md transition-shadow">
                                            <div class="flex items-center">
                                                <i class="fas fa-globe text-purple-600 text-xl mr-3"></i>
                                                <span class="text-gray-900 dark:text-white font-medium">Website</span>
                                            </div>
                                        </a>
                                        <?php endif; ?>
                                    </div>

                                    <?php if (empty($student['linkedin']) && empty($student['github']) && empty($student['twitter']) && empty($student['website'])): ?>
                                    <div class="text-center py-8">
                                        <i class="fas fa-share-alt text-4xl text-gray-300 dark:text-gray-500 mb-3"></i>
                                        <p class="text-gray-500 dark:text-gray-400">No social profiles linked.</p>
                                        <a href="edit_student.php?id=<?= $student['id'] ?>" class="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 text-sm font-medium">Add social profiles</a>
                                    </div>
                                    <?php endif; ?>
                                </div>

                                <!-- Address Information -->
                                <?php if (!empty($student['address']) || !empty($student['city']) || !empty($student['state'])): ?>
                                <div class="bg-gray-50 dark:bg-gray-700 rounded-xl p-6 border border-gray-200 dark:border-gray-600">
                                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-6 flex items-center">
                                        <div class="w-8 h-8 bg-red-100 dark:bg-red-900 rounded-lg flex items-center justify-center mr-3">
                                            <i class="fas fa-map-marker-alt text-red-600 dark:text-red-300 text-sm"></i>
                                        </div>
                                        Address
                                    </h3>
                                    <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
                                        <?php if (!empty($student['address'])): ?>
                                        <p class="text-gray-900 dark:text-white"><?= htmlspecialchars($student['address']) ?></p>
                                        <?php endif; ?>
                                        <div class="flex flex-wrap gap-2 mt-2">
                                            <?php if (!empty($student['city'])): ?>
                                            <span class="text-gray-600 dark:text-gray-400"><?= htmlspecialchars($student['city']) ?></span>
                                            <?php endif; ?>
                                            <?php if (!empty($student['state'])): ?>
                                            <span class="text-gray-600 dark:text-gray-400"><?= htmlspecialchars($student['state']) ?></span>
                                            <?php endif; ?>
                                            <?php if (!empty($student['country'])): ?>
                                            <span class="text-gray-600 dark:text-gray-400"><?= htmlspecialchars($student['country']) ?></span>
                                            <?php endif; ?>
                                            <?php if (!empty($student['pincode'])): ?>
                                            <span class="text-gray-600 dark:text-gray-400"><?= htmlspecialchars($student['pincode']) ?></span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Activity Tab -->
                    <div id="content-activity" class="tab-content hidden">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                            <!-- Mentorship Requests -->
                            <div class="bg-gray-50 dark:bg-gray-700 rounded-xl p-6 border border-gray-200 dark:border-gray-600">
                                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-6 flex items-center">
                                    <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-hands-helping text-blue-600 dark:text-blue-300 text-sm"></i>
                                    </div>
                                    Mentorship Requests
                                </h3>
                                <?php if (!empty($mentorships)): ?>
                                    <div class="space-y-4">
                                        <?php foreach ($mentorships as $mentorship): ?>
                                            <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
                                                <div class="flex items-start gap-3">
                                                    <div class="w-12 h-12 rounded-xl bg-gray-100 dark:bg-gray-700 flex-shrink-0 overflow-hidden">
                                                        <?php if (!empty($mentorship['alumni_photo'])): ?>
                                                            <img src="../uploads/<?= htmlspecialchars($mentorship['alumni_photo']) ?>" alt="Alumni" class="w-full h-full object-cover">
                                                        <?php else: ?>
                                                            <div class="w-full h-full flex items-center justify-center text-gray-400 dark:text-gray-500">
                                                                <i class="fas fa-user"></i>
                                                            </div>
                                                        <?php endif; ?>
                                                    </div>
                                                    <div class="flex-grow">
                                                        <div class="flex justify-between items-start">
                                                            <h4 class="font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($mentorship['alumni_name']) ?></h4>
                                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                                                <?= $mentorship['status'] === 'pending' ? 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300' :
                                                                   ($mentorship['status'] === 'accepted' ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300' :
                                                                   'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300') ?>">
                                                                <?= ucfirst($mentorship['status']) ?>
                                                            </span>
                                                        </div>
                                                        <p class="text-sm text-gray-600 dark:text-gray-400 mt-1 line-clamp-2"><?= htmlspecialchars($mentorship['message']) ?></p>
                                                        <p class="text-xs text-gray-500 dark:text-gray-500 mt-2"><?= date('M d, Y', strtotime($mentorship['created_at'])) ?></p>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                <?php else: ?>
                                    <div class="text-center py-12">
                                        <i class="fas fa-hands-helping text-4xl text-gray-300 dark:text-gray-500 mb-3"></i>
                                        <p class="text-gray-500 dark:text-gray-400">No mentorship requests found.</p>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- Account Activity -->
                            <div class="bg-gray-50 dark:bg-gray-700 rounded-xl p-6 border border-gray-200 dark:border-gray-600">
                                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-6 flex items-center">
                                    <div class="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-user-shield text-green-600 dark:text-green-300 text-sm"></i>
                                    </div>
                                    Account Activity
                                </h3>
                                <div class="space-y-4">
                                    <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center">
                                                <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mr-3">
                                                    <i class="fas fa-user-plus text-blue-600 dark:text-blue-300"></i>
                                                </div>
                                                <div>
                                                    <p class="font-medium text-gray-900 dark:text-white">Account Created</p>
                                                    <p class="text-sm text-gray-600 dark:text-gray-400"><?= date('F j, Y \a\t g:i A', strtotime($student['created_at'])) ?></p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <?php if (isset($student['updated_at']) && $student['updated_at']): ?>
                                    <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center">
                                                <div class="w-10 h-10 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mr-3">
                                                    <i class="fas fa-edit text-green-600 dark:text-green-300"></i>
                                                </div>
                                                <div>
                                                    <p class="font-medium text-gray-900 dark:text-white">Last Profile Update</p>
                                                    <p class="text-sm text-gray-600 dark:text-gray-400"><?= date('F j, Y \a\t g:i A', strtotime($student['updated_at'])) ?></p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <?php endif; ?>

                                    <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center">
                                                <div class="w-10 h-10 <?= $student['verified'] ? 'bg-green-100 dark:bg-green-900' : 'bg-yellow-100 dark:bg-yellow-900' ?> rounded-lg flex items-center justify-center mr-3">
                                                    <i class="fas <?= $student['verified'] ? 'fa-check-circle text-green-600 dark:text-green-300' : 'fa-clock text-yellow-600 dark:text-yellow-300' ?>"></i>
                                                </div>
                                                <div>
                                                    <p class="font-medium text-gray-900 dark:text-white">Verification Status</p>
                                                    <p class="text-sm <?= $student['verified'] ? 'text-green-600 dark:text-green-300' : 'text-yellow-600 dark:text-yellow-300' ?>">
                                                        <?= $student['verified'] ? 'Account Verified' : 'Pending Verification' ?>
                                                    </p>
                                                </div>
                                            </div>
                                            <?php if (!$student['verified']): ?>
                                            <a href="verify_student.php?id=<?= $student['id'] ?>" class="inline-flex items-center px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors">
                                                <i class="fas fa-check-circle mr-1"></i> Verify
                                            </a>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- JavaScript for Tab Functionality -->
    <script>
        function switchTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.add('hidden');
            });

            // Remove active class from all tab buttons
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('active', 'border-blue-600', 'text-blue-600');
                button.classList.add('border-transparent', 'text-gray-500', 'hover:text-gray-700', 'hover:border-gray-300', 'dark:text-gray-400', 'dark:hover:text-gray-300');
            });

            // Show selected tab content
            document.getElementById('content-' + tabName).classList.remove('hidden');

            // Add active class to selected tab button
            const activeButton = document.getElementById('tab-' + tabName);
            activeButton.classList.add('active', 'border-blue-600', 'text-blue-600');
            activeButton.classList.remove('border-transparent', 'text-gray-500', 'hover:text-gray-700', 'hover:border-gray-300', 'dark:text-gray-400', 'dark:hover:text-gray-300');
        }

        function toggleDropdown(dropdownId) {
            const dropdown = document.getElementById(dropdownId);
            dropdown.classList.toggle('hidden');

            // Close dropdown when clicking outside
            document.addEventListener('click', function(event) {
                if (!event.target.closest('#' + dropdownId) && !event.target.closest('button')) {
                    dropdown.classList.add('hidden');
                }
            });
        }

        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            if (!event.target.closest('.relative')) {
                document.querySelectorAll('[id$="Dropdown"]').forEach(dropdown => {
                    dropdown.classList.add('hidden');
                });
            }
        });
    </script>

<?php
// Include the footer
require_once '../includes/footer.php';
?> 